package com.bees360.config;

import com.bees360.event.EventPublisher;
import com.bees360.firebase.FirebaseListener;
import com.bees360.firebase.listener.CollectionChangedListenerRelay;

import org.springframework.context.annotation.Bean;

public class FirebaseHoverJobConfig {

    @Bean
    FirebaseListener hoverJobChangedListener(EventPublisher eventPublisher) {
        return new CollectionChangedListenerRelay(
                eventPublisher, firestore -> firestore.collection("hover_job"), false);
    }
}
