package com.bees360.hover;

import static com.bees360.util.ProtoStructAdapter.jsonToStruct;

import com.bees360.common.Message.QuerySymbol;
import com.bees360.estintel.Factor;
import com.bees360.estintel.FactorProvider;
import com.bees360.estintel.FactorQuery;
import com.bees360.estintel.FactorValueProvider;
import com.bees360.estintel.FactorValueQuery;
import com.bees360.estintel.LambdaExecutionRequest;
import com.bees360.estintel.Message;
import com.bees360.estintel.NonBlockingLambdaExecutionManager;
import com.bees360.util.Iterables;
import com.bees360.util.ProtoStructAdapter;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import lombok.extern.log4j.Log4j2;

import java.util.List;

@Log4j2
public class EstintelProjectHoverManager extends ForwardingProjectHoverManager {
    private final NonBlockingLambdaExecutionManager nonBlockingLambdaExecutionManager;
    private final FactorProvider factorProvider;
    private final FactorValueProvider factorValueProvider;

    private static final String UPGRADE_HOVER_LAMBDA_KEY = "ORDER_HOVER_UPGRADE";

    private static final List<String> HOVER_ORDER_KEYS = List.of("HOVER_JOB_METADATA");

    /** 默认为 main Dwelling */
    private static final String DEFAULT_BUILDING_TYPE = "Main Dwelling";

    private static final Gson gson = new Gson();

    private static final String SYSTEM_USER = "10000";

    public EstintelProjectHoverManager(
            ProjectHoverManager projectHoverManager,
            NonBlockingLambdaExecutionManager nonBlockingLambdaExecutionManager,
            FactorProvider factorProvider,
            FactorValueProvider factorValueProvider) {
        super(projectHoverManager);
        this.nonBlockingLambdaExecutionManager = nonBlockingLambdaExecutionManager;
        this.factorProvider = factorProvider;
        this.factorValueProvider = factorValueProvider;
        log.info(
                "Created {}(projectHoverManager={}, nonBlockingLambdaExecutionManager={},"
                        + " factorValueProvider={}).",
                this,
                projectHoverManager,
                this.nonBlockingLambdaExecutionManager,
                this.factorValueProvider);
    }

    @Override
    public boolean upgradeType(String projectId, String type) {
        var factorQuery =
                FactorQuery.from(
                        Message.FactorQueryRequest.newBuilder()
                                .addAllKey(HOVER_ORDER_KEYS)
                                .build());
        var factors = factorProvider.findByQuery(factorQuery);
        var factorIds = Iterables.transform(factors, Factor::getId);

        JsonObject attribute = new JsonObject();
        JsonObject projectIdAttribute = new JsonObject();
        projectIdAttribute.addProperty(QuerySymbol.EQ.name(), projectId);
        JsonObject buildingTypeAttribute = new JsonObject();
        buildingTypeAttribute.addProperty(QuerySymbol.EQ.name(), DEFAULT_BUILDING_TYPE);
        attribute.add("projectId", projectIdAttribute);
        /** 默认为 main Dwelling */
        attribute.add("buildingType", buildingTypeAttribute);

        var factorValueQuery =
                FactorValueQuery.from(
                        Message.FactorValueQueryRequest.newBuilder()
                                .addAllFactorId(factorIds)
                                .setAttribute(ProtoStructAdapter.jsonToStruct(attribute.toString()))
                                .setLimit(
                                        Message.LimitQueryRequest.newBuilder().setLimit(1).build())
                                .build());
        var count = factorValueProvider.findAndCountByQuery(factorValueQuery);
        if (count.getTotalCount() <= 0) {
            throw new IllegalArgumentException(
                    String.format(
                            "No hover job found : projectId is  %s and type is %s.",
                            projectId, type));
        }

        SendHoverDto dto = SendHoverDto.builder().projectId(projectId).type(type).build();

        var runId =
                nonBlockingLambdaExecutionManager.triggerLambdaExecution(
                        LambdaExecutionRequest.from(
                                Message.LambdaExecutionRequest.newBuilder()
                                        .setLambdaKey(UPGRADE_HOVER_LAMBDA_KEY)
                                        .setParams(jsonToStruct(gson.toJson(dto)))
                                        .setExecutedBy(SYSTEM_USER)
                                        .build()));
        log.info(
                "Triggered lambda execution with runId={}, lambdaKey: {}, parameter: {}",
                runId,
                UPGRADE_HOVER_LAMBDA_KEY,
                dto);
        return true;
    }
}
