package com.bees360.threedmodel.data;

import com.bees360.api.common.Point;
import com.bees360.threedmodel.BiImageId;
import com.bees360.threedmodel.ImagePoint;
import com.bees360.threedmodel.MappingInfo;
import com.bees360.threedmodel.Message.ThreeDModelMessage.Component.Facet.Line.Type;
import com.bees360.threedmodel.ThreeDModelManager;
import com.bees360.util.Iterables;
import com.google.gson.Gson;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.platform.commons.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
public abstract class AbstractThreeDModelTest {

    private static final String TEST_PROJECT_ID = String.valueOf(RandomUtils.nextLong());

    private static final String TEST_USER_ID = "test_user_id";

    private static final double PRECISION = 0.001;

    private final ThreeDModelDataManager repository;

    private final ThreeDModelManager threeDModelManager;

    private final Gson gson = new Gson();

    protected String modelIdUnnamed;

    protected String modelIdNamed;

    protected List<ModelImage> modelImageUnnamed;

    protected List<ModelImage> modelImageNamed;

    protected List<ModelFacet> modelFacetNamed;

    protected List<ModelFacet> modelFacetUnnamed;

    protected List<ImageFacet> imageFacetNamed;

    protected List<ImageFacet> imageFacetUnnamed;

    protected String northNamed;

    protected String northUnnamed;

    protected List<ModelImage> camPropertyMatrixNamed;

    protected List<ModelImage> camPropertyMatrixUnnamed;

    protected String modelNameNamed;

    protected String modelNameUnnamed;

    protected Function<String, Iterable<Point>> wktPolygonToPoint;

    public AbstractThreeDModelTest(
            ThreeDModelDataManager repository, ThreeDModelManager threeDModelManager) {
        this.repository = repository;
        this.threeDModelManager = threeDModelManager;
    }

    /** test threeDModelDataManager */
    public void threeDDataTest() throws IOException {
        // 3d
        testSaveModel();
        testSaveModelImage();
        // postBoundary
        testGetModelId();
        testSetNorthAndJsonKey();
        testSaveModelFacet();
        testSaveImageFacet();
        testSetCamPropertyMatrix();
    }

    /** init protected data from local file :maplist.txt and postBoundary.json */
    protected abstract void initData() throws IOException;

    private void testSaveModel() throws IOException {
        String testModelName = "test_building";
        Model modelNamed = Model.of(null, testModelName, TEST_PROJECT_ID, null, 0, null);
        modelIdNamed = repository.saveModel(modelNamed);
        String defaultModelName = "default_model_name";
        Model modelUnnamed = Model.of(null, defaultModelName, TEST_PROJECT_ID, null, 0, null);
        modelIdUnnamed = repository.saveModel(modelUnnamed);
        var models = repository.getModelByGroupKey(TEST_PROJECT_ID);
        var modelIds = models.stream().map(Model::getId).collect(Collectors.toList());
        var modelById = repository.getModelById(modelIds);
        Assertions.assertTrue(modelById.containsKey(modelIdNamed));
        Assertions.assertTrue(modelById.containsKey(modelIdUnnamed));
        initData();
    }

    private void testSetNorthAndJsonKey() {
        String testJsonKey = "test_json_key";
        repository.setNorthAndJsonKey(modelIdNamed, northNamed, testJsonKey);
        repository.setNorthAndJsonKey(modelIdUnnamed, northUnnamed, testJsonKey);
        var modelMap = repository.getModelById(List.of(modelIdNamed, modelIdUnnamed));
        Assertions.assertEquals(2, modelMap.size());
        Assertions.assertEquals(northNamed, modelMap.get(modelIdNamed).getNorth());
        Assertions.assertEquals(testJsonKey, modelMap.get(modelIdNamed).getJsonKey());
        Assertions.assertEquals(northUnnamed, modelMap.get(modelIdUnnamed).getNorth());
        Assertions.assertEquals(testJsonKey, modelMap.get(modelIdUnnamed).getJsonKey());
    }

    private void testSaveImageFacet() {
        repository.saveImageFacet(imageFacetNamed);
        repository.saveImageFacet(imageFacetUnnamed);
        var imageFacetMap =
                repository.getImageFacetByModelId(List.of(modelIdNamed, modelIdUnnamed));
        Assertions.assertEquals(2, imageFacetMap.size());
        var facetIdNamed =
                imageFacetNamed.stream().map(ImageFacet::getFacetId).collect(Collectors.toSet());
        var facetIdUnnamed =
                imageFacetUnnamed.stream().map(ImageFacet::getFacetId).collect(Collectors.toSet());
        Assertions.assertEquals(imageFacetMap.get(modelIdNamed).size(), imageFacetNamed.size());
        Assertions.assertEquals(imageFacetMap.get(modelIdUnnamed).size(), imageFacetUnnamed.size());
        imageFacetMap
                .get(modelIdNamed)
                .forEach(
                        imageFacet ->
                                Assertions.assertTrue(
                                        facetIdNamed.contains(imageFacet.getFacetId())));
        imageFacetMap
                .get(modelIdUnnamed)
                .forEach(
                        imageFacet ->
                                Assertions.assertTrue(
                                        facetIdUnnamed.contains(imageFacet.getFacetId())));
    }

    private void testSaveModelFacet() {
        repository.saveModelFacet(modelFacetNamed);
        repository.saveModelFacet(modelFacetUnnamed);
        var modelFacetByModelId =
                repository.getModelFacetByModelId(List.of(modelIdNamed, modelIdUnnamed));
        Assertions.assertEquals(2, modelFacetByModelId.size());
        var facetIdNamed =
                modelFacetNamed.stream().map(ModelFacet::getFacetId).collect(Collectors.toSet());
        var facetIdUnnamed =
                modelFacetUnnamed.stream().map(ModelFacet::getFacetId).collect(Collectors.toSet());
        Assertions.assertEquals(
                modelFacetNamed.size(), modelFacetByModelId.get(modelIdNamed).size());
        Assertions.assertEquals(
                modelFacetUnnamed.size(), modelFacetByModelId.get(modelIdUnnamed).size());
        modelFacetByModelId
                .get(modelIdNamed)
                .forEach(
                        modelFacet ->
                                Assertions.assertTrue(
                                        facetIdNamed.contains(modelFacet.getFacetId())));
        modelFacetByModelId
                .get(modelIdUnnamed)
                .forEach(
                        modelFacet ->
                                Assertions.assertTrue(
                                        facetIdUnnamed.contains(modelFacet.getFacetId())));
    }

    private void testSaveModelImage() {
        repository.saveModelImage(modelImageNamed);
        repository.saveModelImage(modelImageUnnamed);
        var modelImageByModelId =
                repository.getModelImageByModelId(List.of(modelIdNamed, modelIdUnnamed));
        var imageIdNamed =
                modelImageNamed.stream().map(ModelImage::getImageId).collect(Collectors.toSet());
        var imageIdUnnamed =
                modelImageUnnamed.stream().map(ModelImage::getImageId).collect(Collectors.toSet());
        Assertions.assertEquals(
                modelImageNamed.size(), modelImageByModelId.get(modelIdNamed).size());
        Assertions.assertEquals(
                modelImageUnnamed.size(), modelImageByModelId.get(modelIdUnnamed).size());
        modelImageByModelId
                .get(modelIdNamed)
                .forEach(
                        modelImage ->
                                Assertions.assertTrue(
                                        imageIdNamed.contains(modelImage.getImageId())));
        modelImageByModelId
                .get(modelIdUnnamed)
                .forEach(
                        modelImage ->
                                Assertions.assertTrue(
                                        imageIdUnnamed.contains(modelImage.getImageId())));
    }

    private void testSetCamPropertyMatrix() {
        repository.setCamPropertyMatrix(camPropertyMatrixNamed);
        repository.setCamPropertyMatrix(camPropertyMatrixUnnamed);
        var modelImageByModelId =
                repository.getModelImageByModelId(List.of(modelIdNamed, modelIdUnnamed));
        modelImageByModelId.forEach(
                (modelId, modelImageList) ->
                        modelImageList.forEach(
                                modelImage ->
                                        Assertions.assertTrue(
                                                StringUtils.isNotBlank(
                                                        modelImage.getCamPropertyMatrix()))));
    }

    private void testGetModelId() {
        String idNamed = repository.getModelId(TEST_PROJECT_ID, modelNameNamed);
        String idUnnamed = repository.getModelId(TEST_PROJECT_ID, modelNameUnnamed);
        Assertions.assertEquals(modelIdNamed, idNamed);
        Assertions.assertEquals(modelIdUnnamed, idUnnamed);
    }

    public void testBatchSetImageInModel() {
        var modelImageByModelId = repository.getModelImageByModelId(modelIdNamed);
        var imageIds =
                modelImageByModelId.stream()
                        .map(ModelImage::getImageId)
                        .collect(Collectors.toList());
        // 先删除
        threeDModelManager.deleteImageFromModel(imageIds, modelIdNamed, TEST_USER_ID);
        modelImageByModelId = repository.getModelImageByModelId(modelIdNamed);
        Assertions.assertEquals(0, modelImageByModelId.size());
        // 增加
        threeDModelManager.addImageInModel(imageIds, modelIdNamed, TEST_USER_ID);
        modelImageByModelId = repository.getModelImageByModelId(modelIdNamed);
        Assertions.assertEquals(imageIds.size(), modelImageByModelId.size());
    }

    public void testSetImageInModel() {
        String testImageId = "test_set_image_in_model_image_id";
        // 增加
        threeDModelManager.addImageInModel(testImageId, modelIdNamed, TEST_USER_ID);
        var modelImageByModelId = repository.getModelImageByModelId(modelIdNamed);
        Assertions.assertTrue(
                modelImageByModelId.stream()
                        .map(ModelImage::getImageId)
                        .collect(Collectors.toSet())
                        .contains(testImageId));
        // 删除
        int sizeBeforeDelete = modelImageByModelId.size();
        threeDModelManager.deleteImageFromModel(testImageId, modelIdNamed, TEST_USER_ID);
        modelImageByModelId = repository.getModelImageByModelId(modelIdNamed);
        Assertions.assertEquals(sizeBeforeDelete - 1, modelImageByModelId.size());
        Assertions.assertFalse(
                modelImageByModelId.stream()
                        .map(ModelImage::getImageId)
                        .collect(Collectors.toSet())
                        .contains(testImageId));
    }

    public void testSetLineType() {
        int testFacetId = 0;
        Type testType = Type.forNumber(1);
        int testLineIndex = 0;
        threeDModelManager.setLineType(
                modelIdNamed, 1, testFacetId, testLineIndex, testType, TEST_USER_ID);
        var modelFacetByModelId = repository.getModelFacetByModelId(modelIdNamed);
        for (ModelFacet modelFacet : modelFacetByModelId) {
            if (modelFacet.getFacetId() == testFacetId) {
                var pathType = gson.fromJson(modelFacet.getPathType(), int[].class);
                Assertions.assertEquals(testType.getNumber(), pathType[testLineIndex]);
                break;
            }
        }
    }

    public void testSetRotationDegree() {
        double testRotationDegree = 108.65;
        threeDModelManager.setRotationDegree(modelIdNamed, testRotationDegree, TEST_USER_ID);
        var model = repository.getModelById(modelIdNamed);
        Assertions.assertTrue(doubleEquals(testRotationDegree, model.getRotationDegree()));
    }

    public void testGetModelIdByImageIds() {
        var modelImageByModelId = repository.getModelImageByModelId(modelIdNamed);
        var imageIds =
                modelImageByModelId.stream()
                        .map(ModelImage::getImageId)
                        .collect(Collectors.toList());
        var modelIdMap = threeDModelManager.getModelId(imageIds);
        modelIdMap.forEach(
                (imageId, modeIds) -> Assertions.assertTrue(Iterables.toList(modeIds).size() >= 2));
    }

    public void testGetModelByGroupKey() {
        var threeDModels = Iterables.toList(threeDModelManager.getModel(TEST_PROJECT_ID));
        var modelIds = Set.of(modelIdNamed, modelIdUnnamed);
        threeDModels.forEach(model -> Assertions.assertTrue(modelIds.contains(model.getId())));
    }

    public void testGetImageMapping() {
        List<Point> mappingExpect = new ArrayList<>();
        // data from upstream:
        // originImageId: -gZUS9KHPPAQ-VvLsFx8kAqecr8tXikj
        // targetImageId: 02N2ItoZjR2F85xjmTcSB4XmOCEQdRs0
        // 603.3594074517024, -1694.2596039876132
        // 2479.476149801094, -1548.0515002685029
        // 2648.9003859039362, -1213.5304950031043
        // 1416.9969632243724, -1321.5903084510333
        // 603.3594074517024, -1694.2596039876132
        mappingExpect.add(Point.of(603.3594074517024, -1694.2596039876132));
        mappingExpect.add(Point.of(2479.476149801094, -1548.0515002685029));
        mappingExpect.add(Point.of(2648.9003859039362, -1213.5304950031043));
        mappingExpect.add(Point.of(1416.9969632243724, -1321.5903084510333));
        mappingExpect.add(Point.of(603.3594074517024, -1694.2596039876132));
        String originImageId = "-gZUS9KHPPAQ-VvLsFx8kAqecr8tXikj";
        String targetImageId = "02N2ItoZjR2F85xjmTcSB4XmOCEQdRs0";
        var imageMapping =
                Iterables.toList(
                        threeDModelManager.getImageMapping(
                                BiImageId.of(originImageId, targetImageId)));
        Assertions.assertEquals(1, imageMapping.size());
        var mappingInfo = imageMapping.get(0);
        var mapping = Iterables.toList(mappingInfo.getMapping());
        log.info("mapping on facet: {}", mappingInfo.getFacetId());
        Assertions.assertEquals(mappingExpect.size(), mapping.size());
        for (int i = 0; i < mapping.size(); i++) {
            var point = mapping.get(i);
            var pointExpect = mappingExpect.get(i);
            log.info("mapping to point: {}, {}", point.getX(), point.getY());
            Assertions.assertTrue(pointEquals(pointExpect, point));
        }
    }

    public void testGetPointsMapping() {
        // data from upstream:
        // originImageId: -gZUS9KHPPAQ-VvLsFx8kAqecr8tXikj
        // targetImageId: 0LJLB_vTlPDrkXnx1r8HMlUiOTvmnnzF
        // 3520.738116781786, -2108.0005489764653
        // 6115.740382646573, -2097.6308536822794
        // 4948.200764972234, -1692.7652241188532
        // 3356.945977569924, -1692.173334621761
        // 3520.738116781786, -2108.0005489764653
        Map<String, List<Point>> mappingExpectedMap = new HashMap<>();
        mappingExpectedMap.put(
                "02N2ItoZjR2F85xjmTcSB4XmOCEQdRs0",
                List.of(
                        Point.of(603.3594074517024, -1694.2596039876132),
                        Point.of(2479.476149801094, -1548.0515002685029),
                        Point.of(2648.9003859039362, -1213.5304950031043),
                        Point.of(1416.9969632243724, -1321.5903084510333),
                        Point.of(603.3594074517024, -1694.2596039876132)));
        mappingExpectedMap.put(
                "0LJLB_vTlPDrkXnx1r8HMlUiOTvmnnzF",
                List.of(
                        Point.of(3520.738116781786, -2108.0005489764653),
                        Point.of(6115.740382646573, -2097.6308536822794),
                        Point.of(4948.200764972234, -1692.7652241188532),
                        Point.of(3356.945977569924, -1692.173334621761),
                        Point.of(3520.738116781786, -2108.0005489764653)));
        var imageFacetByModelId = repository.getImageFacetByModelId(modelIdNamed);
        var testImageFacet =
                imageFacetByModelId.stream()
                        .filter(imageFacet -> imageFacet.getFacetId() == 0)
                        .findFirst()
                        .orElse(null);
        assert testImageFacet != null;
        var originPoints = wktPolygonToPoint.apply(testImageFacet.getPath2DSeen());
        String originImageId = "-gZUS9KHPPAQ-VvLsFx8kAqecr8tXikj";
        var pointMapping =
                threeDModelManager.getPointMapping(
                        MappingInfo.of(originImageId, null, originPoints));
        pointMapping.forEach(
                mappingInfo -> {
                    log.info("mapping to image: {}", mappingInfo.getImageId());
                    var mapping = Iterables.toList(mappingInfo.getMapping());
                    var mappingExpected = mappingExpectedMap.get(mappingInfo.getImageId());
                    Assertions.assertEquals(mappingExpected.size(), mapping.size());
                    for (int i = 0; i < mapping.size(); i++) {
                        Point pointActual = mapping.get(i);
                        Point pointExpected = mappingExpected.get(i);
                        log.info(
                                "mapping to point: {}, {}", pointActual.getX(), pointActual.getY());
                        Assertions.assertTrue(pointEquals(pointActual, pointExpected));
                    }
                });
    }

    public void testGetPointOrientation() {
        var imageFacetByModelId = repository.getImageFacetByModelId(modelIdNamed);
        var modelFacetMap =
                repository.getModelFacetByModelId(modelIdNamed).stream()
                        .collect(
                                Collectors.toMap(ModelFacet::getFacetId, modelFacet -> modelFacet));
        imageFacetByModelId.forEach(
                imageFacet -> {
                    var testPoint =
                            Iterables.toList(wktPolygonToPoint.apply(imageFacet.getPath2DSeen()))
                                    .get(2);
                    var pointOrientation =
                            threeDModelManager.getPointOrientation(
                                    ImagePoint.of(imageFacet.getImageId(), testPoint));
                    Assertions.assertEquals(
                            modelFacetMap.get(imageFacet.getFacetId()).getOrientation(),
                            pointOrientation.getOrientation().getNumber());
                });
    }

    private boolean pointEquals(Point origin, Point target) {
        return doubleEquals(origin.getX(), target.getX())
                && doubleEquals(origin.getY(), target.getY());
    }

    private boolean doubleEquals(double origin, double target) {
        return Math.abs(origin - target) < PRECISION;
    }
}
