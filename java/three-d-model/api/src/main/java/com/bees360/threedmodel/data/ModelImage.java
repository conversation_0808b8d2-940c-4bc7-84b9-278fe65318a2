package com.bees360.threedmodel.data;

/** image in 3d model */
interface ModelImage {
    /**
     * get model id
     *
     * @return {@link String}
     * @see String
     */
    String getModelId();

    /**
     * get image id
     *
     * @return {@link String}
     */
    String getImageId();

    /**
     * get cam property matrix
     *
     * @return {@link String} 2维json数组 3x4
     */
    String getCamPropertyMatrix();

    static ModelImage of(String modelId, String imageId, String camPropertyMatrix) {
        return new ModelImage() {
            @Override
            public String getModelId() {
                return modelId;
            }

            @Override
            public String getImageId() {
                return imageId;
            }

            @Override
            public String getCamPropertyMatrix() {
                return camPropertyMatrix;
            }
        };
    }
}
