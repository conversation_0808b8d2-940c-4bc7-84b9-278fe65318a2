package com.bees360.threedmodel.data;

import com.bees360.job.config.ResourcePoolConfig;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.threedmodel.config.JobParamProviderConfig;
import com.bees360.threedmodel.config.ModelNameProviderConfig;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.Function;

@Import({
    ResourcePoolConfig.class,
    JooqThreeDModelDataManagerConfig.class,
    ModelNameProviderConfig.class,
    JobParamProviderConfig.class,
})
@Configuration
public class JobExecutorRegistry {
    @Bean
    public PostBoundaryDataJobExecutor postBoundaryDataJobExecutor(
            ResourcePool resourcePool,
            ThreeDModelDataManager threeDModelDataManager,
            @Qualifier("groupKeyProvider") Function<String, String> groupKeyProvider,
            @Qualifier("jsonKeyProvider") Function<String, String> jsonKeyProvider) {
        return new PostBoundaryDataJobExecutor(
                resourcePool, threeDModelDataManager, groupKeyProvider, jsonKeyProvider);
    }

    @Bean
    public ThreeDDataJobExecutor threeDDataJobExecutor(
            ResourcePool resourcePool,
            ThreeDModelDataManager threeDModelDataManager,
            @Qualifier("modelNameProvider") Function<Resource, String> modelNameProvider,
            @Qualifier("groupKeyProvider") Function<String, String> groupKeyProvider,
            @Qualifier("mapListKeyProvider") Function<String, String> mapListKeyProvider) {
        return new ThreeDDataJobExecutor(
                resourcePool,
                threeDModelDataManager,
                modelNameProvider,
                groupKeyProvider,
                mapListKeyProvider);
    }
}
