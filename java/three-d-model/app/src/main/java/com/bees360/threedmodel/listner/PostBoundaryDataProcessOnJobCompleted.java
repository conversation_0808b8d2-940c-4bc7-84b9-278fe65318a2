package com.bees360.threedmodel.listner;

import com.bees360.event.registry.JobCompleted;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.PostBoundaryDataJob;
import com.bees360.job.util.EventTriggeredJob;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class PostBoundaryDataProcessOnJobCompleted extends EventTriggeredJob<JobCompleted> {

    private static final String ROUTING_KEY = "job_completed.point_cloud";

    public PostBoundaryDataProcessOnJobCompleted(JobScheduler jobScheduler) {
        super(jobScheduler);
        log.info("Register Event Listener PostBoundaryDataProcessOnJobCompleted");
    }

    @Override
    protected boolean filter(JobCompleted jobCompleted) {
        return jobCompleted.getId().indexOf("pb") == 0;
    }

    @Override
    protected Job convert(JobCompleted jobCompleted) {
        log.info("Converting postBoundary job, job_id: {}", jobCompleted.getId());
        return Job.ofPayload(
                new PostBoundaryDataJob(jobCompleted.getId(), jobCompleted.getStatus()));
    }

    @Override
    public String getRoutingKey() {
        return ROUTING_KEY;
    }
}
