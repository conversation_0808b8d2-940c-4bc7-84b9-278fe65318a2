package com.bees360.threedmodel;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.threedmodel.config.JooqThreeDModelManagerConfig;
import com.bees360.threedmodel.config.ThreeDModelDataJobConfig;
import com.bees360.threedmodel.data.JobExecutorRegistry;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import lombok.extern.log4j.Log4j2;

import org.springframework.context.annotation.Import;

@Import({
    // job
    JobExecutorRegistry.class,
    RabbitJobScheduler.class,
    RabbitJobDispatcher.class,
    AutoRegisterJobExecutorConfig.class,

    // jooq
    JooqThreeDModelManagerConfig.class,

    // grpc
    GrpcThreeDModelService.class,

    // event
    ThreeDModelDataJobConfig.class,
    RabbitApiConfig.class,
    RabbitEventPublisher.class,
    RabbitEventDispatcher.class,
    AutoRegisterEventListenerConfig.class,
})
@Log4j2
@EnableEncryptableProperties
@ApplicationAutoConfig
public class ThreeDModelApp {
    public static void main(final String[] args) {
        ExitableSpringApplication.run(ThreeDModelApp.class, args);
    }
}
