package com.bees360.threedmodel;

import com.bees360.api.common.ThreeDPoint;

import jakarta.annotation.Nonnull;

public class RecordLine implements Line {

    private String id;

    private Message.ThreeDModelMessage.Component.Facet.Line.Type type;

    private ThreeDPoint startPoint;

    private ThreeDPoint endPoint;

    public RecordLine(
            String id,
            Message.ThreeDModelMessage.Component.Facet.Line.Type type,
            ThreeDPoint startPoint,
            ThreeDPoint endPoint) {
        this.id = id;
        this.type = type;
        this.startPoint = startPoint;
        this.endPoint = endPoint;
    }

    @Nonnull
    @Override
    public String getId() {
        return id;
    }

    @Nonnull
    @Override
    public Message.ThreeDModelMessage.Component.Facet.Line.Type getType() {
        return type;
    }

    @Nonnull
    @Override
    public ThreeDPoint getStart() {
        return startPoint;
    }

    @Nonnull
    @Override
    public ThreeDPoint getEnd() {
        return endPoint;
    }
}
