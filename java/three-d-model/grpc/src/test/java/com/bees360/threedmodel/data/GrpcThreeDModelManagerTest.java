package com.bees360.threedmodel.data;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.threedmodel.GrpcThreeDModelClient;
import com.bees360.threedmodel.MappingUtil;
import com.bees360.threedmodel.ThreeDModelManager;
import com.bees360.threedmodel.data.config.GrpcThreeDModelManagerTestConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;

@SpringBootTest
@ApplicationAutoConfig
public class GrpcThreeDModelManagerTest extends BaseThreeDModelTest {

    @Import(GrpcThreeDModelManagerTestConfig.class)
    @Configuration
    static class Config {
        @Bean
        ThreeDModelDataManager repository() {
            return new InMemoryThreeDModelDataManager();
        }

        @Bean
        ThreeDModelManager grpcThreeDModelServiceManager(ThreeDModelDataManager repository) {
            return new InMemoryThreeDModelManager(
                    repository,
                    GeometryUtil::wktPolygonToPoint,
                    GeometryUtil::pointInWktPolygon,
                    MappingUtil::twoDPointToAnother);
        }
    }

    public GrpcThreeDModelManagerTest(
            @Autowired ThreeDModelDataManager repository,
            @Autowired GrpcThreeDModelClient threeDModelManager)
            throws IOException {
        super(repository, threeDModelManager);
        threeDDataTest();
    }

    @Test
    @Override
    public void testBatchSetImageInModel() {
        super.testBatchSetImageInModel();
    }

    @Test
    @Override
    public void testSetImageInModel() {
        super.testSetImageInModel();
    }

    @Test
    @Override
    public void testSetLineType() {
        super.testSetLineType();
    }

    @Test
    @Override
    public void testSetRotationDegree() {
        super.testSetRotationDegree();
    }

    @Test
    @Override
    public void testGetModelIdByImageIds() {
        super.testGetModelIdByImageIds();
    }

    @Test
    @Override
    public void testGetModelByGroupKey() {
        super.testGetModelByGroupKey();
    }

    @Test
    @Override
    public void testGetImageMapping() {
        super.testGetImageMapping();
    }

    @Test
    @Override
    public void testGetPointsMapping() {
        super.testGetPointsMapping();
    }

    @Test
    @Override
    public void testGetPointOrientation() {
        super.testGetPointOrientation();
    }
}
