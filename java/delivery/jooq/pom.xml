<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bees360</groupId>
    <artifactId>bees360-delivery</artifactId>
    <version>${revision}${changelist}</version>
  </parent>

  <artifactId>bees360-delivery-jooq</artifactId>
  <packaging>jar</packaging>

  <name>bees360-delivery-jooq</name>

    <properties>
        <jooq-tables>
            image_tag_category | project_delivery_task | project_delivery_task_status | report_type
        </jooq-tables>
        <jooq-package>com.bees360.jooq.persistent.delivery</jooq-package>
    </properties>
  <dependencies>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-delivery-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-jooq</artifactId>
    </dependency>
  </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen-maven</artifactId>
                <executions>
                    <execution>
                        <id>convergence</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
