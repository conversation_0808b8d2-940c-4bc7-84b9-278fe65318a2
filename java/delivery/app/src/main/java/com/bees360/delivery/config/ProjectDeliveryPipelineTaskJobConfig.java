package com.bees360.delivery.config;

import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.delivery.JobProjectDelivery;
import com.bees360.delivery.ProjectDelivery;
import com.bees360.delivery.config.ProjectDeliveryPipelineTaskJobConfig.DeliveryPipelineTaskProperties.PipelineTaskProperties;
import com.bees360.event.DeliveryProjectOnPipelineTaskOnGoing;
import com.bees360.event.EventListener;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.SetOnGoingOnPipelineTaskReady;
import com.bees360.event.util.EventListeners;
import com.bees360.event.util.PipelineTaskChangedListeners;
import com.bees360.job.DeliverProjectJobExecutor;
import com.bees360.job.ExecutePipelineTaskDeliveryJobExecutor;
import com.bees360.job.JobScheduler;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.PipelineService;

import jakarta.annotation.PostConstruct;

import lombok.Data;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

@Configuration
public class ProjectDeliveryPipelineTaskJobConfig {

    @Data
    static class DeliveryPipelineTaskProperties {
        private Map<String, DeliveryType> taskKeyToDeliverType;
        private Map<String, PipelineTaskProperties> properties;
        private RetryProperties retry;

        @Data
        static class PipelineTaskProperties {
            private Set<String> autoOnGoingProjectsOnly;
        }
    }

    @Bean
    @ConfigurationProperties(prefix = "app.delivery.job.retry")
    RetryProperties deliveryJobRetryProperties() {
        return new RetryProperties();
    }

    @Bean
    @RefreshableConfigurationProperties(prefix = "app.delivery.pipeline-task")
    DeliveryPipelineTaskProperties deliveryPipelineTaskProperties() {
        return new DeliveryPipelineTaskProperties();
    }

    @Bean
    JobProjectDelivery jobProjectDelivery(
            JobScheduler jobScheduler, RetryProperties deliveryJobRetryProperties) {
        return new JobProjectDelivery(
                jobScheduler,
                deliveryJobRetryProperties.getRetryCount(),
                deliveryJobRetryProperties.getRetryDelay(),
                deliveryJobRetryProperties.getRetryDelayIncreaseFactor());
    }

    @Bean
    ExecutePipelineTaskDeliveryJobExecutor deliverProjectPipelineTaskJobExecutor(
            PipelineService pipelineService,
            JobProjectDelivery jobProjectDelivery,
            DeliveryPipelineTaskProperties properties,
            RabbitJobDispatcher rabbitJobDispatcher,
            @Value("${app.job.execute-pipeline-task-delivery-job.max-concurrency:1}")
                    int maxConcurrency) {
        var jobExecutor =
                new ExecutePipelineTaskDeliveryJobExecutor(
                        pipelineService,
                        jobProjectDelivery,
                        taskKey -> properties.getTaskKeyToDeliverType().get(taskKey).name());
        rabbitJobDispatcher.enlist(jobExecutor, maxConcurrency);
        return jobExecutor;
    }

    @Bean
    DeliverProjectJobExecutor deliverProjectJobExecutor(
            @Qualifier("deliverProjectJobExecutorProjectDelivery")
                    ProjectDelivery deliverProjectJobExecutorProjectDelivery,
            RabbitJobDispatcher rabbitJobDispatcher,
            @Value("${app.job.delivery-project-job-executor.max-concurrency:1}")
                    int maxConcurrency) {
        var jobExecutor = new DeliverProjectJobExecutor(deliverProjectJobExecutorProjectDelivery);
        rabbitJobDispatcher.enlist(jobExecutor, maxConcurrency);
        return jobExecutor;
    }

    @Bean
    BiPredicate<String, String> pipelineTaskOnGoingPredicate(
            DeliveryPipelineTaskProperties properties) {
        return (projectId, taskKey) ->
                Optional.ofNullable(properties.getProperties())
                        .map(map -> map.get(taskKey))
                        .map(PipelineTaskProperties::getAutoOnGoingProjectsOnly)
                        .map(ids -> ids.contains(projectId))
                        .orElse(true);
    }

    @Bean
    Iterable<EventListener> setOnGoingOnPipelineTaskReady(
            PipelineService pipelineService,
            DeliveryPipelineTaskProperties properties,
            @Qualifier("pipelineTaskOnGoingPredicate")
                    BiPredicate<String, String> pipelineTaskOnGoingPredicate) {

        return properties.getTaskKeyToDeliverType().keySet().stream()
                .map(
                        taskKey -> {
                            var listener =
                                    new SetOnGoingOnPipelineTaskReady(
                                            taskKey, pipelineService, pipelineTaskOnGoingPredicate);
                            return PipelineTaskChangedListeners.forwardToStatusChangedListener(
                                    listener,
                                    EventListeners.getListenerName(listener.getClass())
                                            + "."
                                            + taskKey,
                                    taskKey,
                                    PipelineStatus.READY);
                        })
                .collect(Collectors.toList());
    }

    @Bean
    Iterable<EventListener> deliveryProjectOnPipelineTaskOnGoing(
            JobScheduler jobScheduler, DeliveryPipelineTaskProperties properties) {
        var retry = properties.getRetry();
        return properties.getTaskKeyToDeliverType().keySet().stream()
                .map(
                        taskKey -> {
                            var listener =
                                    new DeliveryProjectOnPipelineTaskOnGoing(
                                            taskKey,
                                            jobScheduler,
                                            Optional.ofNullable(retry)
                                                    .map(RetryProperties::getRetryCount)
                                                    .orElse(null),
                                            Optional.ofNullable(retry)
                                                    .map(RetryProperties::getRetryDelay)
                                                    .orElse(null),
                                            Optional.ofNullable(retry)
                                                    .map(
                                                            RetryProperties
                                                                    ::getRetryDelayIncreaseFactor)
                                                    .orElse(null));
                            return PipelineTaskChangedListeners.forwardToStatusChangedListener(
                                    listener,
                                    EventListeners.getListenerName(listener.getClass())
                                            + "."
                                            + taskKey,
                                    taskKey,
                                    PipelineStatus.ONGOING);
                        })
                .collect(Collectors.toList());
    }

    @Autowired Iterable<EventListener> setOnGoingOnPipelineTaskReady;

    @Autowired Iterable<EventListener> deliveryProjectOnPipelineTaskOnGoing;

    @Autowired RabbitEventDispatcher rabbitEventDispatcher;

    @PostConstruct
    void registerEventListeners() {
        setOnGoingOnPipelineTaskReady.forEach(rabbitEventDispatcher::enlist);
        deliveryProjectOnPipelineTaskOnGoing.forEach(rabbitEventDispatcher::enlist);
    }
}
