package com.bees360.job;

import com.bees360.delivery.ProjectDelivery;
import com.bees360.job.registry.ExecutePipelineTaskDeliveryJob;
import com.bees360.job.util.AbstractAsyncJobExecutor;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.PipelineService;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.extern.log4j.Log4j2;

import org.checkerframework.checker.nullness.qual.Nullable;

import java.util.function.Function;

/**
 * 执行 Pipeline Task 的自动化任务。
 *
 * <p>执行任务之前检查 pipeline task 的状态。如果是 READY，则设置 pipeline 为 OnGoing，并提交新的 Delivery 任务。如果是
 * OnGoing，则尝试获取最新的 Delivery 任务。等待 delivery 任务执行，完成之后设置 pipeline task的状态。该机制确保任务重试时，pipeline task
 * 已进入 OnGoing 状态，不会重新提交 Delivery 任务。
 */
@Log4j2
public class ExecutePipelineTaskDeliveryJobExecutor
        extends AbstractAsyncJobExecutor<ExecutePipelineTaskDeliveryJob> {

    private final PipelineService pipelineService;

    private final ProjectDelivery projectDelivery;

    private final Function<String, String> taskKeyToDeliveryTypeConverter;

    public ExecutePipelineTaskDeliveryJobExecutor(
            PipelineService pipelineService,
            ProjectDelivery projectDelivery,
            Function<String, String> taskKeyToDeliveryTypeConverter) {
        this.pipelineService = pipelineService;
        this.projectDelivery = projectDelivery;
        this.taskKeyToDeliveryTypeConverter = taskKeyToDeliveryTypeConverter;
        log.info(
                "Created"
                    + " {}(pipelineService={},projectDelivery={},taskKeyToDeliveryTypeConverter={})",
                this,
                pipelineService,
                projectDelivery,
                taskKeyToDeliveryTypeConverter);
    }

    @Override
    protected ListenableFuture<Void> accept(ExecutePipelineTaskDeliveryJob job) {
        log.info("Start to execute job {}.", job);
        var projectId = job.getPipelineId();
        var pipelineId = job.getPipelineId();
        var taskKey = job.getTaskKey();
        var deliveryType = taskKeyToDeliveryTypeConverter.apply(taskKey);
        var version = job.getExecutedAt().toEpochMilli();
        ListenableFuture<Void> future = projectDelivery.deliver(projectId, deliveryType, version);
        Futures.addCallback(
                future,
                new FutureCallback<>() {
                    @Override
                    public void onSuccess(@Nullable Void result) {
                        log.info("Set the pipeline task ({}, {}) to Done.", pipelineId, taskKey);
                        pipelineService.setTaskStatus(pipelineId, taskKey, PipelineStatus.DONE);
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        log.warn("Fail to deliver project {} with job {}.", projectId, job, t);
                        var message =
                                String.format(
                                        "Fail to deliver project %s: %s.",
                                        projectId, t.getMessage());
                        pipelineService.setTaskStatus(
                                pipelineId, taskKey, PipelineStatus.ERROR, message);
                    }
                },
                MoreExecutors.directExecutor());
        return future;
    }
}
