package com.bees360.delivery;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.bees360.util.ListenableFutures;
import com.google.common.util.concurrent.Futures;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.UnaryOperator;

@SpringJUnitConfig
class ProjectResourceDeliveryTest {
    private Map<String, List<DeliverableResource>> projectResourceMap = new HashMap<>();
    private Map<String, String> projectTargetMap = new HashMap<>();
    private UnaryOperator<String> projectIdTargetIdConverter = projectTargetMap::get;
    private ResourceDelivery resourceDelivery = mock(ResourceDelivery.class);
    private Function<String, Iterable<? extends DeliverableResource>> projectResourceProvider =
            projectResourceMap::get;
    private ProjectResourceDelivery projectResourceDelivery =
            new ProjectResourceDelivery(
                    projectResourceProvider, projectIdTargetIdConverter, resourceDelivery);

    @Test
    void testNoTargetDeliverFailed() {
        var projectId = "1" + RandomStringUtils.randomNumeric(3);
        var deliveryType = "REPORT";
        var version = Instant.now().toEpochMilli();

        var future = projectResourceDelivery.deliver(projectId, deliveryType, version);
        assertThrows(
                IllegalArgumentException.class,
                () -> Futures.getChecked(future, IllegalArgumentException.class));
    }

    @Test
    void testNoResourceDeliverFailed() {
        var projectId = "1" + RandomStringUtils.randomNumeric(3);
        var deliveryType = "REPORT";
        var version = Instant.now().toEpochMilli();

        projectTargetMap.put(projectId, projectId);
        var future = projectResourceDelivery.deliver(projectId, deliveryType, version);
        assertThrows(
                IllegalArgumentException.class,
                () -> Futures.getChecked(future, IllegalArgumentException.class));
    }

    @Test
    void testResourceDeliverFailed() {
        var projectId = "1" + RandomStringUtils.randomNumeric(3);
        var deliveryType = "REPORT";
        var version = Instant.now().toEpochMilli();
        var deliverableResource = DeliverableResource.of("", "", "", "", Map.of());

        projectTargetMap.put(projectId, projectId);
        projectResourceMap.put(projectId, List.of(deliverableResource));
        doReturn(
                        ListenableFutures.fromCompletable(
                                CompletableFuture.failedFuture(
                                        new IllegalArgumentException("test illegal argument"))))
                .when(resourceDelivery)
                .deliver(projectId, deliverableResource);
        var future = projectResourceDelivery.deliver(projectId, deliveryType, version);
        assertThrows(
                IllegalArgumentException.class,
                () -> Futures.getChecked(future, IllegalArgumentException.class));
    }
}
