package com.bees360.delivery.util;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.bees360.resource.transfer.ResourceTransferService;
import com.google.common.util.concurrent.Futures;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.function.TriFunction;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@SpringJUnitConfig
class ResourceTransferProjectDeliveryTest {

    private ResourceTransferService resourceTransferService = mock(ResourceTransferService.class);

    private Map<Triple<String, String, Long>, List<String>> transferKeyMap = new HashMap<>();

    private TriFunction<String, String, Long, Iterable<String>> transferKeyProvider =
            (projectId, deliveryType, version) ->
                    transferKeyMap.get(Triple.of(projectId, deliveryType, version));

    private ResourceTransferProjectDelivery transferDelivery =
            new ResourceTransferProjectDelivery(resourceTransferService, transferKeyProvider);

    @Test
    void testDeliverFailed() {
        var projectId = "1" + RandomStringUtils.randomNumeric(3);
        var deliveryType = "REPORT";
        var version = Instant.now().toEpochMilli();
        var key = RandomStringUtils.randomAlphabetic(3) + "_" + projectId;
        transferKeyMap.put(Triple.of(projectId, deliveryType, version), List.of(key));
        doReturn(
                        CompletableFuture.failedFuture(
                                new IllegalArgumentException("mock illegal argument")))
                .when(resourceTransferService)
                .transfer(anyString(), any());

        var future = transferDelivery.deliver(projectId, deliveryType, version);
        assertThrows(
                IllegalArgumentException.class,
                () -> Futures.getChecked(future, IllegalArgumentException.class));
    }
}
