package com.bees360.delivery;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import com.bees360.oauth.OAuthGrant;
import com.bees360.oauth.OAuthRefreshableToken;
import com.bees360.oauth.OAuthToken;
import com.bees360.rct.RctApi;
import com.bees360.rct.RctOAuthTokenGranter;
import com.bees360.report.ReportGroupManager;
import com.bees360.resource.InMemoryResourceRepository;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.sdk.rct.ApiClient;
import com.bees360.sdk.rct.api.TasksApi;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ActiveProfiles;

import java.net.URI;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.function.Function;
import java.util.function.Supplier;

@SpringBootTest(classes = RctReportResourceDeliveryConfigTest.Config.class)
@ActiveProfiles("test")
public class RctReportResourceDeliveryConfigTest {
    @Import({
        RctReportResourceDeliveryConfig.class,
    })
    @Configuration
    static class Config {
        @Bean
        public RctApi<TasksApi> rctTasksApi(ApiClient rctApiClient) {
            return RctApi.of(new TasksApi(rctApiClient));
        }

        @Bean
        ResourcePool resourcePool() {
            return new InMemoryResourceRepository(URI.create("test.resource_pool_for_rct_report"));
        }

        @Bean
        Function<String, Resource> resourceProvider(ResourcePool resourcePool) {
            return url -> resourcePool.get(url);
        }

        @Bean
        Supplier<String> robotUserIdSupplier() {
            return () -> "10000";
        }

        @MockBean ReportGroupManager reportGroupManager;

        @Bean
        @Primary
        RctOAuthTokenGranter oAuthTokenGranter(RctOAuthTokenGranter rctOAuthTokenGranter) {
            var accessToken =
                    OAuthToken.of(
                            "accessToken", LocalDateTime.now().minus(Duration.ofHours(5)), "scope");
            var refreshToken = OAuthRefreshableToken.of(accessToken, "xsrfToken");
            rctOAuthTokenGranter = Mockito.spy(rctOAuthTokenGranter);
            doReturn(refreshToken).when(rctOAuthTokenGranter).grant(any(OAuthGrant.class));
            return rctOAuthTokenGranter;
        }
    }

    @Test
    void testLoadContext() {}
}
