package com.bees360.address;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.bees360.boot.ApplicationAutoConfig;
import com.google.maps.model.AddressComponent;
import com.google.maps.model.AddressComponentType;
import com.google.maps.model.GeocodingResult;
import com.google.maps.model.Geometry;
import com.google.maps.model.LatLng;

import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringBootTest
@ApplicationAutoConfig(exclude = {GrpcServerFactoryAutoConfiguration.class})
@SpringJUnitConfig
public class GoogleAddressGpsProviderTest {
    @MockBean GoogleGeoApi googleGeoApi;

    @Autowired GoogleAddressGpsProvider googleAddressGpsProvider;

    @Configuration
    static class Config {
        @Bean
        AddressGpsProvider addressGpsProvider(GoogleGeoApi api) {
            return new GoogleAddressGpsProvider(api, "US");
        }
    }

    static Address ADDRESS =
            Address.AddressBuilder.newBuilder()
                    .setAddress("11820 Toppell Trl, Haslet, TX 76052")
                    .setStreetNumber("11820")
                    .setRoute("Toppell Trl")
                    .setCity("Haslet")
                    .setCounty("Tarrant County")
                    .setState("TX")
                    .setCountry("US")
                    .setZip("76052")
                    .build();

    static Address ADDRESS_NO_ZIP =
            Address.AddressBuilder.newBuilder()
                    .setAddress("11820 Toppell Trl, Haslet, TX 76052")
                    .setStreetNumber("11820")
                    .setRoute("Toppell Trl")
                    .setCity("Haslet")
                    .setCounty("Tarrant County")
                    .setState("TX")
                    .setCountry("US")
                    .build();

    @Test
    public void testFindGpsByGoogleApiShouldSucceed() {
        var lat = RandomUtils.nextDouble();
        var lng = RandomUtils.nextDouble();

        GeocodingResult[] results = buildGoogleApiResult(lat, lng, ADDRESS.getZip());
        Mockito.when(googleGeoApi.apply(Mockito.any())).thenReturn(results);
        var gps = googleAddressGpsProvider.getZipcodeGpsByAddress(ADDRESS);
        assertEquals(lat, gps.getLatitude());
        assertEquals(lng, gps.getLongitude());
    }

    @Test
    public void testFindGpsByGoogleApiWithDiffZipcodeShouldFailed() {
        var lat = RandomUtils.nextDouble();
        var lng = RandomUtils.nextDouble();

        GeocodingResult[] results =
                buildGoogleApiResult(lat, lng, String.valueOf(RandomUtils.nextInt(10000, 100000)));
        Mockito.when(googleGeoApi.apply(Mockito.any())).thenReturn(results);
        var result = googleAddressGpsProvider.fillAddressGps(ADDRESS);
        assertNull(result.getLat());
        assertNull(result.getLng());
    }

    @Test
    public void testFindByZipcodeShouldSucceed() {
        var lat = RandomUtils.nextDouble();
        var lng = RandomUtils.nextDouble();

        GeocodingResult[] results =
                buildGoogleApiResult(lat, lng, ADDRESS_NO_ZIP.getCity(), ADDRESS_NO_ZIP.getState());
        Mockito.when(googleGeoApi.apply(Mockito.any())).thenReturn(results);
        var result = googleAddressGpsProvider.fillAddressGps(ADDRESS_NO_ZIP);
        assertEquals(lat, result.getLat());
        assertEquals(lng, result.getLng());
    }

    private GeocodingResult[] buildGoogleApiResult(Double lat, Double lng, String zipcode) {
        var geometry = new Geometry();
        var result = new GeocodingResult();
        geometry.location = new LatLng(lat, lng);
        result.geometry = geometry;
        var zipcodeComponents = new AddressComponent();
        zipcodeComponents.types = new AddressComponentType[] {AddressComponentType.POSTAL_CODE};
        zipcodeComponents.shortName = zipcode;
        result.addressComponents = new AddressComponent[] {zipcodeComponents};
        return new GeocodingResult[] {result};
    }

    private GeocodingResult[] buildGoogleApiResult(
            Double lat, Double lng, String city, String state) {
        var geometry = new Geometry();
        var result = new GeocodingResult();
        geometry.location = new LatLng(lat, lng);
        result.geometry = geometry;
        var cityComponents = new AddressComponent();
        cityComponents.types = new AddressComponentType[] {AddressComponentType.LOCALITY};
        cityComponents.shortName = city;
        var stateComponents = new AddressComponent();
        stateComponents.types =
                new AddressComponentType[] {AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_1};
        stateComponents.shortName = state;
        result.addressComponents = new AddressComponent[] {cityComponents, stateComponents};
        return new GeocodingResult[] {result};
    }
}
