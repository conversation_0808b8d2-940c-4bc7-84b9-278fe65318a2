spring:
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
  jooq:
    sql-dialect: POSTGRES

grpc:
  server:
    port: ${GRPC_SERVER_PORT:9898}
  client:
    addressManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    hiveLocationManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    addressFlyZoneTypeManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
