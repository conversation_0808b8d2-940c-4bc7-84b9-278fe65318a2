package com.bees360.address;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig
class AddressNormalizeServiceTest {

    static final String ADDRESS_US = "11820 Toppell Trl, Haslet, TX 76052";

    static Address ADDRESS =
            Address.AddressBuilder.newBuilder()
                    .setLat(32.9416154)
                    .setLng(-97.3977666)
                    .setAddress(ADDRESS_US)
                    .setStreetNumber("11820")
                    .setRoute("Toppell Trl")
                    .setCity("Haslet")
                    .setCounty("Tarrant County")
                    .setState("TX")
                    .setCountry("US")
                    .setZip("76052")
                    .build();

    static Address WRONG_ADDRESS =
            Address.AddressBuilder.newBuilder()
                    .setLat(32.9416154)
                    .setLng(-97.3977666)
                    .setAddress("Wrong" + ADDRESS_US)
                    .setStreetNumber("Wrong 11820")
                    .setRoute("Toppell Trl")
                    .setCity("Haslet")
                    .setCounty("Tarrant County")
                    .setState("TX")
                    .setCountry("US")
                    .setZip("123456")
                    .build();

    @Test
    public void normalize() {
        var addressRepository = Mockito.mock(AddressRepository.class);
        var addressProvider = Mockito.mock(AddressProvider.class);
        var normalizeService = new AddressNormalizeService(addressProvider, addressRepository);

        Mockito.when(addressRepository.findByAddress(ADDRESS_US)).thenReturn(null);
        Mockito.when(addressProvider.findByAddress(ADDRESS_US)).thenReturn(ADDRESS);
        Mockito.when(addressProvider.normalize(Mockito.any(Address.class)))
                .thenAnswer(
                        arg -> {
                            Address address = arg.getArgument(0);
                            if (StringUtils.equals(address.getAddress(), ADDRESS_US)) {
                                return ADDRESS;
                            }
                            return null;
                        });
        Mockito.when(addressRepository.save(ADDRESS)).thenReturn("1");

        Address address =
                normalizeService.normalize(
                        Address.AddressBuilder.newBuilder().setAddress(ADDRESS_US).build());
        Assertions.assertNotNull(address);
        Assertions.assertEquals(ADDRESS_US, address.getAddress());
    }

    @Test
    void normalizeWithNotFoundFromProvider() {
        var addressRepository = Mockito.mock(AddressRepository.class);
        var addressProvider = Mockito.mock(AddressProvider.class);
        var normalizeService =
                new AddressNormalizeService(addressProvider, addressRepository) {
                    @Override
                    public Address normalize(Address address) {
                        var result = super.normalize(address);
                        if (result != null) {
                            return result;
                        }
                        String id = addressRepository.save(address);
                        return Address.from(address.toMessage().toBuilder().setId(id).build());
                    }
                };

        Mockito.when(addressRepository.findByAddress(ADDRESS_US)).thenReturn(null);
        Mockito.when(addressProvider.findByAddress(ADDRESS_US)).thenReturn(null);
        Mockito.when(addressRepository.save(ADDRESS)).thenReturn("1");

        Address address = normalizeService.normalize(ADDRESS);
        Assertions.assertNotNull(address);
        Assertions.assertEquals(ADDRESS_US, address.getAddress());
    }

    @Test
    void normalizeWithDifferentZipcode() {
        var addressRepository = Mockito.mock(AddressRepository.class);
        var addressProvider = Mockito.mock(AddressProvider.class);
        var normalizeService =
                new AddressNormalizeService(addressProvider, addressRepository) {
                    @Override
                    public Address normalize(Address address) {
                        var result = super.normalize(address);
                        if (result != null) {
                            return result;
                        }
                        String id = addressRepository.save(address);
                        return Address.from(address.toMessage().toBuilder().setId(id).build());
                    }
                };

        Mockito.when(addressRepository.findByAddress(ADDRESS_US)).thenReturn(null);
        Mockito.when(addressProvider.findByAddress(ADDRESS_US)).thenReturn(WRONG_ADDRESS);
        Mockito.when(addressRepository.save(WRONG_ADDRESS)).thenReturn("1");
        Mockito.when(addressRepository.save(ADDRESS)).thenReturn("2");

        Address address = normalizeService.normalize(ADDRESS);
        Assertions.assertNotNull(address);
        Assertions.assertEquals(ADDRESS_US, address.getAddress());
        Assertions.assertEquals("2", address.getId());
    }
}
