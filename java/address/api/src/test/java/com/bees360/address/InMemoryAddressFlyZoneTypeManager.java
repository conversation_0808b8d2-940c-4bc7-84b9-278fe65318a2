package com.bees360.address;

import com.bees360.address.Message.AddressMessage.FlyZoneType;

import jakarta.annotation.Nullable;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class InMemoryAddressFlyZoneTypeManager implements AddressFlyZoneTypeManager {

    private final Map<String, FlyZoneType> addressFlyZoneType = new ConcurrentHashMap<>();

    @Override
    public void updateAddressFlyZoneType(String addressId, FlyZoneType flyZoneType) {
        addressFlyZoneType.put(addressId, flyZoneType);
    }

    @Nullable
    @Override
    public FlyZoneType findByAddressId(String addressId) {
        return addressFlyZoneType.get(addressId);
    }
}
