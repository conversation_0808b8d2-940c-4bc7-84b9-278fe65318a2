package com.bees360.address;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.bees360.address.Message.AddressMessage.FlyZoneType;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.RandomUtils;

@Log4j2
public class AddressFlyZoneTypeManagerTest {

    protected final AddressFlyZoneTypeManager addressFlyZoneTypeManager;

    public AddressFlyZoneTypeManagerTest(AddressFlyZoneTypeManager addressFlyZoneTypeManager) {
        this.addressFlyZoneTypeManager = addressFlyZoneTypeManager;
        log.info("Created {}(addressFlyZoneTypeManager={})", this, addressFlyZoneTypeManager);
    }

    public void updateThenFind() {
        var addressId = RandomUtils.nextLong(1000, 10000000L) + "";
        var flyZoneType = FlyZoneType.AUTHORIZATION_ZONE;
        addressFlyZoneTypeManager.updateAddressFlyZoneType(addressId, flyZoneType);
        flyZoneType = FlyZoneType.WARNING_ZONE;
        addressFlyZoneTypeManager.updateAddressFlyZoneType(addressId, flyZoneType);

        var resultFlyZoneType = addressFlyZoneTypeManager.findByAddressId(addressId);
        assertNotNull(resultFlyZoneType);
        assertEquals(flyZoneType, resultFlyZoneType);
    }

    public void findNonExisted() {
        var addressId = RandomUtils.nextLong(10000000L, 100000000L) + "";
        var resultFlyZoneType = addressFlyZoneTypeManager.findByAddressId(addressId);
        assertNull(resultFlyZoneType);
    }
}
