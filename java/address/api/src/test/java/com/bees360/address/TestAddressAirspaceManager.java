package com.bees360.address;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.common.collect.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.RandomUtils;

import java.time.Instant;
import java.util.List;

@Log4j2
public class TestAddressAirspaceManager {

    protected List<String> airspaceStatuses =
            List.of(
                    "clear",
                    "advisory_required",
                    "laanc_auto_approval",
                    "laanc_not_auto_approval",
                    "restricted");

    private final AddressAirspaceManager addressAirspaceManager;

    TestAddressAirspaceManager(AddressAirspaceManager addressAirspaceManager) {
        this.addressAirspaceManager = addressAirspaceManager;
        log.info("Created {}(addressAirspaceManager={})", this, addressAirspaceManager);
    }

    void updateThenFetch() {
        var addressId = String.valueOf(RandomUtils.nextLong(10000, 1000000));

        assertNull(addressAirspaceManager.getLatestAirspace(addressId));
        assertTrue(Iterables.isEmpty(addressAirspaceManager.getAirspaceHistory(addressId)));

        var airspace = randomAirspace();
        var airspaceSecond = randomAirspace();
        addressAirspaceManager.updateAirspace(addressId, airspace);

        var latestAirspace = addressAirspaceManager.getLatestAirspace(addressId);
        assertAirspace(airspace, latestAirspace);

        addressAirspaceManager.updateAirspace(addressId, airspaceSecond);
        var airspaceHistory =
                com.bees360.util.Iterables.toList(
                        addressAirspaceManager.getAirspaceHistory(addressId));
        assertEquals(2, airspaceHistory.size());
        assertAirspace(airspaceSecond, airspaceHistory.get(0));
        assertAirspace(airspace, airspaceHistory.get(1));
    }

    protected Airspace randomAirspace() {

        final var status = airspaceStatuses.get(RandomUtils.nextInt(0, airspaceStatuses.size()));
        final var heightCeiling = RandomUtils.nextInt(10, 1000);

        return new Airspace() {

            @Override
            public String getStatus() {
                return status;
            }

            @Override
            public Integer getHeightCeiling() {
                return heightCeiling;
            }

            @Override
            public Instant getUpdatedAt() {
                return null;
            }
        };
    }

    protected void assertAirspace(Airspace expected, Airspace actual) {
        if (expected == actual) {
            return;
        }
        if (expected == null || actual == null) {
            throw new AssertionError(String.format("Expected %s but %s.", expected, actual));
        }
        assertEquals(expected.getStatus(), actual.getStatus());
        assertEquals(expected.getHeightCeiling(), actual.getHeightCeiling());
        assertNotNull(actual.getUpdatedAt());
    }
}
