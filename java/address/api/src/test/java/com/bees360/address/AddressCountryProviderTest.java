package com.bees360.address;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig
public class AddressCountryProviderTest {

    static final String ADDRESS_US = "11820 Toppell Trl, Haslet, TX 76052";
    static final Address ADDRESS =
            Address.AddressBuilder.newBuilder()
                    .setAddress("11820 Toppell Trl, Haslet, TX 76052, USA")
                    .setZip("76052")
                    .setCountry("US")
                    .build();

    private AddressCountryProvider addressCountryProvider;

    @BeforeEach
    public void init() {
        var addressProvider = Mockito.mock(AddressProvider.class);
        Mockito.when(addressProvider.normalize(ADDRESS)).thenReturn(ADDRESS);
        addressCountryProvider = new AddressCountryProvider(addressProvider);
    }

    @Test
    public void normalize() {
        Address addressQuery = addressCountryProvider.normalize(ADDRESS);
        Assertions.assertEquals(ADDRESS_US, addressQuery.getAddress());
    }
}
