package com.bees360.address;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;

@SpringJUnitConfig
public class CompositeAddressProviderTest {

    static Address ADDRESS =
            Address.AddressBuilder.newBuilder()
                    .setLat(32.9416154)
                    .setLng(-97.3977666)
                    .setAddress("11820 Toppell Trl, Haslet, TX 76052")
                    .setStreetNumber("11820")
                    .setRoute("Toppell Trl")
                    .setCity("Haslet")
                    .setCounty("Tarrant County")
                    .setState("TX")
                    .setCountry("US")
                    .setZip("76052")
                    .build();
    static Address ADDRESS2 =
            Address.AddressBuilder.newBuilder()
                    .setLat(32.9416154)
                    .setLng(-97.3977666)
                    .setAddress("10001 Toppell Trl, Haslet, TX 76052")
                    .setStreetNumber("10001")
                    .setRoute("Toppell Trl")
                    .setCity("Haslet")
                    .setCounty("Tarrant County")
                    .setState("TX")
                    .setCountry("US")
                    .setZip("76052")
                    .build();

    @Test
    public void normalize() {
        var addressProvider1 = Mockito.mock(AddressProvider.class);
        var addressProvider2 = Mockito.mock(AddressProvider.class);
        var addressProvider3 = Mockito.mock(AddressProvider.class);
        var compositeAddressProvider =
                new CompositeAddressProvider(List.of(addressProvider1, addressProvider2));

        Mockito.when(addressProvider1.normalize(Mockito.any(Address.class))).thenReturn(null);
        Mockito.when(addressProvider2.normalize(Mockito.any(Address.class))).thenReturn(ADDRESS);
        Mockito.when(addressProvider3.normalize(Mockito.any(Address.class))).thenReturn(ADDRESS2);

        Address address = compositeAddressProvider.normalize(ADDRESS);
        Assertions.assertNotNull(address);
        Assertions.assertEquals(ADDRESS.getAddress(), address.getAddress());
    }
}
