package com.bees360.address;

import com.google.common.collect.ForwardingObject;

public abstract class ForwardingAddressProvider extends ForwardingObject
        implements AddressProvider {

    private final AddressProvider addressProvider;

    public ForwardingAddressProvider(AddressProvider addressProvider) {
        this.addressProvider = addressProvider;
    }

    @Override
    protected AddressProvider delegate() {
        return addressProvider;
    }

    @Override
    public Address normalize(Address address) {
        return delegate().normalize(address);
    }

    @Override
    public Address normalize(String address) {
        return delegate().normalize(address);
    }

    @Override
    public Address findById(String id) {
        return delegate().findById(id);
    }

    @Override
    public Address findByAddress(String address) {
        return delegate().findByAddress(address);
    }

    @Override
    public Iterable<? extends Address> findAllById(Iterable<String> ids) {
        return delegate().findAllById(ids);
    }
}
