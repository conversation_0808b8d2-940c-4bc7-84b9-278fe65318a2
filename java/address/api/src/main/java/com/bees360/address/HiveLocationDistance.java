package com.bees360.address;

import com.bees360.api.Proto;
import com.bees360.util.Functions;

import jakarta.annotation.Nullable;

public interface HiveLocationDistance extends Proto<Message.HiveLocationDistance> {

    String getZipcode();

    String getCity();

    String getState();

    /**
     * hiveLocation与目标地址的距离
     *
     * @return miles
     */
    Float getDistanceMiles();

    /** 目标地址 */
    @Nullable
    String getAddressId();

    static HiveLocationDistance from(final Message.HiveLocationDistance address) {
        if (Message.HiveLocationDistance.getDefaultInstance().equals(address)) {
            return null;
        }
        return new HiveLocationDistance() {
            @Override
            public String getCity() {
                return address.getCity();
            }

            @Override
            public String getState() {
                return address.getState();
            }

            @Override
            public Float getDistanceMiles() {
                return address.getDistanceMiles();
            }

            @Override
            public String getAddressId() {
                return null;
            }

            @Override
            public String getZipcode() {
                return address.getZipcode();
            }
        };
    }

    @Override
    default Message.HiveLocationDistance toMessage() {
        Message.HiveLocationDistance.Builder builder = Message.HiveLocationDistance.newBuilder();
        Functions.acceptIfNotNull(builder::setZipcode, getZipcode());
        Functions.acceptIfNotNull(builder::setCity, getCity());
        Functions.acceptIfNotNull(builder::setState, getState());
        Functions.acceptIfNotNull(builder::setDistanceMiles, getDistanceMiles());
        return builder.build();
    }
}
