package com.bees360.address;

import com.bees360.repository.Repository;
import com.bees360.util.Iterables;

import java.util.Objects;
import java.util.TimeZone;

public interface AddressRepository extends AddressProvider, Repository<Address> {

    @Override
    default Iterable<? extends Address> findAllById(Iterable<String> ids) {
        return Iterables.from(Iterables.toStream(ids).map(this::findById).filter(Objects::nonNull));
    }

    void updateAddressTimeZoneById(String id, TimeZone timeZone);
}
