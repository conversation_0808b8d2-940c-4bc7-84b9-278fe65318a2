package com.bees360.address;

import com.bees360.address.Message.AirspaceMessage;
import com.bees360.util.DateTimes;

import lombok.NonNull;

import java.time.Instant;

class ProtoAirspace implements Airspace {

    private final AirspaceMessage message;

    private ProtoAirspace(@NonNull AirspaceMessage message) {
        this.message = message;
    }

    @Override
    public String getStatus() {
        return message.getStatus();
    }

    @Override
    public Integer getHeightCeiling() {
        return message.hasHeightCeiling() ? message.getHeightCeiling().getValue() : null;
    }

    @Override
    public Instant getUpdatedAt() {
        return message.hasUpdatedAt() ? DateTimes.toInstant(message.getUpdatedAt()) : null;
    }

    @Override
    public AirspaceMessage toMessage() {
        return message;
    }

    public static ProtoAirspace from(AirspaceMessage message) {
        if (message == null || AirspaceMessage.getDefaultInstance().equals(message)) {
            return null;
        }
        return new ProtoAirspace(message);
    }
}
