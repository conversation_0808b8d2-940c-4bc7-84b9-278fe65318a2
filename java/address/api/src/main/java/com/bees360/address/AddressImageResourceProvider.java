package com.bees360.address;

import com.bees360.resource.Resource;

public interface AddressImageResourceProvider {

    /**
     * Get the images of the property at a specify address
     *
     * @param addressId the id of the address
     * @return the list of image resource. Empty list if not images found.
     * @throws IllegalArgumentException if address not found.
     */
    Iterable<? extends Resource> getAddressImages(String addressId);
}
