package com.bees360.address;

import com.bees360.address.Message.AirspaceMessage;
import com.bees360.api.Proto;
import com.bees360.util.DateTimes;
import com.bees360.util.Functions;
import com.google.protobuf.Int32Value;

import java.time.Instant;

public interface Airspace extends Proto<AirspaceMessage> {

    /** empty string if not set */
    String getStatus();

    Integer getHeightCeiling();

    Instant getUpdatedAt();

    static Airspace of(String status, Integer heightCeiling, Instant updatedAt) {
        return new Airspace() {
            @Override
            public String getStatus() {
                return status;
            }

            @Override
            public Integer getHeightCeiling() {
                return heightCeiling;
            }

            @Override
            public Instant getUpdatedAt() {
                return updatedAt;
            }
        };
    }

    @Override
    default AirspaceMessage toMessage() {
        var builder = AirspaceMessage.newBuilder();
        Functions.acceptIfNotNull(builder::setStatus, getStatus());
        Functions.acceptIfNotNull(builder::setHeightCeiling, getHeightCeiling(), Int32Value::of);
        Functions.acceptIfNotNull(builder::setUpdatedAt, getUpdatedAt(), DateTimes::toTimestamp);
        return builder.build();
    }
}
