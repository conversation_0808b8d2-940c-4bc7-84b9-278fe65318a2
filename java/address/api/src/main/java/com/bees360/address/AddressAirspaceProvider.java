package com.bees360.address;

import java.util.List;
import java.util.Map;

public interface AddressAirspaceProvider {

    default Airspace getLatestAirspace(String addressId) {
        var map = getLatestAirspace(List.of(addressId));
        return map.getOrDefault(addressId, null);
    }

    Map<String, ? extends Airspace> getLatestAirspace(Iterable<String> addressIds);

    Iterable<? extends Airspace> getAirspaceHistory(String addressId);
}
