package com.bees360.address;

import com.google.common.base.Preconditions;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/** 将会按顺序向provider发起请求，如果得到响应不为空，则返回，如果响应结果为空，则将请求发送给下一个provider. */
@Log4j2
public class CompositeAddressProvider implements AddressProvider {

    private final List<? extends AddressProvider> addressProviders;

    public <V extends AddressProvider> CompositeAddressProvider(
            @NonNull final Iterable<V> addressProviders) {
        this.addressProviders =
                StreamSupport.stream(addressProviders.spliterator(), false)
                        .collect(Collectors.toList());
        Preconditions.checkArgument(this.addressProviders.size() > 0, "must provide at least one.");
        log.info("Created '{}' '{}'", this, addressProviders);
    }

    @Override
    public Address normalize(Address address) {
        return doApply(p -> p.normalize(address));
    }

    @Override
    public Address normalize(String address) {
        return doApply(p -> p.normalize(address));
    }

    @Override
    public Address findById(String id) {
        return doApply(p -> p.findById(id));
    }

    @Override
    public Address findByAddress(String address) {
        return doApply(p -> p.findByAddress(address));
    }

    @Override
    public Iterable<? extends Address> findAllById(Iterable<String> ids) {
        return doApply(p -> p.findAllById(ids));
    }

    public <T> T doApply(Function<AddressProvider, T> function) {
        for (AddressProvider provider : addressProviders) {
            var normalize = function.apply(provider);
            if (normalize != null) {
                return normalize;
            }
        }
        return null;
    }
}
