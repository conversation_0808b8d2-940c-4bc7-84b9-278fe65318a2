package com.bees360.address;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Log4j2
public class AddressNormalizeService implements AddressProvider {

    private final AddressProvider addressProviders;
    private final AddressRepository addressRepository;
    private final Boolean enableCorrectZipcode;

    public AddressNormalizeService(
            AddressProvider addressProviders,
            AddressRepository addressRepository,
            Boolean enableCorrectZipcode) {
        this.addressProviders = addressProviders;
        this.addressRepository = addressRepository;
        this.enableCorrectZipcode = enableCorrectZipcode;
        log.info(
                "Created '{}' addressProviders '{}' addressRepository '{}', enableCorrectZipcode"
                        + " '{}'",
                this,
                addressProviders,
                addressRepository,
                enableCorrectZipcode);
    }

    public AddressNormalizeService(
            AddressProvider addressProviders, AddressRepository addressRepository) {
        this(addressProviders, addressRepository, false);
    }

    @Override
    public Address normalize(Address address) {
        var place = addressProviders.normalize(address);
        place = saveIfNotExist(place);
        if (place == null || enableCorrectZipcode) {
            return place;
        }
        if (isZipCodeSame(address, place)) {
            return place;
        }
        log.warn(
                "The address from other is not the same address origin '{}' now '{}'",
                address.toMessage(),
                place.toMessage());
        return null;
    }

    @Override
    public Address normalize(String address) {
        var place = findByAddress(address);
        log.info("Normalize success and save address '{}'", address);
        return saveIfNotExist(place);
    }

    private Address saveIfNotExist(Address place) {
        if (place == null) {
            return null;
        }
        if (place.getId() != null) {
            return place;
        }
        var exists = addressRepository.findByAddress(place.getAddress());
        if (exists != null) {
            return exists;
        }
        log.info("Normalize success and save address '{}'", place.getAddress());
        String id = addressRepository.save(place);
        return Address.from(place.toMessage().toBuilder().setId(id).build());
    }

    @Override
    public Address findById(String id) {
        Address exists = addressRepository.findById(id);
        if (exists != null) {
            return exists;
        }
        return addressProviders.findById(id);
    }

    @Override
    public Address findByAddress(String address) {
        Address exists = addressRepository.findByAddress(address);
        if (exists != null) {
            return exists;
        }
        return addressProviders.findByAddress(address);
    }

    @Override
    public Iterable<? extends Address> findAllById(@NonNull Iterable<String> ids) {
        Iterable<? extends Address> exits = addressRepository.findAllById(ids);
        if (exits != null) {
            return exits;
        }
        return addressProviders.findAllById(ids);
    }

    private boolean isZipCodeSame(Address origin, Address remote) {
        if (origin == null || remote == null) {
            return false;
        }
        if (StringUtils.isBlank(origin.getZip())) {
            return true;
        }
        return Objects.equals(toShortZipcode(remote.getZip()), toShortZipcode(origin.getZip()));
    }

    private String toShortZipcode(String zipcode) {
        if (zipcode == null) {
            return null;
        }
        var shortName = StringUtils.substringBefore(zipcode, "-");
        return StringUtils.stripStart(shortName, "0");
    }
}
