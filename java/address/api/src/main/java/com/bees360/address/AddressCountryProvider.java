package com.bees360.address;

import org.apache.commons.lang3.StringUtils;

public class AddressCountryProvider extends ForwardingAddressProvider {

    public AddressCountryProvider(AddressProvider addressProvider) {
        super(addressProvider);
    }

    @Override
    public Address normalize(Address address) {
        var normalize = delegate().normalize(address);
        return AddressCountryProcessor.getCountryDetailAddress(normalize, address.getCountry());
    }

    @Override
    public Address normalize(String address) {
        var normalize = delegate().normalize(address);
        return AddressCountryProcessor.getCountryDetailAddress(normalize, normalize.getCountry());
    }

    @Override
    public Address findById(String id) {
        var normalize = delegate().findById(id);
        return AddressCountryProcessor.getCountryDetailAddress(normalize, normalize.getCountry());
    }

    @Override
    public Address findByAddress(String address) {
        var normalize = delegate().findByAddress(address);
        return AddressCountryProcessor.getCountryDetailAddress(normalize, normalize.getCountry());
    }

    public static class AddressCountryProcessor {
        public static Address getCountryDetailAddress(Address address, String country) {
            if (null == country) {
                return us(address);
            }
            switch (country) {
                case "US":
                case "USA":
                    return us(address);
                default:
                    return us(address);
            }
        }

        private static Address us(Address address) {
            if (address == null) {
                return null;
            }
            var detail = StringUtils.removeEnd(address.getAddress(), ", USA");
            return Address.from(address.toMessage().toBuilder().setAddress(detail).build());
        }
    }
}
