package com.bees360.address;

import com.bees360.address.Message.AddressQueryRequest;
import com.bees360.hazardhub.AddressEnhancedProperty;
import com.bees360.hazardhub.AddressHazardHub;
import com.bees360.hazardhub.AddressReplacementCosts;
import com.bees360.hazardhub.AddressRisks;

/**
 * Address CURD for outside
 *
 * <AUTHOR>
 */
public interface AddressManager extends AddressProvider {
    /**
     * Query hazardhub address by address id
     *
     * @param request address request
     * @return address hazardhub
     */
    AddressHazardHub findAddressHazardhub(AddressQueryRequest request);

    /**
     * Query address risks by address
     *
     * @param request address request
     * @return address hazardhub
     */
    AddressRisks findAddressRisks(AddressQueryRequest request);

    /**
     * Query address enhanced property by address
     *
     * @param request address request
     * @return address hazardhub
     */
    AddressEnhancedProperty findAddressEnhancedProperty(AddressQueryRequest request);

    /**
     * Query address replacement costs by address
     *
     * @param request address request
     * @return address hazardhub
     */
    AddressReplacementCosts findAddressReplacementCosts(AddressQueryRequest request);
}
