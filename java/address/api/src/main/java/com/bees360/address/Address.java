package com.bees360.address;

import com.bees360.address.Message.AddressMessage;
import com.bees360.address.Message.AddressMessage.FlyZoneType;
import com.bees360.api.Entity;
import com.bees360.api.Proto;
import com.bees360.util.DateTimes;
import com.bees360.util.Functions;

import jakarta.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.Optional;
import java.util.TimeZone;

public interface Address extends Proto<AddressMessage>, Entity {

    @Override
    default String getNamespace() {
        return "address";
    }

    Double getLat();

    Double getLng();

    /** 69 Baltimore St, Staten Island, NY 10308 */
    String getAddress();

    /** 门牌号 e.g. 69 */
    String getStreetNumber();

    /** 街道 e.g. Baltimore St */
    String getRoute();

    /** 城市city e.g. Staten Island */
    String getCity();

    /** 郡县 e.g. Richmond County */
    String getCounty();

    /** 州 e.g. NY */
    String getState();

    /** 国家地区 e.g. US */
    String getCountry();

    String getZip();

    Instant getCreatedAt();

    Instant getUpdatedAt();

    HiveLocationDistance getHiveDistance();

    FlyZoneType getFlyZoneType();

    String getStreetAddress();

    @Nullable
    TimeZone getTimeZone();

    Boolean isGpsApproximate();

    Airspace getAirspace();

    class AddressBuilder {

        @lombok.Builder(
                builderClassName = "Builder",
                builderMethodName = "newBuilder",
                setterPrefix = "set")
        static Address of(
                String id,
                Double lat,
                Double lng,
                String address,
                String streetNumber,
                String route,
                String city,
                String county,
                String state,
                String country,
                String zip,
                Instant createdAt,
                Instant updatedAt,
                HiveLocationDistance hiveDistance,
                FlyZoneType flyZoneType,
                String streetAddress,
                TimeZone timeZone,
                Boolean isGpsApproximate,
                Airspace airspace) {
            return Address.of(
                    id,
                    lat,
                    lng,
                    address,
                    streetNumber,
                    route,
                    city,
                    county,
                    state,
                    country,
                    zip,
                    createdAt,
                    updatedAt,
                    hiveDistance,
                    flyZoneType,
                    streetAddress,
                    timeZone,
                    isGpsApproximate,
                    airspace);
        }
    }

    static Address of(
            String id,
            Double lat,
            Double lng,
            String address,
            String streetNumber,
            String route,
            String city,
            String county,
            String state,
            String country,
            String zip,
            Instant createdAt,
            Instant updatedAt,
            HiveLocationDistance hiveDistance,
            FlyZoneType flyZoneType,
            String streetAddress,
            TimeZone timeZone,
            Boolean isGpsApproximate,
            Airspace airspace) {

        return new Address() {

            @Override
            public String getId() {
                return id;
            }

            @Override
            public Double getLat() {
                return lat;
            }

            @Override
            public Double getLng() {
                return lng;
            }

            @Override
            public String getAddress() {
                return address;
            }

            @Override
            public String getStreetNumber() {
                return streetNumber;
            }

            @Override
            public String getRoute() {
                return route;
            }

            @Override
            public String getCity() {
                return city;
            }

            @Override
            public String getState() {
                return state;
            }

            @Override
            public String getCountry() {
                return country;
            }

            @Override
            public String getCounty() {
                return county;
            }

            @Override
            public String getZip() {
                return zip;
            }

            @Override
            public Instant getCreatedAt() {
                return createdAt;
            }

            @Override
            public Instant getUpdatedAt() {
                return updatedAt;
            }

            @Override
            public HiveLocationDistance getHiveDistance() {
                return hiveDistance;
            }

            @Nullable
            @Override
            public FlyZoneType getFlyZoneType() {
                return flyZoneType;
            }

            @Override
            public String getStreetAddress() {
                return streetAddress;
            }

            @Override
            public TimeZone getTimeZone() {
                return timeZone;
            }

            @Override
            public Boolean isGpsApproximate() {
                return isGpsApproximate;
            }

            @Override
            public Airspace getAirspace() {
                return airspace;
            }
        };
    }

    static Address from(final AddressMessage address) {
        if (address.equals(AddressMessage.getDefaultInstance())) {
            return null;
        }

        return new Address() {

            @Override
            public String getId() {
                return address.getId();
            }

            @Override
            public Double getLat() {
                return address.getLat();
            }

            @Override
            public Double getLng() {
                return address.getLng();
            }

            @Override
            public String getAddress() {
                return address.getAddress();
            }

            @Override
            public String getStreetNumber() {
                return address.getStreetNumber();
            }

            @Override
            public String getRoute() {
                return address.getRoute();
            }

            @Override
            public String getCity() {
                return address.getCity();
            }

            @Override
            public String getState() {
                return address.getState();
            }

            @Override
            public String getCountry() {
                return address.getCountry();
            }

            @Override
            public String getCounty() {
                return address.getCounty();
            }

            @Override
            public String getZip() {
                return address.getZip();
            }

            @Override
            public Instant getCreatedAt() {
                return DateTimes.toInstant(address.getCreatedAt());
            }

            @Override
            public Instant getUpdatedAt() {
                return DateTimes.toInstant(address.getUpdatedAt());
            }

            @Override
            public HiveLocationDistance getHiveDistance() {
                return HiveLocationDistance.from(address.getHiveDistance());
            }

            @Override
            public FlyZoneType getFlyZoneType() {
                return address.getFlyZoneType();
            }

            @Override
            public String getStreetAddress() {
                return address.getStreetAddress();
            }

            @Override
            @Nullable
            public TimeZone getTimeZone() {
                if (StringUtils.isEmpty(address.getTimeZone())) {
                    return null;
                }
                return TimeZone.getTimeZone(address.getTimeZone());
            }

            @Override
            public Boolean isGpsApproximate() {
                return address.getIsGpsApproximate();
            }

            @Override
            public Airspace getAirspace() {
                return ProtoAirspace.from(address.getAirspace());
            }
        };
    }

    @Override
    default AddressMessage toMessage() {
        AddressMessage.Builder builder = AddressMessage.newBuilder();
        Functions.acceptIfNotNull(builder::setId, getId());
        Functions.acceptIfNotNull(builder::setAddress, getAddress());
        Functions.acceptIfNotNull(builder::setStreetNumber, getStreetNumber());
        Functions.acceptIfNotNull(builder::setRoute, getRoute());
        Functions.acceptIfNotNull(builder::setCity, getCity());
        Functions.acceptIfNotNull(builder::setCounty, getCounty());
        Functions.acceptIfNotNull(builder::setState, getState());
        Functions.acceptIfNotNull(builder::setCountry, getCountry());
        Functions.acceptIfNotNull(builder::setZip, getZip());
        Functions.acceptIfNotNull(builder::setLat, getLat());
        Functions.acceptIfNotNull(builder::setLng, getLng());
        Functions.acceptIfNotNull(builder::setStreetAddress, getStreetAddress());
        Functions.acceptIfNotNull(builder::setTimeZone, getTimeZone(), TimeZone::getID);
        Functions.acceptIfNotNull(
                e -> builder.setCreatedAt(DateTimes.toTimestamp(e)), getCreatedAt());
        Functions.acceptIfNotNull(
                e -> builder.setUpdatedAt(DateTimes.toTimestamp(e)), getUpdatedAt());

        Optional.ofNullable(getHiveDistance())
                .ifPresent(
                        hive -> {
                            var hiveDistanceBuilder = Message.HiveLocationDistance.newBuilder();
                            Functions.acceptIfNotNull(
                                    hiveDistanceBuilder::setZipcode, hive.getZipcode());
                            Functions.acceptIfNotNull(
                                    hiveDistanceBuilder::setDistanceMiles, hive.getDistanceMiles());
                            Functions.acceptIfNotNull(hiveDistanceBuilder::setCity, hive.getCity());
                            Functions.acceptIfNotNull(
                                    hiveDistanceBuilder::setState, hive.getState());
                            builder.setHiveDistance(hiveDistanceBuilder);
                        });
        Functions.acceptIfNotNull(builder::setFlyZoneType, getFlyZoneType());
        Functions.acceptIfNotNull(builder::setIsGpsApproximate, isGpsApproximate());
        Functions.acceptIfNotNull(builder::setAirspace, getAirspace(), Airspace::toMessage);
        return builder.build();
    }
}
