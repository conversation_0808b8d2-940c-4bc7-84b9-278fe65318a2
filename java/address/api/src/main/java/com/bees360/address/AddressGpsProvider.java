package com.bees360.address;

import com.bees360.api.common.Gps;

public interface AddressGpsProvider {

    /**
     * 根据address信息查询对应的gps。返回的gps所在地的zipcode需与address.getZip()相同。
     *
     * @param address 用于查询gps的address信息。
     * @return address信息查询对应的gps。
     * @exception IllegalArgumentException address.getZip()为null。
     * @exception java.util.NoSuchElementException 未查询到address对应gps的信息。
     */
    @Deprecated
    Gps getZipcodeGpsByAddress(Address address);

    /**
     * 查询address对应的gps信息，并填充在原有的address中。
     *
     * @param address 用于查询gps的address信息。
     * @return 填充后的address，若未查询到gps信息，则不会更新原有的address。
     */
    Address fillAddressGps(Address address);
}
