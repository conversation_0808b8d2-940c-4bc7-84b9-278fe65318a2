package com.bees360.address;

import com.bees360.address.Message.AddressEnhancedPropertyMessage;
import com.bees360.address.Message.AddressQueryRequest;
import com.bees360.address.Message.AddressReplacementCostsMessage;
import com.bees360.address.Message.AddressRisksMessage;
import com.bees360.hazardhub.AddressEnhancedProperty;
import com.bees360.hazardhub.AddressReplacementCosts;
import com.bees360.hazardhub.AddressRisks;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Import;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/** 地址服务HTTP接入点 */
@ConditionalOnProperty(prefix = "http.address", value = "endpoint")
@RestController
@Log4j2
@Import({ProtoHttpMessageConverterConfig.class, ApiExceptionHandler.class})
public class AddressManagerEndpoint {

    private final AddressManager addressManager;

    AddressManagerEndpoint(@NonNull final AddressManager grpcAddressManager) {
        this.addressManager = grpcAddressManager;

        log.info("Created {}", this);
    }

    @PostMapping(value = "${http.address.endpoint}/risks")
    public AddressRisksMessage findAddressRisks(@RequestBody AddressQueryRequest request) {
        return Optional.ofNullable(addressManager.findAddressRisks(request))
                .map(AddressRisks::toMessage)
                .orElse(AddressRisksMessage.getDefaultInstance());
    }

    @PostMapping(value = "${http.address.endpoint}/enhanced-property")
    public AddressEnhancedPropertyMessage findAddressEnhancedProperty(
            @RequestBody AddressQueryRequest request) {
        return Optional.ofNullable(addressManager.findAddressEnhancedProperty(request))
                .map(AddressEnhancedProperty::toMessage)
                .orElse(AddressEnhancedPropertyMessage.getDefaultInstance());
    }

    @PostMapping(value = "${http.address.endpoint}/replacement-costs")
    public AddressReplacementCostsMessage findAddressReplacementCosts(
            @RequestBody AddressQueryRequest request) {
        return Optional.ofNullable(addressManager.findAddressReplacementCosts(request))
                .map(AddressReplacementCosts::toMessage)
                .orElse(AddressReplacementCostsMessage.getDefaultInstance());
    }
}
