package com.bees360.beespilot;

import static com.bees360.project.Message.ServiceType.SCHEDULING_ONLY;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.bees360.bundle.Message.BundleMessage;
import com.bees360.http.HttpClient;
import com.bees360.util.DateTimes;
import com.google.gson.Gson;
import com.google.protobuf.StringValue;
import com.google.protobuf.Timestamp;

import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.net.URI;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

class HttpBeespilotBundleManagerTest {

    @Mock private HttpClient httpClient;

    private URI context;
    private Map<BeespilotBundleParamEnum, String> pathMap;
    private HttpBeespilotBundleManager manager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        context = URI.create("http://example.com");
        pathMap = new HashMap<>();
        pathMap.put(BeespilotBundleParamEnum.BUNDLE_CREATED, "/bundle/create");
        pathMap.put(BeespilotBundleParamEnum.BUNDLE_UPDATE, "/bundle/update");
        pathMap.put(BeespilotBundleParamEnum.BIND_PROJECT, "/bundle/project/bind");
        pathMap.put(BeespilotBundleParamEnum.REMOVE_PROJECT, "/bundle/project/remove");
        manager = new HttpBeespilotBundleManager(context, pathMap, httpClient);
    }

    @Test
    void createBundle_success() throws Exception {
        // Arrange
        var bundle = createTestBundle();
        // Fix: Use doReturn().when() instead of doNothing().when() since execute likely returns a
        // value
        doReturn(null).when(httpClient).execute(any(HttpPost.class), any());

        // Act
        manager.createBundle(bundle);

        // Assert
        var requestCaptor = ArgumentCaptor.forClass(HttpPost.class);
        verify(httpClient).execute(requestCaptor.capture(), any());

        var capturedRequest = requestCaptor.getValue();
        assertEquals("http://example.com/bundle/create", capturedRequest.getURI().toString());

        var entity = (ByteArrayEntity) capturedRequest.getEntity();
        var json = new String(entity.getContent().readAllBytes());
        var gson = new Gson();
        var map = gson.fromJson(json, Map.class);

        assertEquals("123", map.get("bundleId"));
        assertEquals("INSPECTION123", map.get("inspectionNumber"));
        assertEquals((double) SCHEDULING_ONLY.getNumber(), map.get("serviceTypeCode"));
        assertEquals("OPEN", map.get("state"));
        assertEquals("CREATED", map.get("status"));

        var addressMap = (Map<String, Object>) map.get("address");
        assertEquals(37.7749, addressMap.get("lat"));
        assertEquals(-122.4194, addressMap.get("lng"));
        assertEquals("San Francisco", addressMap.get("city"));
        assertEquals("USA", addressMap.get("country"));
        assertEquals("CA", addressMap.get("state"));
        assertEquals("123 Main St", addressMap.get("streetAddress"));
        assertEquals("94105", addressMap.get("zip"));
    }

    @Test
    void createBundle_pathNotFound_throwsException() {
        // Arrange
        pathMap.remove(BeespilotBundleParamEnum.BUNDLE_CREATED);
        var bundle = createTestBundle();

        // Act & Assert
        var exception =
                assertThrows(IllegalStateException.class, () -> manager.createBundle(bundle));
        assertTrue(
                exception.getMessage().contains("Cannot find BEESPILOT path for BUNDLE_CREATED"));
    }

    @Test
    void updateBundle_success() throws Exception {
        // Arrange
        var bundle = createTestBundle();

        // Mock static DateTimes.toInstant
        try (MockedStatic<DateTimes> dateTimesMock = mockStatic(DateTimes.class)) {
            var instant = Instant.ofEpochMilli(1616161616161L);
            dateTimesMock.when(() -> DateTimes.toInstant(any(Timestamp.class))).thenReturn(instant);

            doReturn(null).when(httpClient).execute(any(HttpPost.class), any());

            // Act
            manager.updateBundle(bundle);

            // Assert
            var requestCaptor = ArgumentCaptor.forClass(HttpPost.class);
            verify(httpClient).execute(requestCaptor.capture(), any());

            var capturedRequest = requestCaptor.getValue();
            assertEquals("http://example.com/bundle/update", capturedRequest.getURI().toString());

            var entity = (ByteArrayEntity) capturedRequest.getEntity();
            var json = new String(entity.getContent().readAllBytes());
            var gson = new Gson();
            var map = gson.fromJson(json, Map.class);

            assertEquals("123", map.get("bundleId"));
            assertEquals("INSPECTION123", map.get("inspectionNumber"));
            assertEquals((double) SCHEDULING_ONLY.getNumber(), map.get("serviceTypeCode"));
            assertEquals("OPEN", map.get("state"));
            assertEquals("CREATED", map.get("status"));
            // Fix: Compare as double since JSON parsing converts large numbers to double in
            // scientific notation
            assertEquals((double) 1616161616161L, (Double) map.get("createTime"), 0.001);

            var addressMap = (Map<String, Object>) map.get("address");
            assertEquals(37.7749, addressMap.get("lat"));
            assertEquals(-122.4194, addressMap.get("lng"));
            assertEquals("San Francisco", addressMap.get("city"));
            assertEquals("USA", addressMap.get("country"));
            assertEquals("CA", addressMap.get("state"));
            assertEquals("123 Main St", addressMap.get("streetAddress"));
            assertEquals("94105", addressMap.get("zip"));
        }
    }

    @Test
    void updateBundle_pathNotFound_throwsException() {
        // Arrange
        pathMap.remove(BeespilotBundleParamEnum.BUNDLE_UPDATE);
        var bundle = createTestBundle();

        // Act & Assert
        var exception =
                assertThrows(IllegalStateException.class, () -> manager.updateBundle(bundle));
        assertTrue(exception.getMessage().contains("Cannot find BEESPILOT path for BUNDLE_UPDATE"));
    }

    @Test
    void addBundleProject_success() throws Exception {
        // Arrange
        String bundleId = "123";
        List<String> projectIds = List.of("proj1", "proj2");
        doReturn(null).when(httpClient).execute(any(HttpPost.class), any());

        // Act
        manager.addBundleProject(bundleId, projectIds);

        // Assert
        ArgumentCaptor<HttpPost> requestCaptor = ArgumentCaptor.forClass(HttpPost.class);
        verify(httpClient).execute(requestCaptor.capture(), any());

        HttpPost capturedRequest = requestCaptor.getValue();
        assertEquals("http://example.com/bundle/project/bind", capturedRequest.getURI().toString());

        ByteArrayEntity entity = (ByteArrayEntity) capturedRequest.getEntity();
        String json = new String(entity.getContent().readAllBytes());
        Gson gson = new Gson();
        Map<String, Object> map = gson.fromJson(json, Map.class);

        assertEquals(bundleId, map.get("bundleId"));
        assertEquals(projectIds, map.get("projectIds"));
    }

    @Test
    void addBundleProject_pathNotFound_throwsException() {
        // Arrange
        pathMap.remove(BeespilotBundleParamEnum.BIND_PROJECT);
        String bundleId = "123";
        List<String> projectIds = List.of("proj1", "proj2");

        // Act & Assert
        IllegalStateException exception =
                assertThrows(
                        IllegalStateException.class,
                        () -> manager.addBundleProject(bundleId, projectIds));

        assertTrue(exception.getMessage().contains("Cannot find BEESPILOT path for BIND_PROJECT"));
    }

    @Test
    void removeBundleProject_success() throws Exception {
        // Arrange
        String bundleId = "123";
        List<String> projectIds = List.of("proj1", "proj2");
        doReturn(null).when(httpClient).execute(any(HttpPost.class), any());

        // Act
        manager.removeBundleProject(bundleId, projectIds);

        // Assert
        ArgumentCaptor<HttpPost> requestCaptor = ArgumentCaptor.forClass(HttpPost.class);
        verify(httpClient).execute(requestCaptor.capture(), any());

        HttpPost capturedRequest = requestCaptor.getValue();
        assertEquals(
                "http://example.com/bundle/project/remove", capturedRequest.getURI().toString());

        ByteArrayEntity entity = (ByteArrayEntity) capturedRequest.getEntity();
        String json = new String(entity.getContent().readAllBytes());
        Gson gson = new Gson();
        Map<String, Object> map = gson.fromJson(json, Map.class);

        assertEquals(bundleId, map.get("bundleId"));
        assertEquals(projectIds, map.get("projectIds"));
    }

    @Test
    void removeBundleProject_pathNotFound_throwsException() {
        // Arrange
        pathMap.remove(BeespilotBundleParamEnum.REMOVE_PROJECT);
        String bundleId = "123";
        List<String> projectIds = List.of("proj1");

        // Act & Assert
        IllegalStateException exception =
                assertThrows(
                        IllegalStateException.class,
                        () -> manager.removeBundleProject(bundleId, projectIds));

        assertTrue(
                exception.getMessage().contains("Cannot find BEESPILOT path for REMOVE_PROJECT"));
    }

    private BundleMessage createTestBundle() {
        var addressBuilder =
                com.bees360.address.Message.AddressMessage.newBuilder()
                        .setLat(37.7749)
                        .setLng(-122.4194)
                        .setCity("San Francisco")
                        .setCountry("USA")
                        .setState("CA")
                        .setStreetAddress("123 Main St")
                        .setZip("94105");

        var createdAt = Timestamp.newBuilder().setSeconds(Instant.now().getEpochSecond()).build();

        return BundleMessage.newBuilder()
                .setId(StringValue.of("123"))
                .setInspectionNo(StringValue.of("INSPECTION123"))
                .setServiceType(SCHEDULING_ONLY)
                .setState(com.bees360.bundle.Message.BundleState.OPEN)
                .setStatus(com.bees360.bundle.Message.BundleStatus.CREATED)
                .setAddress(addressBuilder.build())
                .setCreatedAt(createdAt)
                .build();
    }
}
