package com.bees360.beespilot;

import com.bees360.bundle.Message;
import com.bees360.http.HttpClient;
import com.bees360.util.DateTimes;
import com.google.gson.Gson;

import lombok.extern.log4j.Log4j2;

import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;

import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Log4j2
public class HttpBeespilotBundleManager implements BeespilotBundleManager {
    private static final Gson gson = new Gson().newBuilder().serializeNulls().create();

    private final URI context;

    private final Map<BeespilotBundleParamEnum, String> pathMap;

    private final HttpClient httpClient;

    public HttpBeespilotBundleManager(
            URI context, Map<BeespilotBundleParamEnum, String> pathMap, HttpClient httpClient) {
        this.context = context;
        this.pathMap = pathMap;
        this.httpClient = httpClient;
        log.info(
                "Created {}(context={}, pathMap={}, httpClient={}).",
                this,
                context,
                pathMap,
                httpClient);
    }

    @Override
    public void createBundle(Message.BundleMessage bundle) {
        log.debug("Creating beespilot bundle {}.", bundle);
        executeBundle(BeespilotBundleParamEnum.BUNDLE_CREATED, bundle);
    }

    @Override
    public void updateBundle(Message.BundleMessage bundle) {
        log.debug("Updating beespilot bundle {}.", bundle);
        executeBundle(BeespilotBundleParamEnum.BUNDLE_UPDATE, bundle);
    }

    @Override
    public void addBundleProject(String bundleId, List<String> projectIds) {
        log.debug("Adding beespilot bundle project {} to bundle {}.", projectIds, bundleId);
        updateBundleProject(BeespilotBundleParamEnum.BIND_PROJECT, bundleId, projectIds);
    }

    @Override
    public void removeBundleProject(String bundleId, List<String> projectIds) {
        log.debug("Removing beespilot bundle project {} from bundle {}.", projectIds, bundleId);
        updateBundleProject(BeespilotBundleParamEnum.REMOVE_PROJECT, bundleId, projectIds);
    }

    private void executeBundle(BeespilotBundleParamEnum executeType, Message.BundleMessage bundle) {
        var path = pathMap.get(executeType);
        if (Objects.isNull(path)) {
            throw new IllegalStateException(
                    "Cannot find BEESPILOT path for %s.".formatted(executeType));
        }
        var uri = context.resolve(path);
        var postRequest = new HttpPost(uri);
        var map = new HashMap<>();
        map.put("bundleId", bundle.getId().getValue());
        map.put("createTime", DateTimes.toInstant(bundle.getCreatedAt()).toEpochMilli());
        map.put("inspectionNumber", bundle.getInspectionNo().getValue());
        map.put("serviceTypeCode", bundle.getServiceType().getNumber());
        map.put("state", bundle.getState().name());
        map.put("status", bundle.getStatus().name());
        var address = bundle.getAddress();
        var addressMap = new HashMap<>();
        addressMap.put("lat", address.getLat());
        addressMap.put("lng", address.getLng());
        addressMap.put("city", address.getCity());
        addressMap.put("country", address.getCountry());
        addressMap.put("state", address.getState());
        addressMap.put("streetAddress", address.getStreetAddress());
        addressMap.put("zip", address.getZip());
        map.put("address", addressMap);
        var json = gson.toJson(map);
        log.debug("Executing beespilot bundle with json: {}.", json);
        var entity = new ByteArrayEntity(json.getBytes(), ContentType.APPLICATION_JSON);
        postRequest.setEntity(entity);
        httpClient.execute(
                postRequest,
                rsp ->
                        HttpClient.throwApiExceptionNon2xxResponse(
                                rsp, HttpClient::convertResponseToString));
    }

    private void updateBundleProject(
            BeespilotBundleParamEnum updateType, String bundleId, List<String> projectIds) {
        var path = pathMap.get(updateType);
        if (Objects.isNull(path)) {
            throw new IllegalStateException(
                    "Cannot find BEESPILOT path for %s.".formatted(updateType));
        }
        var uri = context.resolve(path);
        var request = new HttpPost(uri);
        var map = new HashMap<>();
        map.put("bundleId", bundleId);
        map.put("projectIds", projectIds);
        var json = gson.toJson(map);
        log.debug("Updating beespilot bundle project with json: {}.", json);
        var entity = new ByteArrayEntity(json.getBytes(), ContentType.APPLICATION_JSON);
        request.setEntity(entity);
        httpClient.execute(
                request,
                rsp ->
                        HttpClient.throwApiExceptionNon2xxResponse(
                                rsp, HttpClient::convertResponseToString));
    }
}
