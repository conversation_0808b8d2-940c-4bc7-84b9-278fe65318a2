package com.bees360.pipeline; // package com.bees360.pipeline;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.pipeline.config.GrpcPipelineDefClientConfig;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@TestPropertySource(properties = "spring.config.location = classpath:GrpcPipelineServiceTest.yml")
@DirtiesContext
public class GrpcPipelineDefServiceTest extends AbstractPipelineDefServiceTest {
    @Configuration
    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class, // Create required server beans
        GrpcServerFactoryAutoConfiguration.class, // Select server implementation
        GrpcClientAutoConfiguration.class,
        ExceptionTranslateInterceptor.class,
    }) // Support @GrpcClient annotation
    @Import({
        GrpcPipelineDefClientConfig.class,
    })
    static class Config {
        @Bean
        public GrpcPipelineDefService grpcPipelineDefService() {
            return new GrpcPipelineDefService(new InMemoryPipelineDefService());
        }
    }

    private final PipelineDefService pipelineDefService;

    @Override
    PipelineDefService getPipelineDefService() {
        return pipelineDefService;
    }

    public GrpcPipelineDefServiceTest(@Autowired PipelineDefService pipelineDefService) {
        this.pipelineDefService = pipelineDefService;
    }

    @Test
    public void testCreatePipelineDefAndGetId() {
        super.testCreatePipelineDefAndGetId();
    }

    @Test
    public void testCreatePipelineAndGetByKeyAndStage() {
        super.testCreatePipelineAndGetByKeyAndStage();
    }

    @Test
    public void testCreatePipelineAndGetByKeyAndStageWithPrereq() {
        super.testCreatePipelineAndGetByKeyAndStageWithPrereq();
    }

    @Test
    public void testUpdatePipelineDefNameShouldWork() {
        super.testUpdatePipelineDefNameShouldWork();
    }

    @Test
    public void testUpdateTaskNameShouldWork() {
        super.testUpdateTaskNameShouldWork();
    }

    @Test
    public void testUpdateBatchTaskNameShouldWork() {
        super.testUpdateBatchTaskNameShouldWork();
    }

    @Test
    public void testFindAllTaskDef() {
        super.testFindAllTaskDef();
    }

    @Test
    void testCreatePipelineAndRenamePipelineDef() {
        super.testCreatePipelineAndRenamePipelineDef();
    }

    @Test
    void testCreatePipelineAndActivatePipelineDef() {
        super.testCreatePipelineAndActivatePipelineDef();
    }

    @Test
    void testCreateRepeatedlyThrowException() {
        super.testCreateRepeatedlyThrowException();
    }

    @Test
    void testCreatePipelineAndDeleteById() {
        super.testCreatePipelineAndDeleteById();
    }

    @Test
    void testCreatePipelineAndFindAndCountByQuery() {
        super.testCreatePipelineAndFindAndCountByQuery();
    }
}
