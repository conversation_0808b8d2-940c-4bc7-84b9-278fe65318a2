package com.bees360.pipeline;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.pipeline.assign.AssignRuleManager;
import com.bees360.pipeline.config.GrpcAssignRuleClientConfig;
import com.bees360.pipeline.impl.InMemoryAssignRuleManager;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringBootTest
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:GrpcPipelineServiceTest.yml")
@DirtiesContext
@ApplicationAutoConfig
public class GrpcAssignRuleServiceTest extends AbstractAssignRuleManagerTest {

    @Configuration
    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class, // Create required server beans
        GrpcServerFactoryAutoConfiguration.class, // Select server implementation
        GrpcClientAutoConfiguration.class
    }) // Support @GrpcClient annotation
    @Import({
        GrpcAssignRuleClientConfig.class,
        InMemoryAssignRuleManager.class,
        ExceptionTranslateInterceptor.class,
    })
    static class Config {
        @Bean
        public GrpcAssignRuleService grpcAssignRuleManager(
                InMemoryAssignRuleManager assignRuleManager) {
            return new GrpcAssignRuleService(assignRuleManager);
        }
    }

    @Autowired private GrpcAssignRuleClient assignRuleManager;

    @Override
    AssignRuleManager getAssignRuleManager() {
        return assignRuleManager;
    }

    @Test
    public void testCreateAndFindAndDeleteAssignRule() {
        super.testCreateAndFindAndDeleteAssignRule();
    }

    @Test
    public void testCreateAndListAssignRule() {
        super.testCreateAndListAssignRule();
    }

    @Test
    public void testCreateWidthFullFilterAndLossFunctionShouldSuccess() {
        super.testCreateWidthFullFilterAndLossFunctionShouldSuccess();
    }

    @Test
    public void createWithBlankAssigneeIdShouldThrow() {
        super.createWithBlankAssigneeIdShouldThrow();
    }

    @Test
    public void createWithPercentageExceedsTheRangeShouldThrow() {
        super.createWithPercentageExceedsTheRangeShouldThrow();
    }

    @Test
    public void testCreateWithAssigneePercentageShouldSuccess() {
        super.testCreateWithAssigneePercentageShouldSuccess();
    }

    @Test
    public void testCreateWithSameKeyShouldSuccess() {
        super.testCreateWithSameKeyShouldSuccess();
    }
}
