package com.bees360.pipeline;

import static com.bees360.pipeline.TestUtils.randomKey;
import static com.bees360.pipeline.TestUtils.randomOwner;
import static com.bees360.pipeline.TestUtils.randomStage;
import static com.bees360.pipeline.TestUtils.randomStatus;

import com.bees360.api.InvalidArgumentException;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.pipeline.config.GrpcPipelineClientConfig;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@TestPropertySource(properties = "spring.config.location = classpath:GrpcPipelineServiceTest.yml")
@DirtiesContext
public class GrpcPipelineServiceTest extends AbstractPipelineServiceTest {
    @Configuration
    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class, // Create required server beans
        GrpcServerFactoryAutoConfiguration.class, // Select server implementation
        GrpcClientAutoConfiguration.class,
    }) // Support @GrpcClient annotation
    @Import({GrpcPipelineClientConfig.class, ExceptionTranslateInterceptor.class})
    static class Config {
        @Bean
        public PipelineDefService pipelineDefService() {
            return new InMemoryPipelineDefService();
        }

        @Bean
        public GrpcPipelineService grpcTaskStateService(PipelineDefService pipelineDefService) {
            return new GrpcPipelineService(new InMemoryPipelineService(pipelineDefService));
        }
    }

    private final PipelineDefService pipelineDefService;
    private final PipelineService pipelineService;

    public GrpcPipelineServiceTest(
            @Autowired PipelineDefService pipelineDefService,
            @Autowired PipelineService pipelineService) {
        this.pipelineDefService = pipelineDefService;
        this.pipelineService = pipelineService;
    }

    @Override
    PipelineService getPipelineService() {
        return pipelineService;
    }

    @Override
    PipelineDefService getPipelineDefService() {
        return pipelineDefService;
    }

    @Test
    void testSetPipelineTaskStatus() {
        super.testSetPipelineTaskStatus();
    }

    @Test
    void testSetPipelineTaskOwner() {
        super.testSetPipelineTaskOwner();
    }

    @Test
    void testSetPipelineTaskOwnerWithNullChangedByShouldSuccess() {
        super.testSetPipelineTaskOwnerWithNullChangedByShouldSuccess();
    }

    @Test
    void testSetPipelineStageOwner() {
        super.testSetPipelineStageOwner();
    }

    @Test
    void testFindByStage() {
        super.testFindByStage();
    }

    @Test
    void testFindByStageWithPrereq() {
        super.testFindByStageWithPrereq();
    }

    @Test
    void testGetPipelineIdByTask() {
        super.testGetPipelineIdByTask();
    }

    @Test
    void testGetPipelineIdByTaskWithOwner() {
        super.testGetPipelineIdByTaskWithOwner();
    }

    @Test
    void testGetPipelineIdByTaskAndWithTimeRange1() {
        super.testGetPipelineIdByTaskAndWithTimeRange1();
    }

    @Test
    void testGetPipelineIdByTaskAndWithTimeRange2() {
        super.testGetPipelineIdByTaskAndWithTimeRange2();
    }

    @Test
    void testGetPipelineIdByTaskAndWithTimeRange3() {
        super.testGetPipelineIdByTaskAndWithTimeRange3();
    }

    @Test
    void testGetPipelineIdByTaskAndWithTimeRange4() {
        super.testGetPipelineIdByTaskAndWithTimeRange4();
    }

    @Test
    void testSetTaskStatusThrowIllegalArgumentExceptionWhenPipelineDoesNotExist() {
        Assertions.assertThrows(
                InvalidArgumentException.class,
                () -> getPipelineService().setTaskStatus(randomKey(), randomKey(), randomStatus()));
    }

    @Test
    void testSetTaskOwnerThrowIllegalArgumentExceptionWhenPipelineDestNotExist() {
        Assertions.assertThrows(
                InvalidArgumentException.class,
                () ->
                        getPipelineService()
                                .setTaskOwner(
                                        randomKey(),
                                        randomKey(),
                                        randomOwner().getId(),
                                        randomOwner().getId()));
    }

    @Test
    void testSetStageOwnerThrowIllegalArgumentExceptionWhenPipelineDoesNotExist() {
        Assertions.assertThrows(
                InvalidArgumentException.class,
                () ->
                        getPipelineService()
                                .setStageOwner(randomKey(), randomStage(), randomOwner().getId()));
    }

    @Test
    void testBatchSetTaskOwnerShouldSucceed() {
        super.testBatchSetTaskOwnerShouldSucceed();
    }

    @Test
    void testBatchSetTaskStatusShouldSucceed() {
        super.testBatchSetTaskStatusShouldSucceed();
    }

    @Test
    void testBatchSetWithIllegalPipelineShouldThrowAndOtherShouldSucceed() {
        super.testBatchSetWithIllegalPipelineShouldThrowAndOtherShouldSucceed();
    }

    @Test
    void testClosePipeline() {
        super.testClosePipeline();
    }

    @Test
    void testRecoverPipeline() {
        super.testRecoverPipeline(Message.PipelineStatus.NONE);
    }

    @Test
    void testBatchSetSupervisorShouldSucceed() {
        super.testBatchSetSupervisorShouldSucceed();
    }

    @Test
    void testSetSupervisorWithIllegalTaskShouldThrowWithOtherSucceed() {
        super.testSetSupervisorWithIllegalTaskShouldThrowWithOtherSucceed();
    }
}
