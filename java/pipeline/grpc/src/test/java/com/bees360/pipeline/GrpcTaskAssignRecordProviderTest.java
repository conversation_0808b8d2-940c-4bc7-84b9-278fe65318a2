package com.bees360.pipeline;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.pipeline.assign.AssignHandler;
import com.bees360.pipeline.assign.AssignManager;
import com.bees360.pipeline.assign.AssignRuleManager;
import com.bees360.pipeline.assign.AssigneeManager;
import com.bees360.pipeline.assign.JooqPipelineTaskProvider;
import com.bees360.pipeline.assign.TaskAssignRecordProvider;
import com.bees360.pipeline.config.GrpcTaskAssignRecordClientConfig;
import com.bees360.pipeline.impl.InMemoryAssignManager;
import com.bees360.pipeline.impl.InMemoryAssignRuleManager;
import com.bees360.pipeline.impl.InMemoryAssigneeManager;
import com.bees360.pipeline.impl.InMemoryTaskAssignRecordManager;

import jakarta.annotation.Resource;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringBootTest
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:GrpcPipelineServiceTest.yml")
@DirtiesContext
@ApplicationAutoConfig
public class GrpcTaskAssignRecordProviderTest extends AbstractTaskAssignRecordProviderTest {
    @Configuration
    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class, // Create required server beans
        GrpcServerFactoryAutoConfiguration.class, // Select server implementation
        GrpcClientAutoConfiguration.class
    }) // Support @GrpcClient annotation
    @Import({
        GrpcTaskAssignRecordClientConfig.class,
        JooqConfig.class,
        JooqPipelineDefService.class,
        JooqPipelineDefService.JooqPipelineDefRepository.class,
        JooqTaskDefService.class,
        JooqPipelineService.class,
        JooqPipelineManager.class,
        JooqPipelineTaskProvider.class,
        InMemoryAssigneeManager.class,
        InMemoryTaskAssignRecordManager.class,
        InMemoryAssignRuleManager.class,
        InMemoryAssignManager.class,
    })
    static class Config {
        @Bean
        public GrpcTaskAssignRecordService grpcAssignRuleManager(
                InMemoryTaskAssignRecordManager taskAssignRecordManager) {
            return new GrpcTaskAssignRecordService(taskAssignRecordManager);
        }
    }

    @Autowired private AssigneeManager assigneeManager;
    @Autowired private AssignRuleManager assignRuleManager;
    @Autowired private PipelineDefService pipelineDefService;
    @Autowired private PipelineService pipelineService;
    @Autowired private TaskService taskService;
    @Autowired private InMemoryTaskAssignRecordManager taskAssignRecordManager;
    @Autowired private AssignManager assignManager;

    @Resource(name = "grpcTaskAssignRecordClient")
    private TaskAssignRecordProvider taskAssignRecordProvider;

    @Override
    AssignHandler getAssignHandler() {
        return new AssignHandler(assigneeManager, assignManager);
    }

    @Override
    TaskAssignRecordProvider getTaskAssignRecordManager() {
        return taskAssignRecordProvider;
    }

    @Override
    AssignRuleManager getAssignRuleManager() {
        return assignRuleManager;
    }

    @Override
    PipelineDefService getPipelineDefService() {
        return pipelineDefService;
    }

    @Override
    PipelineService getPipelineService() {
        return pipelineService;
    }

    @Test
    void testAssignAndSearchShouldSucceed() {
        super.testAssignAndSearchShouldSucceed();
    }

    @Test
    void testAssignAndSearchWithPaginationShouldSucceed() {
        super.testAssignAndSearchWithPaginationShouldSucceed();
    }
}
