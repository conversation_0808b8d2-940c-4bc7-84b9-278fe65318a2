package com.bees360.pipeline;

import com.bees360.assignment.AssigneeManagerGrpc;
import com.bees360.assignment.Message;
import com.bees360.grpc.ListStreamObserver;
import com.bees360.pipeline.assign.AssigneeManager;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@GrpcService
@Log4j2
public class GrpcAssigneeService extends AssigneeManagerGrpc.AssigneeManagerImplBase {
    private final AssigneeManager assigneeManager;

    public GrpcAssigneeService(AssigneeManager assigneeManager) {
        this.assigneeManager = assigneeManager;

        log.info("Created '{}'(assigneeManager={})", this, this.assigneeManager);
    }

    @Override
    public void getAssigneeStatus(
            Message.AssigneeScheduleRequest request,
            StreamObserver<Message.AssigneeScheduleResponse> responseObserver) {
        var result =
                assigneeManager.getAssigneeStatus(
                        request.getGroupId(), DateTimes.toInstant(request.getEndTime()));
        Iterables.toStream(result)
                .forEach(schedule -> responseObserver.onNext(schedule.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void assign(Message.AssignRequest request, StreamObserver<Empty> responseObserver) {
        assigneeManager.assign(
                request.getUserId(),
                request.getPipelineId(),
                request.getTaskKeyList(),
                com.bees360.pipeline.Message.TaskAssignChannelEnum.AUTO_ASSIGN_SCHEDULED,
                request.getAssignRuleKey(),
                StringUtils.defaultIfBlank(request.getAssignerId(), null));
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public StreamObserver<Message.SetAssigneeCapacityRequest> setAssigneeCapacity(
            StreamObserver<StringValue> responseObserver) {
        return new ListStreamObserver<>(
                templateKeys -> {
                    Map<String, Integer> userCapacityMap = new ConcurrentHashMap<>();
                    templateKeys.forEach(
                            request ->
                                    userCapacityMap.put(
                                            request.getUser().getId(), request.getCapacity()));
                    var capacity = assigneeManager.setAssigneeCapacity(userCapacityMap);
                    Iterables.toStream(capacity)
                            .filter(Objects::nonNull)
                            .forEach(m -> responseObserver.onNext(StringValue.of(m)));
                    responseObserver.onCompleted();
                });
    }

    public void setAssigneeCapacityScript(
            Message.SetAssigneeCapacityScriptRequest request,
            StreamObserver<BoolValue> responseObserver) {
        var result =
                assigneeManager.setAssigneeCapacityScript(
                        request.getGroupId(),
                        request.getCapacityScript(),
                        request.getUpdatedBy().getId());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }

    public void findAssigneeGroupByGroupId(
            StringValue request, StreamObserver<Message.AssigneeGroupMessage> responseObserver) {
        var assigneeGroup = assigneeManager.findAssigneeGroupByGroupId(request.getValue());
        responseObserver.onNext(assigneeGroup.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public void getAllAssignee(
            Empty request, StreamObserver<Message.AssigneeScheduleResponse> responseObserver) {
        var assigneeList = assigneeManager.listAllAssignee();
        assigneeList.forEach(a -> responseObserver.onNext(a.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void deleteAssignee(StringValue request, StreamObserver<Empty> responseObserver) {
        assigneeManager.deleteById(request.getValue());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
