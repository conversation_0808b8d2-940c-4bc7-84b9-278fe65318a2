package com.bees360.pipeline;

import static com.bees360.pipeline.assign.TaskAssignRecord.buildTaskToBeAssigned;

import com.bees360.api.Proto;
import com.bees360.pipeline.assign.SetTaskStatusRequest;
import com.bees360.util.Defaults;
import com.bees360.util.Iterables;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@GrpcService
@Log4j2
public class GrpcPipelineService extends PipelineServiceGrpc.PipelineServiceImplBase {
    public GrpcPipelineService(PipelineService grpcPipelineService) {
        this.pipelineService = grpcPipelineService;

        log.info("Created '{}'(pipelineService={})'", this, this.pipelineService);
    }

    private final PipelineService pipelineService;

    @Override
    public void getPipelineDefKey(
            StringValue request, StreamObserver<StringValue> responseObserver) {
        var r = pipelineService.findPipelineDefKey(request.getValue());
        var build = StringValue.newBuilder();
        Optional.ofNullable(r).ifPresent(build::setValue);
        responseObserver.onNext(build.build());
        responseObserver.onCompleted();
    }

    @Override
    public void createPipeline(
            Message.CreatePipelineRequest request,
            StreamObserver<Message.PipelineMessage> responseObserver) {
        var r = pipelineService.createPipeline(request.getId(), request.getDef());
        responseObserver.onNext(r.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public void setStageOwner(
            Message.SetStageOwnerRequest request, StreamObserver<Empty> responseObserver) {
        pipelineService.setStageOwner(
                request.getId(), request.getStage(), Defaults.nullIfEmpty(request.getOwner()));
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void setTaskOwner(
            Message.SetTaskOwnerRequest request, StreamObserver<Empty> responseObserver) {
        String ownerId = Defaults.nullIfEmpty(request.getOwner());
        pipelineService.setTaskOwner(
                request.getId(), request.getTask(), ownerId, request.getChangedBy());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void batchSetTaskOwner(
            Message.BatchSetTaskOwnerRequest request,
            StreamObserver<Message.AssignSummary> responseObserver) {
        var assignedTaskList =
                Iterables.toStream(request.getRequestsList())
                        .map(
                                r ->
                                        buildTaskToBeAssigned(
                                                r.getOwner(),
                                                r.getId(),
                                                r.getTask(),
                                                r.getChangedBy()))
                        .collect(Collectors.toList());
        var channelExternalId =
                Objects.equals(
                                request.getChannelExternalId(),
                                StringValue.getDefaultInstance().getValue())
                        ? null
                        : request.getChannelExternalId();
        var changedBy =
                Objects.equals(request.getChangedBy(), StringValue.getDefaultInstance().getValue())
                        ? null
                        : request.getChangedBy();
        var assignSummary =
                pipelineService.batchSetTaskOwner(
                        assignedTaskList, request.getChannel(), channelExternalId, changedBy);
        responseObserver.onNext(assignSummary.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public void setTaskStatus(
            Message.SetTaskStatusRequest request, StreamObserver<Empty> responseObserver) {
        pipelineService.setTaskStatus(
                request.getId(),
                request.getTask(),
                request.getStatus(),
                Defaults.nullIfEmpty(request.getComment()),
                Defaults.nullIfEmpty(request.getUpdatedBy()));
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void batchSetTaskStatus(
            Message.BatchSetTaskStatusRequest batchRequest,
            StreamObserver<Empty> responseObserver) {
        var requests = batchRequest.getRequestsList();
        pipelineService.batchSetTaskStatus(
                requests.stream()
                        .map(
                                request ->
                                        SetTaskStatusRequest.builder()
                                                .pipelineExternalId(request.getId())
                                                .taskDefKey(request.getTask())
                                                .status(request.getStatus())
                                                .comment(request.getComment())
                                                .build())
                        .collect(Collectors.toList()),
                batchRequest.getUpdatedBy());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getByPipelineId(
            StringValue request, StreamObserver<Message.PipelineMessage> responseObserver) {
        var p = pipelineService.findById(request.getValue());
        var message =
                Optional.ofNullable(p)
                        .map(Proto::toMessage)
                        .orElse(Message.PipelineMessage.getDefaultInstance());
        responseObserver.onNext(message);
        responseObserver.onCompleted();
    }

    @Override
    public void getByPipelineIdAndStageWithPrereq(
            Message.GetByExternalIdAndStageRequest request,
            StreamObserver<Message.PipelineMessage> responseObserver) {
        Pipeline p;
        if (request.getIncludePrereq()) {
            p = pipelineService.findByIdAndStageWithPrereq(request.getId(), request.getStage());
        } else {
            p = pipelineService.findByIdAndStage(request.getId(), request.getStage());
        }
        var message =
                Optional.ofNullable(p)
                        .map(Proto::toMessage)
                        .orElse(Message.PipelineMessage.getDefaultInstance());
        responseObserver.onNext(message);
        responseObserver.onCompleted();
    }

    @Override
    public StreamObserver<Message.PipelineTaskQueryRequest> getPipelineIdByTask(
            StreamObserver<StringValue> responseObserver) {

        return new StreamObserver<>() {
            final List<PipelineTaskQuery> queries = new ArrayList<>();

            @Override
            public void onNext(Message.PipelineTaskQueryRequest request) {
                queries.add(PipelineTaskQuery.from(request));
            }

            @Override
            public void onError(Throwable throwable) {
                log.warn(
                        "Received error from GRPC client while executing getPipelineIdByTask()."
                                + " Abort.");
            }

            @Override
            public void onCompleted() {
                var pipelineIds = pipelineService.findPipelineIdByTask(queries);
                pipelineIds.forEach(
                        id ->
                                responseObserver.onNext(
                                        StringValue.newBuilder().setValue(id).build()));

                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public void closePipeline(
            Message.ChangePipelineStateRequest request, StreamObserver<Empty> responseObserver) {
        var pipelineId = request.getPipelineId();
        var changedBy = request.hasChangedBy() ? request.getChangedBy().getValue() : null;
        var version = request.hasVersion() ? request.getVersion().getValue() : null;
        pipelineService.closePipeline(pipelineId, changedBy, version);
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void recoverPipeline(
            Message.ChangePipelineStateRequest request, StreamObserver<Empty> responseObserver) {
        var pipelineId = request.getPipelineId();
        var changedBy = request.hasChangedBy() ? request.getChangedBy().getValue() : null;
        var version = request.hasVersion() ? request.getVersion().getValue() : null;
        pipelineService.recoverPipeline(pipelineId, changedBy, version);
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void findPipelineState(
            StringValue request, StreamObserver<Message.PipelineMessage.Stage> responseObserver) {
        var state = pipelineService.findPipelineState(request.getValue());
        var stateMessage =
                state == null
                        ? Message.PipelineMessage.Stage.getDefaultInstance()
                        : state.toMessage();
        responseObserver.onNext(stateMessage);
        responseObserver.onCompleted();
    }

    @Override
    public void batchSetTaskSupervisor(
            Message.BatchSetTaskSupervisorRequest request, StreamObserver<Empty> responseObserver) {
        var requestList =
                request.getRequestsList().stream()
                        .map(SetTaskSupervisorRequest::from)
                        .collect(Collectors.toList());
        pipelineService.batchSetTaskSupervisor(requestList, request.getChangedBy());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
