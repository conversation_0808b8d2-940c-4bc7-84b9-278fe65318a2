package com.bees360.pipeline;

import com.bees360.assignment.AssignRuleManagerGrpc;
import com.bees360.assignment.Message;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.grpc.ListStreamObserver;
import com.bees360.pipeline.assign.AssignRule;
import com.bees360.pipeline.assign.AssignRuleManager;
import com.bees360.util.Iterables;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.context.annotation.Import;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@GrpcService
@Log4j2
@Import({ExceptionTranslateInterceptor.class})
public class GrpcAssignRuleService extends AssignRuleManagerGrpc.AssignRuleManagerImplBase {
    public GrpcAssignRuleService(AssignRuleManager assignRuleManager) {
        this.assignRuleManager = assignRuleManager;

        log.info("Created '{}'(assignRuleManager={})", this, this.assignRuleManager);
    }

    private final AssignRuleManager assignRuleManager;

    @Override
    public void create(
            Message.AssignRule request, StreamObserver<Message.AssignRule> responseObserver) {
        var assignRule = assignRuleManager.create(AssignRule.from(request));
        responseObserver.onNext(
                Optional.ofNullable(assignRule)
                        .map(AssignRule::toMessage)
                        .orElse(Message.AssignRule.getDefaultInstance()));
        responseObserver.onCompleted();
    }

    @Override
    public void delete(StringValue request, StreamObserver<Empty> responseObserver) {
        assignRuleManager.deleteById(request.getValue());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public StreamObserver<StringValue> findByKey(
            StreamObserver<Message.AssignRule> responseObserver) {
        return new ListStreamObserver<>(
                keys -> {
                    var strs =
                            Iterables.toStream(keys)
                                    .map(StringValue::getValue)
                                    .collect(Collectors.toList());
                    var result = assignRuleManager.findByKey(strs);
                    Iterables.toStream(result)
                            .filter(Objects::nonNull)
                            .forEach(m -> responseObserver.onNext(m.toMessage()));
                    responseObserver.onCompleted();
                });
    }

    @Override
    public void getAllAssignRule(
            Empty request, StreamObserver<Message.AssignRule> responseObserver) {
        var assignRule = assignRuleManager.listAllAssignRule();
        assignRule.forEach(a -> responseObserver.onNext(a.toMessage()));
        responseObserver.onCompleted();
    }
}
