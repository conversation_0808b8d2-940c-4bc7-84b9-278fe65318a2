package com.bees360.pipeline;

import com.bees360.util.Iterables;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import java.util.Optional;

@GrpcService
@Log4j2
public class GrpcPipelineDefService extends PipelineDefServiceGrpc.PipelineDefServiceImplBase {
    public GrpcPipelineDefService(PipelineDefService grpcPipelineDefService) {
        this.pipelineDefService = grpcPipelineDefService;

        log.info("Created '{}'(pipelineDefService={})", this, this.pipelineDefService);
    }

    private final PipelineDefService pipelineDefService;

    @Override
    public void getByKey(
            StringValue request, StreamObserver<Message.PipelineDefMessage> responseObserver) {
        var def = pipelineDefService.findByKey(request.getValue());
        responseObserver.onNext(
                Optional.ofNullable(def)
                        .map(PipelineDef::toMessage)
                        .orElse(Message.PipelineDefMessage.getDefaultInstance()));
        responseObserver.onCompleted();
    }

    @Override
    public void getByKeyAndStageWithPrereq(
            Message.GetByKeyAndStageRequest request,
            StreamObserver<Message.PipelineDefMessage> responseObserver) {
        PipelineDef def;
        if (request.getIncludePrereq()) {
            def =
                    pipelineDefService.findByKeyAndStageWithPrereq(
                            request.getKey(), request.getStage());
        } else {
            def = pipelineDefService.findByKeyAndStage(request.getKey(), request.getStage());
        }
        responseObserver.onNext(
                Optional.ofNullable(def)
                        .map(PipelineDef::toMessage)
                        .orElse(Message.PipelineDefMessage.getDefaultInstance()));
        responseObserver.onCompleted();
    }

    @Override
    public void findAllTaskDefByType(
            StringValue request, StreamObserver<Message.PipelineTaskDefList> responseObserver) {
        var taskDefList =
                pipelineDefService.findAllTaskDef(
                        Message.PipelineDefMessage.Type.valueOf(request.getValue()));
        var builder = Message.PipelineTaskDefList.newBuilder();
        if (taskDefList != null) {
            builder.addAllTask(Iterables.transform(taskDefList, PipelineTaskDef::toMessage));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void savePipelineTaskDef(
            Message.PipelineDefMessage.Task request,
            StreamObserver<Message.PipelineDefMessage.Task> responseObserver) {
        var p = pipelineDefService.savePipelineTaskDef(PipelineTaskDef.from(request));
        responseObserver.onNext(p.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public void create(
            Message.SavePipelineDefRequest request,
            StreamObserver<Message.PipelineDefMessage> responseObserver) {
        var p = pipelineDefService.create(SavePipelineDefRequest.from(request));
        responseObserver.onNext(p.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public void update(
            Message.SavePipelineDefRequest request,
            StreamObserver<Message.PipelineDefMessage> responseObserver) {
        var p = pipelineDefService.update(SavePipelineDefRequest.from(request));
        responseObserver.onNext(p.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public void findAndCountByQuery(
            Message.PipelineDefQueryRequest request,
            StreamObserver<Message.PipelineDefList> responseObserver) {
        var p = pipelineDefService.findAndCountByQuery(PipelineDefQuery.from(request));
        responseObserver.onNext(p.toMessage());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteByKey(StringValue request, StreamObserver<Empty> responseObserver) {
        pipelineDefService.deleteByKey(request.getValue());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
