package com.bees360.pipeline;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.pipeline.config.SetPipelineStateOnStageStateChangedConfig;
import com.bees360.pipeline.config.SystemUserConfig;
import com.bees360.pipeline.util.PipelineResetUpdaterByChannelService;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;

@Import({
    SystemUserConfig.class,
    // Repository
    JooqPipelineConfig.class,

    // Grpc Service
    ExceptionTranslateInterceptor.class,
    GrpcPipelineService.class,
    GrpcPipelineDefService.class,
    GrpcClientConfig.class,

    // Listener
    RabbitApiConfig.class,
    RabbitEventDispatcher.class,
    SetPipelineStateOnStageStateChangedConfig.class,
})
@Log4j2
@EnableEncryptableProperties
@ApplicationAutoConfig
@EnableTransactionManagement
public class PipelineApp {
    @Configuration
    static class Config {

        @Bean(name = {"channelUpdaterProvider"})
        Function<Message.TaskAssignChannelEnum, String> channelUpdaterProvider(
                @Qualifier(value = "systemUserSupplier") Supplier<String> systemUserSupplier) {
            return channel -> {
                if (Objects.equals(channel, Message.TaskAssignChannelEnum.AUTO_ASSIGN_MANUAL)) {
                    return systemUserSupplier.get();
                }
                return null;
            };
        }

        @Bean
        @ConditionalOnMissingBean(name = "grpcPipelineService")
        public PipelineService grpcPipelineService(
                PipelineService jooqPipelineService,
                @Qualifier(value = "channelUpdaterProvider")
                        Function<Message.TaskAssignChannelEnum, String> channelUpdaterProvider) {
            return new PipelineResetUpdaterByChannelService(
                    jooqPipelineService, channelUpdaterProvider);
        }

        @Bean
        @ConditionalOnMissingBean(name = "grpcPipelineDefService")
        public PipelineDefService grpcPipelineDefService(
                JooqPipelineDefService jooqPipelineDefService) {
            return jooqPipelineDefService;
        }
    }

    public static void main(String[] args) {
        ExitableSpringApplication.run(PipelineApp.class, args);
    }
}
