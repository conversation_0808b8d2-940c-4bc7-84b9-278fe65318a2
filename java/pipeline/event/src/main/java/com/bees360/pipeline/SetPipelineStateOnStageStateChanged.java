package com.bees360.pipeline;

import com.bees360.event.registry.PipelineStageChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.ToString;
import lombok.extern.log4j.Log4j2;

import java.util.Objects;

@ToString
@Log4j2
public class SetPipelineStateOnStageStateChanged
        extends AbstractNamedEventListener<PipelineStageChanged> {
    private final PipelineManager pipelineManager;

    public SetPipelineStateOnStageStateChanged(PipelineManager pipelineManager) {
        this.pipelineManager = pipelineManager;

        log.info("Created '{}'", this);
    }

    @Override
    public void handle(PipelineStageChanged event) {
        var newState = event.getState();
        var oldState = event.getOldState();
        if (oldState == null || !Objects.equals(oldState.getStatus(), newState.getStatus())) {
            var stage = event.getStage();
            var pipelineId = event.getPipelineId();
            pipelineManager.calAndSetPipelineStatus(pipelineId);
            log.info(
                    "Successfully set pipeline '{}' status on stage(number={},status={}) happened.",
                    pipelineId,
                    stage,
                    newState.getStatus());
        }
    }
}
