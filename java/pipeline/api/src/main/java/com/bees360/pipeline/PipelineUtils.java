package com.bees360.pipeline;

import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
public class PipelineUtils {
    private final PipelineService pipelineService;
    private final PipelineDefService pipelineDefService;
    // check (or not check) pipeline definition valid when change pipeline.
    private final Boolean checkPipelineDefValid;

    public PipelineUtils(
            PipelineService pipelineService,
            PipelineDefService pipelineDefService,
            @Nullable Boolean checkPipelineDefValid) {
        this.pipelineService = pipelineService;
        this.pipelineDefService = pipelineDefService;
        this.checkPipelineDefValid = Optional.ofNullable(checkPipelineDefValid).orElse(false);
        log.info(
                "Created '{}(pipelineService={},pipelineDefService={}, checkPipelineDefValid={})'",
                this,
                this.pipelineService,
                this.pipelineDefService,
                this.checkPipelineDefValid);
    }

    /**
     * Add a task to the running pipeline.
     *
     * <p>If the pipeline is not found or the task is already in the pipeline, it will not execute
     * the add logic and return false. The task status will be calculated correctly after it is
     * added to the pipeline and so does other tasks in the pipeline.
     *
     * @param pipelineId pipeline id
     * @param taskDef task definition
     * @param nextTaskDefKey the task definition key in the pipeline that rely on the task specified
     *     by {@code taskDef}
     */
    public void addTask2Pipeline(
            String pipelineId,
            PipelineTaskDef taskDef,
            List<String> nextTaskDefKey,
            String versionSuffix) {
        var pipelineDefKey = pipelineService.findPipelineDefKey(pipelineId);
        if (pipelineDefKey == null) {
            return;
        }
        pipelineDefService.savePipelineTaskDef(taskDef);
        var taskDefKey = taskDef.getKey();
        var pipelineDef = pipelineDefService.getByKey(pipelineDefKey);
        Map<String, PipelineDefTaskRelation> taskRelationMap =
                Iterables.toStream(pipelineDef.getTask())
                        .map(x -> PipelineDefTaskRelation.from(x.getKey(), x.getPrereqTaskDefKey()))
                        .collect(
                                Collectors.toMap(
                                        PipelineDefTaskRelation::getKey, Function.identity()));
        if (taskRelationMap.get(taskDefKey) != null) {
            log.info(
                    "Stop add task '{}' to pipeline '{}' definition '{}' because it is already"
                            + " existed.",
                    taskDefKey,
                    pipelineId,
                    pipelineDefKey);
            return;
        }

        for (String next : nextTaskDefKey) {
            var nextTaskDef = taskRelationMap.get(next);
            if (nextTaskDef != null) {
                nextTaskDef =
                        PipelineDefTaskRelation.from(
                                nextTaskDef.toMessage().toBuilder()
                                        .addPrereqTaskDef(taskDefKey)
                                        .build());
                taskRelationMap.put(next, nextTaskDef);
            }
        }

        var taskRelation =
                PipelineDefTaskRelation.from(taskDef.getKey(), taskDef.getPrereqTaskDefKey());
        taskRelationMap.put(taskDefKey, taskRelation);
        var copiedKey = pipelineDefKey + versionSuffix;
        if (pipelineDefService.findByKey(copiedKey) == null) {
            var copiedPipelineDef =
                    SavePipelineDefRequest.from(
                            Message.SavePipelineDefRequest.newBuilder()
                                    .setKey(copiedKey)
                                    .setName(pipelineDef.getName())
                                    .addAllTask(
                                            Iterables.transform(
                                                    taskRelationMap.values(),
                                                    PipelineDefTaskRelation::toMessage))
                                    .build());
            pipelineDefService.create(copiedPipelineDef);
        } else if (checkPipelineDefValid) {
            var newPipelineDef = pipelineDefService.getByKey(copiedKey);
            var taskDefMap =
                    Iterables.toStream(newPipelineDef.getTask())
                            .collect(
                                    Collectors.toMap(PipelineTaskDef::getKey, Function.identity()));
            Preconditions.checkState(
                    taskDefMap.containsKey(taskDefKey),
                    "The task def '%s' should existed in pipeline definition '%s'.",
                    taskDefKey,
                    pipelineDefKey);
        }

        pipelineService.changePipelineDef(pipelineId, copiedKey);
        log.info(
                "Successfully changed pipeline '{}' definition from '{}' to '{}'",
                pipelineId,
                pipelineDefKey,
                copiedKey);
    }

    private boolean checkNotExisted(String pipelineDefKey, String pipelineId, String taskDefKey) {
        var pipelineDef = pipelineDefService.getByKey(pipelineDefKey);
        Map<String, PipelineTaskDef> taskDefMap =
                Iterables.toStream(pipelineDef.getTask())
                        .collect(Collectors.toMap(PipelineTaskDef::getKey, Function.identity()));
        if (taskDefMap.get(taskDefKey) == null) {
            log.info(
                    "Stop add task '{}' to pipeline '{}' definition '{}' because it is already"
                            + " removed.",
                    taskDefKey,
                    pipelineId,
                    pipelineDefKey);
            return true;
        }
        return false;
    }

    public void removeTaskFromPipeline(String pipelineId, String taskDefKey, String versionSuffix) {
        var pipelineDefKey = pipelineService.findPipelineDefKey(pipelineId);
        if (pipelineDefKey == null) {
            return;
        }

        if (checkNotExisted(pipelineDefKey, pipelineId, taskDefKey)) {
            return;
        }

        var copiedKey = pipelineDefKey + versionSuffix;
        var pipelineDef = pipelineDefService.findByKey(copiedKey);
        if (pipelineDef == null) {
            pipelineDef = pipelineDefService.getByKey(pipelineDefKey);
            Map<String, PipelineTaskDef> taskDefMap =
                    Iterables.toStream(pipelineDef.getTask())
                            .collect(
                                    Collectors.toMap(PipelineTaskDef::getKey, Function.identity()));
            var pipelineTask = taskDefMap.get(taskDefKey);
            var prereq = pipelineTask.getPrereqTaskDefKey();
            var nextTasks =
                    taskDefMap.values().stream()
                            .filter(
                                    task ->
                                            Iterables.toSet(task.getPrereqTaskDefKey())
                                                    .contains(taskDefKey))
                            .collect(Collectors.toList());
            taskDefMap.remove(taskDefKey);
            for (PipelineTaskDef nextTask : nextTasks) {
                var prereqSet =
                        Stream.concat(
                                        Iterables.toStream(prereq),
                                        Iterables.toStream(nextTask.getPrereqTaskDefKey()))
                                .map(k -> (String) k)
                                .collect(Collectors.toSet());
                prereqSet.remove(taskDefKey);
                var builder = nextTask.toMessage().toBuilder().clearPrereqTaskDef();
                taskDefMap.put(
                        nextTask.getKey(),
                        PipelineTaskDef.from(builder.addAllPrereqTaskDef(prereqSet).build()));
            }
            var builder = Message.SavePipelineDefRequest.newBuilder();
            builder.setKey(copiedKey);
            builder.setName(pipelineDef.getName());
            builder.addAllTask(
                    Iterables.transform(
                            taskDefMap.values(),
                            x ->
                                    PipelineDefTaskRelation.from(
                                                    x.getKey(), x.getPrereqTaskDefKey())
                                            .toMessage()));
            var copiedPipelineDef = SavePipelineDefRequest.from(builder.build());
            pipelineDefService.create(copiedPipelineDef);
        } else if (checkPipelineDefValid) {
            Map<String, PipelineTaskDef> taskDefMap =
                    Iterables.toStream(pipelineDef.getTask())
                            .collect(
                                    Collectors.toMap(PipelineTaskDef::getKey, Function.identity()));
            Preconditions.checkState(
                    !taskDefMap.containsKey(taskDefKey),
                    "The task def '%s' should not existed in pipeline definition '%s'.",
                    taskDefKey,
                    pipelineDefKey);
        }

        pipelineService.changePipelineDef(pipelineId, copiedKey);
        log.info(
                "Successfully changed pipeline '{}' definition from '{}' to '{}'",
                pipelineId,
                pipelineDefKey,
                copiedKey);
    }
}
