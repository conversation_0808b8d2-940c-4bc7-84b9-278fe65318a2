package com.bees360.project.listener;

import com.bees360.api.InvalidArgumentException;
import com.bees360.event.registry.PilotCallRecordAddedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Map;
import java.util.function.BiPredicate;

@Log4j2
public class SetPipelineTaskStatusOnCallRecord
        extends AbstractNamedEventListener<PilotCallRecordAddedEvent> {
    private final Map<BiPredicate<String, String>, Record> recordMap;
    private final PipelineService pipelineService;

    @Data
    @AllArgsConstructor
    @Builder
    public static class Record {
        private final String taskDefKey;
        private final Message.PipelineStatus status;
    }

    public SetPipelineTaskStatusOnCallRecord(
            Map<BiPredicate<String, String>, Record> recordMap, PipelineService pipelineService) {
        this.recordMap = recordMap;
        this.pipelineService = pipelineService;

        log.info(
                "Created '{}(pipelineService={}, recordMap={})'",
                this,
                this.pipelineService,
                this.recordMap);
    }

    @Override
    public void handle(PilotCallRecordAddedEvent event) throws IOException {
        var content = event.getContent();
        var pipelineId = event.getProjectId();
        recordMap.forEach(
                (predicate, record) -> {
                    if (predicate.test(pipelineId, content)) {
                        setPipelineTaskStatus(
                                pipelineId, record.getTaskDefKey(), record.getStatus(), content);
                    }
                });
    }

    private void setPipelineTaskStatus(
            String pipelineId, String taskDefKey, Message.PipelineStatus status, String content) {
        try {
            pipelineService.setTaskStatus(pipelineId, taskDefKey, status, content);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn(
                    "Failed to set pipeline '{}' task '{} to '{}'",
                    pipelineId,
                    taskDefKey,
                    status,
                    e);
        }
    }
}
