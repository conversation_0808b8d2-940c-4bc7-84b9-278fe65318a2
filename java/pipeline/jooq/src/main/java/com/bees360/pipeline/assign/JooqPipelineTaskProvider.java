package com.bees360.pipeline.assign;

import static com.bees360.jooq.persistent.pipeline.Tables.ADDRESS;
import static com.bees360.jooq.persistent.pipeline.Tables.CONTRACT;
import static com.bees360.jooq.persistent.pipeline.Tables.CUSTOMER;
import static com.bees360.jooq.persistent.pipeline.Tables.PIPELINE;
import static com.bees360.jooq.persistent.pipeline.Tables.PIPELINE_TASK;
import static com.bees360.jooq.persistent.pipeline.Tables.PIPELINE_TASK_DEF;
import static com.bees360.jooq.persistent.pipeline.Tables.POLICY;
import static com.bees360.jooq.persistent.pipeline.Tables.PROJECT;
import static com.bees360.jooq.persistent.pipeline.Tables.PROJECT_CLAIM;
import static com.bees360.jooq.persistent.pipeline.Tables.PROJECT_DAYS_OLD;
import static com.bees360.jooq.persistent.pipeline.Tables.PROJECT_INSPECTION;
import static com.bees360.jooq.persistent.pipeline.Tables.PROJECT_STATE;
import static com.bees360.jooq.persistent.pipeline.Tables.PROJECT_UNDERWRITING;
import static com.bees360.pipeline.Message.PipelineStatus.DONE_VALUE;

import static org.jooq.impl.DSL.arrayAgg;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.select;
import static org.jooq.impl.DSL.sum;

import com.bees360.pipeline.PipelineService;

import org.apache.commons.lang3.StringUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Record3;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class JooqPipelineTaskProvider implements PipelineTaskProvider {
    private final DSLContext dsl;

    static final String DIFFICULTY = "difficulty";
    static final String TASK_KEY = "task_key";
    private static final String DEFAULT_TRUE_FILTER = "\"true\"";
    static final String PIPELINE_TASK_STATUS = "pipeline_task.status";

    public JooqPipelineTaskProvider(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    public Iterable<PipelineAssignedTask> getTaskKeyByQuery(PipelineTaskFilter filter) {
        var pipelineFilter =
                StringUtils.defaultString(filter.getPipelineFilter(), DEFAULT_TRUE_FILTER);
        var taskFilter = StringUtils.defaultString(filter.getTaskFilter(), DEFAULT_TRUE_FILTER);
        Condition taskCondition =
                PIPELINE_TASK
                        .PIPELINE_ID
                        .eq(PIPELINE.ID)
                        .and(PIPELINE_TASK.DELETED.eq(false))
                        .and(taskFilter);
        if (!taskFilter.contains(PIPELINE_TASK_STATUS)) {
            taskCondition = taskCondition.and(PIPELINE_TASK.STATUS.ne(DONE_VALUE));
        }
        var difficulty =
                field(
                                select(sum(PIPELINE_TASK_DEF.DIFFICULTY))
                                        .from(PIPELINE_TASK_DEF)
                                        .join(PIPELINE_TASK)
                                        .on(PIPELINE_TASK.TASK_DEF_ID.eq(PIPELINE_TASK_DEF.ID))
                                        .where(taskCondition))
                        .as(DIFFICULTY);
        var taskKey =
                field(
                                select(arrayAgg(PIPELINE_TASK_DEF.KEY))
                                        .from(PIPELINE_TASK_DEF)
                                        .join(PIPELINE_TASK)
                                        .on(PIPELINE_TASK_DEF.ID.eq(PIPELINE_TASK.TASK_DEF_ID))
                                        .where(taskCondition))
                        .as(TASK_KEY);
        return dsl.select(PIPELINE.EXTERNAL_ID, taskKey, difficulty)
                .from(PIPELINE)
                .where(
                        PIPELINE.EXTERNAL_ID.in(
                                dsl.select(PIPELINE.EXTERNAL_ID)
                                        .from(PIPELINE)
                                        .innerJoin(PIPELINE_TASK)
                                        .on(PIPELINE.ID.eq(PIPELINE_TASK.PIPELINE_ID))
                                        .leftOuterJoin(PROJECT)
                                        .on(PROJECT.ID.eq(PIPELINE.EXTERNAL_ID))
                                        .leftOuterJoin(CONTRACT)
                                        .on(CONTRACT.ID.eq(PROJECT.CONTRACT_ID))
                                        .leftOuterJoin(PROJECT_CLAIM)
                                        .on(PROJECT_CLAIM.PROJECT_ID.eq(PROJECT.ID))
                                        .leftOuterJoin(PROJECT_UNDERWRITING)
                                        .on(PROJECT_UNDERWRITING.PROJECT_ID.eq(PROJECT.ID))
                                        .leftOuterJoin(POLICY)
                                        .on(PROJECT.POLICY_ID.eq(POLICY.ID))
                                        .leftOuterJoin(CUSTOMER)
                                        .on(CUSTOMER.ID.eq(CONTRACT.INSURED_BY))
                                        .leftOuterJoin(PROJECT_INSPECTION)
                                        .on(PROJECT_INSPECTION.PROJECT_ID.eq(PROJECT.ID))
                                        .leftOuterJoin(ADDRESS)
                                        .on(POLICY.ADDRESS_ID.eq(ADDRESS.ID))
                                        .leftOuterJoin(PROJECT_DAYS_OLD)
                                        .on(PROJECT.ID.eq(PROJECT_DAYS_OLD.PROJECT_ID))
                                        .leftOuterJoin(PROJECT_STATE)
                                        .on(PROJECT.ID.eq(PROJECT_STATE.PROJECT_ID))
                                        .where(pipelineFilter)
                                        .and(PIPELINE.STAGE.ne(PipelineService.CLOSED_STAGE))
                                        .and(
                                                PROJECT.IS_TEST_CASE
                                                        .isNull()
                                                        .or(PROJECT.IS_TEST_CASE.ne(true)))
                                        .groupBy(PIPELINE.EXTERNAL_ID)
                                        .fetch(PIPELINE.EXTERNAL_ID)))
                .groupBy(PIPELINE.EXTERNAL_ID, PIPELINE.ID)
                .fetchStream()
                .map(this::mapping)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private PipelineAssignedTask mapping(Record3<String, String[], BigDecimal> r) {
        // get target pipeline but has no eligible tasks
        if (r.value2() == null) return null;
        var difficulty = Optional.ofNullable(r.value3()).map(BigDecimal::intValue).orElse(0);
        var pipelineId = r.get(PIPELINE.EXTERNAL_ID);
        var taskKeys = List.of(r.value2());
        return new PipelineAssignedTask(pipelineId, taskKeys, difficulty);
    }
}
