package com.bees360.pipeline.assign;

import com.bees360.user.GroupProvider;
import com.bees360.user.User;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import lombok.Data;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Configuration
@Log4j2
public class AssigneeCalendarConfig {

    @Configuration
    @ConfigurationProperties(prefix = "pipeline.app")
    @EnableConfigurationProperties
    @Data
    static class Property {
        @Data
        static class Assignee {
            private String groupId;
            private String userId;
            private List<Calendar> oddMonthCalendar;
            private List<Calendar> evenMonthCalendar;
            private String timezone;
        }

        List<Assignee> assignees = new ArrayList<>();
        List<Calendar> defaultCalendar =
                new ArrayList<>(
                        Arrays.asList(
                                Calendar.MON,
                                Calendar.TUE,
                                Calendar.WED,
                                Calendar.THU,
                                Calendar.FRI));
    }

    enum Calendar {
        MON(1),
        TUE(2),
        WED(3),
        THU(4),
        FRI(5),
        SAT(6),
        SUN(7),
        ;

        @Getter private final int dayOfWeek;

        Calendar(int dayOfWeek) {
            this.dayOfWeek = dayOfWeek;
        }
    }

    @Bean
    public UserAvailableScheduleProvider assigneeWorkTimeProvider(
            Property property, GroupProvider groupProvider) {
        log.info("Create user available schedule provider with property :{}", property);

        final Integer odd = 1;
        final Integer even = 0;
        final String defaultTimezone = "America/Chicago";
        var assigneeCalendar = property.getAssignees();
        var assigneeCalendarMap =
                assigneeCalendar.stream()
                        .collect(
                                Collectors.toMap(
                                        assignee ->
                                                Optional.ofNullable(assignee.getUserId())
                                                        .orElse(assignee.getGroupId()),
                                        list ->
                                                Map.of(
                                                        odd,
                                                        list.getOddMonthCalendar().stream()
                                                                .map(Calendar::getDayOfWeek)
                                                                .collect(Collectors.toList()),
                                                        even,
                                                        list.getEvenMonthCalendar().stream()
                                                                .map(Calendar::getDayOfWeek)
                                                                .collect(Collectors.toList()))));
        var assigneeTimezoneMap =
                assigneeCalendar.stream()
                        .collect(
                                Collectors.toMap(
                                        assignee ->
                                                Optional.ofNullable(assignee.getUserId())
                                                        .orElse(assignee.getGroupId()),
                                        assignee ->
                                                Optional.ofNullable(assignee.getTimezone())
                                                        .orElse(defaultTimezone)));
        var dutyGroups =
                assigneeCalendar.stream()
                        .map(Property.Assignee::getGroupId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return new UserAvailableScheduleProvider() {

            // working from MON to FRI as default calendar
            private final List<Integer> defaultCalendar =
                    property.getDefaultCalendar().stream()
                            .map(Calendar::getDayOfWeek)
                            .collect(Collectors.toList());

            private List<Integer> getUserCalendar(String userId) {
                // find calendar by userId, if null then find it by groupId
                var groupCalendar = assigneeCalendarMap.get(userId);
                if (groupCalendar == null) {
                    // find the group contains the user
                    var groupId =
                            dutyGroups.stream()
                                    .filter(
                                            g -> {
                                                var group = groupProvider.findGroupById(g);
                                                if (group != null) {
                                                    return Iterables.toStream(group.getAllUser())
                                                            .map(User::getId)
                                                            .collect(Collectors.toSet())
                                                            .contains(userId);
                                                }
                                                return false;
                                            })
                                    .findFirst()
                                    .orElse("");
                    groupCalendar = assigneeCalendarMap.get(groupId);
                    if (groupCalendar == null) {
                        return defaultCalendar;
                    }
                }
                // check the parity of current month
                var month =
                        (java.util.Calendar.getInstance().get(java.util.Calendar.MONTH) + 1) % 2;
                List<Integer> defaultCalendar = Collections.emptyList();
                return groupCalendar.getOrDefault(month, defaultCalendar);
            }

            private ZoneId getUserTimeZone(String userId) {
                var groupZone = assigneeTimezoneMap.get(userId);
                if (groupZone == null) {
                    // find the group contains the user
                    var groupId =
                            dutyGroups.stream()
                                    .filter(
                                            g -> {
                                                var group = groupProvider.findGroupById(g);
                                                if (group != null) {
                                                    return Iterables.toStream(group.getAllUser())
                                                            .map(User::getId)
                                                            .collect(Collectors.toSet())
                                                            .contains(userId);
                                                }
                                                return false;
                                            })
                                    .findFirst()
                                    .orElse("");
                    groupZone = assigneeTimezoneMap.get(groupId);
                    if (groupZone == null) {
                        return ZoneId.of(defaultTimezone);
                    }
                }
                return ZoneId.of(groupZone);
            }

            /**
             * get one assignee's available workTime during the assign period
             *
             * @param userId userId
             * @param endTime end time for this assign period
             * @return amount of available work days
             */
            public Integer getTotalAvailableDay(String userId, Instant endTime) {
                var calendarList = getUserCalendar(userId);
                if (calendarList.size() == 0) {
                    return 0;
                }

                var zoneId = getUserTimeZone(userId);
                var endTimeWithTimeZone = endTime.atZone(zoneId).toLocalDate();
                Preconditions.checkArgument(endTimeWithTimeZone != null);
                var date = ZonedDateTime.now(zoneId);
                int workDay = 0;
                while (!date.toLocalDate().isEqual(endTimeWithTimeZone)) {
                    if (calendarList.contains(date.getDayOfWeek().getValue())) {
                        workDay++;
                    }
                    date = date.plusDays(1);
                }
                return workDay;
            }
        };
    }
}
