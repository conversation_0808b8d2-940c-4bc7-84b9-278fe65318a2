package com.bees360.pipeline;

import static com.bees360.jooq.persistent.pipeline.Tables.PIPELINE_DEF;
import static com.bees360.jooq.persistent.pipeline.Tables.PIPELINE_DEF_TASK_DEF;
import static com.bees360.jooq.persistent.pipeline.Tables.PIPELINE_TASK_DEF_PREREQ;
import static com.bees360.jooq.persistent.pipeline.tables.PipelineTaskDef.PIPELINE_TASK_DEF;

import static org.jooq.impl.DSL.arrayAgg;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.select;

import com.bees360.jooq.persistent.pipeline.Tables;
import com.bees360.jooq.persistent.pipeline.tables.records.PipelineTaskDefPrereqRecord;
import com.bees360.jooq.persistent.pipeline.tables.records.PipelineTaskDefRecord;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;

import lombok.Builder;
import lombok.Data;

import org.apache.logging.log4j.util.Strings;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.InsertOnDuplicateSetMoreStep;
import org.jooq.Record;
import org.jooq.Record7;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class JooqTaskDefService implements TaskDefService {
    private final DSLContext dsl;
    private final com.bees360.jooq.persistent.pipeline.tables.PipelineTaskDef taskDefLink =
            Tables.PIPELINE_TASK_DEF.as("taskDefLink");
    private static final Integer MAX_KEY_LENGTH = 32;
    private final boolean removeIndirectPrerequisite;

    public JooqTaskDefService(
            DSLContext dsl, @Autowired(required = false) Boolean removeIndirectPrerequisite) {
        this.dsl = dsl;
        this.removeIndirectPrerequisite =
                Optional.ofNullable(removeIndirectPrerequisite).orElse(false);
    }

    @Override
    public void updateAll(String pipelineDefId, Iterable<? extends PipelineDefTaskRelation> tasks) {
        Set<? extends PipelineDefTaskRelation> pipelineTaskSet =
                Iterables.toStream(tasks).collect(Collectors.toSet());
        Map<String, Set<String>> taskPreMap = new HashMap<>();
        pipelineTaskSet.forEach(task -> addTaskToMap(task, taskPreMap));
        checkValid(pipelineTaskSet, taskPreMap);
        // delete from taskDef by pipelineDefId
        deleteTaskDefByPipelineDefId(pipelineDefId);
        deleteTaskDefPrereq(pipelineDefId);
        // save task to pipeline def
        var key2IdMap = saveTaskToPipelineDef(pipelineDefId, pipelineTaskSet);
        // save task prerequisite
        if (removeIndirectPrerequisite) {
            removeIndirectPrerequisite(taskPreMap);
        }
        savePipelineTaskPrereq(pipelineDefId, taskPreMap, key2IdMap);
    }

    @Override
    public void addAll(String pipelineDefId, Iterable<? extends PipelineDefTaskRelation> tasks) {
        Set<? extends PipelineDefTaskRelation> pipelineTaskSet =
                Iterables.toStream(tasks).collect(Collectors.toSet());
        Map<String, Set<String>> key2Prerequisite = new HashMap<>();
        pipelineTaskSet.forEach(task -> addTaskToMap(task, key2Prerequisite));
        checkValid(pipelineTaskSet, key2Prerequisite);

        // save task to pipeline def
        var key2IdMap = saveTaskToPipelineDef(pipelineDefId, pipelineTaskSet);
        // save task prerequisite
        if (removeIndirectPrerequisite) {
            removeIndirectPrerequisite(key2Prerequisite);
        }
        savePipelineTaskPrereq(pipelineDefId, key2Prerequisite, key2IdMap);
    }

    private void deleteTaskDefPrereq(String pipelineDefId) {
        dsl.update(PIPELINE_TASK_DEF_PREREQ)
                .set(PIPELINE_TASK_DEF_PREREQ.DELETED, true)
                .where(PIPELINE_TASK_DEF_PREREQ.PIPELINE_DEF_ID.eq(pipelineDefId))
                .execute();
    }

    private void deleteTaskDefByPipelineDefId(String pipelineDefId) {
        dsl.update(PIPELINE_DEF_TASK_DEF)
                .set(PIPELINE_DEF_TASK_DEF.DELETED, true)
                .set(PIPELINE_DEF_TASK_DEF.UPDATED_AT, Timestamp.from(Instant.now()))
                .where(PIPELINE_DEF_TASK_DEF.PIPELINE_DEF_ID.eq(pipelineDefId))
                .execute();
    }

    /**
     * Remove indirect task prerequisites in pipeline.
     *
     * <p>For example, we have a pipeline with task defs: {name=A, prerequisites=[]}, {name=B,
     * prerequisites=[A]}, {name=C, prerequisites=[A,B]}.
     *
     * <p>Pipeline is A -> B -> C
     *
     * <p>The indirect prerequisite of C is A, if we remove it, so we can get simplified pipeline
     * with task defs: {name=A, prerequisites=[]}, {name=B, prerequisites=[A]}, {name=C,
     * prerequisites=[B]}
     *
     * <p>Which called removed indirect prerequisite pipeline def.
     *
     * @param key2Prerequisite task key to prerequisites map that should be removed indirect
     *     prerequisite.
     */
    private void removeIndirectPrerequisite(Map<String, Set<String>> key2Prerequisite) {
        // Build tasks as tree, every task is a node in the tree.
        // Via the level tree, we can remove the indirect prerequisites which cross the tree level.
        // The root nodes in the tree should be the nodes that have no prerequisites.
        var preLevelNodes =
                key2Prerequisite.entrySet().stream()
                        .filter(en -> Iterables.toCollection(en.getValue()).isEmpty())
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toSet());
        var visited = new HashSet<>(preLevelNodes);
        while (!preLevelNodes.isEmpty()) {
            Set<String> finalPreLevelTaskKeys = preLevelNodes;
            preLevelNodes =
                    key2Prerequisite.entrySet().stream()
                            .filter(en -> !visited.contains(en.getKey()))
                            // locate current level nodes by task prerequisites are all in pre level
                            .filter(
                                    en -> {
                                        var prerequisites = en.getValue();
                                        var diff =
                                                Sets.difference(
                                                        prerequisites, finalPreLevelTaskKeys);
                                        if (!diff.isEmpty()) {
                                            // the node is not this level, reset prerequisite.
                                            if (diff.size() != prerequisites.size()) {
                                                key2Prerequisite.put(
                                                        en.getKey(), new HashSet<>(diff));
                                            }
                                            return false;
                                        }
                                        // task prerequisite is all in the pre level
                                        return true;
                                    })
                            .map(Map.Entry::getKey)
                            .collect(Collectors.toSet());
            visited.addAll(preLevelNodes);
        }
    }

    private void checkValid(
            Set<? extends PipelineDefTaskRelation> pipelineTaskSet,
            Map<String, Set<String>> taskPrereqMap) {
        // check if the prerequisite task in the taskPrereqMap
        pipelineTaskSet.forEach(
                def ->
                        Iterables.toStream(def.getPrereqTaskDefKey())
                                .forEach(
                                        pre ->
                                                Preconditions.checkArgument(
                                                        taskPrereqMap.containsKey(pre),
                                                        "The prerequisite task '%s'"
                                                                + " definition is not found.",
                                                        pre)));
        // check if task rely on itself
        taskPrereqMap.forEach(
                (key, value) ->
                        Preconditions.checkArgument(
                                !value.contains(key),
                                "The task %s should not rely on itself.",
                                key));
        var startVectors =
                taskPrereqMap.entrySet().stream()
                        .filter(e -> CollectionUtils.isEmpty(e.getValue()))
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toSet());
        if (startVectors.size() == 0) {
            taskPrereqMap.keySet().forEach(s -> checkValidDAG(s, taskPrereqMap, new ArrayList<>()));
        } else {
            startVectors.forEach(s -> checkValidDAG(s, taskPrereqMap, new ArrayList<>()));
        }
    }

    private void checkValidDAG(
            String vector, Map<String, Set<String>> key2PrerequisiteMap, List<String> visitedList) {
        var succeeds = key2PrerequisiteMap.get(vector);
        Preconditions.checkArgument(
                !visitedList.contains(vector),
                String.format(
                        "The pipeline task should not circular dependency: '%s'",
                        String.join(" ->", visitedList)));
        succeeds.forEach(succeed -> checkValidDAG(vector, key2PrerequisiteMap, visitedList));
    }

    /**
     * save pipeline task to pipeline def and return def key to def id map.
     *
     * @param pipelineDefId pipeline definition id
     * @param entities entity iterable
     * @return task def key to def id map
     */
    private Map<String, String> saveTaskToPipelineDef(
            String pipelineDefId, Iterable<? extends PipelineDefTaskRelation> entities) {
        var taskDefs = Iterables.toSet(entities);
        var recordMap =
                dsl
                        .select(PIPELINE_TASK_DEF.ID, PIPELINE_TASK_DEF.KEY)
                        .from(PIPELINE_TASK_DEF)
                        .where(
                                PIPELINE_TASK_DEF.KEY.in(
                                        taskDefs.stream()
                                                .map(PipelineDefTaskRelation::getKey)
                                                .collect(Collectors.toSet())))
                        .fetch()
                        .stream();
        var map =
                recordMap.collect(
                        Collectors.toMap(
                                r -> r.get(PIPELINE_TASK_DEF.KEY),
                                r -> r.get(PIPELINE_TASK_DEF.ID)));
        saveTaskDef2PipelineDef(pipelineDefId, map.values());
        return map;
    }

    @Override
    public PipelineTaskDef save(PipelineTaskDef taskDef) {
        checkTaskDefValid(List.of(taskDef));
        dsl.insertInto(
                        PIPELINE_TASK_DEF,
                        PIPELINE_TASK_DEF.KEY,
                        PIPELINE_TASK_DEF.NAME,
                        PIPELINE_TASK_DEF.STAGE,
                        PIPELINE_TASK_DEF.DESCRIPTION,
                        PIPELINE_TASK_DEF.DIFFICULTY)
                .values(
                        taskDef.getKey(),
                        taskDef.getName(),
                        taskDef.getStage(),
                        taskDef.getDescription(),
                        taskDef.getDifficulty())
                .onConflict(PIPELINE_TASK_DEF.KEY)
                .doUpdate()
                .set(PIPELINE_TASK_DEF.NAME, taskDef.getName())
                .set(PIPELINE_TASK_DEF.DESCRIPTION, taskDef.getDescription())
                .set(PIPELINE_TASK_DEF.STAGE, taskDef.getStage())
                .set(PIPELINE_TASK_DEF.DELETED, false)
                .set(PIPELINE_TASK_DEF.DIFFICULTY, taskDef.getDifficulty())
                .execute();
        return getPipelineTaskDefByKey(taskDef.getKey());
    }

    private PipelineTaskDef getPipelineTaskDefByKey(String key) {
        return dsl.selectFrom(PIPELINE_TASK_DEF)
                .where(PIPELINE_TASK_DEF.KEY.eq(key))
                .fetchOne(this::map);
    }

    private void checkTaskDefValid(Iterable<? extends PipelineTaskDef> pipelineTaskSet) {
        // check if the task definition key
        pipelineTaskSet.forEach(
                taskDef ->
                        Preconditions.checkArgument(
                                taskDef.getKey().length() <= MAX_KEY_LENGTH,
                                "The pipeline task definition key length should be not bigger than "
                                        + MAX_KEY_LENGTH));
    }

    private void saveTaskDef2PipelineDef(String pipelineDefId, Iterable<String> taskDefId) {
        var steps =
                Iterables.toStream(taskDefId)
                        .map(
                                id -> {
                                    var step =
                                            dsl.insertInto(
                                                    PIPELINE_DEF_TASK_DEF,
                                                    PIPELINE_DEF_TASK_DEF.PIPELINE_DEF_ID,
                                                    PIPELINE_DEF_TASK_DEF.TASK_DEF_ID);
                                    return step.values(pipelineDefId, id)
                                            .onConflict(
                                                    PIPELINE_DEF_TASK_DEF.TASK_DEF_ID,
                                                    PIPELINE_DEF_TASK_DEF.PIPELINE_DEF_ID)
                                            .doUpdate()
                                            .set(PIPELINE_DEF_TASK_DEF.DELETED, false)
                                            .set(
                                                    PIPELINE_DEF_TASK_DEF.UPDATED_AT,
                                                    Timestamp.from(Instant.now()));
                                });
        dsl.batch(steps.collect(Collectors.toSet())).execute();
    }

    private void savePipelineTaskPrereq(
            String pipelineDefId,
            Map<String, Set<String>> taskDefPrereqMap,
            Map<String, String> key2IdMap) {
        var tasks = new HashSet<InsertOnDuplicateSetMoreStep<PipelineTaskDefPrereqRecord>>();
        for (var entity : taskDefPrereqMap.entrySet()) {
            var taskDefId = key2IdMap.get(entity.getKey());
            var prerequisites = entity.getValue();
            for (String prereq : prerequisites) {
                prereq = key2IdMap.get(prereq);
                var step =
                        dsl.insertInto(
                                PIPELINE_TASK_DEF_PREREQ,
                                PIPELINE_TASK_DEF_PREREQ.TASK_DEF_ID,
                                PIPELINE_TASK_DEF_PREREQ.PREREQ_TASK_DEF_ID,
                                PIPELINE_TASK_DEF_PREREQ.PIPELINE_DEF_ID);
                var t =
                        step.values(taskDefId, prereq, pipelineDefId)
                                .onConflict(
                                        PIPELINE_TASK_DEF_PREREQ.TASK_DEF_ID,
                                        PIPELINE_TASK_DEF_PREREQ.PREREQ_TASK_DEF_ID,
                                        PIPELINE_TASK_DEF_PREREQ.PIPELINE_DEF_ID)
                                .doUpdate()
                                .set(PIPELINE_TASK_DEF_PREREQ.DELETED, false)
                                .set(PIPELINE_TASK_DEF_PREREQ.PREREQ_TASK_DEF_ID, prereq)
                                .set(
                                        PIPELINE_TASK_DEF_PREREQ.UPDATED_AT,
                                        Timestamp.from(Instant.now()));
                tasks.add(t);
            }
        }
        dsl.batch(tasks).execute();
    }

    private void addTaskToMap(PipelineDefTaskRelation task, Map<String, Set<String>> taskDefMap) {
        Collection<String> prerequisites =
                Iterables.toStream(task.getPrereqTaskDefKey())
                        .filter(Strings::isNotBlank)
                        .collect(Collectors.toList());
        if (prerequisites.isEmpty()) {
            taskDefMap.computeIfAbsent(task.getKey(), (k) -> new HashSet<>());
        } else {
            prerequisites.forEach(pre -> flatPrerequisite(task, pre, taskDefMap));
        }
    }

    private void flatPrerequisite(
            PipelineDefTaskRelation task, String pre, Map<String, Set<String>> taskDefMap) {
        taskDefMap.compute(
                task.getKey(),
                (k, v) -> {
                    if (v == null) {
                        v = new HashSet<>();
                    }
                    v = v.stream().filter(Strings::isNotBlank).collect(Collectors.toSet());
                    v.add(pre);
                    return v;
                });
    }

    @Override
    public PipelineTaskDef findByPipelineDefAndTaskDef(String pipelineDefKey, String taskDefKey) {
        var pipelineDefId = findPipelineDefId(pipelineDefKey);
        if (pipelineDefId == null) {
            return null;
        }
        var innerCondition = PIPELINE_TASK_DEF_PREREQ.PIPELINE_DEF_ID.eq(pipelineDefKey);
        var outerCondition =
                PIPELINE_DEF.KEY.eq(pipelineDefKey).and(PIPELINE_TASK_DEF.KEY.eq(taskDefKey));
        return find(outerCondition, innerCondition).stream().findAny().orElse(null);
    }

    @Override
    public Iterable<? extends PipelineTaskDef> getDirectNextTask(
            String pipelineDefKey, String taskDefKey) {
        var pipelineDefId = findPipelineDefId(pipelineDefKey);
        if (pipelineDefId == null) {
            return Collections.emptyList();
        }
        var innerCondition = PIPELINE_TASK_DEF_PREREQ.PIPELINE_DEF_ID.eq(pipelineDefId);
        var outerCondition =
                PIPELINE_DEF.KEY.eq(pipelineDefKey).and(taskDefLink.KEY.eq(taskDefKey));
        return findNext(outerCondition, innerCondition);
    }

    @Override
    public Iterable<? extends PipelineTaskDef> getByPipelineDef(String defKey) {
        var pipelineDefId = findPipelineDefId(defKey);
        if (pipelineDefId == null) {
            return Collections.emptyList();
        }
        var innerCondition = PIPELINE_TASK_DEF_PREREQ.PIPELINE_DEF_ID.eq(pipelineDefId);
        var outerCondition = PIPELINE_DEF.KEY.eq(defKey);
        return find(outerCondition, innerCondition);
    }

    @Override
    public Iterable<? extends PipelineTaskDef> getByPipelineDefAndStage(String defKey, int stage) {
        var pipelineDefId = findPipelineDefId(defKey);
        if (pipelineDefId == null) {
            return Collections.emptyList();
        }
        var innerCondition = PIPELINE_TASK_DEF_PREREQ.PIPELINE_DEF_ID.eq(pipelineDefId);
        var outerCondition = PIPELINE_DEF.KEY.eq(defKey).and(PIPELINE_TASK_DEF.STAGE.eq(stage));
        return find(outerCondition, innerCondition);
    }

    private String findPipelineDefId(String pipelineDefKey) {
        return dsl.select(PIPELINE_DEF.ID)
                .from(PIPELINE_DEF)
                .where(PIPELINE_DEF.KEY.eq(pipelineDefKey))
                .fetchAny(PIPELINE_DEF.ID);
    }

    @Override
    public Iterable<? extends PipelineTaskDef> getByPipelineDefAndStageWithPrereq(
            String defKey, int stage) {
        var innerTaskDef = PIPELINE_TASK_DEF.as("innerTaskDef");
        var prereq = PIPELINE_TASK_DEF_PREREQ.as("prereq");
        var innerCondition =
                PIPELINE_TASK_DEF_PREREQ
                        .TASK_DEF_ID
                        .eq(PIPELINE_TASK_DEF.ID)
                        .and(PIPELINE_TASK_DEF_PREREQ.DELETED.eq(false))
                        .and(innerTaskDef.DELETED.eq(false))
                        .and(PIPELINE_TASK_DEF.DELETED.eq(false));
        var field =
                field(
                        select(arrayAgg(innerTaskDef.KEY))
                                .from(PIPELINE_TASK_DEF_PREREQ)
                                .join(innerTaskDef)
                                .on(innerTaskDef.ID.eq(PIPELINE_TASK_DEF_PREREQ.PREREQ_TASK_DEF_ID))
                                .where(innerCondition));
        var condition =
                PIPELINE_DEF
                        .KEY
                        .eq(defKey)
                        .and(
                                PIPELINE_TASK_DEF
                                        .STAGE
                                        .eq(stage)
                                        .or(
                                                taskDefLink
                                                        .STAGE
                                                        .eq(stage)
                                                        .and(prereq.DELETED.eq(false))
                                                        .and(taskDefLink.DELETED.eq(false)))
                                        .and(PIPELINE_TASK_DEF.DELETED.eq(false))
                                        .and(PIPELINE_DEF_TASK_DEF.DELETED.eq(false)));
        return dsl.selectDistinct(
                        PIPELINE_TASK_DEF.ID,
                        PIPELINE_TASK_DEF.KEY,
                        PIPELINE_TASK_DEF.NAME,
                        PIPELINE_TASK_DEF.STAGE,
                        PIPELINE_TASK_DEF.DESCRIPTION,
                        PIPELINE_TASK_DEF.DIFFICULTY,
                        field)
                .from(PIPELINE_TASK_DEF)
                .leftJoin(PIPELINE_DEF_TASK_DEF)
                .on(PIPELINE_TASK_DEF.ID.eq(PIPELINE_DEF_TASK_DEF.TASK_DEF_ID))
                .leftJoin(PIPELINE_DEF)
                .on(PIPELINE_DEF.ID.eq(PIPELINE_DEF_TASK_DEF.PIPELINE_DEF_ID))
                .leftJoin(prereq)
                .on(PIPELINE_TASK_DEF.ID.eq(prereq.PREREQ_TASK_DEF_ID))
                .leftJoin(taskDefLink)
                .on(taskDefLink.ID.eq(prereq.TASK_DEF_ID))
                .where(condition)
                .fetch()
                .map(this::map);
    }

    @Override
    public Iterable<? extends PipelineTaskDef> findAllByType(Message.PipelineDefMessage.Type type) {
        Preconditions.checkArgument(
                Objects.equals(type, Message.PipelineDefMessage.Type.PROJECT),
                "Only support find all pipeline task def by "
                        + Message.PipelineDefMessage.Type.PROJECT);
        return dsl.selectFrom(PIPELINE_TASK_DEF)
                .where(PIPELINE_TASK_DEF.DELETED.eq(false))
                .fetch()
                .map(this::map);
    }

    private List<PipelineTaskDefEntity> findNext(
            Condition outerCondition, Condition innerCondition) {
        var innerTaskDef = PIPELINE_TASK_DEF.as("innerTaskDef");
        var prereq = PIPELINE_TASK_DEF_PREREQ.as("prereq");
        innerCondition =
                innerCondition
                        .and(PIPELINE_TASK_DEF_PREREQ.TASK_DEF_ID.eq(PIPELINE_TASK_DEF.ID))
                        .and(PIPELINE_TASK_DEF_PREREQ.DELETED.eq(false))
                        .and(PIPELINE_TASK_DEF.DELETED.eq(false))
                        .and(innerTaskDef.DELETED.eq(false));
        var field =
                field(
                        select(arrayAgg(innerTaskDef.KEY))
                                .from(PIPELINE_TASK_DEF_PREREQ)
                                .join(innerTaskDef)
                                .on(innerTaskDef.ID.eq(PIPELINE_TASK_DEF_PREREQ.PREREQ_TASK_DEF_ID))
                                .where(innerCondition));
        outerCondition =
                outerCondition
                        .and(PIPELINE_TASK_DEF.DELETED.eq(false))
                        .and(PIPELINE_DEF_TASK_DEF.DELETED.eq(false))
                        .and(taskDefLink.DELETED.eq(false))
                        .and(prereq.DELETED.eq(false));
        return dsl.selectDistinct(
                        PIPELINE_TASK_DEF.ID,
                        PIPELINE_TASK_DEF.KEY,
                        PIPELINE_TASK_DEF.NAME,
                        PIPELINE_TASK_DEF.STAGE,
                        PIPELINE_TASK_DEF.DESCRIPTION,
                        PIPELINE_TASK_DEF.DIFFICULTY,
                        field)
                .from(PIPELINE_TASK_DEF)
                .leftJoin(PIPELINE_DEF_TASK_DEF)
                .on(PIPELINE_TASK_DEF.ID.eq(PIPELINE_DEF_TASK_DEF.TASK_DEF_ID))
                .leftJoin(PIPELINE_DEF)
                .on(PIPELINE_DEF.ID.eq(PIPELINE_DEF_TASK_DEF.PIPELINE_DEF_ID))
                .leftJoin(prereq)
                .on(PIPELINE_TASK_DEF.ID.eq(prereq.TASK_DEF_ID))
                .leftJoin(taskDefLink)
                .on(taskDefLink.ID.eq(prereq.PREREQ_TASK_DEF_ID))
                .where(outerCondition)
                .fetch()
                .map(this::map);
    }

    private List<PipelineTaskDefEntity> find(Condition outerCondition, Condition innerCondition) {
        var innerTaskDef = PIPELINE_TASK_DEF.as("innerTaskDef");
        innerCondition =
                innerCondition
                        .and(PIPELINE_TASK_DEF_PREREQ.TASK_DEF_ID.eq(PIPELINE_TASK_DEF.ID))
                        .and(PIPELINE_TASK_DEF_PREREQ.DELETED.eq(false))
                        .and(PIPELINE_TASK_DEF.DELETED.eq(false))
                        .and(innerTaskDef.DELETED.eq(false));
        var field =
                field(
                        select(arrayAgg(innerTaskDef.KEY))
                                .from(PIPELINE_TASK_DEF_PREREQ)
                                .join(innerTaskDef)
                                .on(innerTaskDef.ID.eq(PIPELINE_TASK_DEF_PREREQ.PREREQ_TASK_DEF_ID))
                                .where(innerCondition));
        outerCondition =
                outerCondition
                        .and(PIPELINE_TASK_DEF.DELETED.eq(false))
                        .and(PIPELINE_DEF_TASK_DEF.DELETED.eq(false));
        return dsl.selectDistinct(
                        PIPELINE_TASK_DEF.ID,
                        PIPELINE_TASK_DEF.KEY,
                        PIPELINE_TASK_DEF.NAME,
                        PIPELINE_TASK_DEF.STAGE,
                        PIPELINE_TASK_DEF.DESCRIPTION,
                        PIPELINE_TASK_DEF.DIFFICULTY,
                        field)
                .from(PIPELINE_TASK_DEF)
                .leftJoin(PIPELINE_DEF_TASK_DEF)
                .on(PIPELINE_TASK_DEF.ID.eq(PIPELINE_DEF_TASK_DEF.TASK_DEF_ID))
                .leftJoin(PIPELINE_DEF)
                .on(PIPELINE_DEF.ID.eq(PIPELINE_DEF_TASK_DEF.PIPELINE_DEF_ID))
                .where(outerCondition)
                .fetch()
                .map(this::map);
    }

    private PipelineTaskDefEntity map(PipelineTaskDefRecord record) {
        return map(record, Collections.emptyList());
    }

    private PipelineTaskDefEntity map(Record record, Iterable<String> prereq) {
        return PipelineTaskDefEntity.builder()
                .id(record.get(PIPELINE_TASK_DEF.ID))
                .name(record.get(PIPELINE_TASK_DEF.NAME))
                .key(record.get(PIPELINE_TASK_DEF.KEY))
                .stage(record.get(PIPELINE_TASK_DEF.STAGE))
                .difficulty(Optional.ofNullable(record.get(PIPELINE_TASK_DEF.DIFFICULTY)).orElse(0))
                .description(record.get(PIPELINE_TASK_DEF.DESCRIPTION))
                .prereqTaskDefKey(prereq)
                .build();
    }

    private PipelineTaskDefEntity map(
            Record7<String, String, String, Integer, String, Integer, String[]> record) {
        var prereq =
                Optional.ofNullable(record.value7())
                        .map(s -> Arrays.stream(s).collect(Collectors.toSet()))
                        .orElse(Set.of());
        return map(record, prereq);
    }

    @Builder
    @Data
    static class PipelineTaskDefEntity implements PipelineTaskDef {
        private String id;
        private String name;
        private String key;
        private String description;
        private int stage;
        private Iterable<String> prereqTaskDefKey;
        private int difficulty;
    }
}
