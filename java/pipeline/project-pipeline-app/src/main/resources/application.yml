spring:
  profiles:
    active: ${ENV}
    include: actuator

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}
grpc:
  client:
    projectManager:
      address: static://bees360web-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    addressFlyZoneTypeManager:
      address: static://bees360-address-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineService:
      address: static://bees360-pipeline-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineDefService:
      address: static://bees360-pipeline-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIIManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    customerManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    userProvider:
      address: 'static://bees360-bifrost-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password
app:
  project-pipeline:
    user:
      robot-email: <EMAIL>
