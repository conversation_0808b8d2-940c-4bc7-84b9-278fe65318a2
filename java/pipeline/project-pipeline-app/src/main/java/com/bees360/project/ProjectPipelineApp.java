package com.bees360.project;

import com.bees360.address.config.GrpcAddressFlyZoneManagerConfig;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.customer.config.GrpcCustomerClientConfig;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.pipeline.config.GrpcPipelineClientConfig;
import com.bees360.pipeline.config.GrpcPipelineDefClientConfig;
import com.bees360.project.config.GrpcProjectClientConfig;
import com.bees360.project.config.GrpcProjectIIMangerConfig;
import com.bees360.project.config.ProjectPipelineTaskConfig;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.user.config.GrpcUserManagerConfig;
import com.bees360.util.Iterables;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

import java.util.Comparator;
import java.util.function.Supplier;

/**
 * Project 与 Pipeline关联服务，主要部署一些Project与Pipeline相关的逻辑, 比如 {@link
 * com.bees360.event.registry.PipelineTaskChanged}的监听器等
 */
@Log4j2
@EnableEncryptableProperties
@ApplicationAutoConfig
@Import(
        value = {
            // Grpc Client
            GrpcPipelineClientConfig.class,
            GrpcPipelineDefClientConfig.class,
            GrpcProjectClientConfig.class,
            GrpcProjectIIMangerConfig.class,
            GrpcAddressFlyZoneManagerConfig.class,
            GrpcCustomerClientConfig.class,
            GrpcClientConfig.class,
            GrpcUserManagerConfig.class,

            // Event Listener
            RabbitApiConfig.class,
            RabbitEventDispatcher.class,
            ProjectPipelineTaskConfig.class,
        })
public class ProjectPipelineApp {

    public static void main(String[] args) {
        ExitableSpringApplication.run(ProjectPipelineApp.class, args);
    }

    @Bean
    Supplier<String> robotUserIdSupplier(
            UserProvider userProvider,
            @Value("${app.project-pipeline.user.robot-email}") String robotUserEmail) {

        Supplier<String> robotUserIdSupplier =
                () -> {
                    var robotUser =
                            Iterables.toList(userProvider.findUserByEmail(robotUserEmail)).stream()
                                    .min(Comparator.comparing(User::getId))
                                    .orElse(null);
                    if (robotUser == null) {
                        var message =
                                String.format(
                                        "Robot User with email %s not found.", robotUserEmail);
                        throw new IllegalStateException(message);
                    }
                    return robotUser.getId();
                };
        log.info(
                "Created {}(userProvider={},robotUserEmail={})",
                robotUserIdSupplier,
                userProvider,
                robotUserEmail);
        return robotUserIdSupplier;
    }
}
