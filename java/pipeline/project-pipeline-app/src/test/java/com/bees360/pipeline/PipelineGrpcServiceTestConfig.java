package com.bees360.pipeline;

import com.bees360.pipeline.JooqPipelineDefService.JooqPipelineDefRepository;

import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PipelineGrpcServiceTestConfig {

    @Bean
    GrpcPipelineService grpcPipelineService(
            DSLContext dsl, JooqPipelineDefService jooqPipelineDefService) {
        var pipelineManager = new JooqPipelineManager(dsl);
        var pipelineService =
                new JooqPipelineService(
                        dsl, jooqPipelineDefService, pipelineManager, pipelineManager, null);
        return new GrpcPipelineService(pipelineService);
    }

    @Bean
    GrpcPipelineDefService grpcPipelineDefService(JooqPipelineDefService jooqPipelineDefService) {
        return new GrpcPipelineDefService(jooqPipelineDefService);
    }

    @Bean
    JooqPipelineDefService jooqPipelineDefService(
            DSLContext dsl,
            @Value("${pipeline.remove-indirect-prerequisite:false}")
                    boolean removeIndirectPrerequisite) {
        var pipelineTaskService = new JooqTaskDefService(dsl, removeIndirectPrerequisite);
        return new JooqPipelineDefService(new JooqPipelineDefRepository(dsl), pipelineTaskService);
    }
}
