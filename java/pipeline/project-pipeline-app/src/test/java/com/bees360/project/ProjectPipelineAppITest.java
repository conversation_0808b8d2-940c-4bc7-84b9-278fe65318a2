package com.bees360.project;

import static com.bees360.pipeline.TestUtils.createPipelineDef;
import static com.bees360.pipeline.TestUtils.randomKey;
import static com.bees360.pipeline.TestUtils.randomPipelineDef;
import static com.bees360.pipeline.TestUtils.randomTaskDef;
import static com.bees360.project.Message.ServiceType.FOUR_POINT_UNDERWRITING;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.bees360.address.AddressFlyZoneTypeManager;
import com.bees360.customer.CustomerProvider;
import com.bees360.customer.Message;
import com.bees360.event.EventPublisher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.registry.InteriorDamageStatusChangedEvent;
import com.bees360.event.registry.ProjectTagAdded;
import com.bees360.event.registry.ProjectTagChangedObject;
import com.bees360.pipeline.GrpcPipelineDefClient;
import com.bees360.pipeline.Pipeline;
import com.bees360.pipeline.PipelineDef;
import com.bees360.pipeline.PipelineDefService;
import com.bees360.pipeline.PipelineGrpcServiceTestConfig;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.PipelineTask;
import com.bees360.pipeline.PipelineTaskDef;
import com.bees360.project.config.ProjectPipelineTaskConfig;
import com.bees360.repository.Provider;
import com.bees360.util.Iterables;
import com.google.common.collect.Maps;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@SpringBootTest()
@TestPropertySource(
        properties = {
            "GRPC_SERVER_PORT=9012",
            "spring.config.location = classpath:application.yml,classpath:application-test.yml",
        })
public class ProjectPipelineAppITest {
    public ProjectPipelineAppITest() {}

    @Import({
        ProjectPipelineApp.class,
        ProjectPipelineTaskConfig.class,
        RabbitEventPublisher.class,
        PipelineGrpcServiceTestConfig.class,
    })
    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class, // Create required server beans
        GrpcServerFactoryAutoConfiguration.class, // Select server implementation
        GrpcClientAutoConfiguration.class,
    }) // Support @GrpcClient annotation
    @Configuration
    static class Config {

        @MockBean Provider<ProjectII> projectIIProvider;
        @MockBean AddressFlyZoneTypeManager addressFlyZoneTypeManager;

        @Bean
        Executor executor() {
            return Executors.newCachedThreadPool();
        }

        @Bean
        PipelineDefService pipelineDefService(GrpcPipelineDefClient grpcPipelineDefClient) {
            return grpcPipelineDefClient;
        }
    }

    @Autowired private PipelineDefService pipelineDefService;
    @Autowired private PipelineService pipelineService;
    @Autowired private EventPublisher eventPublisher;
    @Autowired private Provider<ProjectII> projectIIProvider;
    @MockBean private CustomerProvider customerProvider;

    @Autowired private ProjectPipelineTaskConfig.PipelineTaskConfigProperties properties;

    @BeforeEach
    void setup() {
        properties
                .getTasks()
                .forEach(
                        taskProperties -> {
                            var key = taskProperties.getKey();
                            var name = taskProperties.getName();
                            int stage = taskProperties.getStage();
                            if (StringUtils.isNotBlank(key) || StringUtils.isNotBlank(name)) {
                                pipelineDefService.savePipelineTaskDef(
                                        PipelineTaskDef.from(
                                                com.bees360.pipeline.Message.PipelineDefMessage.Task
                                                        .newBuilder()
                                                        .setKey(key)
                                                        .setName(name)
                                                        .setStage(stage)
                                                        .build()));
                            }
                        });
    }

    @Test
    void testAddPipelineOnInteriorDamage() {
        var initTaskKey = "init_task";
        PipelineTaskDef taskDef =
                PipelineTaskDef.from(
                        randomTaskDef().toMessage().toBuilder().setKey(initTaskKey).build());
        PipelineDef pipelineDef = randomPipelineDef(Set.of(taskDef));
        String defKey = pipelineDef.getKey();

        createPipelineDef(pipelineDefService, pipelineDef);
        var pipelineId = randomKey();
        var insuredBy = Message.CustomerMessage.newBuilder().setKey("insuredBy").build();
        var processBy = Message.CustomerMessage.newBuilder().setKey("processedBy").build();
        var project = randomProject(pipelineId, insuredBy, processBy, FOUR_POINT_UNDERWRITING);
        Mockito.when(projectIIProvider.findById(pipelineId))
                .thenReturn(ProjectII.from(project.toMessage()));
        pipelineService.createPipeline(pipelineId, defKey);
        var event = new InteriorDamageStatusChangedEvent();
        event.setProjectId(pipelineId);
        event.setHasInteriorDamage(true);
        eventPublisher.publish(event);

        await().atMost(Duration.ofSeconds(5))
                .pollInterval(Duration.ofMillis(500))
                .until(() -> !pipelineService.getById(pipelineId).getDefKey().equals(defKey));

        var testTaskKey = "test_task";
        var pipeline = pipelineService.getById(pipelineId);
        var taskKeySet =
                Iterables.toStream(pipeline.getTask())
                        .map(PipelineTask::getKey)
                        .collect(Collectors.toSet());
        Assertions.assertTrue(
                taskKeySet.contains(testTaskKey),
                String.format(
                        "The pipeline '%s' should contains task '%s'", pipelineId, testTaskKey));
        Assertions.assertTrue(
                taskKeySet.contains(initTaskKey),
                String.format(
                        "The pipeline '%s' should contains task '%s'", pipelineId, initTaskKey));
    }

    public static Project randomProject(
            String id,
            Message.CustomerMessage insuredBy,
            Message.CustomerMessage processedBy,
            com.bees360.project.Message.ServiceType serviceType) {
        return Project.of(
                com.bees360.project.Message.ProjectMessage.newBuilder()
                        .setId(id)
                        .setServiceType(serviceType)
                        .setContract(
                                com.bees360.contract.Message.ContractMessage.newBuilder()
                                        .setInsuredBy(insuredBy)
                                        .setProcessedBy(processedBy)
                                        .build())
                        .setPolicy(
                                com.bees360.policy.Message.PolicyMessage.newBuilder()
                                        .setAddress(
                                                com.bees360.address.Message.AddressMessage
                                                        .newBuilder()
                                                        .setId(
                                                                RandomStringUtils.randomAlphabetic(
                                                                        6))
                                                        .build())
                                        .build())
                        .build());
    }

    @Test
    void testAddProjectTagTriggerAddMultiPipelineTaskWith101() {
        List.of("contact_insured", "approve_fur", "upload_report_to_towerhill");
        Map<String, Set<String>> taskKeyPrereqsExpected =
                Map.of(
                        "approve_fur", Set.of(),
                        "submit_nr", Set.of(),
                        "approve_nr", Set.of("submit_nr"),
                        "generate_fsr_rce", Set.of("approve_nr", "approve_fur"),
                        "upload_report_to_towerhill", Set.of("generate_fsr_rce", "approve_fur"));
        var versionSuffix =
                "_submit_nr_on_t_rc_report_a_approve_nr_on_t_rc_report_generate_fsr_rce_on_t_rc_report";
        var initialTaskKeys = List.of("approve_fur", "upload_report_to_towerhill");
        testAddProjectTagTriggerAddMultiPipelineTask(
                "101",
                "Tower Hill Insurance Group",
                initialTaskKeys,
                taskKeyPrereqsExpected,
                versionSuffix);
    }

    void testAddProjectTagTriggerAddMultiPipelineTask(
            String tagId,
            String insuredByKey,
            List<String> initialTaskKeys,
            Map<String, Set<String>> expectedTaskKeyPrereqs,
            String expectedVersionSuffix) {

        var pipelineId = randomKey();

        var originPipeline = createPipelineDefWithLinkedTasks(pipelineId, initialTaskKeys);

        var insuredBy = Message.CustomerMessage.newBuilder().setKey(insuredByKey).build();
        var processBy = Message.CustomerMessage.newBuilder().setKey("processedBy").build();
        var project = randomProject(pipelineId, insuredBy, processBy, FOUR_POINT_UNDERWRITING);
        Mockito.when(projectIIProvider.findById(pipelineId))
                .thenReturn(ProjectII.from(project.toMessage()));

        var tagAddedEvent = new ProjectTagAdded();
        var tagChangedObject = new ProjectTagChangedObject();
        tagChangedObject.setTagId(tagId);
        tagChangedObject.setProjectId(pipelineId);
        tagAddedEvent.setList(List.of(tagChangedObject));
        eventPublisher.publish(tagAddedEvent);

        await().atMost(Duration.ofSeconds(5))
                .pollInterval(Duration.ofMillis(500))
                .until(
                        () ->
                                pipelineService
                                        .getById(pipelineId)
                                        .getDefKey()
                                        .endsWith(expectedVersionSuffix));

        var finalPipeline = pipelineService.getById(pipelineId);

        assertEquals(originPipeline.getDefKey() + expectedVersionSuffix, finalPipeline.getDefKey());

        var tasks = Iterables.transform(finalPipeline.getTask(), PipelineTask::getKey);
        assertEquals(expectedTaskKeyPrereqs.keySet(), Sets.newHashSet(tasks));

        var pipelineDef = pipelineDefService.findByKey(finalPipeline.getDefKey());
        var actualTaskPrereqs = pipelineDefToTaskKeyPrereq(pipelineDef);
        assertEquals(expectedTaskKeyPrereqs, actualTaskPrereqs);
    }

    private Map<String, Set<String>> pipelineDefToTaskKeyPrereq(PipelineDef pipelineDef) {
        Map<String, Set<String>> map = Maps.newHashMap();
        for (var task : pipelineDef.getTask()) {
            map.computeIfAbsent(task.getKey(), key -> Sets.newHashSet())
                    .addAll(Iterables.toCollection(task.getPrereqTaskDefKey()));
        }
        return map;
    }

    private Pipeline createPipelineDefWithLinkedTasks(
            String pipelineId, Iterable<String> taskKeysLinked) {
        var taskLinked = new ArrayList<PipelineTaskDef>();
        var preTaskKey = "";
        for (var taskKey : taskKeysLinked) {
            var taskDefBuilder = randomTaskDef().toMessage().toBuilder().setKey(taskKey);
            if (StringUtils.isNotEmpty(preTaskKey)) {
                taskDefBuilder.addPrereqTaskDef(preTaskKey);
            }
            taskLinked.add(PipelineTaskDef.from(taskDefBuilder.build()));
            preTaskKey = taskKey;
        }
        var pipelineDef = randomPipelineDef(taskLinked);

        createPipelineDef(pipelineDefService, pipelineDef);
        return pipelineService.changePipelineDef(pipelineId, pipelineDef.getKey());
    }
}
