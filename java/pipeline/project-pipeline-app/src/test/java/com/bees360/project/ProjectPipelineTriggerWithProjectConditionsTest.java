package com.bees360.project;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.bees360.project.listener.ChangePipelineTaskOnProjectTagAddedWithConditions;
import com.bees360.project.listener.ChangePipelineTaskOnProjectTagRemovedWithConditions;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.Map;
import java.util.Set;

@SpringBootTest(classes = ProjectPipelineAppITest.Config.class)
@TestPropertySource(
        properties = {
            "GRPC_SERVER_PORT=9013",
            "pipeline.listener.project-pipeline-task-listeners.project-tag.with-project-conditions=true",
            "spring.config.location = classpath:application.yml,classpath:application-test.yml",
        })
class ProjectPipelineTriggerWithProjectConditionsTest extends ProjectPipelineAppITest {

    @Autowired ApplicationContext applicationContext;

    @Test
    void testBeanExist() {
        assertNotNull(
                applicationContext.getBean(
                        ChangePipelineTaskOnProjectTagRemovedWithConditions.class));
        assertNotNull(
                applicationContext.getBean(
                        ChangePipelineTaskOnProjectTagAddedWithConditions.class));
    }

    @Test
    void testAddProjectTagTriggerAddMultiPipelineTaskWithRemoveTriggerAndAddTrigger() {
        Map<String, Set<String>> expectedTaskKeyPrereqs =
                Map.of(
                        "approve_fur", Set.of(),
                        "submit_nr", Set.of(),
                        "approve_nr", Set.of("submit_nr"),
                        "generate_fsr_rce", Set.of("approve_nr", "approve_fur"),
                        "upload_report_to_towerhill", Set.of("generate_fsr_rce", "approve_fur"));
        var expectedVersionSuffix =
                "_rm_c_insured_submit_nr_on_t_rc_report_a_approve_nr_on_t_rc_report_generate_fsr_rce_on_t_rc_report";
        // contact_insured will be removed
        var initialTaskKeys =
                List.of("contact_insured", "approve_fur", "upload_report_to_towerhill");
        testAddProjectTagTriggerAddMultiPipelineTask(
                "102",
                "Tower Hill Insurance Group",
                initialTaskKeys,
                expectedTaskKeyPrereqs,
                expectedVersionSuffix);
    }
}
