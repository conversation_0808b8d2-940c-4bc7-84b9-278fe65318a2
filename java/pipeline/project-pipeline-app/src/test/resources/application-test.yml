spring:
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver

grpc:
  server:
    port: ${GRPC_SERVER_PORT:9898}
  client:
    projectManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    addressFlyZoneTypeManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineService:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineDefService:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
pipeline:
  tasks:
    - key: test_task
      name: 'Test Task'
      stage: 30
      prereq-task-def-key: []
      next-task-def-key: [init_task]
      add-trigger:
        - tag-id: 98
          tag-changed-type: ADD
          version-suffix: _add_on_tag_add
        - tag-id: 98
          tag-changed-type: REMOVED
          version-suffix: _add_on_tag_remove
        - feedback-regex: ^.*feedback.*$
          service-type: [QUICK_INSPECT]
          version-suffix: _feedback
        - flyZone-type: [ RESTRICTED_ZONE ]
          service-type: [ QUICK_INSPECT ]
          version-suffix: _restricted
        - interior-damage: true
          service-type: [ FOUR_POINT ]
          insured_by: [insuredBy]
          processed_by: [processedBy]
          version-suffix: _interior
      status-trigger:
        - tag-id: 98
          tag-changed-type: REMOVED
          status: DONE
        - feedback-regex: ^.*feedback.*$
          status: ERROR
        - call-result-regex: ^.*test call.*$
          status: ERROR
        - project-status: PROJECT_REWORK
          status: ERROR
        - state: PROJECT_CLOSE
          status: DONE
        - added-operation-tag-id: 22
          status: ERROR
        - removed-operation-tag-id: 22
          status: DONE
        - trigger-status: READY
          service-type: [ QUICK_INSPECT ]
          status: ONGOING
        - flyZone-type: [ RESTRICTED_ZONE ]
          service-type: [ QUICK_INSPECT ]
          status: ERROR
        - trigger-status: READY
          source-key: source_key
          service-type: [ QUICK_INSPECT ]
          status: ONGOING
    - key: submit_nr
      name: 'Submit Estimate'
      stage: 40
      add-trigger:
        - tag-id: 101
          tag-changed-type: ADD
          version-suffix: _submit_nr_on_t_rc_report
        - tag-id: 102
          tag-changed-type: ADD
          version-suffix: _submit_nr_on_t_rc_report
          insured-by: [ 'Tower Hill Insurance Group' ]
    # generate_fsr_rce 依赖了 approve_nr
    - key: approve_nr
      name: 'Approve Estimate'
      stage: 40
      prereq-task-def-key: [ submit_nr ]
      add-trigger:
        - tag-id: 101
          tag-changed-type: ADD
          version-suffix: _a_approve_nr_on_t_rc_report
        - tag-id: 102
          tag-changed-type: ADD
          version-suffix: _a_approve_nr_on_t_rc_report
          insured-by: [ 'Tower Hill Insurance Group' ]
    - key: generate_fsr_rce
      name: 'Generate FSR_RCE'
      stage: 40
      prereq-task-def-key: [ approve_nr, approve_fur ]
      next-task-def-key: [ upload_report_to_towerhill ]
      add-trigger:
        - tag-id: 101
          tag-changed-type: ADD
          version-suffix: _generate_fsr_rce_on_t_rc_report
        - tag-id: 102
          tag-changed-type: ADD
          version-suffix: _generate_fsr_rce_on_t_rc_report
          insured-by: [ 'Tower Hill Insurance Group' ]
    - key: contact_insured
      name: 'Contact Insured'
      stage: 40
      remove-trigger:
        - tag-id: 102
          tag-changed-type: ADD
          version-suffix: _rm_c_insured
          insured-by: [ 'Tower Hill Insurance Group' ]
    - key: deliver_reports_to_rct
      name: 'Deliver Report To RCT'
      stage: 40
      remove-trigger:
        - tag-id: 102
          tag-changed-type: ADD
          version-suffix: _add_d_r_to_rct
          insured-by: [ 'Berkley One Insurance' ]
    - key: deliver_images_to_rct
      name: 'Deliver Images To RCT'
      stage: 40
      add-trigger:
        - tag-id: 102
          tag-changed-type: ADD
          version-suffix: _add_d_i_to_rct
          insured-by: [ 'Berkley One Insurance' ]
