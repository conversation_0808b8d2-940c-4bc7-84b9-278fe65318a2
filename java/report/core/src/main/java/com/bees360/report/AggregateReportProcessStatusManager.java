package com.bees360.report;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import java.util.List;

@Log4j2
public class AggregateReportProcessStatusManager implements ReportProcessStatusManager {

    private final ReportProcessStatusManager reportProcessStatusManager;

    public AggregateReportProcessStatusManager(
            ReportProcessStatusManager reportProcessStatusManager) {
        this.reportProcessStatusManager = reportProcessStatusManager;
        log.info(
                "Created {}(reportProcessStatusManager={}).",
                this,
                this.reportProcessStatusManager);
    }

    @Override
    @Nullable
    public List<Message.ReportProcessStatus> getReportProcessStatus(
            String groupKey,
            String groupType,
            @Nullable String reportTypeCode,
            @Nullable Message.ReportProcessStatus.Type type) {
        log.debug(
                "Getting report process status for groupKey={}, groupType={}, reportType={},"
                        + " type={}.",
                groupKey,
                groupType,
                reportTypeCode,
                type);
        return reportProcessStatusManager.getReportProcessStatus(
                groupKey, groupType, reportTypeCode, type);
    }

    @Override
    @Nullable
    public Message.ReportProcessStatus updateReportProcessStatus(
            String groupKey,
            String groupType,
            String reportType,
            Message.ReportProcessStatus.Type type,
            Message.ReportProcessStatus.Status status,
            String updatedBy,
            @Nullable String comment) {
        log.debug(
                "Updating report process status for groupKey={}, groupType={}, reportType={},"
                        + " type={}, status={}, updatedBy={}, comment={}",
                groupKey,
                groupType,
                reportType,
                type,
                status,
                updatedBy,
                comment);
        return reportProcessStatusManager.updateReportProcessStatus(
                groupKey, groupType, reportType, type, status, updatedBy, comment);
    }
}
