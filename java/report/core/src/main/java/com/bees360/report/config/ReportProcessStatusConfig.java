package com.bees360.report.config;

import com.bees360.redis.config.RedissonConfig;
import com.bees360.redis.config.codec.RedisProtoCodecConfig;
import com.bees360.report.AggregateReportProcessStatusManager;
import com.bees360.report.Message;
import com.bees360.report.RedisReportProcessStatusManager;
import com.bees360.report.ReportProcessStatusManager;
import com.bees360.report.grpc.GrpcReportProcessStatusService;
import com.bees360.report.listener.UpdateReportProcessStatusOnMergeReportJobCompleted;
import com.bees360.report.listener.UpdateReportProcessStatusOnSaveReportJobCompleted;

import lombok.Data;

import org.redisson.api.MapOptions;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.CompositeCodec;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Duration;

@Import({
    RedissonConfig.class,
    RedisProtoCodecConfig.class,
    GrpcReportProcessStatusService.class,
})
@Configuration
@ConditionalOnProperty(prefix = "report", name = "processor", havingValue = "with-process-status")
public class ReportProcessStatusConfig {

    @Data
    public static class ReportProcessStatusProperties {
        public String systemUserId;
        public Duration statusExpireDuration = Duration.ofMinutes(10L);
    }

    @Bean
    @ConfigurationProperties(prefix = "report.process-status")
    public ReportProcessStatusProperties reportProcessStatusProperties() {
        return new ReportProcessStatusProperties();
    }

    @Bean
    public RedisReportProcessStatusManager redisReportProcessStatusManager(
            RedissonClient redissonClient,
            ReportProcessStatusProperties properties,
            Codec redissonCodec) {
        var processStatusCodeC = new CompositeCodec(StringCodec.INSTANCE, redissonCodec);
        RMapCache<String, Message.ReportProcessStatus> map =
                redissonClient.getMapCache(
                        "report_process_status", processStatusCodeC, MapOptions.defaults());
        return new RedisReportProcessStatusManager(map, properties.getStatusExpireDuration());
    }

    @Bean({"reportProcessStatusManager", "grpcReportProcessStatusManager"})
    public ReportProcessStatusManager reportProcessStatusManager(
            ReportProcessStatusManager redisReportProcessStatusManager) {
        return new AggregateReportProcessStatusManager(redisReportProcessStatusManager);
    }

    @Bean
    public UpdateReportProcessStatusOnSaveReportJobCompleted
            updateReportProcessStatusOnJobCompleted(
                    ReportProcessStatusManager reportProcessStatusManager,
                    ReportProcessStatusProperties properties) {
        return new UpdateReportProcessStatusOnSaveReportJobCompleted(
                reportProcessStatusManager, properties.getSystemUserId());
    }

    @Bean
    public UpdateReportProcessStatusOnMergeReportJobCompleted
            updateReportProcessStatusOnMergeReportJobCompleted(
                    ReportProcessStatusManager reportProcessStatusManager,
                    ReportProcessStatusProperties properties) {
        return new UpdateReportProcessStatusOnMergeReportJobCompleted(
                reportProcessStatusManager, properties.getSystemUserId());
    }
}
