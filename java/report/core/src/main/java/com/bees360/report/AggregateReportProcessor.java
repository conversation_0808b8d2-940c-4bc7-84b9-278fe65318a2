package com.bees360.report;

import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.ListenableFuture;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

@Log4j2
public class AggregateReportProcessor implements ReportProcessor {

    private final ReportProcessor reportProcessor;
    private final ReportProcessStatusManager reportProcessStatusManager;

    public AggregateReportProcessor(
            ReportProcessor reportProcessor,
            ReportProcessStatusManager reportProcessStatusManager) {
        this.reportProcessor = reportProcessor;
        this.reportProcessStatusManager = reportProcessStatusManager;
        log.info(
                "Created {}(reportProcessor={}, reportProcessStatusManager={}).",
                this,
                this.reportProcessor,
                this.reportProcessStatusManager);
    }

    @Override
    public ListenableFuture<Void> mergeReport(
            String groupKey,
            String groupType,
            String mergeReportType,
            Iterable<String> resourceIds,
            String mergedBy) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(groupKey),
                "Fail to merge report: group key cannot be blank.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(groupType),
                "Fail to merge report: group type cannot be blank.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(mergeReportType),
                "Fail to merge report: report type cannot be blank.");
        Preconditions.checkArgument(
                resourceIds != null && resourceIds.iterator().hasNext(),
                "Fail to merge report: report resources cannot be empty.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(mergedBy),
                "Fail to merge report: mergedBy cannot be blank.");
        log.debug(
                "Merge report for groupKey: {}, groupType: {}, mergeReportType: {}, resourceIds:"
                        + " {}, mergedBy: {}",
                groupKey,
                groupType,
                mergeReportType,
                resourceIds,
                mergedBy);
        // set report merge status to PENDING
        var mergeStatus =
                reportProcessStatusManager.updateReportProcessStatus(
                        groupKey,
                        groupType,
                        mergeReportType,
                        Message.ReportProcessStatus.Type.MERGE,
                        Message.ReportProcessStatus.Status.PENDING,
                        mergedBy,
                        null);

        if (mergeStatus == null) {
            throw new IllegalStateException(
                    "Report merge is already in progress. Please wait for completion before"
                            + " initiating another merge.");
        }

        return reportProcessor.mergeReport(
                groupKey, groupType, mergeReportType, resourceIds, mergedBy);
    }
}
