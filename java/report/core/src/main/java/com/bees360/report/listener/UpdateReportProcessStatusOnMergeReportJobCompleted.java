package com.bees360.report.listener;

import static com.bees360.job.ReportJobNames.GENERATE_MERGE_PDF_REPORT;

import com.bees360.event.registry.JobCompleted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.report.Message;
import com.bees360.report.Message.ReportProcessStatus.Type;
import com.bees360.report.ReportProcessStatusManager;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.IOException;

@Log4j2
public class UpdateReportProcessStatusOnMergeReportJobCompleted
        extends AbstractNamedEventListener<JobCompleted> {

    private final ReportProcessStatusManager reportProcessStatusManager;
    private final String systemUserId;

    public UpdateReportProcessStatusOnMergeReportJobCompleted(
            ReportProcessStatusManager reportProcessStatusManager, String systemUserId) {
        this.reportProcessStatusManager = reportProcessStatusManager;
        this.systemUserId = systemUserId;
        log.info(
                "Created {}(reportProcessStatusManager={}, systemUserId={}).",
                this,
                this.reportProcessStatusManager,
                this.systemUserId);
    }

    @Override
    public void handle(JobCompleted event) throws IOException {
        log.debug("Received merge pdf job completed event {}.", event);
        var jobIdParts = StringUtils.split(event.getId(), "-");
        // only regular job id trigger this handler
        if (jobIdParts.length < 4) {
            return;
        }
        var processType = Type.valueOf(jobIdParts[0]);
        var groupKey = jobIdParts[1];
        var groupType = jobIdParts[2];
        var reportType = jobIdParts[3];

        if (event.getStatus().isOk()
                || Type.UNKNOWN_PROCESS_TYPE.equals(processType)
                || Type.UNRECOGNIZED.equals(processType)) {
            return;
        }

        reportProcessStatusManager.updateReportProcessStatus(
                groupKey,
                groupType,
                reportType,
                processType,
                Message.ReportProcessStatus.Status.FAILED,
                systemUserId,
                ExceptionUtils.getRootCauseMessage(event.getStatus().getCause()));
    }

    @Override
    public String getRoutingKey() {
        return "job_completed." + GENERATE_MERGE_PDF_REPORT;
    }
}
