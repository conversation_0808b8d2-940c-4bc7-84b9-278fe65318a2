package com.bees360.report.listener;

import com.bees360.event.registry.JobCompleted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SaveReportJob;
import com.bees360.report.Message;
import com.bees360.report.ReportProcessStatusManager;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.IOException;

@Log4j2
public class UpdateReportProcessStatusOnSaveReportJobCompleted
        extends AbstractNamedEventListener<JobCompleted> {

    private final ReportProcessStatusManager reportProcessStatusManager;
    private final String systemUserId;

    public UpdateReportProcessStatusOnSaveReportJobCompleted(
            ReportProcessStatusManager reportProcessStatusManager, String systemUserId) {
        this.reportProcessStatusManager = reportProcessStatusManager;
        this.systemUserId = systemUserId;
        log.info(
                "Created {}(reportProcessStatusManager={}, systemUserId={}).",
                this,
                this.reportProcessStatusManager,
                this.systemUserId);
    }

    @Override
    public void handle(JobCompleted event) throws IOException {
        log.debug("Received save report job completed event {}.", event);
        var jobIdParts = StringUtils.split(event.getId(), "-");
        // only regular job id trigger this handler
        if (jobIdParts.length < 4) {
            return;
        }

        var processType = Message.ReportProcessStatus.Type.valueOf(jobIdParts[0]);
        var groupKey = jobIdParts[1];
        var groupType = jobIdParts[2];
        var reportType = jobIdParts[3];
        var reportMergeStatus =
                event.getStatus().isOk()
                        ? Message.ReportProcessStatus.Status.COMPLETED
                        : Message.ReportProcessStatus.Status.FAILED;
        var comment =
                event.getStatus().isOk()
                        ? null
                        : ExceptionUtils.getRootCauseMessage(event.getStatus().getCause());

        if (Message.ReportProcessStatus.Type.UNKNOWN_PROCESS_TYPE.equals(processType)
                || Message.ReportProcessStatus.Type.UNRECOGNIZED.equals(processType)) {
            return;
        }

        reportProcessStatusManager.updateReportProcessStatus(
                groupKey,
                groupType,
                reportType,
                processType,
                reportMergeStatus,
                systemUserId,
                comment);
    }

    @Override
    public String getRoutingKey() {
        return "job_completed." + JobPayloads.getJobName(SaveReportJob.class);
    }
}
