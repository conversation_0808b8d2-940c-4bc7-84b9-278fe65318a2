package com.bees360.report;

import com.bees360.util.Functions;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import com.google.protobuf.StringValue;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Log4j2
public class RedisReportProcessStatusManager implements ReportProcessStatusManager {

    private final RMapCache<String, Message.ReportProcessStatus> reportProcessStatusMap;
    private final Long expiration;

    public RedisReportProcessStatusManager(
            RMapCache<String, Message.ReportProcessStatus> reportProcessStatusMap,
            Duration expiration) {
        this.reportProcessStatusMap = reportProcessStatusMap;
        this.expiration = expiration.toMillis();
        log.info(
                "Created {}(reportProcessStatusMap={}, expiration={}).",
                this,
                this.reportProcessStatusMap,
                this.expiration);
    }

    @Override
    @Nullable
    public Message.ReportProcessStatus updateReportProcessStatus(
            String groupKey,
            String groupType,
            String reportType,
            Message.ReportProcessStatus.Type type,
            Message.ReportProcessStatus.Status status,
            String updatedBy,
            @Nullable String comment) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(groupKey),
                "Fail to update report process status: group key can not be blank.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(groupType),
                "Fail to update report process status: group type can not be blank.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(reportType),
                "Fail to update report process status: report type can not be blank.");
        Preconditions.checkArgument(
                type != null
                        && !Message.ReportProcessStatus.Type.UNKNOWN_PROCESS_TYPE.equals(type)
                        && !Message.ReportProcessStatus.Type.UNRECOGNIZED.equals(type),
                "Fail to update report process status: process type is illegal.");
        Preconditions.checkArgument(
                status != null
                        && !Message.ReportProcessStatus.Status.UNKNOWN_PROCESS_STATUS.equals(status)
                        && !Message.ReportProcessStatus.Status.UNRECOGNIZED.equals(status),
                "Fail to update report process status: process status is illegal.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(updatedBy),
                "Fail to update report process status: updated by can not be blank.");
        log.debug(
                "Updating report process status for groupKey={}, groupType={}, reportType={},"
                        + " type={}, status={}, updatedBy={}, comment={}",
                groupKey,
                groupType,
                reportType,
                type,
                status,
                updatedBy,
                comment);
        var statusKey = generateReportProcessStatusKey(groupKey, groupType, reportType, type);

        if (status == Message.ReportProcessStatus.Status.PENDING) {
            return updateReportProcessStatusWithLock(
                    statusKey, groupKey, groupType, reportType, type, status, updatedBy, comment);
        }

        return updateReportProcessStatusWithoutLock(
                statusKey, groupKey, groupType, reportType, type, status, updatedBy, comment);
    }

    public Message.ReportProcessStatus updateReportProcessStatusWithLock(
            String statusKey,
            String groupKey,
            String groupType,
            String reportType,
            Message.ReportProcessStatus.Type type,
            Message.ReportProcessStatus.Status status,
            String updatedBy,
            @Nullable String comment) {
        log.debug(
                "Updating report process status with lock for statusKey={}, groupKey={},"
                    + " groupType={}, reportType={}, type={}, status={}, updatedBy={}, comment={}",
                statusKey,
                groupKey,
                groupType,
                reportType,
                type,
                status,
                updatedBy,
                comment);
        var statusMessageBuilder =
                Message.ReportProcessStatus.newBuilder()
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .setReportType(reportType)
                        .setStatus(status)
                        .setType(type)
                        .setUpdatedBy(updatedBy);
        Functions.acceptIfNotNull(statusMessageBuilder::setComment, comment, StringValue::of);
        var newValue = statusMessageBuilder.build();
        var oldValue = reportProcessStatusMap.get(statusKey);

        // only update if last process status is completed or failed
        if (oldValue == null
                || Message.ReportProcessStatus.Status.COMPLETED.equals(oldValue.getStatus())
                || Message.ReportProcessStatus.Status.FAILED.equals(oldValue.getStatus())) {
            reportProcessStatusMap.put(statusKey, newValue, expiration, TimeUnit.MILLISECONDS);
            return newValue;
        }

        return null;
    }

    public Message.ReportProcessStatus updateReportProcessStatusWithoutLock(
            String statusKey,
            String groupKey,
            String groupType,
            String reportType,
            Message.ReportProcessStatus.Type type,
            Message.ReportProcessStatus.Status status,
            String updatedBy,
            @Nullable String comment) {
        log.debug(
                "Updating report process status without lock for statusKey={}, groupKey={},"
                    + " groupType={}, reportType={}, type={}, status={}, updatedBy={}, comment={}",
                statusKey,
                groupKey,
                groupType,
                reportType,
                type,
                status,
                updatedBy,
                comment);
        var statusMessageBuilder =
                Message.ReportProcessStatus.newBuilder()
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .setReportType(reportType)
                        .setStatus(status)
                        .setType(type)
                        .setUpdatedBy(updatedBy);
        Functions.acceptIfNotNull(statusMessageBuilder::setComment, comment, StringValue::of);
        var statusMessage = statusMessageBuilder.build();
        reportProcessStatusMap.put(statusKey, statusMessage, expiration, TimeUnit.MILLISECONDS);
        return statusMessage;
    }

    @Override
    @Nullable
    public List<Message.ReportProcessStatus> getReportProcessStatus(
            String groupKey,
            String groupType,
            @Nullable String reportTypeCode,
            @Nullable Message.ReportProcessStatus.Type type) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(groupKey),
                "Fail to get report process status: group key can not be blank.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(groupType),
                "Fail to get report process status: group type can not be blank.");
        log.debug(
                "Getting report process status for groupKey={}, groupType={}, reportType={},"
                        + " type={}",
                groupKey,
                groupType,
                reportTypeCode,
                type);
        var statusKey = generateReportProcessStatusKey(groupKey, groupType, reportTypeCode, type);
        statusKey = StringUtils.prependIfMissing("*", statusKey);
        statusKey = StringUtils.appendIfMissing("*", statusKey);
        return Iterables.toList(reportProcessStatusMap.values(statusKey));
    }

    private String generateReportProcessStatusKey(
            String groupKey,
            String groupType,
            String reportType,
            Message.ReportProcessStatus.Type type) {
        var typeString = type == null ? "*" : type.name();
        reportType = reportType == null ? "*" : reportType;
        return StringUtils.joinWith("-", groupKey, groupType, reportType, typeString);
    }
}
