package com.bees360.report;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.bees360.report.Message.ReportProcessStatus;
import com.bees360.report.config.ReportProcessStatusConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(
        classes = {
            AggregateReportProcessStatusManagerTest.Config.class,
        },
        properties = {
            "report.processor=with-process-status",
            "report.process-status.system-user-id=10000",
            "report.process-status.status-expire-duration=PT1M",
        })
@DirtiesContext
class AggregateReportProcessStatusManagerTest extends AbstractReportProcessStatusManagerTest {

    @Import({
        ReportProcessStatusConfig.class,
    })
    @Configuration
    static class Config {}

    public AggregateReportProcessStatusManagerTest(
            @Autowired ReportProcessStatusManager reportProcessStatusManager) {
        super(reportProcessStatusManager);
    }

    @Test
    public void testUpdateReportProcessStatus() {
        super.testUpdateReportProcessStatus();
    }

    @Test
    public void testUpdatePendingStatusTwiceShouldFailed() {
        var groupKey = randomId();
        var groupType = randomGroupType();
        var reportType = randomReportType();
        var type = randomProcessType();
        var status = ReportProcessStatus.Status.PENDING;
        var updatedBy = randomId();
        var comment = randomStringUtils.nextAlphabetic(24);
        var savedStatus =
                assertDoesNotThrow(
                        () ->
                                reportProcessStatusManager.updateReportProcessStatus(
                                        groupKey,
                                        groupType,
                                        reportType,
                                        type,
                                        status,
                                        updatedBy,
                                        comment));
        assertNotNull(savedStatus);
        // concurrent update PENDING status will return null
        savedStatus =
                assertDoesNotThrow(
                        () ->
                                reportProcessStatusManager.updateReportProcessStatus(
                                        groupKey,
                                        groupType,
                                        reportType,
                                        type,
                                        status,
                                        updatedBy,
                                        comment));
        assertNull(savedStatus);
    }

    @Test
    public void testUpdateProcessingStatusTwiceShouldSucceed() {
        var groupKey = randomId();
        var groupType = randomGroupType();
        var reportType = randomReportType();
        var type = randomProcessType();
        var status = ReportProcessStatus.Status.PROCESSING;
        var updatedBy = randomId();
        var comment = randomStringUtils.nextAlphabetic(24);
        var savedStatus =
                assertDoesNotThrow(
                        () ->
                                reportProcessStatusManager.updateReportProcessStatus(
                                        groupKey,
                                        groupType,
                                        reportType,
                                        type,
                                        status,
                                        updatedBy,
                                        comment));
        assertNotNull(savedStatus);
        // concurrent update PROCESSING status will return null
        savedStatus =
                assertDoesNotThrow(
                        () ->
                                reportProcessStatusManager.updateReportProcessStatus(
                                        groupKey,
                                        groupType,
                                        reportType,
                                        type,
                                        status,
                                        updatedBy,
                                        comment));
        assertNotNull(savedStatus);
    }
}
