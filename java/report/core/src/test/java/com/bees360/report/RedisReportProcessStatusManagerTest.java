package com.bees360.report;

import com.bees360.redis.config.RedissonConfig;
import com.bees360.redis.config.codec.RedisProtoCodecConfig;
import com.bees360.report.config.ReportProcessStatusConfig;

import org.junit.jupiter.api.Test;
import org.redisson.api.MapOptions;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.CompositeCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(
        classes = {
            RedisReportProcessStatusManagerTest.Config.class,
        })
@DirtiesContext
class RedisReportProcessStatusManagerTest extends AbstractReportProcessStatusManagerTest {

    @Import({
        RedissonConfig.class,
        RedisProtoCodecConfig.class,
        ReportProcessStatusConfig.ReportProcessStatusProperties.class,
    })
    @Configuration
    public static class Config {
        @Bean
        public RedisReportProcessStatusManager redisReportProcessStatusManager(
                RedissonClient redissonClient,
                ReportProcessStatusConfig.ReportProcessStatusProperties properties,
                Codec redissonCodec) {
            var processStatusCodec = new CompositeCodec(StringCodec.INSTANCE, redissonCodec);
            RMapCache<String, Message.ReportProcessStatus> map =
                    redissonClient.getMapCache(
                            "report_process_status", processStatusCodec, MapOptions.defaults());
            return new RedisReportProcessStatusManager(map, properties.getStatusExpireDuration());
        }
    }

    public RedisReportProcessStatusManagerTest(
            @Autowired ReportProcessStatusManager redisReportProcessStatusManager) {
        super(redisReportProcessStatusManager);
    }

    @Test
    public void testUpdateReportProcessStatus() {
        super.testUpdateReportProcessStatus();
    }
}
