package com.bees360.report.listener;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.bees360.api.ApiStatus;
import com.bees360.event.registry.JobCompleted;
import com.bees360.report.Message;
import com.bees360.report.ReportProcessStatusManager;
import com.bees360.report.config.ReportProcessStatusConfig;
import com.bees360.status.Message.StatusMessage;
import com.bees360.util.SecureTokens;

import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.io.IOException;

@SpringBootTest(
        classes = {
            UpdateReportProcessStatusOnMergeReportJobCompletedTest.Config.class,
        },
        properties = {
            "report.processor=with-process-status",
            "report.process-status.system-user-id=10000",
            "report.process-status.status-expire-duration=PT1M",
        })
@DirtiesContext
class UpdateReportProcessStatusOnMergeReportJobCompletedTest {

    @Import({
        ReportProcessStatusConfig.class,
    })
    @Configuration
    static class Config {}

    @Autowired private UpdateReportProcessStatusOnMergeReportJobCompleted listener;

    @Autowired private ReportProcessStatusConfig.ReportProcessStatusProperties properties;

    @MockitoSpyBean private ReportProcessStatusManager reportProcessStatusManager;

    @Test
    void testHandleJobCompletedWithSuccessStatusShouldNotUpdateStatus() throws IOException {
        // Given
        var groupKey = String.valueOf(RandomUtils.secure().randomInt());
        JobCompleted event =
                new JobCompleted(
                        "job-name",
                        "MERGE-"
                                + groupKey
                                + "-GROUP_BUNDLE-DAR-"
                                + SecureTokens.generateRandomHexToken(),
                        ApiStatus.OK);

        // When
        listener.handle(event);

        // Then
        verify(reportProcessStatusManager, never())
                .updateReportProcessStatus(
                        anyString(),
                        anyString(),
                        anyString(),
                        any(Message.ReportProcessStatus.Type.class),
                        any(Message.ReportProcessStatus.Status.class),
                        anyString(),
                        anyString());
    }

    @Test
    void testHandleJobCompletedWithFailedStatusShouldUpdateStatus() throws IOException {
        // Given
        String groupKey = String.valueOf(RandomUtils.secure().randomInt());
        String groupType = "GROUP_BUNDLE";
        String reportType = "DAR";
        String jobId =
                StringUtils.joinWith(
                        "-",
                        "MERGE",
                        groupKey,
                        groupType,
                        reportType,
                        SecureTokens.generateRandomHexToken(8));
        String errorMessage = "Test error message";

        ApiStatus failedStatus =
                new ApiStatus(
                        StatusMessage.Code.FAILED_PRECONDITION,
                        "Job failed",
                        new RuntimeException(errorMessage));
        JobCompleted event = new JobCompleted("job-name", jobId, failedStatus);

        // When
        listener.handle(event);

        // Then
        ArgumentCaptor<Message.ReportProcessStatus.Type> typeCaptor =
                ArgumentCaptor.forClass(Message.ReportProcessStatus.Type.class);
        ArgumentCaptor<Message.ReportProcessStatus.Status> statusCaptor =
                ArgumentCaptor.forClass(Message.ReportProcessStatus.Status.class);

        verify(reportProcessStatusManager)
                .updateReportProcessStatus(
                        eq(groupKey),
                        eq(groupType),
                        eq(reportType),
                        typeCaptor.capture(),
                        statusCaptor.capture(),
                        eq(properties.getSystemUserId()),
                        anyString());

        assertEquals(Message.ReportProcessStatus.Type.MERGE, typeCaptor.getValue());
        assertEquals(Message.ReportProcessStatus.Status.FAILED, statusCaptor.getValue());
    }

    @Test
    void testHandleJobCompletedWithInvalidJobIdShouldNotUpdateStatus() throws IOException {
        // Given
        ApiStatus failedStatus =
                new ApiStatus(
                        StatusMessage.Code.FAILED_PRECONDITION,
                        "Job failed",
                        new RuntimeException("Test error"));
        JobCompleted event = new JobCompleted("job-name", "invalid-job-id", failedStatus);

        // When
        listener.handle(event);

        // Then
        verify(reportProcessStatusManager, never())
                .updateReportProcessStatus(
                        anyString(),
                        anyString(),
                        anyString(),
                        any(Message.ReportProcessStatus.Type.class),
                        any(Message.ReportProcessStatus.Status.class),
                        anyString(),
                        anyString());
    }

    @Test
    void testHandleJobCompletedWithWrongJobIdFormatShouldNotUpdateStatus() throws IOException {
        // Given
        ApiStatus failedStatus =
                new ApiStatus(
                        StatusMessage.Code.FAILED_PRECONDITION,
                        "Job failed",
                        new RuntimeException("Test error"));
        JobCompleted event = new JobCompleted("job-name", "MERGE-123-project", failedStatus);

        // When
        listener.handle(event);

        // Then
        verify(reportProcessStatusManager, never())
                .updateReportProcessStatus(
                        anyString(),
                        anyString(),
                        anyString(),
                        any(Message.ReportProcessStatus.Type.class),
                        any(Message.ReportProcessStatus.Status.class),
                        anyString(),
                        anyString());
    }
}
