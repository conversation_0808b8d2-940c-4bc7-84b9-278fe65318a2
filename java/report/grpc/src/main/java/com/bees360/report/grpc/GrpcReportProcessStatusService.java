package com.bees360.report.grpc;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.report.Message;
import com.bees360.report.ReportProcessStatusManager;
import com.bees360.report.ReportProcessStatusServiceGrpc;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.context.annotation.Import;

@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
public class GrpcReportProcessStatusService
        extends ReportProcessStatusServiceGrpc.ReportProcessStatusServiceImplBase {

    private final ReportProcessStatusManager grpcReportProcessStatusManager;

    public GrpcReportProcessStatusService(
            ReportProcessStatusManager grpcReportProcessStatusManager) {
        this.grpcReportProcessStatusManager = grpcReportProcessStatusManager;
        log.info(
                "Create '{}'(grpcReportProcessStatusManager={})",
                this,
                this.grpcReportProcessStatusManager);
    }

    @Override
    public void getProcessStatus(
            Message.GetReportProcessStatusRequest request,
            StreamObserver<Message.ReportProcessStatusListResponse> responseObserver) {
        log.debug("Get report process status by request: {}.", request);
        var groupKey = request.getGroupKey();
        var groupType = request.getGroupType();
        var reportType = request.hasReportType() ? request.getReportType().getValue() : null;
        var type =
                Message.ReportProcessStatus.Type.UNKNOWN_PROCESS_TYPE.equals(request.getType())
                        ? null
                        : request.getType();
        var ReportProcessStatuses =
                grpcReportProcessStatusManager.getReportProcessStatus(
                        groupKey, groupType, reportType, type);
        responseObserver.onNext(
                Message.ReportProcessStatusListResponse.newBuilder()
                        .addAllReportProcessStatus(ReportProcessStatuses)
                        .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateProcessStatus(
            Message.UpdateReportProcessStatusRequest request,
            StreamObserver<Message.ReportProcessStatus> responseObserver) {
        log.debug("Update report process status by request: {}.", request);
        var groupKey = request.getGroupKey();
        var groupType = request.getGroupType();
        var reportType = request.getReportType();
        var type = request.getType();
        var status = request.getStatus();
        var updatedBy = request.getUpdatedBy();
        var comment = request.hasComment() ? request.getComment().getValue() : null;
        var mergeStatus =
                grpcReportProcessStatusManager.updateReportProcessStatus(
                        groupKey, groupType, reportType, type, status, updatedBy, comment);
        responseObserver.onNext(mergeStatus);
        responseObserver.onCompleted();
    }
}
