package com.bees360.job;

import static com.bees360.job.ReportJobNames.GENERATE_PDF_REPORT;
import static com.bees360.job.ReportJobNames.GENERATE_REPORT_DOCUMENT;

import com.bees360.codec.ByteStringTypeAdapter;
import com.bees360.job.registry.GenericCommandJob;
import com.bees360.report.ReportTypeEnum;
import com.bees360.report.job.ReportJobParameter;
import com.bees360.util.CollectionUtils;
import com.bees360.util.SecureTokens;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class ReportDocumentJob {

    private ReportDocumentJob() {}

    private static final String BASE_COMMAND = "report-doc-gen";
    private static final Integer RETRY_COUNT = 5;
    private static final Duration RETRY_DELAY = Duration.ofSeconds(5);
    private static final float RETRY_REFACTOR = 2.f;
    private static final String REPORT_XML_KEY_TEMPLATE = "project/%s/editor/%s.xml";

    public static Job getGenHtmlAndSchedulePdfJob(
            String groupKey,
            String groupType,
            ReportTypeEnum reportType,
            List<ReportJobParameter> inputParameters,
            String generatedBy) {
        if (CollectionUtils.isEmpty(inputParameters)) {
            throw new IllegalArgumentException(
                    String.format(
                            "The generation logic of report type '%s' in groupKey '%s' cannot be"
                                    + " missing input parameters.",
                            reportType, groupKey));
        }
        var checkJobId = SecureTokens.generateRandomHexToken();
        var verificationResourceKey = checkJobId + "-verification.json";
        var exportDataResourceKey = checkJobId + "-exportData.json";
        var htmlResourceKey = checkJobId + ".html";
        var summaryResourceKey = checkJobId + "-summary.json";
        var reportXmlResourceKey =
                String.format(REPORT_XML_KEY_TEMPLATE, groupKey, reportType.getCode());
        var reportDocumentCheckJob =
                getCommandJob(
                        checkJobId,
                        inputParameters,
                        List.of(
                                Pair.of("outputHtml", reportXmlResourceKey),
                                Pair.of("outputVerification", verificationResourceKey),
                                Pair.of("outputExportData", exportDataResourceKey),
                                Pair.of("outputRender", htmlResourceKey),
                                Pair.of("outputSummary", summaryResourceKey)));

        var pdfResourceKey = checkJobId + ".pdf";

        String generateJobId = String.join("-", "generate_report", groupKey, reportType.getKey());
        var htmlToPdfJob = HtmlToPdfJob.getInstance(htmlResourceKey, pdfResourceKey);
        var generateJob =
                GenericCommandJob.getInstance(
                        Map.of(
                                "groupKey",
                                groupKey,
                                "reportType",
                                reportType.getKey(),
                                "reportKey",
                                pdfResourceKey,
                                "summary",
                                summaryResourceKey,
                                "htmlKey",
                                htmlResourceKey,
                                "createdBy",
                                generatedBy),
                        htmlToPdfJob.toMessage(),
                        GENERATE_PDF_REPORT,
                        generateJobId);

        var job =
                GenericCommandJob.getInstance(
                        Map.of(
                                "groupKey",
                                groupKey,
                                "verificationResourceKey",
                                verificationResourceKey,
                                "groupType",
                                groupType,
                                "reportType",
                                reportType.getKey(),
                                "reportTypeCode",
                                String.valueOf(reportType.getCode()),
                                "exportData",
                                exportDataResourceKey,
                                "postProcessJobName",
                                GENERATE_PDF_REPORT,
                                "postProcessJobPayload",
                                ByteStringTypeAdapter.ByteStringUtil.toBase64(
                                        generateJob.getPayload())),
                        reportDocumentCheckJob.toMessage(),
                        GENERATE_REPORT_DOCUMENT,
                        checkJobId);
        return RetryableJob.of(job, RETRY_COUNT, RETRY_DELAY, RETRY_REFACTOR);
    }

    private static CommandJob getCommandJob(
            String jobId,
            List<ReportJobParameter> inputParameters,
            List<Pair<String, String>> outputParameters) {
        var jobBuilder =
                CommandJob.newBuilder()
                        .setId(jobId)
                        .setName(GENERATE_REPORT_DOCUMENT)
                        .addCommandComponent(BASE_COMMAND)
                        .setFilenameRule(
                                Message.CommandMessage.FilenameRule.USE_KEY_SUFFIX_AS_FILENAME)
                        .setTimeout(HtmlToPdfJob.PDF_EXECUTION_TIME);

        var parameterCount = new AtomicInteger(0);
        for (var inputParameter : inputParameters) {
            jobBuilder.addFileArgument(inputParameter.getContent(), inputParameter.getFilePath());
            jobBuilder.addCommandComponent(
                    StringUtils.join(
                            "--",
                            inputParameter.getJobParameter(),
                            "={" + parameterCount.getAndIncrement() + "}"));
        }

        for (var outputParameter : outputParameters) {
            jobBuilder.addOutputArgument(outputParameter.getRight());
            jobBuilder.addCommandComponent(
                    StringUtils.join(
                            "--",
                            outputParameter.getLeft(),
                            "={" + parameterCount.getAndIncrement() + "}"));
        }

        return jobBuilder.build();
    }
}
