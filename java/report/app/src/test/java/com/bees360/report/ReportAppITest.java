package com.bees360.report;

import static com.bees360.job.HtmlToPdfJobTest.generateHtmlResource;
import static com.bees360.job.ReportJobNames.GENERATE_REPORT_RESOURCE;
import static com.bees360.job.ReportJobNames.UNARCHIVE_AND_SAVE_REPORT;
import static com.bees360.report.listener.UnarchiveAndSaveCubicasaVideoTriggerJob.cubicasaVideoKeyPattern;
import static com.bees360.resource.ResourcePools.buildPrefixCompositeResourcePool;

import com.bees360.api.ApiStatus;
import com.bees360.api.InvalidArgumentException;
import com.bees360.event.EventDispatcher;
import com.bees360.event.EventPublisher;
import com.bees360.event.PostgresEventDispatcher;
import com.bees360.event.PostgresEventListener;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.config.PostgresEventDispatcherConfig;
import com.bees360.event.registry.CubicasaCompletedEvent;
import com.bees360.event.registry.JobCompleted;
import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.http.HttpClient;
import com.bees360.job.CommandJobTest;
import com.bees360.job.HtmlToPdfJobTest;
import com.bees360.job.JobScheduler;
import com.bees360.job.PdfCompressJob;
import com.bees360.job.command.GenericCommandChainJobExecutor;
import com.bees360.job.command.HtmlToPdfJobExecutor;
import com.bees360.job.registry.GenerateReportAndSaveChainJob;
import com.bees360.job.registry.GenericCommandJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SaveReportJob;
import com.bees360.job.registry.SaveReportResourceJob;
import com.bees360.job.registry.ZipReportJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.job.util.PdfCompressPostProcessor;
import com.bees360.map.util.InMemoryMap;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.report.config.GrpcReportGroupManagerConfig;
import com.bees360.report.config.GrpcReportManagerClientConfig;
import com.bees360.resource.HttpSafeResourceClient;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.resource.config.S3ResourceRepositoryFactoryConfig;
import com.bees360.resource.factory.S3ResourceRepositoryFactory;
import com.bees360.resource.util.TestResourcePool;
import com.bees360.util.Iterables;
import com.bees360.util.ListenableFutures;
import com.bees360.util.SecureTokens;
import com.google.common.base.Preconditions;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.protobuf.ByteString;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.io.IOException;
import java.net.URI;
import java.time.Duration;
import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@SpringBootTest(
        properties = {
            "grpc-client.resource.disabled=true",
            "resource.url.provider.disabled=true",
            "resource.pool.provider.disabled=true",
            "report.cubicasa.enable=true",
            "report.cubicasa.unarchive-video-zip.enable=true",
            "resource.pool.provider.disabled=true",
            "report.app.resource-key.remove-gs-prefix.enable=true"
        },
        webEnvironment = WebEnvironment.RANDOM_PORT)
@SpringJUnitConfig
@DirtiesContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ReportAppITest {

    @Import({
        ReportApp.class,
        GrpcReportManagerClientConfig.class,
        GrpcReportGroupManagerConfig.class,
        S3ResourceRepositoryFactoryConfig.class,
        PostgresEventDispatcherConfig.class,
        RabbitEventPublisher.class,
        CompressPdfReportOnReportGroupAddedTest.class,
        UnarchiveReportAndSaveReport.class,
        MockGenerateReportAndSaveChainJobExecutor.class,
    })
    @Configuration
    static class ITestConfig {

        @Bean
        public ResourcePool resourcePool(
                S3ResourceRepositoryFactory s3ResourceRepositoryFactory,
                @Value("${report.resource.uri}") String reportResourceURI,
                HttpClient httpClient) {
            var resourcePool = s3ResourceRepositoryFactory.get(URI.create(reportResourceURI));
            Map<String, ResourcePool> map = new LinkedHashMap<>();
            map.put("http:", new HttpSafeResourceClient(URI.create("http:/"), httpClient));
            map.put("https:", new HttpSafeResourceClient(URI.create("https:/"), httpClient));
            map.put("", resourcePool);
            return buildPrefixCompositeResourcePool(map.entrySet());
        }

        @Bean
        public ResourceUrlProvider resourceUrlProvider(ResourcePool resourcePool) {
            Preconditions.checkArgument(
                    resourcePool.isResourceUrlProvider(),
                    "Resource url provider should be provide");
            return resourcePool.asResourceUrlProvider();
        }

        @Bean
        public ReportGroupManager grpcReportGroupManager(ReportGroupManager reportGroupManager) {
            return reportGroupManager;
        }

        @Bean
        TestReportManager testReportManager(
                ReportManager grpcReportManagerClient,
                ResourcePool testResourcePool,
                Supplier<String> summaryVersionSupplier) {
            return new TestReportManager(
                    grpcReportManagerClient, testResourcePool, summaryVersionSupplier);
        }

        @Bean
        TestReportGroupManager testReportGroupManager(
                ReportManager grpcReportManagerClient,
                ReportGroupManager grpcReportGroupManagerClient,
                ResourcePool testResourcePool) {
            return new TestReportGroupManager(
                    grpcReportManagerClient, grpcReportGroupManagerClient, testResourcePool);
        }

        @Bean
        TestReportProcessor testReportProcessor(
                ReportProcessor reportProcessor,
                ReportGroupManager grpcReportGroupManagerClient,
                ResourcePool testResourcePool,
                JooqReportResourceManager jooqReportResourceManager) {
            return new TestReportProcessor(
                    reportProcessor,
                    grpcReportGroupManagerClient,
                    testResourcePool,
                    jooqReportResourceManager);
        }

        @Bean
        EventDispatcher eventDispatcher(RabbitEventDispatcher rabbitEventDispatcher) {
            return rabbitEventDispatcher;
        }

        @Bean
        GenericCommandChainJobExecutor generatePdfByHtml(
                JobScheduler jobScheduler, ResourcePool resourcePool) {
            var htmlToPdfJobExecutor =
                    new HtmlToPdfJobExecutor(
                            resourcePool,
                            GENERATE_REPORT_RESOURCE,
                            null,
                            List.of(new PdfCompressPostProcessor()));
            return new GenericCommandChainJobExecutor(
                    jobScheduler,
                    htmlToPdfJobExecutor,
                    new InMemoryMap<>(),
                    job -> List.of(getSaveReportResourceJob(job)));
        }

        private Object getSaveReportResourceJob(GenericCommandJob projectCommandJob) {
            var metadata = projectCommandJob.getMetadata();
            var reportId = metadata.get("reportId");
            var reportType = metadata.get("type");
            var reportKey = metadata.get("reportKey");
            return SaveReportResourceJob.newBuilder()
                    .setReportId(reportId)
                    .setType(Integer.parseInt(reportType))
                    .setResourceKey(reportKey)
                    .build();
        }
    }

    public ReportAppITest(
            @Autowired PostgresEventDispatcher postgresEventDispatcher,
            @Autowired EventPublisher eventPublisher) {
        postgresEventDispatcher.enlist(new PostgresEventListener("publish_event", eventPublisher));
    }

    @Autowired private ResourcePool resourcePool;

    @Autowired
    @Qualifier("grpcReportManager")
    private ReportManager reportManager;

    @Autowired
    @Qualifier("grpcReportGroupManager")
    private ReportGroupManager reportGroupManager;

    @Autowired private TestReportManager testReportManager;

    @Autowired private TestReportGroupManager testReportGroupManager;

    @Autowired private TestReportProcessor testReportProcessor;

    @Autowired private EventPublisher eventPublisher;
    @Autowired private JobScheduler jobScheduler;

    @Test
    void createThenGetWhenCompressJobCompleted() throws InterruptedException {
        String reportKey = Instant.now().toEpochMilli() + SecureTokens.generateRandomHexToken();
        var resource = TestResourcePool.createRandomResource();
        resourcePool.put(reportKey, resource);

        var userId = RandomStringUtils.randomAlphanumeric(6);
        var reportType = RandomReport.randomReportType();
        var htmlKey = RandomReport.getTestHtmlResourceKey(resourcePool);
        var summary = RandomReport.getTestSummary();
        var report = reportManager.create(reportType, reportKey, summary, htmlKey, userId);
        Assertions.assertNotNull(report);
        reportGroupManager.addReportToGroup("100", "GROUP_PROJECT", report.getId(), userId);

        waitJobExecute();

        var finishedReport = reportManager.get(report.getId());

        Assertions.assertNotNull(finishedReport.getResourceUrl());
        Assertions.assertTrue(finishedReport.getResourceUrl().containsKey(Type.COMPRESSED));
        Assertions.assertNotNull(finishedReport.getResourceUrl().get(Type.COMPRESSED));
    }

    private void waitJobExecute() throws InterruptedException {
        Thread.sleep(Duration.ofSeconds(4).toMillis());
    }

    @Test
    public void createReportTypeTest() {
        checkCreateReportType("18", "18");
        checkCreateReportType("PIR", "15");
        checkCreateReportType("Roof-only Underwriting Report", "17");
    }

    @Test
    public void createReportTypeNotExistsTest() {
        Assertions.assertThrows(
                InvalidArgumentException.class, () -> checkCreateReportType("0", "0"));
        Assertions.assertThrows(
                InvalidArgumentException.class, () -> checkCreateReportType("PIRRR", "15"));
        Assertions.assertThrows(
                InvalidArgumentException.class,
                () -> checkCreateReportType("Roof-only Underwriting Test Report", "17"));
    }

    private void checkCreateReportType(String reportType, String reportTypeId) {
        String reportKey = Instant.now().toEpochMilli() + SecureTokens.generateRandomHexToken();
        var resource = TestResourcePool.createRandomResource();
        resourcePool.put(reportKey, resource);
        var userId = RandomStringUtils.randomAlphanumeric(6);
        var htmlKey = RandomReport.getTestHtmlResourceKey(resourcePool);
        var summary = RandomReport.getTestSummary();
        var report = reportManager.create(reportType, reportKey, summary, htmlKey, userId);
        var createByKeyReport = reportManager.get(report.getId());

        Assertions.assertEquals(reportTypeId, createByKeyReport.getType());
    }

    @Test
    void createThenGeneratePdfResourceTest() throws InterruptedException {
        var userId = RandomStringUtils.randomAlphanumeric(6);
        var summary = RandomReport.getTestSummary();
        var reportType = RandomReport.randomReportType();

        String htmlKey = "test_report.html";
        Resource htmlResource = HtmlToPdfJobTest.generateHtmlResource("html_pdf_test.html");
        resourcePool.put("test_logo.jpg", CommandJobTest.createImageResource(40, 40));
        resourcePool.put("test_direction.jpg", CommandJobTest.createImageResource(60, 60));
        resourcePool.put("test_origin.jpg", CommandJobTest.createImageResource(4000, 3000));
        resourcePool.put("test_crop.jpg", CommandJobTest.createImageResource(400, 300));
        resourcePool.put(htmlKey, htmlResource);

        var report = reportManager.create(reportType, null, summary, htmlKey, userId);

        Thread.sleep(Duration.ofSeconds(10).toMillis());

        var savedReport = reportManager.findById(report.getId());

        Assertions.assertTrue(savedReport.getResource().containsKey(Type.ORIGIN));
    }

    @Test
    void testRemoveGsPrefixResourceKey() throws InterruptedException {
        var userId = RandomStringUtils.randomAlphanumeric(6);
        var summary = RandomReport.getTestSummary();
        var reportType = RandomReport.randomReportType();

        String reportKey = RandomStringUtils.randomAlphanumeric(6);
        Resource resource = generateHtmlResource("html_pdf_test.html");
        resourcePool.put(reportKey, resource);

        var report = reportManager.create(reportType, "gs://" + reportKey, summary, null, userId);

        var savedReport = reportManager.findById(report.getId());
        assert savedReport != null;
        Assertions.assertTrue(
                Objects.requireNonNull(savedReport.getResource()).containsKey(Type.ORIGIN));
    }

    @Test
    public void testSaveAll() {
        testReportManager.saveAll();
    }

    @Test
    public void testSaveThenDelete() {
        testReportManager.saveThenDelete();
    }

    @Test
    public void testSaveMultipleResource() {
        testReportManager.saveMultipleResource();
    }

    @Test
    public void testCreateThenUpdateStatus() {
        testReportManager.createThenUpdateStatus();
    }

    @Test
    public void testCreateThenValidateSummary() {
        testReportManager.createThenValidateSummary();
    }

    @Test
    public void testResourceKey() {
        testReportManager.resourceKeyTest();
    }

    @Test
    public void testFindGroupReportByTypeIdAndKey() {
        testReportGroupManager.findGroupReportByTypeIdAndKey();
    }

    @Test
    public void testCreateGroupReportWithMultipleResource() {
        testReportGroupManager.createGroupReportWithMultipleResource();
    }

    @Test
    public void testCreateGroupReportThenFind() {
        testReportGroupManager.createGroupReportThenFind();
    }

    @Test
    public void testCreateGroupReportWithIllegalParameterShouldThrow() {
        testReportGroupManager.createGroupReportWithIllegalParameterShouldThrow();
    }

    @Test
    public void testSaveThenFindReportInGroup() {
        testReportGroupManager.saveThenFindReportInGroup();
    }

    @Test
    public void testAddReportToGroupWithIllegalParameterShouldThrow() {
        testReportGroupManager.addReportToGroupWithIllegalParameterShouldThrow();
    }

    @Test
    public void testFindGroupKey() {
        testReportGroupManager.findGroupKeyTest();
    }

    @Test
    public void testGroupSaveThenDelete() {
        testReportGroupManager.saveThenDelete();
    }

    @Test
    public void testDeleteByIllegalParameterShouldThrow() {
        testReportGroupManager.deleteByIllegalParameterShouldThrow();
    }

    @Test
    public void testPermanentlyDeleteShouldUnableToQuery() {
        testReportGroupManager.permanentlyDeleteShouldUnableToQuery();
    }

    @Test
    public void testPermanentlyDeleteByIllegalParameterShouldThrow() {
        testReportGroupManager.permanentlyDeleteByIllegalParameterShouldThrow();
    }

    @Test
    public void testSaveReportAndMergeReport() {
        testReportProcessor.saveReportAndMergeReport();
    }

    @Test
    public void testSaveCubicasaOnCubicasaComplete() {
        var reportKey = RandomStringUtils.randomAlphanumeric(8);
        var videoZip = RandomStringUtils.randomAlphanumeric(8) + ".zip";
        resourcePool.put(reportKey, Resource.of(ByteString.EMPTY));
        resourcePool.put(videoZip, Resource.of(ByteString.EMPTY));

        var cubicasaComplete = new CubicasaCompletedEvent();
        var projectId = RandomStringUtils.randomNumeric(8);
        cubicasaComplete.setReportUrl(reportKey);
        cubicasaComplete.setProjectId(projectId);
        cubicasaComplete.setVideoZipUrl(videoZip);
        cubicasaComplete.setOperationTime(Instant.now().toEpochMilli());
        eventPublisher.publish(cubicasaComplete);

        waitComplete();
        var reportMap =
                Iterables.toStream(
                                reportGroupManager.findAllReportInGroup(projectId, "GROUP_PROJECT"))
                        .collect(Collectors.toMap(Report::getType, Function.identity()));
        var cubicasaReportType = "40";
        var cubicasaVideoZipType = "47";
        var createdBy = "10000";
        assertReportMatch(
                reportMap.get(cubicasaReportType), cubicasaReportType, reportKey, createdBy);
        assertReportMatch(
                reportMap.get(cubicasaVideoZipType), cubicasaVideoZipType, videoZip, createdBy);
    }

    @Test
    public void testUnarchiveSaveCubicasaOnCubicasaComplete() {
        var videoZip = RandomStringUtils.randomAlphanumeric(8) + ".zip";
        resourcePool.put(videoZip, Resource.of(ByteString.EMPTY));

        var cubicasaComplete = new CubicasaCompletedEvent();
        var projectId = RandomStringUtils.randomNumeric(8);
        cubicasaComplete.setProjectId(projectId);
        cubicasaComplete.setVideoZipUrl(videoZip);
        cubicasaComplete.setOperationTime(Instant.now().toEpochMilli());
        eventPublisher.publish(cubicasaComplete);

        waitComplete();
        var reportMap =
                Iterables.toStream(
                                reportGroupManager.findAllReportInGroup(projectId, "GROUP_PROJECT"))
                        .collect(Collectors.toMap(Report::getType, Function.identity()));
        var cubicasaVideoType = "48";
        var createdBy = "10000";
        var videoKey = cubicasaVideoKeyPattern.apply(projectId);
        assertReportMatch(reportMap.get(cubicasaVideoType), cubicasaVideoType, videoKey, createdBy);
    }

    @Test
    public void testZipAndSaveReport() throws ExecutionException, InterruptedException {
        var videoZip = RandomStringUtils.randomAlphanumeric(8) + ".zip";
        var groupKey = RandomStringUtils.randomNumeric(8);
        var resourceUrl = RandomStringUtils.randomAlphanumeric(8);
        var filename = "filename";
        var reportType = "HOSTASZ";
        var groupType = "GROUP_PROJECT";

        resourcePool.put(videoZip, Resource.of(ByteString.EMPTY));

        var cubicasaComplete =
                ZipReportJob.newBuilder()
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .setReportType(reportType)
                        .setReportUrl2Filenames(Map.of(resourceUrl, filename))
                        .setZipKey(videoZip)
                        .build();

        var job = jobScheduler.schedule(JobPayloads.encode(cubicasaComplete));
        if (!job.isDone()) {
            job.get();
        }

        waitComplete();
        var reportMap =
                Iterables.toStream(reportGroupManager.findAllReportInGroup(groupKey, groupType))
                        .collect(Collectors.toMap(Report::getType, Function.identity()));
        var createdBy = "10000";
        var type = "49";
        assertReportMatch(reportMap.get(type), type, videoZip, createdBy);
    }

    @Test
    public void testSaveReportSummaryByJsonString() {
        var reportType = ReportTypeEnum.FUR.getKey();
        var groupType = "GROUP_PROJECT";

        String reportKey = Instant.now().toEpochMilli() + SecureTokens.generateRandomHexToken();
        var resource = TestResourcePool.createRandomResource();
        resourcePool.put(reportKey, resource);

        var groupKey = RandomStringUtils.randomNumeric(8);
        var summaryString = "{\"testSummary\":\"testSummary\",\"updated_at\":1741077296211}";
        var saveReportJob =
                SaveReportJob.newBuilder()
                        .setCreatedBy("10000")
                        .setReportKey(reportKey)
                        .setSummaryJsonString(summaryString)
                        .setReportType(reportType)
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .build();
        var future = jobScheduler.schedule(JobPayloads.encode(saveReportJob));
        ListenableFutures.getUnchecked(future);

        waitComplete();
        var reports =
                Iterables.toList(
                        reportGroupManager.findReportInGroup(
                                groupKey, groupType, reportType, null));
        Assertions.assertEquals(1, reports.size());
        Assertions.assertEquals(
                JsonParser.parseString(summaryString),
                JsonParser.parseString(reports.get(0).getSummary().getSummary()));
    }

    @Test
    public void testSaveReportSummaryByJsonStringByBlankShouldEmpty() {
        var reportType = ReportTypeEnum.FUR.getKey();
        var groupType = "GROUP_PROJECT";

        String reportKey = Instant.now().toEpochMilli() + SecureTokens.generateRandomHexToken();
        var resource = TestResourcePool.createRandomResource();
        resourcePool.put(reportKey, resource);

        var groupKey = RandomStringUtils.randomNumeric(8);
        var summaryString = StringUtils.EMPTY;
        var saveReportJob =
                SaveReportJob.newBuilder()
                        .setCreatedBy("10000")
                        .setReportKey(reportKey)
                        .setSummaryJsonString(summaryString)
                        .setReportType(reportType)
                        .setGroupKey(groupKey)
                        .setGroupType(groupType)
                        .build();
        var future = jobScheduler.schedule(JobPayloads.encode(saveReportJob));
        ListenableFutures.getUnchecked(future);

        waitComplete();
        var reports =
                Iterables.toList(
                        reportGroupManager.findReportInGroup(
                                groupKey, groupType, reportType, null));
        Assertions.assertEquals(1, reports.size());
        Assertions.assertEquals(
                reports.get(0).getSummary().getSummary(), new JsonObject().toString());
    }

    private void assertReportMatch(
            Report actualReport,
            String exceptType,
            String exceptReportKey,
            String exceptCreatedBy) {
        Assertions.assertNotNull(actualReport, "The actual report should not null.");
        Assertions.assertEquals(
                exceptType, actualReport.getType(), "The report type should match.");
        Assertions.assertEquals(
                exceptReportKey,
                Objects.requireNonNull(actualReport.getResourceKey()).get(Type.ORIGIN),
                "The report key should match.");
        Assertions.assertEquals(
                exceptCreatedBy, actualReport.getCreatedBy(), "The report createdBy should match.");
    }

    private void waitComplete() {
        try {
            Thread.sleep(4000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IllegalStateException(e);
        }
    }

    static class CompressPdfReportOnReportGroupAddedTest
            extends AbstractNamedEventListener<ReportGroupAdded> {

        private final EventPublisher eventPublisher;

        private final ResourcePool resourcePool;

        CompressPdfReportOnReportGroupAddedTest(
                EventPublisher eventPublisher, ResourcePool resourcePool) {
            this.eventPublisher = eventPublisher;
            this.resourcePool = resourcePool;
        }

        @Override
        public void handle(ReportGroupAdded event) throws IOException {
            var reportId = event.getReportId();
            String compressKey = reportId + "-" + Type.COMPRESSED_VALUE;

            var resource = TestResourcePool.createRandomResource();
            resourcePool.put(compressKey, resource);

            var jobCompleted = new JobCompleted(PdfCompressJob.JOB_NAME, reportId, ApiStatus.OK);
            eventPublisher.publish(jobCompleted);
        }
    }

    static class UnarchiveReportAndSaveReport extends AbstractJobExecutor<GenericCommandJob> {

        private final ReportGroupManager reportGroupManager;
        private final ResourcePool resourcePool;

        UnarchiveReportAndSaveReport(
                ReportGroupManager reportGroupManager, ResourcePool resourcePool) {
            this.reportGroupManager = reportGroupManager;
            this.resourcePool = resourcePool;
        }

        @Override
        public String getName() {
            return UNARCHIVE_AND_SAVE_REPORT;
        }

        @Override
        public void handle(GenericCommandJob event) {
            var metadata = event.getMetadata();
            var groupKey = metadata.get("groupKey");
            var groupType = metadata.get("groupType");
            var reportType = metadata.get("reportType");
            var reportKey = metadata.get("reportKey");
            var createdBy = metadata.get("createdBy");
            // mock generate report and uploaded to resource pool
            resourcePool.put(reportKey, Resource.of(ByteString.EMPTY));
            reportGroupManager.createGroupReport(
                    groupKey,
                    groupType,
                    reportType,
                    reportKey,
                    new JsonObject().toString(),
                    null,
                    createdBy);
        }
    }

    static class MockGenerateReportAndSaveChainJobExecutor
            extends AbstractJobExecutor<GenerateReportAndSaveChainJob> {

        private final ReportGroupManager reportGroupManager;
        private final ResourcePool resourcePool;

        MockGenerateReportAndSaveChainJobExecutor(
                ReportGroupManager reportGroupManager, ResourcePool resourcePool) {
            this.reportGroupManager = reportGroupManager;
            this.resourcePool = resourcePool;
        }

        @Override
        public void handle(GenerateReportAndSaveChainJob event) {
            var metadata = event.getMetadata();
            var groupKey = metadata.get("groupKey");
            var groupType = metadata.get("groupType");
            var reportType = metadata.get("reportType");
            var reportKey = metadata.get("reportKey");
            var createdBy = metadata.get("createdBy");
            // mock generate report and uploaded to resource pool
            resourcePool.put(reportKey, Resource.of(ByteString.EMPTY));
            reportGroupManager.createGroupReport(
                    groupKey,
                    groupType,
                    reportType,
                    reportKey,
                    new JsonObject().toString(),
                    null,
                    createdBy);
        }
    }
}
