package com.bees360.report.listener;

import static com.bees360.report.GroupType.GROUP_PROJECT;

import com.bees360.event.registry.CubicasaCompletedEvent;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.ReportJobNames;
import com.bees360.job.ReportJobs;
import com.bees360.job.RetryableJob;
import com.bees360.job.UnarchiveFileJob;
import com.bees360.job.registry.GenericCommandJob;
import com.bees360.job.util.EventTriggeredJob;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

public class UnarchiveAndSaveCubicasaVideoTriggerJob
        extends EventTriggeredJob<CubicasaCompletedEvent> {
    private static final String CUBICASE_VIDEO_TYPE = "CUBIV";
    private static final String CUBICASE_VIDEO_FILENAME_IN_ZIP = "video.m4v";
    public static final Function<String, String> cubicasaVideoKeyPattern =
            (projectId) ->
                    String.format(
                            "project/%s/cubicasa/%s", projectId, CUBICASE_VIDEO_FILENAME_IN_ZIP);
    private final String createdBy;
    private final Integer retryCount;
    private final Duration retryDelay;
    private final Float retryDelayIncreaseFactor;

    public UnarchiveAndSaveCubicasaVideoTriggerJob(
            JobScheduler jobScheduler,
            String createdBy,
            Integer retryCount,
            Duration retryDelay,
            Float retryDelayIncreaseFactor) {
        super(jobScheduler);
        this.createdBy = createdBy;
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.retryDelayIncreaseFactor = retryDelayIncreaseFactor;
    }

    @Override
    protected boolean filter(CubicasaCompletedEvent event) {
        return Objects.nonNull(event.getVideoZipUrl()) && Objects.nonNull(event.getProjectId());
    }

    @Override
    protected Job convert(CubicasaCompletedEvent event) {
        var videoZipUrl = event.getVideoZipUrl();
        var projectId = event.getProjectId();
        var cubicasaVideoKey = cubicasaVideoKeyPattern.apply(projectId);
        var generalCommandJob = builderJob(videoZipUrl, cubicasaVideoKey, projectId);
        return RetryableJob.of(
                Job.ofPayload(generalCommandJob), retryCount, retryDelay, retryDelayIncreaseFactor);
    }

    private Job builderJob(String videoZipUrl, String cubicasaVideoKey, String projectId) {
        // get unarchive file command job
        var unarchiveFileJob =
                UnarchiveFileJob.getInstance(
                        videoZipUrl, Map.of(cubicasaVideoKey, CUBICASE_VIDEO_FILENAME_IN_ZIP));
        // get report metadata
        var reportMetadata =
                ReportJobs.getReportMetadata(
                        projectId,
                        GROUP_PROJECT.name(),
                        CUBICASE_VIDEO_TYPE,
                        cubicasaVideoKey,
                        createdBy);
        return GenericCommandJob.getInstance(
                reportMetadata,
                unarchiveFileJob.toMessage(),
                ReportJobNames.UNARCHIVE_AND_SAVE_REPORT);
    }
}
