package com.bees360.report;

import com.bees360.event.registry.JobCompleted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.job.PdfCompressJob;
import com.bees360.report.Message.ReportMessage.Resource.Type;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class SaveResourceWhenCompressCompleted extends AbstractNamedEventListener<JobCompleted> {

    private final ReportResourceManager reportResourceManager;

    public SaveResourceWhenCompressCompleted(ReportResourceManager reportResourceManager) {
        this.reportResourceManager = reportResourceManager;
    }

    @Override
    public void handle(JobCompleted event) throws IOException {
        if (!event.getName().equals(PdfCompressJob.JOB_NAME)) {
            return;
        }
        var reportId = event.getId();
        var reportResourceKey = reportId + "-" + Type.COMPRESSED_VALUE;

        reportResourceManager.save(reportId, Type.COMPRESSED, reportResourceKey);
    }
}
