package com.bees360.report.listener;

import com.bees360.event.registry.ReportAdded;
import com.bees360.job.HtmlToPdfJob;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.ReportJobNames;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.GenericCommandJob;
import com.bees360.job.util.EventTriggeredJob;
import com.bees360.report.Message.ReportMessage;
import com.bees360.report.ReportProvider;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.MapUtils;

import java.time.Duration;
import java.util.Map;

@Log4j2
public class GeneratePdfByHtmlOnReportAdded extends EventTriggeredJob<ReportAdded> {

    private final ReportProvider reportProvider;

    public GeneratePdfByHtmlOnReportAdded(
            JobScheduler jobScheduler, ReportProvider reportProvider) {
        super(jobScheduler);
        this.reportProvider = reportProvider;
        log.info("Created '{}(reportProvider={})", this, this.reportProvider);
    }

    @Override
    protected Job convert(ReportAdded event) {
        var reportId = event.getId();
        var report = reportProvider.get(reportId);
        var resources = report.getResourceUrl();

        String pdfResourceKey = reportId + "-" + ReportMessage.Resource.Type.ORIGIN_VALUE;
        var htmlToPdfJob =
                HtmlToPdfJob.getInstance(
                        resources.get(ReportMessage.Resource.Type.HTML), pdfResourceKey);
        var job =
                GenericCommandJob.getInstance(
                        Map.of(
                                "reportId",
                                reportId,
                                "type",
                                ReportMessage.Resource.Type.ORIGIN_VALUE + "",
                                "reportKey",
                                pdfResourceKey),
                        htmlToPdfJob.toMessage(),
                        ReportJobNames.GENERATE_REPORT_RESOURCE);
        log.info("Successfully convert assignment event to job :{}", job);
        return RetryableJob.of(job, 3, Duration.ofMinutes(1), 2F);
    }

    @Override
    public boolean filter(ReportAdded event) {
        log.info("Receive event :{}", event);
        var report = reportProvider.get(event.getId());
        var resources = report.getResource();
        return MapUtils.isNotEmpty(resources)
                && resources.containsKey(ReportMessage.Resource.Type.HTML)
                && !resources.containsKey(ReportMessage.Resource.Type.ORIGIN);
    }
}
