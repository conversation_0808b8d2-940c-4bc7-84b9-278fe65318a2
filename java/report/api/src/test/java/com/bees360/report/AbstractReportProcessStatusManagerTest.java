package com.bees360.report;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.bees360.report.Message.ReportProcessStatus.Status;
import com.bees360.report.Message.ReportProcessStatus.Type;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;

import java.util.Arrays;
import java.util.List;

@Log4j2
public class AbstractReportProcessStatusManagerTest {
    protected static final RandomStringUtils randomStringUtils = RandomStringUtils.secure();
    protected static final List<String> GROUP_TYPES = List.of("GROUP_PROJECT", "GROUP_BUNDLE");
    protected static final List<Type> PROCESS_TYPES =
            Arrays.stream(Type.values())
                    .filter(
                            t ->
                                    !Type.UNKNOWN_PROCESS_TYPE.equals(t)
                                            && !Type.UNRECOGNIZED.equals(t))
                    .toList();
    protected static final List<Status> PROCESS_STATUSES =
            Arrays.stream(Status.values())
                    .filter(
                            s ->
                                    !Status.UNKNOWN_PROCESS_STATUS.equals(s)
                                            && !Status.UNRECOGNIZED.equals(s))
                    .toList();

    protected final ReportProcessStatusManager reportProcessStatusManager;

    public AbstractReportProcessStatusManagerTest(
            ReportProcessStatusManager reportProcessStatusManager) {
        this.reportProcessStatusManager = reportProcessStatusManager;
    }

    public void testUpdateReportProcessStatus() {
        var groupKey = randomId();
        var groupType = randomGroupType();
        var reportType = randomReportType();
        var type = randomProcessType();
        var status = randomProcessStatus();
        var updatedBy = randomId();
        var comment = randomStringUtils.nextAlphabetic(24);
        var savedStatus =
                assertDoesNotThrow(
                        () ->
                                reportProcessStatusManager.updateReportProcessStatus(
                                        groupKey,
                                        groupType,
                                        reportType,
                                        type,
                                        status,
                                        updatedBy,
                                        comment));
        assertNotNull(savedStatus);
        assertEquals(groupKey, savedStatus.getGroupKey());
        assertEquals(groupType, savedStatus.getGroupType());
        assertEquals(reportType, savedStatus.getReportType());
        assertEquals(type, savedStatus.getType());
        assertEquals(status, savedStatus.getStatus());
        assertEquals(updatedBy, savedStatus.getUpdatedBy());
        assertEquals(comment, savedStatus.getComment().getValue());

        var savedStatuses =
                reportProcessStatusManager.getReportProcessStatus(
                        groupKey, groupType, reportType, type);
        assertNotNull(savedStatuses);
        assertEquals(1, savedStatuses.size());
        savedStatus = savedStatuses.get(0);
        assertEquals(groupKey, savedStatus.getGroupKey());
        assertEquals(groupType, savedStatus.getGroupType());
        assertEquals(reportType, savedStatus.getReportType());
        assertEquals(type, savedStatus.getType());
        assertEquals(status, savedStatus.getStatus());
        assertEquals(updatedBy, savedStatus.getUpdatedBy());
        assertEquals(comment, savedStatus.getComment().getValue());

        savedStatuses =
                reportProcessStatusManager.getReportProcessStatus(groupKey, groupType, null, type);
        assertNotNull(savedStatuses);
        assertEquals(1, savedStatuses.size());
        savedStatus = savedStatuses.get(0);
        assertEquals(groupKey, savedStatus.getGroupKey());
        assertEquals(groupType, savedStatus.getGroupType());
        assertEquals(reportType, savedStatus.getReportType());
        assertEquals(type, savedStatus.getType());
        assertEquals(status, savedStatus.getStatus());
        assertEquals(updatedBy, savedStatus.getUpdatedBy());
        assertEquals(comment, savedStatus.getComment().getValue());

        savedStatuses =
                reportProcessStatusManager.getReportProcessStatus(groupKey, groupType, null, null);
        assertNotNull(savedStatuses);
        assertEquals(1, savedStatuses.size());
        savedStatus = savedStatuses.get(0);
        assertEquals(groupKey, savedStatus.getGroupKey());
        assertEquals(groupType, savedStatus.getGroupType());
        assertEquals(reportType, savedStatus.getReportType());
        assertEquals(type, savedStatus.getType());
        assertEquals(status, savedStatus.getStatus());
        assertEquals(updatedBy, savedStatus.getUpdatedBy());
        assertEquals(comment, savedStatus.getComment().getValue());
    }

    protected Status randomProcessStatus() {
        return PROCESS_STATUSES.get(RandomUtils.secure().randomInt(0, PROCESS_STATUSES.size()));
    }

    protected Type randomProcessType() {
        return PROCESS_TYPES.get(RandomUtils.secure().randomInt(0, PROCESS_TYPES.size()));
    }

    protected String randomGroupType() {
        return GROUP_TYPES.get(RandomUtils.secure().randomInt(0, GROUP_TYPES.size()));
    }

    protected String randomReportType() {
        return String.valueOf(
                ReportTypeEnum.values()[
                        RandomUtils.secure().randomInt(0, ReportTypeEnum.values().length)]
                        .getCode());
    }

    protected String randomId() {
        return "1" + randomStringUtils.nextNumeric(10);
    }
}
