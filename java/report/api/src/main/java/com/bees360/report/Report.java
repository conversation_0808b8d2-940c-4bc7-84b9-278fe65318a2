package com.bees360.report;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Entity;
import com.bees360.api.Proto;
import com.bees360.report.Message.ReportMessage;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.resource.Resource;
import com.bees360.util.DateTimes;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.Map;

public interface Report extends Entity, Proto<ReportMessage> {

    String NAMESPACE = "report";

    @Override
    default String getNamespace() {
        return NAMESPACE;
    }

    @Nonnull
    String getId();

    /** The type of report, for example: `Premium Damage Assessment Report` */
    @Nullable
    String getType();

    @Nullable
    ReportMessage.Status getStatus();

    @Nullable
    @Deprecated
    Map<Type, String> getResourceUrl();

    /** Added for refactoring, please use url. */
    @Nullable
    @Deprecated
    Map<Type, String> getResourceKey();

    @Nullable
    @Deprecated
    Map<Type, ? extends Resource> getResource();

    @Nullable
    Iterable<? extends ReportResource> getResources();

    ReportSummary getSummary();

    @Nullable
    String getCreatedBy();

    @Nullable
    Instant getCreatedAt();

    @Nullable
    default String getName() {
        if (StringUtils.isEmpty(getType())) {
            return null;
        }

        return StringUtils.isNumeric(getType())
                ? ReportTypeEnum.valueOf(Integer.parseInt(getType())).getDisplay()
                : ReportTypeEnum.valueOf(getType()).getDisplay();
    }

    @Override
    default ReportMessage toMessage() {
        var builder = ReportMessage.newBuilder();
        acceptIfNotNull(builder::setName, getName());
        acceptIfNotNull(builder::setId, getId());
        acceptIfNotNull(builder::setType, getType());
        acceptIfNotNull(builder::setStatus, getStatus());
        acceptIfNotNull(builder::setSummary, getSummary(), ReportSummary::toMessage);
        acceptIfNotNull(builder::setCreatedBy, getCreatedBy());
        acceptIfNotNull(builder::setCreatedAt, getCreatedAt(), DateTimes::toTimestamp);
        acceptIfNotNull(builder::addResource, getResources(), ReportResource::toMessage);
        return builder.build();
    }
}
