package com.bees360.report;

import jakarta.annotation.Nullable;

import java.util.List;

public interface ReportProcessStatusProvider {

    /**
     * Get report merge status
     *
     * @param groupKey Group Key
     * @param groupType Group Type
     * @param reportTypeCode Report Type Code
     * @return Report merge status information
     */
    @Nullable
    List<Message.ReportProcessStatus> getReportProcessStatus(
            String groupKey,
            String groupType,
            @Nullable String reportTypeCode,
            @Nullable Message.ReportProcessStatus.Type type);
}
