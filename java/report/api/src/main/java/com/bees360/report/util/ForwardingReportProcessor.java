package com.bees360.report.util;

import com.bees360.report.ReportProcessor;
import com.google.common.collect.ForwardingObject;
import com.google.common.util.concurrent.ListenableFuture;

public abstract class ForwardingReportProcessor extends ForwardingObject
        implements ReportProcessor {

    @Override
    protected abstract ReportProcessor delegate();

    @Override
    public ListenableFuture<Void> mergeReport(
            String groupKey,
            String groupType,
            String mergeReportType,
            Iterable<String> resourceIds,
            String mergedBy) {
        return delegate().mergeReport(groupKey, groupType, mergeReportType, resourceIds, mergedBy);
    }
}
