package com.bees360.report.grpc;

import com.bees360.report.Message.ReportMessage;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.report.Report;
import com.bees360.report.ReportResource;
import com.bees360.report.ReportSummary;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourceMetadata;
import com.bees360.util.DateTimes;

import jakarta.annotation.Nullable;

import lombok.NonNull;

import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

class ProtobufReport implements Report {

    private final ReportMessage message;

    private final Function<String, Resource> uriResourceProvider;

    private Map<Type, ? extends Resource> resourceCache;

    private Iterable<? extends ReportResource> reportResources;

    public ProtobufReport(
            @NonNull ReportMessage message,
            @NonNull Function<String, Resource> uriResourceProvider) {
        this.message = message;
        this.uriResourceProvider = uriResourceProvider;
    }

    @Override
    public String getId() {
        return message.getId();
    }

    @Override
    public String getName() {
        return message.getName();
    }

    @Override
    public String getType() {
        return message.getType();
    }

    @Override
    public ReportMessage.Status getStatus() {
        return message.getStatus();
    }

    @Nullable
    @Override
    public Map<Type, String> getResourceUrl() {
        return message.getResourceList().stream()
                .collect(
                        Collectors.toMap(
                                ReportMessage.Resource::getType,
                                ReportMessage.Resource::getUrl,
                                (k1, k2) -> k1));
    }

    @Nullable
    @Override
    public Map<Type, String> getResourceKey() {
        return message.getResourceList().stream()
                .collect(
                        Collectors.toMap(
                                ReportMessage.Resource::getType,
                                ReportMessage.Resource::getResourceKey,
                                (k1, k2) -> k1));
    }

    @Override
    public Map<Type, ? extends Resource> getResource() {
        if (resourceCache == null) {
            resourceCache =
                    message.getResourceList().stream()
                            .collect(
                                    Collectors.toMap(
                                            ReportMessage.Resource::getType,
                                            this::getResource,
                                            (k1, k2) -> k1));
        }
        return resourceCache;
    }

    @Nullable
    @Override
    public Iterable<? extends ReportResource> getResources() {
        if (reportResources == null) {
            reportResources =
                    message.getResourceList().stream()
                            .map(message -> new ProtoReportResource(message, getResource(message)))
                            .collect(Collectors.toList());
        }
        return reportResources;
    }

    private Resource getResource(ReportMessage.Resource reportResource) {
        var eTag = reportResource.getETag();
        var lastModified = DateTimes.toInstant(reportResource.getLastModified());
        var metadata =
                ResourceMetadata.newBuilder()
                        .setContentType(reportResource.getContentType())
                        .setETag(eTag)
                        .setContentLength(reportResource.getContentLength())
                        .setLastModified(lastModified)
                        .build();
        return Resource.of(
                () -> uriResourceProvider.apply(reportResource.getUrl()).open(), metadata);
    }

    @Override
    public ReportSummary getSummary() {
        return new ProtobufReportSummary(message.getSummary());
    }

    @Override
    public String getCreatedBy() {
        return message.getCreatedBy();
    }

    @Override
    public Instant getCreatedAt() {
        return Objects.requireNonNull(DateTimes.toInstant(message.getCreatedAt()));
    }
}
