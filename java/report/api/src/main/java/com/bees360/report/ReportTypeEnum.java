package com.bees360.report;

import java.util.Arrays;

public enum ReportTypeEnum {
    DAR(1, "Premium Damage Assessment Report", "DAR"),

    PMR(2, "Premium Measurement Report", "PMR"),

    MXR(4, "Macro XML Report", "MXR"),

    QDAR(5, "Preliminary Damage Assessment Report", "QDAR"),

    QRER(7, "Highfly Evaluation Report", "QRER"),

    RRIR(8, "Real-time Damage Assessment Report", "RRIR"),

    SXR(9, "Symbility XML Report", "SXR"),

    WR(10, "Weather Report", "WR"),

    NR(11, "Estimate Report", "NR"),

    DR(12, "Measurement DXF Report", "DR"),

    BR(13, "On-Site Bidding Report", "BR"),

    QSR(14, "Real-time Quick Square Report", "QSR"),

    PIR(15, "Property Image Report", "PIR"),

    IDAR(16, "Infrared Damage Assessment Report", "IDAR"),

    ROR(17, "Roof-only Underwriting Report", "ROR"),

    FUR(18, "Full-scope Underwriting Report", "FUR"),

    ICR(19, "Inspection Closeout Report", "ICR"),

    CDF(20, "Claim Damage Form", "CDF"),

    CRR(21, "Post-Construction Audit Report", "CRR"),

    HPR(22, "HOVER Pro Report", "HPR"),

    HE(23, "HOVER", "HE"),

    XE(24, "XACT", "XE"),

    INV(25, "Invoice", "INV"),

    MP(26, "MagicPlan report", "MP"),

    HIS(27, "Homeowner Inspection Survey Results", "HIS"),

    CHECKLIST(32, "Checklist", "CHECKLIST"),

    CLA(33, "Claim Report", "CLA"),

    GLR(34, "General Loss Report", "GLR"),

    DPS(37, "Drone Photo Sheet", "DPS"),

    MPS(38, "Mobile Photo Sheet", "MPS"),

    PLNR(39, "Plnar", "PLNR"),

    CUBI(40, "Cubicasa", "CUBI"),

    HTLA(41, "Hover TLA", "HTLA"),

    FSR_RCE(42, "Full-scope Underwriting Report with Recovery Cost Estimate", "FSR_RCE"),

    OCC(43, "OneClick Code Report", "OCC"),

    EVRP(44, "EagleView Report", "EVRP"),

    EVOB(45, "EV .OBJ", "EVOB"),

    EUR(46, "Express Underwriting Report", "EUR"),

    CUBIVZ(47, "Cubicasa Video Zip", "CUBIVZ"),

    CUBIV(48, "Cubicasa Video", "CUBIV"),

    HOSTASZ(49, "Hosta Summary Zip", "HOSTASZ"),

    HOSTAMZ(50, "Hosta Model Zip", "HOSTAMZ"),

    CHR(51, "Policyholder Property Inspection Report", "CHR"),

    SOS(52, "Scheduling Only Summary", "SOS"),

    BSK(53, "BeesSketch", "BSK"),

    HLOR(54, "High-Level Overview Report", "HLOR"),
    ;

    private final int code;
    private final String display;
    private final String key;

    ReportTypeEnum(int code, String display, String key) {
        this.code = code;
        this.display = display;
        this.key = key;
    }

    public static ReportTypeEnum valueOf(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values()).filter(e -> e.code == code).findFirst().orElse(null);
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }

    public String getKey() {
        return key;
    }
}
