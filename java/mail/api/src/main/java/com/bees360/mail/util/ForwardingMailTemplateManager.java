package com.bees360.mail.util;

import com.bees360.mail.MailTemplate;
import com.bees360.mail.MailTemplateManager;
import com.google.common.collect.ForwardingObject;

import jakarta.annotation.Nullable;

public abstract class ForwardingMailTemplateManager extends ForwardingObject
        implements MailTemplateManager {
    @Override
    public void save(MailTemplate mailTemplate) {
        delegate().save(mailTemplate);
    }

    @Override
    public void deleteAllByTemplateKey(Iterable<String> templateKeys) {
        delegate().deleteAllByTemplateKey(templateKeys);
    }

    @Override
    public void deleteByTemplateKey(String templateKey) {
        delegate().deleteByTemplateKey(templateKey);
    }

    @Nullable
    @Override
    public MailTemplate findByTemplateKey(String templateKey) {
        return delegate().findByTemplateKey(templateKey);
    }

    @Override
    public Iterable<? extends MailTemplate> findAllByTemplateKey(Iterable<String> templateKeys) {
        return delegate().findAllByTemplate<PERSON>ey(templateKeys);
    }

    @Override
    protected abstract MailTemplateManager delegate();
}
