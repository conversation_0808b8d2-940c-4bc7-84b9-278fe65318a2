package com.bees360.mail;

import com.bees360.api.ApiHttpClient;
import com.bees360.api.Message.ApiMessage;
import com.bees360.http.util.URIs;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import jakarta.annotation.Nullable;

import lombok.NonNull;

import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPut;

import java.net.URI;
import java.util.Objects;
import java.util.stream.Collectors;

public class HttpMailTemplateManager implements MailTemplateManager {
    private final String host;
    private final String endpoint;
    private final ApiHttpClient httpClient;

    public HttpMailTemplateManager(
            String host, String endpoint, @NonNull ApiHttpClient httpClient) {
        this.host = host;
        this.endpoint = endpoint;
        this.httpClient = httpClient;
    }

    @Override
    public void save(MailTemplate mailTemplate) {
        Preconditions.checkArgument(mailTemplate != null);
        URI uri = URIs.build(host, uriBuilder -> uriBuilder.setPath(endpoint));
        HttpPut request = new HttpPut(uri);
        var entity = ApiHttpClient.protobufEntity(mailTemplate.toMessage());
        request.setEntity(entity);
        httpClient.execute(request);
    }

    @Override
    public void deleteByTemplateKey(String templateKey) {
        Preconditions.checkArgument(templateKey != null);
        URI uri = URIs.build(host, uriBuilder -> uriBuilder.setPathSegments(endpoint, templateKey));
        httpClient.execute(new HttpDelete(uri));
    }

    @Nullable
    @Override
    public MailTemplate findByTemplateKey(String templateKey) {
        Preconditions.checkArgument(templateKey != null);
        URI uri = URIs.build(host, uriBuilder -> uriBuilder.setPathSegments(endpoint, templateKey));
        ApiMessage result = httpClient.execute(new HttpGet(uri));
        return result.getMailTemplateCount() > 0
                ? MailTemplate.from(result.getMailTemplate(0))
                : null;
    }

    @Override
    public Iterable<? extends MailTemplate> findAllByTemplateKey(Iterable<String> templateKeys) {
        Preconditions.checkArgument(templateKeys != null);
        return Iterables.toStream(templateKeys)
                .map(this::findByTemplateKey)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteAllByTemplateKey(Iterable<String> templateKeys) {
        Preconditions.checkArgument(templateKeys != null);
        Iterables.toStream(templateKeys).forEach(this::deleteByTemplateKey);
    }
}
