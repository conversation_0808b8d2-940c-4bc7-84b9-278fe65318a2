package com.bees360.job.registry;

import com.bees360.mail.MailMessage;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Map;

@Data
@Accessors(chain = true)
@JobPayload
public class SendMailJob {

    private Collection<String> recipients;

    private Collection<String> cc;

    private Collection<String> bcc;

    private String subject;

    private String content;

    private Map<String, String> attachments;

    public static SendMailJob from(MailMessage mailMessage) {
        return new SendMailJob()
                .setRecipients(mailMessage.getRecipients())
                .setCc(mailMessage.getCc())
                .setBcc(mailMessage.getBcc())
                .setSubject(mailMessage.getSubject())
                .setContent(mailMessage.getContent())
                .setAttachments(mailMessage.getAttachments());
    }
}
