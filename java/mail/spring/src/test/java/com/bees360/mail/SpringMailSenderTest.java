package com.bees360.mail;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.mail.config.SpringMailSenderConfig;
import com.bees360.resource.FileResourceRepository;
import com.bees360.resource.ResourcePool;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;

@SpringBootTest
@SpringJUnitConfig
public class SpringMailSenderTest extends TestMailSender {

    @Import({
        SpringMailSenderConfig.class,
    })
    @Configuration
    @ApplicationAutoConfig
    static class Config {
        @Bean
        public ResourcePool attachmentResourcePool() {
            return new FileResourceRepository("/tmp/resource/mail");
        }
    }

    public SpringMailSenderTest(
            @Autowired List<NamedMailSender> senderList,
            @Autowired ResourcePool attachmentResourcePool) {
        super(senderList.get(0), attachmentResourcePool, null);
    }

    @Test
    void testSendMail() {
        sendMail();
    }

    @Test
    void testThrowException() {
        throwException();
    }
}
