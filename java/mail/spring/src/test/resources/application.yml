mail:
  spring:
    senders:
      - host: 127.0.0.1
        username: <EMAIL>
        password: springboot
        protocol: smtp
        port: 3025
        default-encoding: UTF-8
        properties:
          mail:
            from: <EMAIL>
            debug: false
            smtp:
              auth: true
              connectiontimeout: 60000
              timeout: 120000
              writetimeout: 60000
      - host: 127.0.0.1
        username: <EMAIL>
        password: springboot
        protocol: smtp
        port: 3025
        default-encoding: UTF-8
        properties:
          mail:
            from: <EMAIL>
            debug: false
            smtp:
              auth: true
              connectiontimeout: 60000
              timeout: 120000
              writetimeout: 60000
