package com.bees360.mail;

import static com.bees360.jooq.persistent.mail.Tables.MAIL_TEMPLATE;

import com.bees360.jooq.persistent.mail.tables.records.MailTemplateRecord;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import jakarta.annotation.Nullable;

import org.jooq.DSLContext;

import java.util.List;
import java.util.stream.Collectors;

public class JooqMailTemplateManager implements MailTemplateManager {
    private final DSLContext dsl;

    public JooqMailTemplateManager(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    public void save(MailTemplate mailTemplate) {
        Preconditions.checkArgument(mailTemplate != null);
        MailTemplateRecord record = new MailTemplateRecord();
        record.setTemplateKey(mailTemplate.getTemplateKey());
        record.setContentTemplate(mailTemplate.getContentTemplate());
        record.setSubjectTemplate(mailTemplate.getSubjectTemplate());
        dsl.insertInto(MAIL_TEMPLATE)
                .set(record)
                .onConflict(MAIL_TEMPLATE.TEMPLATE_KEY)
                .doUpdate()
                .set(record)
                .execute();
    }

    @Override
    public void deleteAllByTemplateKey(Iterable<String> templateKeys) {
        Preconditions.checkArgument(templateKeys != null);
        dsl.delete(MAIL_TEMPLATE)
                .where(MAIL_TEMPLATE.TEMPLATE_KEY.in(Iterables.toSet(templateKeys)))
                .execute();
    }

    @Nullable
    @Override
    public MailTemplate findByTemplateKey(String templateKey) {
        Preconditions.checkArgument(templateKey != null);
        var mailTemplates = this.findAllByTemplateKey(List.of(templateKey));
        return Iterables.toStream(mailTemplates).findFirst().orElse(null);
    }

    @Override
    public Iterable<? extends MailTemplate> findAllByTemplateKey(Iterable<String> templateKeys) {
        Preconditions.checkArgument(templateKeys != null);
        return dsl
                .select(
                        MAIL_TEMPLATE.TEMPLATE_KEY,
                        MAIL_TEMPLATE.CONTENT_TEMPLATE,
                        MAIL_TEMPLATE.SUBJECT_TEMPLATE)
                .from(MAIL_TEMPLATE)
                .where(MAIL_TEMPLATE.TEMPLATE_KEY.in(Iterables.toSet(templateKeys)))
                .fetchInto(MailTemplateRecord.class)
                .stream()
                .map(
                        record ->
                                MailTemplate.of(
                                        record.getTemplateKey(),
                                        record.getSubjectTemplate(),
                                        record.getContentTemplate()))
                .collect(Collectors.toList());
    }
}
