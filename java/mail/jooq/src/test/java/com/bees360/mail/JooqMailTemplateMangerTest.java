package com.bees360.mail;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.config.JooqConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@ApplicationAutoConfig
@SpringBootTest
public class JooqMailTemplateMangerTest extends TestMailTemplateManager {

    @Import({
        JooqConfig.class,
        JooqMailTemplateManager.class,
    })
    @Configuration
    static class Config {}

    public JooqMailTemplateMangerTest(@Autowired JooqMailTemplateManager jooqMailTemplateManager) {
        super(jooqMailTemplateManager);
    }

    @Test
    void saveAndThenDeleteTest() {
        super.saveAndThenDelete();
    }

    @Test
    void throwExceptionTest() {
        super.throwException();
    }
}
