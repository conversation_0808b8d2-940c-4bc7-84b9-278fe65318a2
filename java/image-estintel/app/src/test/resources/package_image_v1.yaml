package: test.estintel.image
version: 1.0
factor:
  IMAGE:
    name: "Image"
    datatype: "GOOGLE.PROTOBUF.STRINGVALUE"
    attribute:
      static:
        ES_IMAGE: true
        ES_SYNC:
          data: "image_id"
          attribute.uploadTime: "upload_time"
          attribute.shootingTime: "shooting_time"
          attribute.projectId: "project_id"
          attribute.resource: "resources"
      datatype: "COM.BEES360.ESTINTEL.IMAGE_ATTRIBUTE"
    source:
      TEST_LAMBDA_1:
        disabled: false
        default: true
  CATEGORY_ANNOTATION:
    name: "Category Annotation"
    datatype: "COM.BEES360.ESTINTEL.CATEGORY_TYPE_TAG_MESSAGE"
    attribute:
      static:
        ES_IMAGE_TAG: true
        tagType: Category
        ES_SYNC:
          id: "annotations.id"
          attribute.imageId: "image_id"
          data.value: "annotations.tag_id"
          data.name: "annotations.tag_name"
          attribute.projectId: "project_id"
          factor.attribute.tagType: "annotations.tag_type"
          createdBy.id: "annotations.created_by"
          createdAt: "annotations.created_time"
          execution.lambdaKey: "annotations.lambda_key"
          isDeprecated: "annotations.is_deleted"
          updatedBy.id: "annotations.updated_by"
          updatedAt: "annotations.updated_time"
      datatype: "COM.BEES360.ESTINTEL.ANNOTATION_ATTRIBUTE"
    source:
      TEST_LAMBDA_1:
        disabled: false
        default: true
  ADDRESS_VERIFICATION_ANNOTATION:
    name: "Address Verification Annotation"
    datatype: "COM.BEES360.ESTINTEL.BOUNDING_BOX"
    attribute:
      static:
        tagId: 1101
        tagName: Address Verification
        tagType: Object
        ES_IMAGE_ANNOTATION: true
        ES_SYNC:
          id: "annotations.id"
          attribute.imageId: "image_id"
          factor.attribute.tagId: "annotations.tag_id"
          factor.attribute.tagName: "annotations.tag_name"
          attribute.projectId: "project_id"
          factor.attribute.tagType: "annotations.tag_type"
          createdBy.id: "annotations.created_by"
          createdAt: "annotations.created_time"
          execution.lambdaKey: "annotations.lambda_key"
          isDeprecated: "annotations.is_deleted"
          updatedBy.id: "annotations.updated_by"
          updatedAt: "annotations.updated_time"
          data: "annotations.bounding_box"
          attribute.buildingIndex: "annotations.building_index"
          attribute.componentIndex: "annotations.component_index"
          attribute.roomIndex: "annotations.room_index"
          attribute.componentTagId: "annotations.component_tag_id"
      datatype: "COM.BEES360.ESTINTEL.BUILDING_ANNOTATION_ATTRIBUTE"
    source:
      TEST_LAMBDA_1:
        disabled: false
        default: true

lambda:
  TEST_LAMBDA_1:
    name: "lambda 1"
    script:
      path: "/path/to/script"
      type: NODE
    argument:
      def:
        - factor: IMAGE
          constraint:
            compulsory: true
            length: 10
        - factor: CATEGORY_ANNOTATION
          constraint:
            compulsory: true
            length: 10
        - factor: ADDRESS_VERIFICATION_ANNOTATION
          constraint:
            compulsory: false
            length: 5
    return:
      IMAGE:
        disabled: false
      CATEGORY_ANNOTATION:
        disabled: false
      ADDRESS_VERIFICATION_ANNOTATION:
        disabled: false
    modifier:
      isDecomposing: true
