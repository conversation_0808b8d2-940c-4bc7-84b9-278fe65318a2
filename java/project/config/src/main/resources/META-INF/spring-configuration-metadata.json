{"groups": [{"name": "handlebar.template", "type": "com.bees360.project.config.HandlebarTemplateProperties", "sourceType": "com.bees360.project.config.HandlebarTemplateProperties"}], "properties": [{"name": "handlebar.template.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable handlebar template configuration.", "sourceType": "com.bees360.project.config.HandlebarTemplateProperties", "defaultValue": true}, {"name": "handlebar.template.resource-prefix", "type": "java.lang.String", "description": "Template resource prefix path. Supports environment variables (e.g. 'cdn://private/${ENV:local}/'). Automatically adds '/' if not present.", "sourceType": "com.bees360.project.config.HandlebarTemplateProperties", "defaultValue": "static/"}, {"name": "handlebar.template.chr-template-name", "type": "java.lang.String", "description": "CHR report template file name.", "sourceType": "com.bees360.project.config.HandlebarTemplateProperties", "defaultValue": "ChrReportTemplate.html"}, {"name": "handlebar.template.dps-template-name", "type": "java.lang.String", "description": "DPS report template file name.", "sourceType": "com.bees360.project.config.HandlebarTemplateProperties", "defaultValue": "DpsReportTemplate.html"}, {"name": "handlebar.template.invoice-template-name", "type": "java.lang.String", "description": "Invoice template file name.", "sourceType": "com.bees360.project.config.HandlebarTemplateProperties", "defaultValue": "NewInvoiceReportTemplate.html"}, {"name": "handlebar.template.mps-template-name", "type": "java.lang.String", "description": "MPS report template file name.", "sourceType": "com.bees360.project.config.HandlebarTemplateProperties", "defaultValue": "MpsReportTemplate.html"}, {"name": "handlebar.template.uw-sos-template-name", "type": "java.lang.String", "description": "UW SOS report template file name.", "sourceType": "com.bees360.project.config.HandlebarTemplateProperties", "defaultValue": "UWSOSReportTemplate.html"}], "hints": []}