package com.bees360.project.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Handlebars template configuration class Enable HandlebarTemplateProperties configuration Can be
 * controlled by handlebar.template.enabled property (default disabled)
 */
@Configuration
@EnableConfigurationProperties(HandlebarTemplateProperties.class)
@ConditionalOnProperty(prefix = "handlebar.template", name = "enabled", havingValue = "true")
public class HandlebarTemplateConfig {
    // Spring Boot will automatically register HandlebarTemplateProperties as a Bean
    // Other modules can directly inject and use it
}
