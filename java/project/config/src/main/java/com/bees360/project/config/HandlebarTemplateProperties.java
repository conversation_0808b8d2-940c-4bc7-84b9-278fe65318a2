package com.bees360.project.config;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * Handlebars template configuration properties class that provides unified template path
 * configuration management
 */
@Data
@Validated
@ConfigurationProperties(prefix = "handlebar.template")
public class HandlebarTemplateProperties {

    /** Whether to enable Handlebars template configuration. Default: true */
    private boolean enabled = true;

    /**
     * Template resource prefix path. Examples: "static/" or <a
     * href="https://cdn.example.com/templates/">https://cdn.example.com/templates/</a>
     */
    private String resourcePrefix = "static/";

    /** CHR report template name. Default: ChrReportTemplate.html */
    @NotBlank private String chrTemplateName = "ChrReportTemplate.html";

    /** DPS report template name. Default: DpsReportTemplate.html */
    @NotBlank private String dpsTemplateName = "DpsReportTemplate.html";

    /** Invoice template name. Default: NewInvoiceReportTemplate.html */
    @NotBlank private String invoiceTemplateName = "NewInvoiceReportTemplate.html";

    /** MPS report template name. Default: MpsReportTemplate.html */
    @NotBlank private String mpsTemplateName = "MpsReportTemplate.html";

    /** UW SOS report template name. Default: UWSOSReportTemplate.html */
    @NotBlank private String uwSosTemplateName = "UWSOSReportTemplate.html";

    /** ICR report template name. Default: ICRReportTemplate.html */
    @NotBlank private String icrTemplateName = "ICRReportTemplate.html";

    // Convenience methods that return complete template paths

    /**
     * Get the complete CHR template path
     *
     * @return Complete template path
     */
    public String getChrTemplatePath() {
        return buildTemplatePath(chrTemplateName);
    }

    /**
     * Get the complete DPS template path
     *
     * @return Complete template path
     */
    public String getDpsTemplatePath() {
        return buildTemplatePath(dpsTemplateName);
    }

    /**
     * Get the complete invoice template path
     *
     * @return Complete template path
     */
    public String getInvoiceTemplatePath() {
        return buildTemplatePath(invoiceTemplateName);
    }

    /**
     * Get the complete MPS template path
     *
     * @return Complete template path
     */
    public String getMpsTemplatePath() {
        return buildTemplatePath(mpsTemplateName);
    }

    /**
     * Get the complete UW SOS template path
     *
     * @return Complete template path
     */
    public String getUwSosTemplatePath() {
        return buildTemplatePath(uwSosTemplateName);
    }

    /**
     * Get the complete DPS template path
     *
     * @return Complete template path
     */
    public String getICRTemplatePath() {
        return buildTemplatePath(icrTemplateName);
    }

    /**
     * Build the complete template path
     *
     * @param templateName Template name
     * @return Complete template path
     */
    private String buildTemplatePath(String templateName) {
        if (resourcePrefix == null || resourcePrefix.isEmpty()) {
            return templateName;
        }

        if (resourcePrefix.endsWith("/")) {
            return resourcePrefix + templateName;
        }
        return resourcePrefix + '/' + templateName;
    }
}
