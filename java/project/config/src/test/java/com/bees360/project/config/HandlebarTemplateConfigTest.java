package com.bees360.project.config;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

/**
 * test HandlebarTemplateConfig Configuration is loaded correctly and test
 * HandlebarTemplateProperties is correctly injected
 */
@SpringBootTest(classes = {HandlebarTemplateConfigTest.TestConfig.class})
@TestPropertySource(
        properties = {
            "handlebar.template.enabled=true",
            "handlebar.template.resource-prefix=test-prefix/",
            "handlebar.template.invoice-template-name=TestInvoiceTemplate.html",
            "handlebar.template.dps-template-name=TestDpsTemplate.html",
            "handlebar.template.mps-template-name=TestMpsTemplate.html"
        })
public class HandlebarTemplateConfigTest {

    @Autowired private ApplicationContext context;

    @Autowired private HandlebarTemplateProperties templateProperties;

    @Test
    public void testConfigLoaded() {
        // Test if the HandlebarTemplateConfig class is loaded correctly
        assertThat(context.getBean(HandlebarTemplateConfig.class)).isNotNull();
    }

    @Test
    public void testPropertiesInjected() {
        // Test if the HandlebarTemplateProperties class is injected correctly
        assertThat(templateProperties).isNotNull();
    }

    @Test
    public void testPropertyValuesLoaded() {
        // Test if the custom property values are loaded correctly
        assertThat(templateProperties.isEnabled()).isTrue();
        assertThat(templateProperties.getResourcePrefix()).isEqualTo("test-prefix/");
        assertThat(templateProperties.getInvoiceTemplateName())
                .isEqualTo("TestInvoiceTemplate.html");
        assertThat(templateProperties.getDpsTemplateName()).isEqualTo("TestDpsTemplate.html");
        assertThat(templateProperties.getMpsTemplateName()).isEqualTo("TestMpsTemplate.html");
    }

    @Test
    public void testTemplatePathMethods() {
        // Test if the getTemplatePath methods return the correct paths
        assertThat(templateProperties.getInvoiceTemplatePath())
                .isEqualTo("test-prefix/TestInvoiceTemplate.html");
        assertThat(templateProperties.getDpsTemplatePath())
                .isEqualTo("test-prefix/TestDpsTemplate.html");
        assertThat(templateProperties.getMpsTemplatePath())
                .isEqualTo("test-prefix/TestMpsTemplate.html");
    }

    @Test
    public void testDefaultValues() {
        // Create a default HandlebarTemplateProperties instance and verify default values
        HandlebarTemplateProperties defaultProps = new HandlebarTemplateProperties();

        assertThat(defaultProps.isEnabled()).isTrue(); // Now defaults to true
        assertThat(defaultProps.getResourcePrefix()).isEqualTo("static/");
        assertThat(defaultProps.getInvoiceTemplateName())
                .isEqualTo("NewInvoiceReportTemplate.html");
        assertThat(defaultProps.getDpsTemplateName()).isEqualTo("DpsReportTemplate.html");
        assertThat(defaultProps.getMpsTemplateName()).isEqualTo("MpsReportTemplate.html");

        // Template paths with static/ prefix
        assertThat(defaultProps.getInvoiceTemplatePath())
                .isEqualTo("static/NewInvoiceReportTemplate.html");
        assertThat(defaultProps.getDpsTemplatePath()).isEqualTo("static/DpsReportTemplate.html");
        assertThat(defaultProps.getMpsTemplatePath()).isEqualTo("static/MpsReportTemplate.html");
    }

    @Test
    public void testBuildTemplatePathLogic() {
        // Test the buildTemplatePath method logic
        HandlebarTemplateProperties props = new HandlebarTemplateProperties();

        // Test with empty resource prefix
        props.setResourcePrefix("");
        assertThat(props.getInvoiceTemplatePath()).isEqualTo("NewInvoiceReportTemplate.html");

        // Test with null resource prefix
        props.setResourcePrefix(null);
        assertThat(props.getInvoiceTemplatePath()).isEqualTo("NewInvoiceReportTemplate.html");

        // Test with prefix ending with "/"
        props.setResourcePrefix("cdn://prefix/");
        assertThat(props.getInvoiceTemplatePath())
                .isEqualTo("cdn://prefix/NewInvoiceReportTemplate.html");

        // Test with prefix not ending with "/"
        props.setResourcePrefix("cdn://prefix");
        assertThat(props.getInvoiceTemplatePath())
                .isEqualTo("cdn://prefix/NewInvoiceReportTemplate.html");
    }

    @Configuration
    @EnableAutoConfiguration
    @Import(HandlebarTemplateConfig.class)
    static class TestConfig {
        // Test configuration that imports HandlebarTemplateConfig with auto-configuration
    }
}
