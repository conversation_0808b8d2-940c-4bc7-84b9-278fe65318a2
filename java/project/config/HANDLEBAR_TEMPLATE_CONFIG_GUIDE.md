# Template Configuration Module

这个模块提供了统一的 Handlebars 模板配置管理功能，让不同模块能够以一致的方式配置和使用模板资源。

## 📋 功能特性

- ✅ 统一的模板路径配置管理
- ✅ 支持环境变量配置，适应不同部署环境
- ✅ 自动路径拼接，智能处理分隔符
- ✅ 配置验证，确保模板名称不为空
- ✅ 便利方法，简化模板路径获取
- ✅ Spring Boot 配置属性支持

## 🛠️ 配置属性

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `handlebar.template.enabled` | Boolean | `false` | 是否启用 Handlebars 模板配置 |
| `handlebar.template.resource-prefix` | String | `"static/"` | 模板资源前缀路径，支持环境变量 |
| `handlebar.template.chr-template-name` | String | `"ChrReportTemplate.html"` | CHR 报告模板文件名 |
| `handlebar.template.dps-template-name` | String | `"DpsReportTemplate.html"` | DPS 报告模板文件名 |
| `handlebar.template.invoice-template-name` | String | `"NewInvoiceReportTemplate.html"` | 发票模板文件名 |
| `handlebar.template.mps-template-name` | String | `"MpsReportTemplate.html"` | MPS 报告模板文件名 |
| `handlebar.template.uw-sos-template-name` | String | `"UWSOSReportTemplate.html"` | UW SOS 报告模板文件名 |

## 🚀 快速开始

### 1. 添加依赖

在你的 `pom.xml` 中添加：

```xml
<dependency>
    <groupId>com.bees360</groupId>
    <artifactId>bees360-project-config</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 2. 基本配置

```yaml
# application.yml
handlebar:
  template:
    enabled: true
    resource-prefix: "static/"
    chr-template-name: "MyChrTemplate.html"
    dps-template-name: "MyDpsTemplate.html"
    invoice-template-name: "MyInvoiceTemplate.html"
    mps-template-name: "MyMpsTemplate.html"
    uw-sos-template-name: "MyUwSosTemplate.html"
```

### 3. 在代码中使用

```java
@Service
public class ReportService {

    @Autowired
    private HandlebarTemplateProperties templateProperties;

    public void generateChrReport() {
        // 获取完整的 CHR 模板路径
        String templatePath = templateProperties.getChrTemplatePath();
        // 结果: "static/MyChrTemplate.html"

        // 使用模板路径生成报告...
        processTemplate(templatePath);
    }

    public void generateInvoiceReport() {
        // 获取完整的发票模板路径
        String templatePath = templateProperties.getInvoiceTemplatePath();
        // 结果: "static/MyInvoiceTemplate.html"

        // 使用模板路径生成报告...
        processTemplate(templatePath);
    }

    public void generateDpsReport() {
        String templatePath = templateProperties.getDpsTemplatePath();
        // 结果: "static/MyDpsTemplate.html"
        processTemplate(templatePath);
    }

    public void generateUwSosReport() {
        String templatePath = templateProperties.getUwSosTemplatePath();
        // 结果: "static/MyUwSosTemplate.html"
        processTemplate(templatePath);
    }
}
```

## 🌍 环境变量支持

支持使用环境变量来配置不同环境的资源路径：

```yaml
# application.yml
handlebar:
  template:
    # 支持环境变量，${ENV:local} 表示使用 ENV 环境变量，如果不存在则使用 "local"
    resource-prefix: "cdn://private/${ENV:local}/"
```

### 环境变量示例

**开发环境:**
```bash
export ENV=dev
# 结果路径: "cdn://private/dev/NewInvoiceReportTemplate.html"
```

**生产环境:**
```bash
export ENV=prod
# 结果路径: "cdn://private/prod/NewInvoiceReportTemplate.html"
```

**无环境变量时:**
```bash
# 使用默认值 "local"
# 结果路径: "cdn://private/local/NewInvoiceReportTemplate.html"
```

## 📁 路径构建逻辑

模块会智能处理路径拼接：

```java
// 配置: resource-prefix = "static/"
templateProperties.getInvoiceTemplatePath()
// 结果: "static/NewInvoiceReportTemplate.html"

// 配置: resource-prefix = "static" (无尾部斜杠)
templateProperties.getInvoiceTemplatePath()
// 结果: "static/NewInvoiceReportTemplate.html" (自动添加斜杠)

// 配置: resource-prefix = "" (空字符串)
templateProperties.getInvoiceTemplatePath()
// 结果: "NewInvoiceReportTemplate.html" (直接返回文件名)

// 配置: resource-prefix = null
templateProperties.getInvoiceTemplatePath()
// 结果: "NewInvoiceReportTemplate.html" (直接返回文件名)
```

## 🔄 条件性配置

可以根据配置状态来条件性地启用某些功能：

```java
@Component
@ConditionalOnProperty(
    prefix = "handlebar.template",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = true  // 默认启用
)
public class TemplateBasedReportGenerator {
    // 只有当模板配置启用时，这个组件才会被创建
}
```

## 🧪 使用示例

### 示例 1: 发票生成器

```java
@Component
public class ProjectInvoicePdfGenerator {

    private final HandlebarTemplateProperties templateProperties;

    public ProjectInvoicePdfGenerator(HandlebarTemplateProperties templateProperties) {
        this.templateProperties = templateProperties;
    }

    public String generateInvoicePdf(String projectId, Invoice invoice) {
        // 获取发票模板路径
        String templatePath = templateProperties.getInvoiceTemplatePath();

        // 创建 PDF 生成任务
        var job = HtmlToPdfJob.getInstance(
            "invoice-" + projectId,
            templatePath,  // 使用配置的模板路径
            generatePdfKey(),
            null,
            buildTemplateData(invoice)
        );

        // 调度任务...
        return job.getPdfKey();
    }
}
```

### 示例 2: 多环境配置

```yaml
# application-dev.yml (开发环境)
handlebar:
  template:
    resource-prefix: "file:///tmp/templates/"

# application-test.yml (测试环境)
handlebar:
  template:
    resource-prefix: "classpath:/test-templates/"

# application-prod.yml (生产环境)
handlebar:
  template:
    resource-prefix: "https://cdn.company.com/templates/${VERSION}/"
```

## 🔧 配置验证

模块包含内置的配置验证：

- `@NotBlank` 验证确保模板名称不为空
- `@Validated` 启用 JSR-303 验证

如果配置无效，应用启动时会抛出验证异常。

## 📊 最佳实践

1. **使用环境变量**: 为不同部署环境配置不同的资源前缀
2. **保持默认值**: 为开发环境提供合理的默认配置
3. **模板版本管理**: 在资源前缀中包含版本信息，便于回滚
4. **路径一致性**: 确保所有模板使用相同的路径规范
5. **配置验证**: 在部署前验证所有必需的模板文件都存在
