package com.bees360.customer;

import com.bees360.api.ApiHttpClient;
import com.bees360.http.util.URIs;

import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;

import java.net.URI;
import java.util.List;

public class HttpCustomerManager implements CustomerManager {
    private final String host;
    private final String endpoint;
    private final ApiHttpClient httpClient;

    public HttpCustomerManager(String host, String endpoint, ApiHttpClient httpClient) {
        this.host = host;
        this.endpoint = endpoint;
        this.httpClient = httpClient;
    }

    @Override
    public String createCustomer(Customer customer) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void updateCustomer(Customer customer) {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean deleteById(String id) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void updateAttribute(String customerId, String attributeName, String attributeValue) {
        var path = String.format("%s/%s/attributes/%s", endpoint, customerId, attributeName);
        URI uri = URIs.build(host, api -> api.setPath(path));
        var request = new HttpPut(uri);
        request.setEntity(new StringEntity(attributeValue, ContentType.APPLICATION_JSON));
        httpClient.execute(request);
    }

    @Override
    public Customer findByName(String name) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Customer findById(String id) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Customer findByKey(String key) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Iterable<? extends Customer> findByRole(
            List<Message.CustomerMessage.CustomerRole> role) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Iterable<String> listOperatingCompany(String customerId) {
        var path = String.format("%s/%s/operating-company", endpoint, customerId);
        URI uri = URIs.build(host, api -> api.setPath(path));
        var request = new HttpGet(uri);
        var resp = httpClient.execute(request);
        return resp.getOperatingCompanyList();
    }
}
