package com.bees360.customer;

import com.bees360.api.ApiHttpClient;
import com.bees360.http.util.URIs;

import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;

import java.net.URI;

public class HttpCustomerPolicyTypeManager implements CustomerPolicyTypeManager {
    private final String host;
    private final String endpoint;
    private final ApiHttpClient httpClient;

    public HttpCustomerPolicyTypeManager(String host, String endpoint, ApiHttpClient httpClient) {
        this.host = host;
        this.endpoint = endpoint;
        this.httpClient = httpClient;
    }

    @Override
    public void createPolicyType(String customerId, Iterable<String> policyTypes) {
        var path = String.format("%s/%s/policy-type", endpoint, customerId);
        URI uri = URIs.build(host, api -> api.setPath(path));
        var request = new HttpPost(uri);
        var message =
                Message.CreateCustomerPolicyTypeRequest.newBuilder()
                        .setCustomer(customerId)
                        .addAllPolicyType(policyTypes)
                        .build();
        request.setEntity(ApiHttpClient.protobufEntity(message));
        httpClient.execute(request);
    }

    @Override
    public Iterable<String> listCustomerPolicyType(String customerId) {
        var path = String.format("%s/%s/policy-type", endpoint, customerId);
        URI uri = URIs.build(host, api -> api.setPath(path));
        var request = new HttpGet(uri);
        var resp = httpClient.execute(request);
        return resp.getPolicyTypeList();
    }
}
