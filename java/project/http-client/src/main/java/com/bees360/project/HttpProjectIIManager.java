package com.bees360.project;

import com.bees360.api.ApiHttpClient;
import com.bees360.http.util.URIs;
import com.bees360.project.util.ForwardingProjectManager;

import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;

import java.net.URI;
import java.util.List;

public class HttpProjectIIManager extends ForwardingProjectManager {
    private final String host;
    private final String endpoint;
    private final ApiHttpClient httpClient;
    private final ProjectIIManager projectIIManager;

    public HttpProjectIIManager(
            String host,
            String endpoint,
            ApiHttpClient httpClient,
            ProjectIIManager projectIIManager) {
        this.host = host;
        this.endpoint = endpoint;
        this.httpClient = httpClient;
        this.projectIIManager = projectIIManager;
    }

    @Override
    public ProjectII findById(String id) {
        // remove forward slash from beginning of endpoint variable if present
        String sanitizedEndpoint = endpoint.startsWith("/") ? endpoint.substring(1) : endpoint;
        var path = String.format("/v2/%s/%s", sanitizedEndpoint, id);
        URI uri = URIs.build(host, api -> api.setPath(path));
        var request = new HttpGet(uri);
        var message = httpClient.execute(request);
        if (message.getProjectCount() <= 0) {
            return null;
        }
        return ProjectII.from(message.getProject(0));
    }

    @Override
    public boolean updateNumberOfInteriorRooms(String projectId, Integer interiorRoom) {
        var path = String.format("%s/%s/inspection/interior-room", endpoint, projectId);
        URI uri =
                URIs.build(
                        host,
                        api ->
                                api.setPath(path)
                                        .addParameters(
                                                List.of(
                                                        new BasicNameValuePair(
                                                                "count",
                                                                String.valueOf(interiorRoom)))));
        var request = new HttpPut(uri);
        httpClient.execute(request);
        return true;
    }

    @Override
    public boolean updateNumberOfOutBuildings(String projectId, Integer additionalStructure) {
        var path = String.format("%s/%s/inspection/out-building", endpoint, projectId);
        URI uri =
                URIs.build(
                        host,
                        api ->
                                api.setPath(path)
                                        .addParameters(
                                                List.of(
                                                        new BasicNameValuePair(
                                                                "count",
                                                                String.valueOf(
                                                                        additionalStructure)))));
        var request = new HttpPut(uri);
        httpClient.execute(request);
        return true;
    }

    @Override
    public boolean updateReplacementCost(String projectId, Double replacementCost) {
        var path = String.format("%s/%s/inspection/replacement-cost", endpoint, projectId);
        var uri =
                URIs.build(
                        host,
                        api ->
                                api.setPath(path)
                                        .addParameters(
                                                List.of(
                                                        new BasicNameValuePair(
                                                                "cost",
                                                                String.valueOf(replacementCost)))));
        var request = new HttpPut(uri);
        httpClient.execute(request);
        return true;
    }

    @Override
    public boolean updateProjectOperatingCompany(String projectId, String operatingCompany) {
        String sanitizedEndpoint = endpoint.startsWith("/") ? endpoint.substring(1) : endpoint;
        var path = String.format("/v2/%s/%s/operating-company", sanitizedEndpoint, projectId);
        URI uri = URIs.build(host, api -> api.setPath(path));
        var request = new HttpPost(uri);
        request.setEntity(new StringEntity("operating-company", operatingCompany));
        httpClient.execute(request);
        return true;
    }

    @Override
    public boolean updateProjectPolicyType(String projectId, String policyType) {
        String sanitizedEndpoint = endpoint.startsWith("/") ? endpoint.substring(1) : endpoint;
        var path = String.format("/v2/%s/%s/policy-type", sanitizedEndpoint, projectId);
        URI uri = URIs.build(host, api -> api.setPath(path));
        var request = new HttpPost(uri);
        request.setEntity(new StringEntity("policy-type", policyType));
        httpClient.execute(request);
        return true;
    }

    @Override
    protected ProjectIIManager delegate() {
        return projectIIManager;
    }
}
