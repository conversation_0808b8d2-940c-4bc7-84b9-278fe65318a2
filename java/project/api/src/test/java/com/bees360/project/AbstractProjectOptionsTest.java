package com.bees360.project;

import com.bees360.customer.CustomerPolicyTypeManager;
import com.bees360.policy.PolicyManager;
import com.bees360.project.options.Message.ProjectGlobalOptionsMessage;
import com.bees360.project.options.ProjectGlobalOptions;
import com.bees360.project.options.ProjectOptionsProvider;
import com.bees360.util.Iterables;
import com.google.protobuf.util.JsonFormat;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;

import java.util.Collections;

public abstract class AbstractProjectOptionsTest {

    protected final ProjectOptionsProvider projectOptionsProvider;

    protected final ProjectIIManager projectIIManager;

    protected final PolicyManager policyManager;

    protected final CustomerPolicyTypeManager customerPolicyTypeManager;

    protected static ProjectGlobalOptions DEFAULT_PROJECT_GLOBAL_OPTIONS =
            AbstractProjectOptionsTest.getDefaultProjectGlobalOptions();

    public AbstractProjectOptionsTest(
            ProjectOptionsProvider projectOptionsProvider,
            ProjectIIManager projectIIManager,
            PolicyManager policyManager,
            CustomerPolicyTypeManager customerPolicyTypeManager) {
        this.projectOptionsProvider = projectOptionsProvider;
        this.projectIIManager = projectIIManager;
        this.policyManager = policyManager;
        this.customerPolicyTypeManager = customerPolicyTypeManager;
    }

    @SneakyThrows
    private static ProjectGlobalOptions getDefaultProjectGlobalOptions() {
        var json =
                """
            {
                "policy_type": [
                  {
                    "name": "Homeowners Insurance",
                    "property_type": [
                      {
                        "code": 16,
                        "name": "Other"
                      },
                      {
                        "code": 0,
                        "name": "Residential-Single Family"
                      }
                    ]
                  }
                ],
                "property_type": [
                  {
                    "code": 0,
                    "name": "Residential-Single Family"
                  },
                  {
                    "code": 1,
                    "name": "Residential-Condo"
                  }
                ]
              }
        """;
        var builder = ProjectGlobalOptionsMessage.newBuilder();
        JsonFormat.parser().merge(json, builder);
        return ProjectGlobalOptions.from(builder.build());
    }

    public void testListOperatingCompany() {
        var userCompany = "1062";
        var expected = createOperatingCompany();
        var list = projectOptionsProvider.listOperatingCompanyByUserCompany(userCompany);
        var actual =
                Iterables.toStream(list)
                        .filter(e -> StringUtils.equals(e, expected))
                        .findAny()
                        .orElse(null);
        Assertions.assertNotNull(actual);
    }

    public void testListPolicyType() {
        var policy = RandomProjectUtil.randomPolicy();
        customerPolicyTypeManager.createPolicyType(
                RandomStringUtils.randomNumeric(10), Collections.singletonList(policy.getType()));
        var result =
                policyManager.create(
                        policy.getPolicyNo(),
                        policy.getPolicyEffectiveDate(),
                        policy.getAddress().getId(),
                        policy.getBuilding().getId(),
                        policy.getType());
        Assertions.assertNotNull(result);

        var policyTypes = projectOptionsProvider.listPolicyType();
        Assertions.assertNotNull(
                Iterables.toStream(policyTypes)
                        .filter(p -> StringUtils.equals(p, policy.getType()))
                        .findFirst()
                        .orElse(null));
    }

    public void testGetGlobalOptions(ProjectGlobalOptions options) {
        // 调用getGlobalOptions方法获取全局配置选项
        var globalOptions = projectOptionsProvider.getGlobalOptions();
        Assertions.assertNotNull(globalOptions);
        Assertions.assertEquals(options.toMessage(), globalOptions.toMessage());
    }

    String createOperatingCompany(String company) {
        var op = RandomStringUtils.randomAlphabetic(8);
        var projectId = RandomProjectUtil.getRandomId();
        projectIIManager.updateProjectOperatingCompany(projectId, op);
        return op;
    }

    String createOperatingCompany() {
        return createOperatingCompany(RandomStringUtils.randomNumeric(4));
    }
}
