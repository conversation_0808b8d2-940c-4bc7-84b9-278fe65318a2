package com.bees360.project;

import com.bees360.project.quiz.ProjectQuiz;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;

import java.util.List;

/**
 * Utility class for generating random project quizzes. This class provides methods to create *
 * random quizzes for testing purposes.
 */
public class RandomProjectQuizUtil {

    public static String randomProjectId() {
        return "1" + RandomStringUtils.randomNumeric(6);
    }

    /**
     * Generates a random project quiz with a randomly generated project ID.
     *
     * @return a randomly generated project quiz
     */
    public static ProjectQuiz randomProjectQuiz() {
        var projectId = randomProjectId();
        return randomProjectQuiz(projectId);
    }

    /**
     * Generates a random project quiz with the specified project ID.
     *
     * @param projectId the project ID for the quiz
     * @return a randomly generated project quiz with the specified project ID
     */
    public static ProjectQuiz randomProjectQuiz(String projectId) {
        var answer = RandomStringUtils.randomAlphabetic(6);
        return ProjectQuiz.from(
                Message.ProjectQuizMessage.newBuilder()
                        .setId(RandomStringUtils.randomNumeric(6))
                        .setProjectId(projectId)
                        .setSubject(RandomStringUtils.randomAlphabetic(6))
                        .setType(RandomUtils.nextInt(1, 4))
                        .addAllChoice(
                                List.of(
                                        RandomStringUtils.randomAlphabetic(6),
                                        RandomStringUtils.randomAlphabetic(6),
                                        RandomStringUtils.randomAlphabetic(6),
                                        RandomStringUtils.randomAlphabetic(6)))
                        .setSequence(RandomUtils.nextInt(1, 10))
                        .addAllAnswers(
                                List.of(
                                        RandomStringUtils.randomAlphabetic(6),
                                        RandomStringUtils.randomAlphabetic(6),
                                        RandomStringUtils.randomAlphabetic(6),
                                        answer))
                        .setAnswer(answer)
                        .build());
    }
}
