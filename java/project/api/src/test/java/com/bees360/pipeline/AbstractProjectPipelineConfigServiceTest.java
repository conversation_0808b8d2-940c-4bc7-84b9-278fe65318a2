package com.bees360.pipeline;

import static com.bees360.util.Functions.acceptIfNotNull;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.project.Message;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.google.protobuf.BoolValue;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.logging.log4j.util.Strings;

import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class AbstractProjectPipelineConfigServiceTest {
    protected abstract ProjectPipelineConfigService delegate();

    void testSaveConfigAndGet() {
        var config = randomConfig();
        delegate().create(config);
        var pipelineDefKey =
                delegate()
                        .findPipelineDefKey(
                                config.getInsuredBy(),
                                config.getProcessedBy(),
                                config.getServiceType());
        assertEquals(
                config.getPipelineDefKey(),
                pipelineDefKey,
                "The config pipeline def key should match");
    }

    void testSaveConfigWithEmptyProcessedBy() {
        var processBy = randomProcessedBy();
        var insuredBy = randomInsuredBy();
        var serviceType = randomServiceType();
        var userId = randomUserId();
        var emptyProcessedByConfig = randomConfig(insuredBy, null, serviceType, userId);
        var config = randomConfig(insuredBy, processBy, serviceType, userId);
        delegate().create(emptyProcessedByConfig);
        delegate().create(config);
        var pipelineDefKeyOfEmptyProcessedBy =
                delegate().findPipelineDefKey(insuredBy, null, serviceType);
        assertEquals(
                emptyProcessedByConfig.getPipelineDefKey(),
                pipelineDefKeyOfEmptyProcessedBy,
                "The config pipeline def key should match");
        var pipelineDefKey = delegate().findPipelineDefKey(insuredBy, processBy, serviceType);
        assertEquals(
                config.getPipelineDefKey(),
                pipelineDefKey,
                "The config pipeline def key should match");
    }

    void testSaveConfigWithEmptyProcessedByAndInsuredBy() {
        var processBy = randomProcessedBy();
        var insuredBy = randomInsuredBy();
        var serviceType = randomServiceType();
        var userId = randomUserId();
        var emptyInsuredByAndProcessedByConfig = randomConfig(null, null, serviceType, userId);
        var config = randomConfig(insuredBy, processBy, serviceType, userId);
        delegate().create(emptyInsuredByAndProcessedByConfig);
        delegate().create(config);
        var pipelineDefKeyOfEmptyInsuredByAndProcessedBy =
                delegate().findPipelineDefKey(null, null, serviceType);
        assertEquals(
                emptyInsuredByAndProcessedByConfig.getPipelineDefKey(),
                pipelineDefKeyOfEmptyInsuredByAndProcessedBy,
                "The config pipeline def key should match");
        var pipelineDefKey = delegate().findPipelineDefKey(insuredBy, processBy, serviceType);
        assertEquals(
                config.getPipelineDefKey(),
                pipelineDefKey,
                "The config pipeline def key should match");
    }

    void testSaveConfigWithEmptyInsuredBy() {
        var processBy = randomProcessedBy();
        var insuredBy = randomInsuredBy();
        var serviceType = randomServiceType();
        var userId = randomUserId();
        var emptyInsuredByConfig = randomConfig(null, processBy, serviceType, userId);
        var config = randomConfig(insuredBy, processBy, serviceType, userId);
        delegate().create(emptyInsuredByConfig);
        delegate().create(config);
        var pipelineDefKeyOfEmptyInsuredBy =
                delegate().findPipelineDefKey(null, processBy, serviceType);
        assertEquals(
                emptyInsuredByConfig.getPipelineDefKey(),
                pipelineDefKeyOfEmptyInsuredBy,
                "The config pipeline def key should match");
        var pipelineDefKey = delegate().findPipelineDefKey(insuredBy, processBy, serviceType);
        assertEquals(
                config.getPipelineDefKey(),
                pipelineDefKey,
                "The config pipeline def key should match");
    }

    void testSaveConfigAndUpdateConfig() {
        var config = randomConfig();
        var userId = config.getUserId();
        var insuredBy = config.getInsuredBy();
        var processedBy = config.getProcessedBy();
        var serviceType = config.getServiceType();
        delegate().create(config);
        var oldDefKey = config.getPipelineDefKey();
        var pipelineDefKey = delegate().findPipelineDefKey(insuredBy, processedBy, serviceType);
        assertEquals(oldDefKey, pipelineDefKey, "The config pipeline def key should match");

        var defs =
                delegate()
                        .findAndCountByQuery(
                                ProjectPipelineDefQuery.from(
                                        Message.ProjectPipelineDefQueryRequest.newBuilder()
                                                .addInsuredBy(insuredBy)
                                                .addProcessedBy(processedBy)
                                                .addServiceType(serviceType)
                                                .build()));
        assertEquals(1, Iterables.toList(defs.getProjectPipelineDef()).size());
        assertEquals(1, defs.getTotalCount());
        var def = Iterables.toList(defs.getProjectPipelineDef()).get(0);
        assertEquals(
                oldDefKey, def.getPipelineDefKey(), "The config pipeline def key should match");

        var newDefKey = randomPipelineDefKey();
        delegate()
                .update(
                        UpdateProjectPipelineConfig.of(
                                def.getId(),
                                newDefKey,
                                userId,
                                insuredBy,
                                processedBy,
                                serviceType));
        var newPipelineDefKey = delegate().findPipelineDefKey(insuredBy, processedBy, serviceType);
        assertEquals(newDefKey, newPipelineDefKey, "The config pipeline def key should match");
    }

    void testCreateConfigWhenInsuredByProcessedByServiceTypeDuplicatedThrowException() {
        // create config1: (insuredBy, processedBy, serviceType, defKey1)
        var config1 = randomConfig();
        var insuredBy = config1.getInsuredBy();
        var processedBy = config1.getProcessedBy();
        var serviceType = config1.getServiceType();
        var defKey1 = config1.getPipelineDefKey();
        delegate().create(config1);
        var pipelineDefKey1 = delegate().findPipelineDefKey(insuredBy, processedBy, serviceType);
        assertEquals(defKey1, pipelineDefKey1, "The config pipeline def key should match");

        // create config2: (insuredBy2, processedBy, serviceType, defKey1)
        var insuredBy2 = randomInsuredBy();
        var config2 =
                ProjectPipelineConfig.from(
                        config1.toMessage().toBuilder().setInsuredBy(insuredBy2).build());
        delegate().create(config2);
        var pipelineDefKey2 = delegate().findPipelineDefKey(insuredBy2, processedBy, serviceType);
        assertEquals(defKey1, pipelineDefKey2, "The config pipeline def key should match");

        // create config3 failed. (insuredBy, processedBy, serviceType, defKey2)
        var defKey3 = randomPipelineDefKey();
        var config3 =
                ProjectPipelineConfig.from(
                        config1.toMessage().toBuilder().setPipelineDefKey(defKey3).build());
        assertThrows(IllegalArgumentException.class, () -> delegate().create(config3));
    }

    void testCreateConfigWhenDeletedAndCreateRepeatedlyConfig() {
        // create config: (insuredBy, processedBy, serviceType, defKey1)
        var config = randomConfig();
        var insuredBy = config.getInsuredBy();
        var processedBy = config.getProcessedBy();
        var serviceType = config.getServiceType();
        var defKey1 = config.getPipelineDefKey();
        var createdConfig1 = delegate().create(config);
        var pipelineDefKey1 = delegate().findPipelineDefKey(insuredBy, processedBy, serviceType);
        assertEquals(
                defKey1,
                createdConfig1.getPipelineDefKey(),
                "The config pipeline def key should match");
        assertEquals(defKey1, pipelineDefKey1, "The config pipeline def key should match");

        // create fallback config
        delegate()
                .create(
                        ProjectPipelineConfig.from(
                                config.toMessage().toBuilder().clearInsuredBy().build()));

        // delete config1
        delegate().deleteById(createdConfig1.getId());

        // Create repeatedly config: (insuredBy, processedBy, serviceType, defKey1)
        var createdConfig2 = delegate().create(config);
        assertEquals(
                defKey1,
                createdConfig2.getPipelineDefKey(),
                "The config pipeline def key should match");

        // Create repeatedly config3 failed. (insuredBy, processedBy, serviceType, defKey2)
        var defKey2 = randomPipelineDefKey();
        var config3 =
                ProjectPipelineConfig.from(
                        config.toMessage().toBuilder().setPipelineDefKey(defKey2).build());
        assertThrows(IllegalArgumentException.class, () -> delegate().create(config3));
    }

    void testUpdateConfigWhenInsuredByProcessedByServiceTypeDuplicatedThrowException() {
        // create config1: (insuredBy1, processedBy, serviceType, defKey1)
        var config1 = randomConfig();
        var userId = config1.getUserId();
        var insuredBy1 = config1.getInsuredBy();
        var processedBy = config1.getProcessedBy();
        var serviceType = config1.getServiceType();
        var defKey1 = config1.getPipelineDefKey();
        delegate().create(config1);
        var pipelineDefKey1 = delegate().findPipelineDefKey(insuredBy1, processedBy, serviceType);
        assertEquals(defKey1, pipelineDefKey1, "The config pipeline def key should match");

        // create config2: (insuredBy2, processedBy, serviceType, defKey2)
        var defKey2 = randomPipelineDefKey();
        var insuredBy2 = randomInsuredBy();
        var config2 =
                ProjectPipelineConfig.from(
                        config1.toMessage().toBuilder()
                                .setPipelineDefKey(defKey2)
                                .setInsuredBy(insuredBy2)
                                .build());
        delegate().create(config2);
        var pipelineDefKey2 = delegate().findPipelineDefKey(insuredBy2, processedBy, serviceType);
        assertEquals(defKey2, pipelineDefKey2, "The config pipeline def key should match");

        var defs =
                delegate()
                        .findAndCountByQuery(
                                ProjectPipelineDefQuery.from(
                                        Message.ProjectPipelineDefQueryRequest.newBuilder()
                                                .addInsuredBy(insuredBy1)
                                                .addProcessedBy(processedBy)
                                                .addServiceType(serviceType)
                                                .build()));
        assertEquals(1, Iterables.toList(defs.getProjectPipelineDef()).size());
        assertEquals(1, defs.getTotalCount());
        var def = Iterables.toList(defs.getProjectPipelineDef()).get(0);
        assertEquals(defKey1, def.getPipelineDefKey(), "The config pipeline def key should match");

        // Successfully update config1.
        // (insuredBy1, processedBy, serviceType, defKey1)
        // -> (insuredBy1, processedBy, serviceType, defKey2)
        delegate()
                .update(
                        UpdateProjectPipelineConfig.of(
                                def.getId(),
                                defKey2,
                                userId,
                                insuredBy1,
                                processedBy,
                                serviceType));
        var defKey = delegate().findPipelineDefKey(insuredBy1, processedBy, serviceType);
        assertEquals(defKey2, defKey, "The config pipeline def key should match");

        // update config1 failed.
        // (insuredBy1, processedBy, serviceType, defKey1)
        // -> (insuredBy2, processedBy, serviceType, defKey2)
        assertThrows(
                IllegalArgumentException.class,
                () ->
                        delegate()
                                .update(
                                        UpdateProjectPipelineConfig.of(
                                                def.getId(),
                                                defKey2,
                                                userId,
                                                insuredBy2,
                                                processedBy,
                                                serviceType)));
    }

    void testSaveConfigAndDeleteById() {
        var config = randomConfig();
        var insuredBy = config.getInsuredBy();
        var processedBy = config.getProcessedBy();
        var serviceType = config.getServiceType();
        delegate().create(config);
        var oldDefKey = config.getPipelineDefKey();
        var pipelineDefKey = delegate().findPipelineDefKey(insuredBy, processedBy, serviceType);
        assertEquals(oldDefKey, pipelineDefKey, "The config pipeline def key should match");

        var defs =
                delegate()
                        .findAndCountByQuery(
                                ProjectPipelineDefQuery.from(
                                        Message.ProjectPipelineDefQueryRequest.newBuilder()
                                                .addInsuredBy(insuredBy)
                                                .addProcessedBy(processedBy)
                                                .addServiceType(serviceType)
                                                .build()));
        assertEquals(1, Iterables.toList(defs.getProjectPipelineDef()).size());
        assertEquals(1, defs.getTotalCount());
        var def = Iterables.toList(defs.getProjectPipelineDef()).get(0);
        assertEquals(
                oldDefKey, def.getPipelineDefKey(), "The config pipeline def key should match");

        // find all fallback config
        var pipelineDefList =
                delegate()
                        .findAndCountByQuery(
                                ProjectPipelineDefQuery.from(
                                        Message.ProjectPipelineDefQueryRequest.newBuilder()
                                                .addAllInsuredBy(List.of(insuredBy, Strings.EMPTY))
                                                .addAllProcessedBy(
                                                        List.of(processedBy, Strings.EMPTY))
                                                .addServiceType(serviceType)
                                                .build()));
        // sort
        var sortConfig =
                Iterables.toStream(pipelineDefList.getProjectPipelineDef())
                        .sorted(
                                Comparator.nullsLast(
                                                Comparator.comparing(
                                                        ProjectPipelineDef::getInsuredBy,
                                                        Comparator.naturalOrder()))
                                        .thenComparing(
                                                Comparator.nullsLast(
                                                        Comparator.comparing(
                                                                ProjectPipelineDef::getProcessedBy,
                                                                Comparator.naturalOrder()))))
                        .collect(Collectors.toList());
        // delete config failed
        assertThrows(
                IllegalArgumentException.class,
                () -> delegate().deleteById(sortConfig.get(0).getId()));
    }

    void testSaveConfigAndDeleteByIdExistsFallback() {
        // create config
        var config = randomConfig();
        var insuredBy = config.getInsuredBy();
        var processedBy = config.getProcessedBy();
        var serviceType = config.getServiceType();
        delegate().create(config);
        var oldDefKey = config.getPipelineDefKey();
        var pipelineDefKey = delegate().findPipelineDefKey(insuredBy, processedBy, serviceType);
        assertEquals(oldDefKey, pipelineDefKey, "The config pipeline def key should match");

        // create fallback config
        var oldDefKey2 = randomPipelineDefKey();
        delegate()
                .create(
                        ProjectPipelineConfig.from(
                                config.toMessage().toBuilder()
                                        .clearInsuredBy()
                                        .setPipelineDefKey(oldDefKey2)
                                        .build()));
        var pipelineDefKey2 = delegate().findPipelineDefKey(null, processedBy, serviceType);
        assertEquals(oldDefKey2, pipelineDefKey2, "The config pipeline def key should match");

        // find defId
        var defs =
                delegate()
                        .findAndCountByQuery(
                                ProjectPipelineDefQuery.from(
                                        Message.ProjectPipelineDefQueryRequest.newBuilder()
                                                .addInsuredBy(insuredBy)
                                                .addProcessedBy(processedBy)
                                                .addServiceType(serviceType)
                                                .build()));
        assertEquals(1, Iterables.toList(defs.getProjectPipelineDef()).size());
        assertEquals(1, defs.getTotalCount());
        var def = Iterables.toList(defs.getProjectPipelineDef()).get(0);
        assertEquals(
                oldDefKey, def.getPipelineDefKey(), "The config pipeline def key should match");

        // delete config by id
        delegate().deleteById(def.getId());
        var newPipelineDefKey = delegate().findPipelineDefKey(insuredBy, processedBy, serviceType);
        assertEquals(oldDefKey2, newPipelineDefKey, "The config pipeline def key should match");
    }

    void testSaveConfigAndFindAndCountByQuery() {
        var userId = randomUserId();
        this.testSaveConfigAndFindAndCountByQuery(userId);
    }

    void testSaveConfigAndFindAndCountByQuery(String userId) {
        // create config1
        var time1 = Instant.now();
        var config = randomConfig(userId);
        var insuredBy = config.getInsuredBy();
        var processedBy = config.getProcessedBy();
        var serviceType = config.getServiceType();
        delegate().create(config);
        var defKey1 = config.getPipelineDefKey();
        var pipelineDefKey = delegate().findPipelineDefKey(insuredBy, processedBy, serviceType);
        assertEquals(defKey1, pipelineDefKey, "The config pipeline def key should match");

        // create config2
        var time2 = Instant.now();
        var defKey2 = randomPipelineDefKey();
        var insuredBy2 = randomInsuredBy();
        delegate()
                .create(
                        ProjectPipelineConfig.from(
                                config.toMessage().toBuilder()
                                        .setInsuredBy(insuredBy2)
                                        .setPipelineDefKey(defKey2)
                                        .build()));
        var pipelineDefKey2 = delegate().findPipelineDefKey(insuredBy2, processedBy, serviceType);
        assertEquals(defKey2, pipelineDefKey2, "The config pipeline def key should match");

        // create config3
        var time3 = Instant.now();
        var defKey3 = randomPipelineDefKey();
        var processedBy3 = randomProcessedBy();
        delegate()
                .create(
                        ProjectPipelineConfig.from(
                                config.toMessage().toBuilder()
                                        .setProcessedBy(processedBy3)
                                        .setPipelineDefKey(defKey3)
                                        .build()));
        var pipelineDefKey3 = delegate().findPipelineDefKey(insuredBy, processedBy3, serviceType);
        assertEquals(defKey3, pipelineDefKey3, "The config pipeline def key should match");

        var time4 = Instant.now();

        // find all project pipeline def
        var defs =
                delegate()
                        .findAndCountByQuery(
                                ProjectPipelineDefQuery.from(
                                        Message.ProjectPipelineDefQueryRequest.newBuilder()
                                                .addAllInsuredBy(List.of(insuredBy, insuredBy2))
                                                .addAllProcessedBy(
                                                        List.of(processedBy, processedBy3))
                                                .addServiceType(serviceType)
                                                .addCreatedBy(userId)
                                                .addUpdatedBy(userId)
                                                .setCreatedAtStart(DateTimes.toTimestamp(time1))
                                                .setCreatedAtEnd(DateTimes.toTimestamp(time4))
                                                .build()));
        assertEquals(3, Iterables.toList(defs.getProjectPipelineDef()).size());
        assertEquals(3, defs.getTotalCount());
        var defKeys =
                Iterables.toList(defs.getProjectPipelineDef()).stream()
                        .map(ProjectPipelineDef::getPipelineDefKey)
                        .collect(Collectors.toList());
        assertTrue(defKeys.containsAll(List.of(defKey1, defKey2, defKey3)));

        // find config1
        defs =
                delegate()
                        .findAndCountByQuery(
                                ProjectPipelineDefQuery.from(
                                        Message.ProjectPipelineDefQueryRequest.newBuilder()
                                                .addAllInsuredBy(List.of(insuredBy))
                                                .addAllProcessedBy(List.of(processedBy))
                                                .addServiceType(serviceType)
                                                .addCreatedBy(userId)
                                                .addUpdatedBy(userId)
                                                .setCreatedAtStart(DateTimes.toTimestamp(time1))
                                                .setCreatedAtEnd(DateTimes.toTimestamp(time4))
                                                .setUpdatedAtStart(DateTimes.toTimestamp(time1))
                                                .setUpdatedAtEnd(DateTimes.toTimestamp(time4))
                                                .build()));
        assertEquals(1, Iterables.toList(defs.getProjectPipelineDef()).size());
        assertEquals(1, defs.getTotalCount());
        defKeys =
                Iterables.toList(defs.getProjectPipelineDef()).stream()
                        .map(ProjectPipelineDef::getPipelineDefKey)
                        .collect(Collectors.toList());
        assertTrue(defKeys.contains(defKey1));

        // find config2
        defs =
                delegate()
                        .findAndCountByQuery(
                                ProjectPipelineDefQuery.from(
                                        Message.ProjectPipelineDefQueryRequest.newBuilder()
                                                .addAllInsuredBy(List.of(insuredBy2))
                                                .addAllProcessedBy(List.of(processedBy))
                                                .addServiceType(serviceType)
                                                .addCreatedBy(userId)
                                                .addUpdatedBy(userId)
                                                .setCreatedAtStart(DateTimes.toTimestamp(time1))
                                                .setCreatedAtEnd(DateTimes.toTimestamp(time4))
                                                .setUpdatedAtStart(DateTimes.toTimestamp(time2))
                                                .setUpdatedAtEnd(DateTimes.toTimestamp(time3))
                                                .build()));
        assertEquals(1, Iterables.toList(defs.getProjectPipelineDef()).size());
        assertEquals(1, defs.getTotalCount());
        defKeys =
                Iterables.toList(defs.getProjectPipelineDef()).stream()
                        .map(ProjectPipelineDef::getPipelineDefKey)
                        .collect(Collectors.toList());
        assertTrue(defKeys.contains(defKey2));

        // find config3
        defs =
                delegate()
                        .findAndCountByQuery(
                                ProjectPipelineDefQuery.from(
                                        Message.ProjectPipelineDefQueryRequest.newBuilder()
                                                .addAllInsuredBy(List.of(insuredBy))
                                                .addAllProcessedBy(List.of(processedBy3))
                                                .addServiceType(serviceType)
                                                .addCreatedBy(userId)
                                                .addUpdatedBy(userId)
                                                .setCreatedAtStart(DateTimes.toTimestamp(time3))
                                                .setCreatedAtEnd(DateTimes.toTimestamp(time4))
                                                .setUpdatedAtStart(DateTimes.toTimestamp(time3))
                                                .setUpdatedAtEnd(DateTimes.toTimestamp(time4))
                                                .build()));
        assertEquals(1, Iterables.toList(defs.getProjectPipelineDef()).size());
        assertEquals(1, defs.getTotalCount());
        defKeys =
                Iterables.toList(defs.getProjectPipelineDef()).stream()
                        .map(ProjectPipelineDef::getPipelineDefKey)
                        .collect(Collectors.toList());
        assertTrue(defKeys.contains(defKey3));
    }

    void testSaveConfigWithPolicyRenewal() {
        var renewalConfig = randomConfig(true);
        var defaultConfig =
                randomConfig(
                        renewalConfig.getInsuredBy(),
                        renewalConfig.getProcessedBy(),
                        renewalConfig.getServiceType(),
                        null,
                        renewalConfig.getUserId());
        var config =
                randomConfig(
                        renewalConfig.getInsuredBy(),
                        renewalConfig.getProcessedBy(),
                        renewalConfig.getServiceType(),
                        false,
                        renewalConfig.getUserId());

        // assert save success
        assertDoesNotThrow(() -> delegate().create(config));
        assertDoesNotThrow(() -> delegate().create(defaultConfig));
        assertDoesNotThrow(() -> delegate().create(renewalConfig));

        // assert findDefKey after save
        var pipelineDefKey =
                delegate()
                        .findPipelineDefKey(
                                renewalConfig.getInsuredBy(),
                                renewalConfig.getProcessedBy(),
                                renewalConfig.getServiceType(),
                                renewalConfig.getPolicyRenewal());
        assertEquals(renewalConfig.getPipelineDefKey(), pipelineDefKey);
        assertNotEquals(
                renewalConfig.getPipelineDefKey(),
                delegate()
                        .findPipelineDefKey(
                                renewalConfig.getInsuredBy(),
                                renewalConfig.getProcessedBy(),
                                renewalConfig.getServiceType()));

        // assert findByQuery after save
        var queryMessage =
                Message.ProjectPipelineDefQueryRequest.newBuilder()
                        .addInsuredBy(renewalConfig.getInsuredBy())
                        .addProcessedBy(renewalConfig.getProcessedBy())
                        .addServiceType(renewalConfig.getServiceType())
                        .setPolicyRenewal(BoolValue.of(true))
                        .build();
        var query = ProjectPipelineDefQuery.from(queryMessage);
        var defs = delegate().findAndCountByQuery(query);
        assertEquals(1, defs.getTotalCount());
        var pipelineDef = defs.getProjectPipelineDef().iterator().next();
        assertEquals(renewalConfig.getPolicyRenewal(), pipelineDef.getPolicyRenewal());
    }

    void testSaveConfigWithPolicyRenewalButNoRenewalPipelineDef() {
        var config = randomConfig(null, randomUserId());
        var renewalConfigWithoutInsuredBy =
                randomConfig(
                        null,
                        config.getProcessedBy(),
                        config.getServiceType(),
                        true,
                        config.getUserId());
        var configWithoutInsuredBy =
                randomConfig(
                        null,
                        config.getProcessedBy(),
                        config.getServiceType(),
                        false,
                        config.getUserId());
        var renewalConfigWithoutProcessedBy =
                randomConfig(
                        config.getInsuredBy(),
                        null,
                        config.getServiceType(),
                        true,
                        config.getUserId());
        var configWithoutProcessedBy =
                randomConfig(
                        config.getInsuredBy(),
                        null,
                        config.getServiceType(),
                        false,
                        config.getUserId());

        // assert save success
        assertDoesNotThrow(() -> delegate().create(config));
        assertDoesNotThrow(() -> delegate().create(renewalConfigWithoutInsuredBy));
        assertDoesNotThrow(() -> delegate().create(configWithoutInsuredBy));
        assertDoesNotThrow(() -> delegate().create(renewalConfigWithoutProcessedBy));
        assertDoesNotThrow(() -> delegate().create(configWithoutProcessedBy));

        // assert findDefKey after save
        var pipelineDefKey =
                delegate()
                        .findPipelineDefKey(
                                config.getInsuredBy(),
                                config.getProcessedBy(),
                                config.getServiceType(),
                                true);
        assertEquals(config.getPipelineDefKey(), pipelineDefKey);

        // assert findByQuery after save
        var queryMessage =
                Message.ProjectPipelineDefQueryRequest.newBuilder()
                        .addInsuredBy(config.getInsuredBy())
                        .addProcessedBy(config.getProcessedBy())
                        .addServiceType(config.getServiceType())
                        .build();
        var query = ProjectPipelineDefQuery.from(queryMessage);
        var defs = delegate().findAndCountByQuery(query);
        assertEquals(1, defs.getTotalCount());
        var pipelineDef = defs.getProjectPipelineDef().iterator().next();
        assertEquals(config.getPolicyRenewal(), pipelineDef.getPolicyRenewal());
    }

    ProjectPipelineConfig randomConfig() {
        return randomConfig(null, randomUserId());
    }

    ProjectPipelineConfig randomConfig(String userId) {
        return randomConfig(null, userId);
    }

    ProjectPipelineConfig randomConfig(Boolean policyRenewal) {
        return randomConfig(policyRenewal, randomUserId());
    }

    ProjectPipelineConfig randomConfig(Boolean policyRenewal, String userId) {
        return randomConfig(randomInsuredBy(), randomProcessedBy(), 0, policyRenewal, userId);
    }

    ProjectPipelineConfig randomConfig(
            String insuredBy, String processedBy, int serviceType, String userId) {
        return randomConfig(insuredBy, processedBy, serviceType, null, userId);
    }

    String randomProcessedBy() {
        return RandomStringUtils.randomAlphabetic(8);
    }

    String randomInsuredBy() {
        return RandomStringUtils.randomAlphabetic(8);
    }

    int randomServiceType() {
        return RandomUtils.nextInt(0, 10000);
    }

    String randomUserId() {
        return RandomStringUtils.randomAlphabetic(8);
    }

    ProjectPipelineConfig randomConfig(
            String insuredBy,
            String processedBy,
            int serviceType,
            Boolean policyRenewal,
            String userId) {
        var builder = Message.ProjectPipelineConfigMessage.newBuilder();
        Optional.ofNullable(insuredBy).ifPresent(builder::setInsuredBy);
        Optional.ofNullable(processedBy).ifPresent(builder::setProcessedBy);
        builder.setServiceType(serviceType)
                .setPipelineDefKey(randomPipelineDefKey())
                .setName(RandomStringUtils.randomAlphabetic(8))
                .setUserId(userId);
        acceptIfNotNull(builder::setPolicyRenewal, policyRenewal, BoolValue::of);
        return ProjectPipelineConfig.from(builder.build());
    }

    String randomPipelineDefKey() {
        return RandomStringUtils.randomNumeric(8);
    }
}
