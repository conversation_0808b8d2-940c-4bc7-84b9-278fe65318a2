package com.bees360.project.statistics;

import static com.bees360.project.statistics.MockProjectStatisticProvider.createValidRequest;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.common.collect.Iterables;

import lombok.extern.log4j.Log4j2;

/** Work with MockProjectStageStatisticsProvider. It's helpful when testing remote connection. */
@Log4j2
public class TestMockProjectStatisticProvider extends ForwardingProjectStatisticProvider {

    public TestMockProjectStatisticProvider(ProjectStatisticProvider projectStatisticProvider) {
        super(projectStatisticProvider);
        log.info("Created {}", this);
    }

    public void getCustomerStatistic() {
        var result = projectStatisticProvider.getCustomerStatistic(createValidRequest());
        validResult(result);
    }

    public void getStageStatistic() {
        var result = projectStatisticProvider.getStageStatistic(createValidRequest());
        validResult(result);
    }

    public void getCurrentStatistic() {
        var result = projectStatisticProvider.getCurrentStatistic(createValidRequest());
        validResult(result);
    }

    private void validResult(Iterable<ProjectStageStatistic> statistics) {
        assertTrue(Iterables.size(statistics) > 0);
    }
}
