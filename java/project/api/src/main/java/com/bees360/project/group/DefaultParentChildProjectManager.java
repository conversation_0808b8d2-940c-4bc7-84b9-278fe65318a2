package com.bees360.project.group;

import com.bees360.util.Iterables;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class DefaultParentChildProjectManager implements ParentChildProjectManager {

    private static final String GROUP_TYPE = "GROUP_PARENT_CHILD";

    private final ProjectGroupManager projectGroupManager;

    public DefaultParentChildProjectManager(ProjectGroupManager projectGroupManager) {
        this.projectGroupManager = projectGroupManager;
    }

    @Override
    public void addChildProject(String parentId, Iterable<String> childIds, String createdBy) {
        var projectIds = appendProjectToGroup(parentId, childIds);
        projectGroupManager.addProjectToGroup(parentId, GROUP_TYPE, projectIds, createdBy);
    }

    @Override
    public void replaceAllChildProject(
            String parentId, Iterable<String> childIds, String updatedBy) {
        var projectIds = appendProjectToGroup(parentId, childIds);
        projectGroupManager.replaceAllProjectInGroup(parentId, GROUP_TYPE, projectIds, updatedBy);
    }

    @Override
    public void deleteChildProject(String parentId, Iterable<String> childIds, String deletedBy) {
        projectGroupManager.deleteProjectInGroup(parentId, GROUP_TYPE, childIds, deletedBy);
    }

    @Override
    public Map.Entry<String, Iterable<String>> findByProjectId(String projectId) {
        return Optional.ofNullable(projectGroupManager.findByProjectId(projectId, GROUP_TYPE))
                .map(this::transformToEntry)
                .orElse(null);
    }

    /** Append parent project to project ids of group. */
    private List<String> appendProjectToGroup(String projectId, Iterable<String> projectIds) {
        if (Iterables.isEmpty(projectIds)) {
            return List.of();
        }

        var projectList = new ArrayList<>(Iterables.toCollection(projectIds));
        projectList.add(projectId);

        return projectList;
    }

    /** Transform a project group to a parent-child project entry */
    private Map.Entry<String, Iterable<String>> transformToEntry(ProjectGroup group) {
        List<String> projectIds =
                Iterables.toStream(group.getProjectIds())
                        .filter(s -> !StringUtils.equals(s, group.getKey()))
                        .collect(Collectors.toList());
        if (projectIds.isEmpty()) {
            return null;
        }

        return Map.entry(group.getKey(), projectIds);
    }
}
