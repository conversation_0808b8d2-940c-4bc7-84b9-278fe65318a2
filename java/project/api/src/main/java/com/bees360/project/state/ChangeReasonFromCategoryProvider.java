package com.bees360.project.state;

import com.bees360.project.statechangereason.Message;

public class ChangeReasonFromCategoryProvider
        implements ChangeReasonFromSpecifiedGroupTypeProvider {
    private static final String CHANGE_REASON_CATEGORY_GROUP_TYPE = "CHANGE_REASON_CATEGORY";

    private final StateChangeReasonGroupManager stateChangeReasonGroupManager;

    public ChangeReasonFromCategoryProvider(
            StateChangeReasonGroupManager stateChangeReasonGroupManager) {
        this.stateChangeReasonGroupManager = stateChangeReasonGroupManager;
    }

    @Override
    public Iterable<String> listReasonGroup() {
        return stateChangeReasonGroupManager.findGroupKeyByType(CHANGE_REASON_CATEGORY_GROUP_TYPE);
    }

    @Override
    public Iterable<? extends ProjectStateChangeReason> findChangeReasonByGroup(String group) {
        return stateChangeReasonGroupManager.findByGroupAndType(
                group,
                CHANGE_REASON_CATEGORY_GROUP_TYPE,
                Message.ProjectStateChangeReasonMessage.ProjectStateChangeReasonType.UNDEFINED);
    }
}
