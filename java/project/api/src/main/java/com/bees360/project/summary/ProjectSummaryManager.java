package com.bees360.project.summary;

public interface ProjectSummaryManager {

    /**
     * Saves a summary for the specified project. If a summary already exists for the project, it
     * will be updated.
     *
     * @param projectId the ID of the project
     * @param summary the summary content to save
     * @return the ID of the saved or updated summary
     */
    String save(String projectId, String summary);

    /**
     * Finds and returns the summary for the specified project.
     *
     * @param projectId the ID of the project
     * @return the project summary, or null if no summary exists for the project
     */
    ProjectSummary findByProjectId(String projectId);
}
