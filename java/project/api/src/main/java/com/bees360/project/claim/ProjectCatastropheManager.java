package com.bees360.project.claim;

import com.bees360.catastrophe.ClaimCatastrophe;
import com.bees360.catastrophe.ClaimCatastropheService;
import com.google.common.base.Preconditions;

import jakarta.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

public interface ProjectCatastropheManager extends ClaimCatastropheService {
    int MAX_CATASTROPHE_SERIAL_NUMBER_LENGTH = 32;
    int MIN_CATASTROPHE_LEVEL = 1;
    int MAX_CATASTROPHE_LEVEL = 5;
    String DEFAULT_CUSTOMER_ID = "0";
    String DEFAULT_ADDRESS_STATE = "";

    /** The catastrophe number cannot be null, and cannot exceed MAX_NUMBER_LENGTH. */
    Consumer<String> SERIAL_NUMBER_CHECKER =
            (number) ->
                    Preconditions.checkArgument(
                            StringUtils.isNotBlank(number)
                                    && number.length() <= MAX_CATASTROPHE_SERIAL_NUMBER_LENGTH,
                            "Catastrophe Invalid: The catastrophe serial number should not"
                                    + " be null and length should not be longer than %s.",
                            MAX_CATASTROPHE_SERIAL_NUMBER_LENGTH);

    /** The catastrophe level can only be null or a value between 1 and 5. */
    Consumer<Integer> LEVEL_CHECKER =
            (level) ->
                    Preconditions.checkArgument(
                            level == null
                                    || (level >= MIN_CATASTROPHE_LEVEL
                                            && level <= MAX_CATASTROPHE_LEVEL),
                            "Catastrophe Invalid: The catastrophe level should between %s and %s.",
                            MIN_CATASTROPHE_LEVEL,
                            MAX_CATASTROPHE_LEVEL);

    /**
     * Add catastrophe with level, customerId and state to project. <br>
     * Create the catastrophe if not existed, <br>
     * otherwise, override the level if the params level is not null. <br>
     * For each customer, each serialNumber can have different levels under different states.
     *
     * @param projectId project id
     * @param catSerialNumber catastrophe serial number
     * @param catLevel catastrophe level
     * @param customerId customer id
     * @param addressState address state
     */
    void addProjectCatastrophe(
            String projectId,
            String catSerialNumber,
            @Nullable Integer catLevel,
            @Nullable String customerId,
            @Nullable String addressState);

    /**
     * Add catastrophe with level to project. Create catastrophe if it is not existed, otherwise
     * override the level if the params level is not null.
     *
     * @param projectId project id
     * @param catSerialNumber catastrophe serial number
     * @param catLevel catastrophe level, there is no effect if this param is null.
     */
    default void addProjectCatastrophe(
            String projectId, String catSerialNumber, @Nullable Integer catLevel) {
        addProjectCatastrophe(
                projectId, catSerialNumber, catLevel, DEFAULT_CUSTOMER_ID, DEFAULT_ADDRESS_STATE);
    }

    /**
     * Add catastrophe to project.
     *
     * @param projectId project id
     * @param catSerialNumber catastrophe serial number
     */
    default void addProjectCatastrophe(String projectId, String catSerialNumber) {
        addProjectCatastrophe(projectId, catSerialNumber, null);
    }

    /**
     * Delete the project catastrophe relation by project id.
     *
     * @param projectId project id
     */
    boolean deleteByProjectId(String projectId);

    /**
     * Find project ids by catastrophe serial number.
     *
     * @param catSerialNumber catastrophe serial number
     * @return project ids bind catastrophe with the specified serial number.
     */
    Iterable<? extends String> findProjectIdBySerialNum(String catSerialNumber);

    /**
     * Find claim catastrophe by project id.
     *
     * @param projectId project id (should not be Null)
     * @return claim catastrophe info
     * @throws IllegalArgumentException if {@code projectId} is null
     */
    @Nullable
    default ClaimCatastrophe findByProjectId(String projectId) {
        Preconditions.checkArgument(StringUtils.isNoneEmpty(projectId));
        var projectToCat = findByProjectIds(List.of(projectId));
        return Optional.ofNullable(projectToCat).map(pc -> pc.get(projectId)).orElse(null);
    }

    /**
     * Find Map ProjectId to ClaimCatastrophe by project id list.
     *
     * @param projectIds project ids (should not be Null or Empty)
     * @return Map ProjectId to ClaimCatastrophe
     * @throws IllegalArgumentException if {@code projectIds} is null or empty
     */
    Map<String, ClaimCatastrophe> findByProjectIds(Iterable<String> projectIds);
}
