package com.bees360.project.state;

import com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage;

public interface StateChangeReasonGroupProvider {
    Iterable<? extends ProjectStateChangeReason> findByGroupAndType(
            String groupKey,
            String groupType,
            ProjectStateChangeReasonMessage.ProjectStateChangeReasonType type);

    Iterable<String> findGroupKeyByType(String groupType);
}
