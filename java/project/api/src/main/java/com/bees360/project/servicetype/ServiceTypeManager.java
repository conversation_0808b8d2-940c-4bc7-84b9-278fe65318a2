package com.bees360.project.servicetype;

import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage;

/**
 * An interface contains methods which related to ServiceType, such as change reasons which are
 * grouped by ServiceType
 *
 * <AUTHOR> liu
 * @since 2023/9/23
 */
public interface ServiceTypeManager {
    /**
     * method for create project state change reason by group
     *
     * @param serviceType service type code, such as 1 represent QUICK_INSPECT
     * @param displayText display text of change reason
     * @param type change reason type, such as UNDEFINED, OPEN, PAUSE, CLOSE, UNPAUSE, REOPEN
     * @param createdBy who created the change reason
     * @return ProjectStateChangeReason
     */
    ProjectStateChangeReason createStateChangeReason(
            String serviceType,
            String displayText,
            ProjectStateChangeReasonMessage.ProjectStateChangeReasonType type,
            String createdBy);

    /**
     * method for find project state change reason in some condition
     *
     * @param serviceType service type code, such as 1 represent QUICK_INSPECT
     * @param type change reason type
     * @return ProjectStateChangeReasons
     */
    Iterable<? extends ProjectStateChangeReason> findStateChangeReason(
            String serviceType, ProjectStateChangeReasonMessage.ProjectStateChangeReasonType type);
}
