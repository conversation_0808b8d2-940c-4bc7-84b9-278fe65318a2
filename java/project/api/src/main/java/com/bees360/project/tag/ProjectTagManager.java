package com.bees360.project.tag;

import jakarta.validation.constraints.NotNull;

import java.util.List;

public interface ProjectTagManager extends ProjectTagProvider, ProjectTagRepository {
    int updateProjectTag(
            @NotNull String projectId,
            @NotNull Iterable<? extends ProjectTag> tag,
            @NotNull Message.ProjectTagType type,
            @NotNull String updatedBy,
            @NotNull String updatedVia);

    ProjectTag createTag(ProjectTag tag);

    List<? extends ProjectTag> batchCreateTag(Iterable<? extends ProjectTag> tags);

    boolean deleteTagById(String tagId, String updatedBy, String updatedVia);
}
