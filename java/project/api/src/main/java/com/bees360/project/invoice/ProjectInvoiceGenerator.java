package com.bees360.project.invoice;

import com.bees360.api.AlreadyExistsException;

public interface ProjectInvoiceGenerator {

    /**
     * Generate invoice for project automatically.
     *
     * @param projectId project id
     * @param createdBy created by
     */
    default void generateProjectInvoice(String projectId, String createdBy) {
        generateProjectInvoice(projectId, null, null, createdBy);
    }

    /**
     * 通过pdfResourceKey生成invoice.
     *
     * @param projectId project id.
     * @param pdfResourceKey pdf资源的key.
     * @param summary invoice summary.
     * @param createdBy 创建者id.
     * @throws IllegalArgumentException 当 pdfResourceKey is blank时抛出
     * @throws AlreadyExistsException 当invoice已存在时抛出，例如summary重复
     */
    void generateProjectInvoice(
            String projectId, String pdfResourceKey, String summary, String createdBy);
    /**
     * 通过htmlResourceKey生成invoice.
     *
     * @param projectId project id.
     * @param htmlResourceKey html资源的key.
     * @param summary invoice summary.
     * @param createdBy 创建者id.
     * @throws IllegalArgumentException 当 pdfResourceKey is blank时抛出
     * @throws AlreadyExistsException 当invoice已存在时抛出，例如summary重复
     */
    void generateProjectInvoiceByHtml(
            String projectId, String htmlResourceKey, String summary, String createdBy);
}
