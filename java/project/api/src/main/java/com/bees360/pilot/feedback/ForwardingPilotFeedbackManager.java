package com.bees360.pilot.feedback;

import com.google.common.collect.ForwardingObject;

public class ForwardingPilotFeedbackManager extends ForwardingObject
        implements PilotFeedbackManager {

    private final PilotFeedbackManager pilotFeedbackManager;

    public ForwardingPilotFeedbackManager(PilotFeedbackManager pilotFeedbackManager) {
        this.pilotFeedbackManager = pilotFeedbackManager;
    }

    @Override
    protected PilotFeedbackManager delegate() {
        return pilotFeedbackManager;
    }

    @Override
    public PilotFeedback findByProjectId(String projectId) {
        return delegate().findByProjectId(projectId);
    }

    @Override
    public boolean save(PilotFeedback pilotFeedback) {
        return delegate().save(pilotFeedback);
    }
}
