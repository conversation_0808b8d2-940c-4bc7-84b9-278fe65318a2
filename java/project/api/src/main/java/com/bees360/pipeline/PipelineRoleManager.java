package com.bees360.pipeline;

import com.bees360.project.member.RoleEnum;

import jakarta.annotation.Nullable;

public interface PipelineRoleManager {
    Iterable<Integer> findStageByRole(RoleEnum role, String projectId);

    @Nullable
    RoleEnum findRoleByTaskKeyAndStatus(
            String taskKey, Message.PipelineStatus status, String projectId);

    Iterable<String> findTaskKeysByRole(RoleEnum role, String projectId);
}
