package com.bees360.project;

import com.bees360.util.DateTimes;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class InmemoryProjectPaymentManager implements ProjectPaymentManager {
    private final Map<String, Boolean> projectPaymentMap = new ConcurrentHashMap<>();

    @Override
    public void setProjectsPaymentStatus(Map<String, Boolean> projectsPaymentMap) {
        projectPaymentMap.putAll(projectsPaymentMap);
    }

    @Override
    public Iterable<? extends Payment> findPaymentByProjectIds(Iterable<String> projectIds) {
        var list = new ArrayList<Payment>();
        projectIds.forEach(
                projectId -> {
                    var payStatus = projectPaymentMap.get(projectId);
                    Message.ProjectMessage.Payment payment =
                            Message.ProjectMessage.Payment.newBuilder()
                                    .setProjectId(projectId)
                                    .setIsPaid(payStatus)
                                    .setUpdatedAt(DateTimes.toTimestamp(Instant.now()))
                                    .build();
                    list.add(
                            new Payment() {
                                @Override
                                public String getProjectId() {
                                    return payment.getProjectId();
                                }

                                @Override
                                public boolean isPaid() {
                                    return payment.getIsPaid();
                                }

                                @Override
                                public Instant getUpdatedAt() {
                                    return DateTimes.toInstant(payment.getUpdatedAt());
                                }
                            });
                });
        return list;
    }
}
