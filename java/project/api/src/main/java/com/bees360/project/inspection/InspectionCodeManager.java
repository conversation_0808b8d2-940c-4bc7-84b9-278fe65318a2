package com.bees360.project.inspection;

import jakarta.annotation.Nullable;

import java.time.Instant;

public interface InspectionCodeManager extends InspectionCodeProvider {

    /**
     * @param inspectionCode not null and not empty
     * @param inspectionCodeLink not null and not empty
     */
    void updateInspectionCode(
            String projectId,
            String inspectionCode,
            String inspectionCodeLink,
            @Nullable Instant expireAt);
}
