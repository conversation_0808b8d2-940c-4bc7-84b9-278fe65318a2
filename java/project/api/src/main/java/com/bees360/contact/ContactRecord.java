package com.bees360.contact;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.activity.Comment.Attachment;
import com.bees360.api.Proto;
import com.bees360.contact.Message.ContactRecordMessage;
import com.bees360.contact.Message.ContactRecordMessage.ContactMethod;
import com.bees360.contact.util.ContactRecordUtil;
import com.bees360.util.DateTimes;
import com.bees360.util.Defaults;
import com.bees360.util.Iterables;
import com.google.protobuf.BoolValue;

import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.Optional;

public interface ContactRecord extends Proto<ContactRecordMessage> {

    String getProjectId();

    String getCreatedBy();

    String getContactFrom();

    String getContactTo();

    Instant getCreatedAt();

    Boolean isReached();

    String getContactMethod();

    String getPhoneNumber();

    String getContent();

    Iterable<? extends Attachment> getAttachments();

    static ContactRecord from(ContactRecordMessage message) {
        return message == null || ContactRecordMessage.getDefaultInstance().equals(message)
                ? null
                : new ContactRecord() {
                    @Override
                    public String getProjectId() {
                        return message.getProjectId();
                    }

                    @Override
                    public String getCreatedBy() {
                        return message.getUserId();
                    }

                    @Override
                    public String getContactFrom() {
                        return Optional.ofNullable(
                                        Defaults.nullIfEmpty(message.getContactInitiator()))
                                .orElseGet(
                                        () -> ContactRecordUtil.getContactFrom(message.getType()));
                    }

                    @Override
                    public String getContactTo() {
                        return Optional.ofNullable(
                                        Defaults.nullIfEmpty(message.getContactRecipient()))
                                .orElseGet(() -> ContactRecordUtil.getContactTo(message.getType()));
                    }

                    @Override
                    public Instant getCreatedAt() {
                        return DateTimes.toInstant(message.getCreatedAt());
                    }

                    @Override
                    public Boolean isReached() {
                        return message.hasIsReached() ? message.getIsReached().getValue() : null;
                    }

                    @Override
                    public String getContactMethod() {
                        if (ContactMethod.UNKNWON == message.getContactMethod()) {
                            return "";
                        }
                        return message.getContactMethod().name();
                    }

                    @Override
                    public String getPhoneNumber() {
                        return message.getPhoneNumber();
                    }

                    @Override
                    public String getContent() {
                        return message.getContent();
                    }

                    @Override
                    public Iterable<? extends Attachment> getAttachments() {
                        return Iterables.transform(message.getAttachmentsList(), Attachment::from);
                    }

                    @Override
                    public ContactRecordMessage toMessage() {
                        return message;
                    }
                };
    }

    @Override
    default ContactRecordMessage toMessage() {
        var builder = ContactRecordMessage.newBuilder();
        acceptIfNotNull(builder::setProjectId, getProjectId());
        acceptIfNotNull(builder::setUserId, getCreatedBy());
        acceptIfNotNull(builder::setContent, getContent());
        acceptIfNotNull(builder::setContactInitiator, getContactFrom());
        acceptIfNotNull(builder::setContactRecipient, getContactTo());
        acceptIfNotNull(builder::setIsReached, isReached(), BoolValue::of);
        acceptIfNotNull(builder::setPhoneNumber, getPhoneNumber());
        Optional.ofNullable(getAttachments())
                .ifPresent(
                        attachments ->
                                builder.addAllAttachments(
                                        Iterables.transform(attachments, Attachment::toMessage)));
        acceptIfNotNull(builder::setCreatedAt, getCreatedAt(), DateTimes::toTimestamp);
        if (StringUtils.isNotEmpty(getContactMethod())) {
            acceptIfNotNull(builder::setContactMethod, getContactMethod(), ContactMethod::valueOf);
        }
        return builder.build();
    }
}
