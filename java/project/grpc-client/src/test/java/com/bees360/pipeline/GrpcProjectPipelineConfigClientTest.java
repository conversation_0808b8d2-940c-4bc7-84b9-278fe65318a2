package com.bees360.pipeline;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.pipeline.config.GrpcProjectPipelineConfigConfig;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(properties = "GRPC_SERVER_PORT=9779")
@DirtiesContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class GrpcProjectPipelineConfigClientTest extends AbstractProjectPipelineConfigServiceTest {

    @Configuration
    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class, // Create required server beans
        GrpcServerFactoryAutoConfiguration.class, // Select server implementation
        GrpcClientAutoConfiguration.class,
        ExceptionTranslateInterceptor.class,
    }) // Support @GrpcClient annotation
    @Import({GrpcProjectPipelineConfigConfig.class})
    static class Config {
        @Bean
        public GrpcProjectPipelineConfigService projectPipelineConfigService() {
            return new GrpcProjectPipelineConfigService(new InMemoryProjectPipelineConfigService());
        }
    }

    @Override
    protected ProjectPipelineConfigService delegate() {
        return configService;
    }

    private final ProjectPipelineConfigService configService;

    public GrpcProjectPipelineConfigClientTest(
            @Autowired ProjectPipelineConfigService configService) {
        this.configService = configService;
    }

    @Test
    void testSaveConfigAndGet() {
        super.testSaveConfigAndGet();
    }

    @Test
    void testSaveConfigWithEmptyProcessedBy() {
        super.testSaveConfigWithEmptyProcessedBy();
    }

    @Test
    void testSaveConfigWithEmptyProcessedByAndInsuredBy() {
        super.testSaveConfigWithEmptyProcessedByAndInsuredBy();
    }

    @Test
    void testSaveConfigWithEmptyInsuredBy() {
        super.testSaveConfigWithEmptyInsuredBy();
    }

    @Test
    void testCreateConfigWhenInsuredByProcessedByServiceTypeDuplicatedThrowException() {
        super.testCreateConfigWhenInsuredByProcessedByServiceTypeDuplicatedThrowException();
    }

    @Test
    void testCreateConfigWhenDeletedAndCreateRepeatedlyConfig() {
        super.testCreateConfigWhenDeletedAndCreateRepeatedlyConfig();
    }

    @Test
    void testUpdateConfigWhenInsuredByProcessedByServiceTypeDuplicatedThrowException() {
        super.testUpdateConfigWhenInsuredByProcessedByServiceTypeDuplicatedThrowException();
    }

    @Test
    void testSaveConfigAndUpdateConfig() {
        super.testSaveConfigAndUpdateConfig();
    }

    @Test
    void testSaveConfigAndDeleteById() {
        super.testSaveConfigAndDeleteById();
    }

    @Test
    void testSaveConfigAndDeleteByIdExistsFallback() {
        super.testSaveConfigAndDeleteByIdExistsFallback();
    }

    @Test
    void testSaveConfigAndFindAndCountByQuery() {
        super.testSaveConfigAndFindAndCountByQuery();
    }

    @Test
    void testSaveConfigWithPolicyRenewal() {
        super.testSaveConfigWithPolicyRenewal();
    }

    @Test
    void testSaveConfigWithPolicyRenewalButNoRenewalPipelineDef() {
        super.testSaveConfigWithPolicyRenewalButNoRenewalPipelineDef();
    }
}
