package com.bees360.customer;

import com.bees360.api.NotFoundException;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.customer.Message.CustomerMessage.CustomerRole;
import com.bees360.customer.config.GrpcCustomerClientConfig;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.util.Iterables;
import com.google.protobuf.util.JsonFormat;

import net.devh.boot.grpc.server.service.GrpcService;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
@DirtiesContext
@SpringBootTest(properties = "GRPC_SERVER_PORT=9777")
public class GrpcCustomerClientTest extends AbstractCustomerTest {

    @Configuration
    @Import({GrpcCustomerClientConfig.class, ExceptionTranslateInterceptor.class})
    static class config {
        @Bean
        public CustomerManager customerManager() {
            return new GrpcCustomerManager();
        }

        @MockBean DivisionManager divisionManager;

        @MockBean CustomerPolicyTypeManager customerPolicyTypeManager;

        @GrpcService
        public GrpcCustomerService grpcCustomerService(@Autowired CustomerManager customerManager) {
            return new GrpcCustomerService(
                    customerManager, divisionManager, customerPolicyTypeManager);
        }
    }

    public GrpcCustomerClientTest(@Autowired GrpcCustomerClient grpcCustomerClient) {
        super(grpcCustomerClient);
    }

    @Test
    @Override
    void testCreate() {
        super.testCreate();
    }

    @Test
    @Override
    void testFindById() {
        super.testFindById();
    }

    @Test
    @Override
    void testFindByKey() {
        super.testFindByKey();
    }

    @Test
    @Override
    void testFindByName() {
        super.testFindByName();
    }

    @Test
    @Override
    void testFindByRole() {
        super.testFindByRole();
    }

    @Test
    @Override
    void testUpdateAttribute() {
        super.testUpdateAttribute();
    }

    @Test
    @Override
    void testUpdate() {
        super.testUpdate();
    }
}

class GrpcCustomerManager implements CustomerManager {

    private final Map<String, Customer> customerMap = new HashMap<>();

    @Override
    public String createCustomer(Customer customer) {
        String id = RandomStringUtils.randomNumeric(6);
        customerMap.put(id, Customer.of(customer.toMessage().toBuilder().setId(id).build()));
        return id;
    }

    @Override
    public void updateCustomer(Customer customer) {
        customerMap.put(customer.getId(), customer);
    }

    @Override
    public boolean deleteById(String id) {
        return customerMap.remove(id) != null;
    }

    @Override
    public Customer findByName(String name) {
        for (Customer value : customerMap.values()) {
            if (StringUtils.equals(value.getName(), name)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public Customer findById(String id) {
        return customerMap.get(id);
    }

    @Override
    public Customer findByKey(String key) {
        for (Customer value : customerMap.values()) {
            if (StringUtils.equals(value.getName(), key)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public Iterable<? extends Customer> findByRole(List<CustomerRole> role) {
        if (CollectionUtils.isEmpty(role)) {
            return customerMap.values();
        }
        for (Customer value : customerMap.values()) {
            if (Iterables.toList(value.getRoles()).contains(role.get(0))) {
                return List.of(value);
            }
        }
        return null;
    }

    @Override
    public Iterable<String> listOperatingCompany(String customerId) {
        return null;
    }

    @Override
    public void updateAttribute(String customerId, String attributeName, String attributesValue) {
        var customer = customerMap.get(customerId);
        if (customer == null) {
            throw new NotFoundException(String.format("Customer %s not found.", customerId));
        }
        var field =
                Message.CustomerAttributes.getDescriptor().getFields().stream()
                        .filter(f -> f.getName().equals(attributeName))
                        .findFirst()
                        .orElseThrow(
                                () ->
                                        new NotFoundException(
                                                String.format(
                                                        "Fail to update attributes: The attribute"
                                                                + " %s is not found.",
                                                        attributeName)));
        var attributeBuilder = Message.CustomerAttributes.newBuilder();
        try {
            JsonFormat.parser().ignoringUnknownFields().merge(attributesValue, attributeBuilder);
        } catch (Exception e) {
            throw new IllegalArgumentException(
                    "Fail to update attributes: The attribute value is invalid.", e);
        }
        var newAttributes =
                customer.toMessage().getAttributes().toBuilder()
                        .setField(field, attributeBuilder.getField(field))
                        .build();

        customer =
                Customer.of(customer.toMessage().toBuilder().setAttributes(newAttributes).build());
        customerMap.put(customerId, customer);
    }
}
