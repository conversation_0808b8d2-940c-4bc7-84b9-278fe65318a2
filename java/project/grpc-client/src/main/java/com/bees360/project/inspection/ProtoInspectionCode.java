package com.bees360.project.inspection;

import com.bees360.project.Message.InspectionCodeMessage;
import com.bees360.util.DateTimes;

import lombok.NonNull;

import java.time.Instant;
import java.util.Optional;

class ProtoInspectionCode implements InspectionCode {

    private final InspectionCodeMessage message;

    public ProtoInspectionCode(@NonNull InspectionCodeMessage message) {
        this.message = message;
    }

    public static ProtoInspectionCode from(@NonNull InspectionCodeMessage message) {
        return new ProtoInspectionCode(message);
    }

    @Override
    public String getProjectId() {
        return message.getProjectId();
    }

    @Override
    public String getInspectionCode() {
        return message.getInspectionCode();
    }

    @Override
    public String getInspectionCodeLink() {
        return message.getInspectionCodeLink();
    }

    @Override
    public Instant getExpireAt() {
        return Optional.ofNullable(message.getExpireAt()).map(DateTimes::toInstant).orElse(null);
    }

    @Override
    public InspectionCodeMessage toMessage() {
        return message;
    }
}
