package com.bees360.project;

import com.bees360.util.DateTimes;

import java.time.Instant;

public class ProtobufPayment implements Payment {
    private final Message.ProjectMessage.Payment message;

    public ProtobufPayment(Message.ProjectMessage.Payment message) {
        this.message = message;
    }

    @Override
    public String getProjectId() {
        return message.getProjectId();
    }

    @Override
    public boolean isPaid() {
        return message.getIsPaid();
    }

    @Override
    public Instant getUpdatedAt() {
        return DateTimes.toInstant(message.getUpdatedAt());
    }

    @Override
    public Message.ProjectMessage.Payment toMessage() {
        return message;
    }
}
