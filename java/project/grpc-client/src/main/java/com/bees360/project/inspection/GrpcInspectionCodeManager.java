package com.bees360.project.inspection;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.grpc.GrpcApi;
import com.bees360.project.Message.InspectionCodeMessage;
import com.bees360.project.Message.UpdateInspectionCodeRequest;
import com.bees360.project.ProjectInspectionCodeServiceGrpc.ProjectInspectionCodeServiceBlockingStub;
import com.bees360.util.DateTimes;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

import java.time.Instant;

@Log4j2
public class GrpcInspectionCodeManager implements InspectionCodeManager {

    private final GrpcApi<ProjectInspectionCodeServiceBlockingStub> grpcApi;

    public GrpcInspectionCodeManager(GrpcApi<ProjectInspectionCodeServiceBlockingStub> grpcApi) {
        this.grpcApi = grpcApi;
        log.info("Created {}(grpcApi={})", this, grpcApi);
    }

    @Override
    public void updateInspectionCode(
            String projectId, String inspectionCode, String inspectionCodeLink, Instant expireAt) {
        var requestBuilder =
                UpdateInspectionCodeRequest.newBuilder()
                        .setProjectId(projectId)
                        .setInspectionCode(inspectionCode)
                        .setInspectionCodeLink(inspectionCodeLink);
        acceptIfNotNull(requestBuilder::setExpireAt, expireAt, DateTimes::toTimestamp);
        grpcApi.apply(api -> api.updateInspectionCode(requestBuilder.build()));
    }

    @Override
    public InspectionCode getByProjectId(String projectId) {
        var message = grpcApi.apply(api -> api.getByProjectId(StringValue.of(projectId)));
        if (InspectionCodeMessage.getDefaultInstance().equals(message)) {
            return null;
        }
        return ProtoInspectionCode.from(message);
    }
}
