package com.bees360.customer.config;

import com.bees360.customer.CustomerServiceGrpc;
import com.bees360.customer.GrpcDivisionClient;
import com.bees360.grpc.GrpcApi;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcDivisionClientConfig {

    @GrpcClient("divisionManager")
    private CustomerServiceGrpc.CustomerServiceBlockingStub blockingStub;

    @GrpcClient("divisionManager")
    private CustomerServiceGrpc.CustomerServiceStub stub;

    @Bean
    public GrpcDivisionClient grpcDivisionClient() {
        return new GrpcDivisionClient(GrpcApi.of(blockingStub), stub);
    }
}
