package com.bees360.om;

import com.bees360.common.Message.TimeQuery;
import com.bees360.grpc.GrpcApi;
import com.bees360.project.OperationsManagerServiceGrpc.OperationsManagerServiceBlockingStub;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.google.protobuf.Empty;

import lombok.extern.log4j.Log4j2;

import java.time.Instant;
import java.util.Iterator;

@Log4j2
public class GrpcOperationsManagerClient implements OperationsManagerProvider {
    private final GrpcApi<OperationsManagerServiceBlockingStub> blockingStub;

    public GrpcOperationsManagerClient(GrpcApi<OperationsManagerServiceBlockingStub> blockingStub) {
        this.blockingStub = blockingStub;

        log.info("Created '{}(blockingStub={})'", this, this.blockingStub);
    }

    @Override
    public Iterable<? extends OperationsManager> getAllOperationsManager() {
        Iterator<com.bees360.project.Message.OperationsManagerMessage> iterator =
                blockingStub.apply(api -> api.getAllOperationsManager(Empty.getDefaultInstance()));
        return blockingStub.get(() -> Iterables.transform(() -> iterator, OperationsManager::from));
    }

    @Override
    public Iterable<? extends OperationsManager> getAllOperationsManagerKPI(
            Instant startTime, Instant endTime) {
        var query =
                TimeQuery.newBuilder()
                        .setStartTime(DateTimes.toTimestamp(startTime))
                        .setEndTime(DateTimes.toTimestamp(endTime))
                        .build();
        Iterator<com.bees360.project.Message.OperationsManagerMessage> iterator =
                blockingStub.apply(api -> api.getAllOperationsManagerKPI(query));
        return blockingStub.get(() -> Iterables.transform(() -> iterator, OperationsManager::from));
    }
}
