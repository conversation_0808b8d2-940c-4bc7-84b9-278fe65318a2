package com.bees360.kpi;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.impl.ForwardingActivityManager;
import com.bees360.kpi.config.KPIProviderConfig;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringBootTest
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class KPIProviderTest extends AbstractKPIProviderTest {

    @Import(value = {KPIProviderConfig.class})
    @Configuration
    static class Config {
        @Bean
        ActivityManager activityManager() {
            return new ForwardingActivityManager() {
                @Override
                protected ActivityManager delegate() {
                    return activityManager;
                }
            };
        }
    }

    static ActivityManager activityManager = getActivityManager();

    public KPIProviderTest(
            @Autowired ActivityManager activityManager, @Autowired KPIProvider kpiProvider) {
        super(activityManager, kpiProvider);
    }

    @BeforeEach
    public void clean() {
        activityManager = getActivityManager();
    }

    @Test
    public void testGetKPISnapshot() {
        super.testGetKPISnapshot();
    }

    @Test
    public void testGetKPISeriesNumbersWithWeek() {
        super.testGetKPISeriesNumbersWithWeek();
    }

    @Test
    public void testGetKPISeriesNumbersWithMonth() {
        super.testGetKPISeriesNumbersWithMonth();
    }

    @Test
    public void testGetOperationsManagerKPIWithImageQuality() {
        super.testGetOperationsManagerKPIWithImageQuality();
    }

    @Test
    public void testGetOperationsManagerKPIWithTurnAround() {
        super.testGetOperationsManagerKPIWithTurnAround();
    }

    @Test
    public void testGetOperationsManagerKPI() {
        super.testGetOperationsManagerKPI();
    }
}
