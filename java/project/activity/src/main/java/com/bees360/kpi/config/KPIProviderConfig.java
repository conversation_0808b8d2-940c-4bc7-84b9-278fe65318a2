package com.bees360.kpi.config;

import com.bees360.kpi.AggregateBasedAggregate;
import com.bees360.kpi.DefaultKPIProvider;
import com.bees360.kpi.KPIProvider;
import com.bees360.kpi.Message;
import com.bees360.kpi.Metrics;
import com.bees360.kpi.MetricsAggregate;
import com.bees360.kpi.MetricsProvider;
import com.bees360.kpi.WeightedAverageAggregate;
import com.bees360.util.TimeZones;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Configuration
@Import(
        value = {
            OmMetricsProviderConfig.class,
        })
public class KPIProviderConfig {
    @Configuration
    @EnableConfigurationProperties
    @ConfigurationProperties(prefix = "kpi.params")
    @Data
    public static class KPIParams {
        private double claimImageQualityWeight;
        private double uwImageQualityWeight;
        private double claimTurnAroundTimeWeight;
        private double uwTurnAroundTimeWeight;
        private double imageQualityWeight;
        private double turnAroundTimeWeight;
        private int fullScore;
        private int fullImageScore;
        private int fullTurnAroundTimeScore;
    }

    @Bean
    public Map<Message.KPINamespace, KPIProvider> kpiProviderMap(KPIProvider omKPIProvider) {
        return Map.of(Message.KPINamespace.OM, omKPIProvider);
    }

    @Bean
    public KPIProvider omKPIProvider(
            MetricsProvider omClaimTurnAroundTimeMetricProvider,
            MetricsProvider omUwTurnAroundTimeMetricProvider,
            MetricsProvider omClaimImageQualityMetricProvider,
            MetricsProvider omUwImageQualityMetricProvider,
            MetricsAggregate imageQualityAggregate,
            MetricsAggregate turnAroundTimeAggregate,
            MetricsAggregate omTotalScoreAggregate,
            Function<String, List<String>> omMetrics2Metrics) {
        return new DefaultKPIProvider(
                omMetrics2Metrics,
                Map.of(
                        "claimImageQuality", omClaimImageQualityMetricProvider,
                        "uwImageQuality", omUwImageQualityMetricProvider,
                        "claimTurnAroundTime", omClaimTurnAroundTimeMetricProvider,
                        "uwTurnAroundTime", omUwTurnAroundTimeMetricProvider),
                Map.of(
                        "imageQuality",
                        imageQualityAggregate,
                        "turnAroundTime",
                        turnAroundTimeAggregate,
                        "totalScore",
                        omTotalScoreAggregate),
                TimeZones.DEFAULT_US_TIMEZONE_ID);
    }

    @Bean
    public MetricsAggregate omTotalScoreAggregate(
            KPIParams params,
            MetricsAggregate turnAroundTimeAggregate,
            MetricsAggregate turnAroundTimeAndImageQualityAggregate) {
        var imageQualityA =
                new WeightedAverageAggregate(
                        getDefaultWeightProvider(
                                1.0d / params.getFullImageScore(),
                                params.getClaimImageQualityWeight() / params.getFullImageScore(),
                                params.getUwImageQualityWeight() / params.getFullImageScore()));
        MetricsAggregate expAggregate =
                list -> {
                    Metrics metrics = list.get(0);
                    return Metrics.from(
                            metrics.toMessage().toBuilder()
                                    .setScore(Math.exp(-metrics.getScore()))
                                    .build());
                };
        var turnAroundAverageAggregate =
                getDefaultWeightProvider(
                        1.0d,
                        params.getClaimTurnAroundTimeWeight(),
                        params.getUwTurnAroundTimeWeight());
        var turnAroundWeightAverageAggregate =
                new WeightedAverageAggregate(turnAroundAverageAggregate);
        var turnAroundA =
                new AggregateBasedAggregate(
                        List.of(expAggregate, expAggregate), turnAroundWeightAverageAggregate, 1);
        return new AggregateBasedAggregate(
                List.of(imageQualityA, turnAroundA), turnAroundTimeAndImageQualityAggregate, 2);
    }

    @Bean
    public MetricsAggregate turnAroundTimeAndImageQualityAggregate(KPIParams params) {
        var fun =
                getDefaultWeightProvider(
                        params.getFullScore(),
                        params.getFullScore() * params.getImageQualityWeight(),
                        params.getFullScore() * params.getTurnAroundTimeWeight());
        return new WeightedAverageAggregate(fun);
    }

    @Bean
    public MetricsAggregate turnAroundTimeAggregate(KPIParams params) {
        var fun =
                getDefaultWeightProvider(
                        1.0d,
                        params.getClaimTurnAroundTimeWeight(),
                        params.getUwTurnAroundTimeWeight());
        return new WeightedAverageAggregate(fun);
    }

    @Bean
    public MetricsAggregate imageQualityAggregate(KPIParams params) {
        var fun =
                getDefaultWeightProvider(
                        1.0d,
                        params.getClaimImageQualityWeight(),
                        params.getUwImageQualityWeight());
        return new WeightedAverageAggregate(fun);
    }

    static Function<List<Metrics>, List<Double>> getDefaultWeightProvider(
            double baseWeight, double weight1, double weight2) {
        return list -> {
            double w1 = weight1;
            double w2 = weight2;
            Metrics metrics1 = list.get(0);
            Metrics metrics2 = list.get(1);
            if (metrics1.getCount() == 0 && metrics2.getCount() == 0) {
                w1 = 0.0d;
                w2 = 0.0d;
            } else if (metrics1.getCount() == 0) {
                w1 = 0.0d;
                w2 = baseWeight;
            } else if (metrics2.getCount() == 0) {
                w1 = baseWeight;
                w2 = 0.0d;
            }
            return List.of(w1, w2);
        };
    }

    @Bean
    public Function<String, List<String>> omMetrics2Metrics() {
        return field -> {
            switch (field) {
                case "imageQuality":
                    return List.of("claimImageQuality", "uwImageQuality");
                case "turnAroundTime":
                    return List.of("claimTurnAroundTime", "uwTurnAroundTime");
                case "totalScore":
                    return List.of(
                            "claimImageQuality",
                            "uwImageQuality",
                            "claimTurnAroundTime",
                            "uwTurnAroundTime");
            }
            return List.of();
        };
    }
}
