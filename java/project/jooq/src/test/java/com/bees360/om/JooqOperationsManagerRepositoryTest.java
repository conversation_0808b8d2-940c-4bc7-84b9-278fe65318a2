package com.bees360.om;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.om.config.JooqOperationsManagerRepositoryConfig;
import com.bees360.util.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;

@SpringBootTest
@SpringJUnitConfig
@DirtiesContext
public class JooqOperationsManagerRepositoryTest {

    @Configuration
    @ApplicationAutoConfig
    @Import({JooqOperationsManagerRepositoryConfig.class})
    static class config {}

    @Autowired OperationsManagerRepository operationsManagerRepository;

    @Test
    void saveAndGetTest() {
        var userId = RandomStringUtils.randomNumeric(8);
        var states = List.of("NY", "FL", "DC");
        operationsManagerRepository.save(userId, states);

        var oms = operationsManagerRepository.getAllOperationsManager();
        Assertions.assertEquals(
                1,
                Iterables.toStream(oms).filter(om -> userId.equals(om.getUser().getId())).count());
    }

    @Test
    void deleteTest() {
        var userId = RandomStringUtils.randomNumeric(10);
        var states = List.of("NY", "FL", "DC");
        operationsManagerRepository.save(userId, states);

        Assertions.assertTrue(operationsManagerRepository.delete(userId));
    }
}
