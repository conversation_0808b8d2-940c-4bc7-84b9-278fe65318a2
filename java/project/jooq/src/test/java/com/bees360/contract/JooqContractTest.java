package com.bees360.contract;

import static com.bees360.jooq.persistent.project.Tables.PROJECT;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.contract.config.JooqContractRepositoryConfig;
import com.bees360.customer.CustomerManager;
import com.bees360.customer.config.JooqCustomerRepositoryConfig;
import com.bees360.project.RandomProjectUtil;
import com.bees360.util.Iterables;

import org.jooq.DSLContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Random;

@DirtiesContext
@SpringBootTest
@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class JooqContractTest extends AbstractContractTest {

    @Import(value = {JooqContractRepositoryConfig.class, JooqCustomerRepositoryConfig.class})
    @Configuration
    static class Config {}

    private final ContractServiceManager contractServiceManager;

    private final CustomerManager customerManager;

    @Autowired private DSLContext dsl;

    public JooqContractTest(
            @Autowired ContractServiceManager contractServiceManager,
            @Autowired ContractManager jooqContractRepository,
            @Autowired CustomerManager jooqCustomerRepository) {
        super(jooqContractRepository);
        this.contractServiceManager = contractServiceManager;
        this.customerManager = jooqCustomerRepository;
    }

    @Override
    public void generateServiceItem(Iterable<? extends ContractServiceItem> items) {
        items.forEach(contractServiceManager::addServiceItem);
    }

    @Test
    public void save() {
        super.save();
    }

    @Test
    public void update() {
        super.update();
    }

    @Test
    public void findById() {
        super.findById();
    }

    @Test
    public void findByUnknownId() {
        super.findByUnknownId();
    }

    @Test
    public void findByCompanyId() {
        super.findByCompanyId();
    }

    @Test
    public void updateAttachment() {
        super.updateAttachment();
    }

    @Test
    public void loadAll() {
        super.loadAll();
    }

    @Test
    public void create() {
        super.create();
    }

    @Test
    public void testDeleteContractServiceItemShouldBeCorrect() {
        var contractA = createContract();
        var expectedSizeA = Iterables.toList(contractA.getServiceItem()).size();
        var idA = contractManager.save(contractA);
        Assertions.assertNotNull(idA);

        var contractB = createContract();
        var expectedSizeB = Iterables.toList(contractB.getServiceItem()).size();
        var idB = contractManager.save(contractB);
        Assertions.assertNotNull(idB);

        var item = contractA.getServiceItem().iterator().next();
        var actualA = contractManager.deleteItemsToContract(idA, List.of(item));
        Assertions.assertNotNull(actualA);
        Assertions.assertEquals(
                expectedSizeA - 1, Iterables.toList(actualA.getServiceItem()).size());

        var actualB = contractManager.findById(idB);
        Assertions.assertEquals(expectedSizeB, Iterables.toList(actualB.getServiceItem()).size());
    }

    @Test
    public void testFindInactiveByCompanyIdShouldBeNull() {
        super.testFindInactiveByCompanyIdShouldBeNull();
    }

    @Test
    public void findByQuery() {
        super.findByQuery();
    }

    @Test
    public void updateContractStatusWithOpenProjectShouldBeCorrect() {
        var contract = createContract();
        var id = contractManager.save(contract);

        var project = RandomProjectUtil.randomProject();
        dsl.insertInto(
                        PROJECT,
                        PROJECT.ID,
                        PROJECT.PROJECT_TYPE,
                        PROJECT.POLICY_ID,
                        PROJECT.CONTRACT_ID)
                .values(
                        project.getId(),
                        project.getProjectType().getCode(),
                        project.getPolicy().getId(),
                        id)
                .execute();

        var result =
                contractManager.updateStatus(
                        id, Message.ContractMessage.ContractStatus.INACTIVE, false);
        Assertions.assertFalse(result);

        result =
                contractManager.updateStatus(
                        id, Message.ContractMessage.ContractStatus.INACTIVE, true);
        Assertions.assertTrue(result);

        var actual = contractManager.findById(id);
        Assertions.assertNotNull(actual);
        Assertions.assertEquals(
                actual.getStatus(), Message.ContractMessage.ContractStatus.INACTIVE.name());
    }

    @Test
    public void updateContractStatusWithTestProjectShouldBeCorrect() {
        var contract = createContract();
        var id = contractManager.save(contract);

        var project = RandomProjectUtil.randomProject();
        dsl.insertInto(
                        PROJECT,
                        PROJECT.ID,
                        PROJECT.PROJECT_TYPE,
                        PROJECT.POLICY_ID,
                        PROJECT.CONTRACT_ID,
                        PROJECT.IS_TEST_CASE)
                .values(
                        project.getId(),
                        project.getProjectType().getCode(),
                        project.getPolicy().getId(),
                        id,
                        true)
                .execute();

        var result =
                contractManager.updateStatus(
                        id, Message.ContractMessage.ContractStatus.INACTIVE, false);
        Assertions.assertTrue(result);

        var actual = contractManager.findById(id);
        Assertions.assertNotNull(actual);
        Assertions.assertEquals(
                actual.getStatus(), Message.ContractMessage.ContractStatus.INACTIVE.name());
    }

    @Test
    public void testFindByCompanyNameWithFuzzySearch() {
        var contract = createContract();
        var id = contractManager.save(contract);

        var insuredCompany = contract.getInsuredBy();
        customerManager.createCustomer(insuredCompany);

        var processedCompany = contract.getProcessedBy();
        customerManager.createCustomer(processedCompany);

        var random = new Random();
        var begin = random.nextInt(9);
        var postfix = insuredCompany.getName().substring(begin).toLowerCase(Locale.ROOT);

        var query = Message.ContractQuery.newBuilder().setCompanyName(postfix);
        var result = contractManager.findByQuery(query.build());
        Assertions.assertNotNull(
                Iterables.toStream(result)
                        .filter(c -> Objects.equals(c.getId(), id))
                        .findFirst()
                        .orElse(null));

        var end = random.nextInt(9);
        var prefix = processedCompany.getName().substring(0, end).toLowerCase(Locale.ROOT);
        query.setCompanyName(prefix);
        result = contractManager.findByQuery(query.build());
        Assertions.assertNotNull(
                Iterables.toStream(result)
                        .filter(c -> Objects.equals(c.getId(), id))
                        .findFirst()
                        .orElse(null));
    }
}
