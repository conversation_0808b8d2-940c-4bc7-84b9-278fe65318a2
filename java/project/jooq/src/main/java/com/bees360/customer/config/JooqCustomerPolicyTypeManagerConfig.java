package com.bees360.customer.config;

import com.bees360.customer.JooqCustomerPolicyTypeManager;
import com.bees360.jooq.config.JooqConfig;

import org.jooq.DSLContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({JooqConfig.class})
public class JooqCustomerPolicyTypeManagerConfig {
    @Bean
    JooqCustomerPolicyTypeManager jooqCustomerPolicyTypeManager(DSLContext dslContext) {
        return new JooqCustomerPolicyTypeManager(dslContext);
    }
}
