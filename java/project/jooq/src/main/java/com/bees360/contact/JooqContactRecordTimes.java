package com.bees360.contact;

import static com.bees360.jooq.persistent.project.Tables.CONTACT_RECORD;

import org.jooq.Record;

public class JooqContactRecordTimes implements ContactRecordTimes {
    private final Record record;
    public static final String CONTACT_TIMES_FIELD = "contact_times";

    public JooqContactRecordTimes(Record record) {
        this.record = record;
    }

    @Override
    public String getProjectId() {
        return record.get(CONTACT_RECORD.PROJECT_ID);
    }

    @Override
    public String getContactFrom() {
        return record.get(CONTACT_RECORD.CONTACT_FROM);
    }

    @Override
    public String getContactTo() {
        return record.get(CONTACT_RECORD.CONTACT_TO);
    }

    @Override
    public Integer getContactTimes() {
        return record.get(CONTACT_TIMES_FIELD, Integer.class);
    }
}
