package com.bees360.project.airspace;

import static com.bees360.jooq.persistent.project.Tables.PROJECT_AIRSPACE;

import com.bees360.jooq.persistent.project.enums.AirspaceStatus;
import com.bees360.util.Iterables;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.jooq.DSLContext;

import java.util.Map;

@Log4j2
public class JooqProjectAirspaceManager implements ProjectAirspaceManager {
    private final DSLContext dsl;

    public JooqProjectAirspaceManager(@NonNull DSLContext dslContext) {
        this.dsl = dslContext;
        log.info("Created {}(dsl={})", this, dsl);
    }

    @Override
    public void updateAirspace(String projectId, Airspace airspace) {
        var status =
                airspace.getStatus() == null ? null : AirspaceStatus.valueOf(airspace.getStatus());
        dsl.insertInto(
                        PROJECT_AIRSPACE,
                        PROJECT_AIRSPACE.PROJECT_ID,
                        PROJECT_AIRSPACE.STATUS,
                        PROJECT_AIRSPACE.HEIGHT_CEILING)
                .values(projectId, status, airspace.getHeightCeiling())
                .onConflict(PROJECT_AIRSPACE.PROJECT_ID)
                .doUpdate()
                .set(PROJECT_AIRSPACE.STATUS, status)
                .set(PROJECT_AIRSPACE.HEIGHT_CEILING, airspace.getHeightCeiling())
                .execute();
    }

    @Override
    public Map<String, ? extends Airspace> getLatestAirspace(Iterable<String> projectIds) {
        return dsl.selectFrom(PROJECT_AIRSPACE)
                .where(PROJECT_AIRSPACE.PROJECT_ID.in(Iterables.toList(projectIds)))
                .fetchMap(r -> r.get(PROJECT_AIRSPACE.PROJECT_ID), AirspaceRecord::new);
    }
}
