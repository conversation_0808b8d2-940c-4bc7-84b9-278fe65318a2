package com.bees360.customer;

import static com.bees360.jooq.persistent.project.Tables.CUSTOMER_POLICY_TYPE;
import static com.bees360.jooq.persistent.project.Tables.POLICY_TYPE;

import com.google.common.collect.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
public class JooqCustomerPolicyTypeManager implements CustomerPolicyTypeManager {
    private final DSLContext dsl;

    public JooqCustomerPolicyTypeManager(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Transactional
    @Override
    public void createPolicyType(String customerId, Iterable<String> policyTypes) {
        if (StringUtils.isBlank(customerId) || Iterables.isEmpty(policyTypes)) {
            return;
        }
        for (String policyType : policyTypes) {
            var policyTypeId =
                    dsl.insertInto(POLICY_TYPE)
                            .set(POLICY_TYPE.NAME, policyType)
                            .onConflict(POLICY_TYPE.NAME)
                            .doUpdate()
                            .set(POLICY_TYPE.NAME, policyType)
                            .returning(POLICY_TYPE.ID)
                            .fetchAny(POLICY_TYPE.ID);
            dsl.insertInto(
                            CUSTOMER_POLICY_TYPE,
                            CUSTOMER_POLICY_TYPE.CUSTOMER_ID,
                            CUSTOMER_POLICY_TYPE.POLICY_TYPE_ID)
                    .values(customerId, policyTypeId)
                    .onConflict(
                            CUSTOMER_POLICY_TYPE.CUSTOMER_ID, CUSTOMER_POLICY_TYPE.POLICY_TYPE_ID)
                    .doNothing()
                    .execute();
            log.debug("Creation of policy type has succeed, policy type is {}", policyType);
        }
    }

    @Override
    public Iterable<String> listCustomerPolicyType(String customerId) {
        return dsl.selectDistinct(POLICY_TYPE.NAME)
                .from(POLICY_TYPE)
                .leftJoin(CUSTOMER_POLICY_TYPE)
                .on(POLICY_TYPE.ID.eq(CUSTOMER_POLICY_TYPE.POLICY_TYPE_ID))
                .where(CUSTOMER_POLICY_TYPE.CUSTOMER_ID.eq(customerId))
                .fetch(POLICY_TYPE.NAME);
    }
}
