package com.bees360.project.inspection;

import static com.bees360.jooq.persistent.project.Tables.PROJECT_INSPECTION_CODE;

import com.bees360.jooq.util.DateTimes;

import lombok.NonNull;

import org.jooq.Record;

import java.time.Instant;

class JooqInspectionCode implements InspectionCode {

    private final Record record;

    public JooqInspectionCode(@NonNull Record record) {
        this.record = record;
    }

    @Override
    public String getProjectId() {
        return record.get(PROJECT_INSPECTION_CODE.PROJECT_ID);
    }

    @Override
    public String getInspectionCode() {
        return record.get(PROJECT_INSPECTION_CODE.INSPECTION_CODE);
    }

    @Override
    public String getInspectionCodeLink() {
        return record.get(PROJECT_INSPECTION_CODE.INSPECTION_CODE_LINK);
    }

    @Override
    public Instant getExpireAt() {
        return DateTimes.toInstant(record.get(PROJECT_INSPECTION_CODE.EXPIRE_AT));
    }
}
