package com.bees360.contract;

import static com.bees360.jooq.persistent.project.Tables.*;
import static com.bees360.jooq.persistent.project.tables.Contract.CONTRACT;

import com.bees360.contract.Message.ContractMessage.ServiceItem;
import com.bees360.jooq.persistent.project.enums.ContractStatusType;
import com.bees360.jooq.persistent.project.enums.ProjectStateEnum;
import com.bees360.jooq.persistent.project.tables.Customer;
import com.bees360.jooq.persistent.project.tables.records.ContractRecord;
import com.bees360.jooq.persistent.project.tables.records.ContractServiceItemRecord;
import com.bees360.jooq.util.DSLUtils;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;

import org.apache.commons.lang3.StringUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class JooqContractRepository implements ContractManager {

    private final String realm;

    private final DSLContext dsl;

    private final Customer customerA = CUSTOMER.as("c1");

    private final Customer customerB = CUSTOMER.as("c2");

    public JooqContractRepository(DSLContext dsl, String realm) {
        this.dsl = dsl;
        this.realm = realm;
    }

    public JooqContractRepository(DSLContext dsl) {
        this(dsl, null);
    }

    @Override
    public String namespace() {
        return realm;
    }

    private Map<String, ? extends Contract> find(Condition... conditions) {
        var conditionStep =
                DSLUtils.commonSelect(
                        dsl.select(
                                        CONTRACT.ID,
                                        CONTRACT.INSURED_BY,
                                        CONTRACT.PROCESSED_BY,
                                        CONTRACT.STATUS,
                                        CONTRACT.ATTACHMENT_ID,
                                        customerA.NAME,
                                        customerA.WEBSITE,
                                        customerA.LOGO,
                                        customerA.KEY,
                                        customerA.ATTRIBUTES,
                                        customerB.NAME,
                                        customerB.WEBSITE,
                                        customerB.LOGO,
                                        customerB.KEY,
                                        customerB.ATTRIBUTES,
                                        CONTRACT_SERVICE.DISPLAY_NAME,
                                        CONTRACT_SERVICE.KEY,
                                        CONTRACT_SERVICE.DEFAULT_PRICE,
                                        CONTRACT_SERVICE_ITEM.UNIT_PRICE,
                                        CONTRACT_SERVICE.NON_TAXABLE,
                                        CONTRACT_SERVICE_ITEM.SERVICE_NAME)
                                .from(CONTRACT)
                                .leftJoin(CONTRACT_SERVICE_ITEM)
                                .on(
                                        CONTRACT.ID.eq(CONTRACT_SERVICE_ITEM.CONTRACT_ID),
                                        CONTRACT_SERVICE_ITEM.IS_DELETED.eq(false))
                                .leftJoin(CONTRACT_SERVICE)
                                .on(CONTRACT_SERVICE_ITEM.SERVICE_ID.eq(CONTRACT_SERVICE.ID))
                                .leftJoin(customerA)
                                .on(CONTRACT.INSURED_BY.eq(customerA.ID))
                                .leftJoin(customerB)
                                .on(CONTRACT.PROCESSED_BY.eq(customerB.ID)),
                        conditions);
        return DSLUtils.mapReduce(
                conditionStep.stream(),
                record -> new RecordContract(record, customerA, customerB),
                record -> record.get(CONTRACT.ID),
                (recordContract, record) -> {
                    ContractServiceItem serviceItem = new RecordContractServiceItem(record);
                    if (!ServiceItem.getDefaultInstance().equals(serviceItem.toMessage())) {
                        recordContract.addServiceItem(serviceItem);
                    }
                    return recordContract;
                });
    }

    @Override
    public Contract findById(String id) {
        return find(CONTRACT.ID.eq(id)).get(id);
    }

    @Override
    public Iterable<? extends Contract> findAllById(Iterable<String> ids) {
        var list = Iterables.toList(ids);
        return find(CONTRACT.ID.in(list)).values();
    }

    @Override
    public Iterable<? extends Contract> findByQuery(Message.ContractQuery query) {
        Condition condition = DSL.trueCondition();
        if (StringUtils.isNotBlank(query.getContractId())) {
            condition = condition.and(CONTRACT.ID.eq(query.getContractId()));
        }

        if (StringUtils.isNotBlank(query.getCompanyId())) {
            condition =
                    condition.and(
                            CONTRACT.INSURED_BY
                                    .eq(query.getCompanyId())
                                    .or(CONTRACT.PROCESSED_BY.eq(query.getCompanyId())));
        }
        if (StringUtils.isNotBlank(query.getInsuredBy())) {
            condition = condition.and(CONTRACT.INSURED_BY.eq(query.getInsuredBy()));
        }
        if (StringUtils.isNotBlank(query.getProcessedBy())) {
            condition = condition.and(CONTRACT.PROCESSED_BY.eq(query.getProcessedBy()));
        }
        if (!CollectionUtils.isEmpty(query.getStatusList())) {
            var typeList =
                    Iterables.toList(
                            Iterables.transform(
                                    query.getStatusList(),
                                    s -> ContractStatusType.valueOf(s.name())));
            condition = condition.and(CONTRACT.STATUS.in(typeList));
        }
        if (StringUtils.isNotBlank(query.getCompanyName())) {
            var regex = "%" + query.getCompanyName() + "%";
            condition =
                    condition.and(
                            customerA
                                    .NAME
                                    .likeIgnoreCase(regex)
                                    .or(customerB.NAME.likeIgnoreCase(regex)));
        }

        return find(condition).values();
    }

    @Override
    @Transactional
    public String save(Contract entity) {
        Preconditions.checkArgument(
                Objects.nonNull(entity.getInsuredBy()),
                "Insured company of contract can not be null.");
        Preconditions.checkArgument(
                Objects.nonNull(entity.getProcessedBy()),
                "Processed company of contract can not be null.");

        var attachmentId =
                entity.getAttachment() == null
                        ? null
                        : Iterables.toStream(entity.getAttachment())
                                .filter(a -> StringUtils.isNoneEmpty(a.getId()))
                                .map(a -> Long.parseLong(a.getId()))
                                .toArray(Long[]::new);
        var id =
                dsl.insertInto(
                                CONTRACT,
                                CONTRACT.INSURED_BY,
                                CONTRACT.PROCESSED_BY,
                                CONTRACT.ATTACHMENT_ID)
                        .values(
                                entity.getInsuredBy().getId(),
                                entity.getProcessedBy().getId(),
                                attachmentId)
                        .onConflict(CONTRACT.INSURED_BY, CONTRACT.PROCESSED_BY)
                        .doNothing()
                        .returning()
                        .fetchOne(CONTRACT.ID);
        if (Objects.isNull(id)) {
            throw new IllegalArgumentException(
                    String.format(
                            "Contract with insuredBy %s and processedBy %s has been created.",
                            entity.getInsuredBy().getId(), entity.getProcessedBy().getId()));
        }
        saveServiceItems(id, entity.getServiceItem());
        return id;
    }

    @Override
    @Transactional
    public Contract update(
            String contractId,
            Iterable<? extends ContractServiceItem> addItems,
            Iterable<? extends ContractServiceItem> removedItems) {
        checkIfContractExists(contractId);

        saveServiceItems(contractId, addItems);
        var deletedKeys =
                Iterables.toList(Iterables.transform(removedItems, ContractServiceItem::getKey));
        dsl.update(CONTRACT_SERVICE_ITEM)
                .set(CONTRACT_SERVICE_ITEM.IS_DELETED, true)
                .where(CONTRACT_SERVICE_ITEM.CONTRACT_ID.eq(contractId))
                .and(
                        CONTRACT_SERVICE_ITEM.SERVICE_ID.in(
                                dsl.select(CONTRACT_SERVICE.ID)
                                        .from(CONTRACT_SERVICE)
                                        .where(CONTRACT_SERVICE.KEY.in(deletedKeys))))
                .execute();
        return findById(contractId);
    }

    @Override
    public boolean updateStatus(
            String contractId, Message.ContractMessage.ContractStatus status, boolean force) {
        if (force || !Objects.equals(Message.ContractMessage.ContractStatus.INACTIVE, status)) {
            return dsl.update(CONTRACT)
                            .set(CONTRACT.STATUS, ContractStatusType.valueOf(status.name()))
                            .where(CONTRACT.ID.eq(contractId))
                            .execute()
                    != 0;
        }

        var preCondition =
                dsl.selectOne()
                        .from(PROJECT)
                        .leftJoin(PROJECT_STATE)
                        .on(PROJECT.ID.eq(PROJECT_STATE.PROJECT_ID))
                        .where(PROJECT.CONTRACT_ID.eq(contractId))
                        .and(
                                PROJECT.IS_TEST_CASE
                                        .eq(false)
                                        .and(
                                                PROJECT_STATE.PROJECT_STATE_.ne(
                                                        ProjectStateEnum.PROJECT_CLOSE)));
        return dsl.update(CONTRACT)
                        .set(CONTRACT.STATUS, ContractStatusType.valueOf(status.name()))
                        .whereNotExists(preCondition)
                        .and(CONTRACT.ID.eq(contractId))
                        .execute()
                != 0;
    }

    @Override
    @Transactional
    public boolean updateAttachment(String contractId, Iterable<String> attachmentIds) {
        var ids = Iterables.toStream(attachmentIds).map(Long::parseLong).toArray(Long[]::new);

        return dsl.update(CONTRACT)
                        .set(CONTRACT.ATTACHMENT_ID, ids)
                        .where(CONTRACT.ID.eq(contractId))
                        .execute()
                != 0;
    }

    private void checkIfContractExists(String id) {
        var contractExists =
                dsl.fetchExists(dsl.selectOne().from(CONTRACT).where(CONTRACT.ID.eq(id)));
        if (!contractExists) {
            throw new IllegalArgumentException(String.format("Contract %s doesn't exist.", id));
        }
    }

    private void saveServiceItems(
            String contractId, Iterable<? extends ContractServiceItem> items) {
        if (items == null) {
            return;
        }

        List<ContractServiceItemRecord> records = new ArrayList<>();
        var serviceMap = dsl.select().from(CONTRACT_SERVICE).fetchMap(CONTRACT_SERVICE.KEY);

        items.forEach(
                it -> {
                    var record = new ContractServiceItemRecord();
                    var service = serviceMap.get(it.getKey());
                    record.setContractId(contractId);
                    record.setServiceId(service.get(CONTRACT_SERVICE.ID));
                    record.setServiceName(
                            Optional.ofNullable(it.getDisplayName())
                                    .orElse(service.get(CONTRACT_SERVICE.DISPLAY_NAME)));
                    record.setUnitPrice(
                            Optional.ofNullable(it.getUnitPrice())
                                    .orElse(service.get(CONTRACT_SERVICE.DEFAULT_PRICE)));
                    record.setIsDeleted(false);
                    records.add(record);
                });

        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        DSLUtils.upsert(
                dsl,
                CONTRACT_SERVICE_ITEM,
                records,
                CONTRACT_SERVICE_ITEM.CONTRACT_ID,
                CONTRACT_SERVICE_ITEM.SERVICE_ID);
    }

    @Override
    public void deleteById(String id) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Iterable<? extends Contract> loadAll() {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean existsById(String id) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Iterable<String> saveAll(Iterable<? extends Contract> entities) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void deleteAllById(Iterable<String> ids) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Contract create(String insuredById, String processedById) {
        ContractRecord r = new ContractRecord();
        r.setInsuredBy(insuredById);
        r.setProcessedBy(processedById);
        ContractRecord record =
                DSLUtils.upsert(dsl, CONTRACT, r, CONTRACT.INSURED_BY, CONTRACT.PROCESSED_BY);
        var id = record == null ? null : record.getId();
        return findById(id);
    }
}
