package com.bees360.project.options;

import com.bees360.api.ApiStatus;
import com.bees360.api.Message;
import com.bees360.auth.CustomTokenReader;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.user.User;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/** ProjectOptionsEndpoint */
@RestController
@RequestMapping("${http.project.endpoint:project}")
@Import({ProtoHttpMessageConverterConfig.class, ApiExceptionHandler.class})
public class ProjectOptionsEndpoint {

    private final ProjectOptionsProvider projectOptionsProvider;

    private final CustomTokenReader customTokenReader;

    private static final String TOKEN_KEY_COMPANY_ID = "company_id";

    public ProjectOptionsEndpoint(
            ProjectOptionsProvider projectOptionsProvider, CustomTokenReader customTokenReader) {
        this.projectOptionsProvider = projectOptionsProvider;
        this.customTokenReader = customTokenReader;
    }

    /**
     * list all project options. Company of user will decide operating company to return
     *
     * @param user user to access options data
     * @return ApiMessage with a list of project options.
     */
    @GetMapping("/options")
    public Message.ApiMessage listProjectOptions(@AuthenticationPrincipal User user) {
        // TODO 这里暂时从token直接取后续应该从User里拿
        var userCompany = customTokenReader.getByKey(TOKEN_KEY_COMPANY_ID);

        var response = Message.ApiMessage.newBuilder().setStatus(ApiStatus.OK.toMessage());
        if (StringUtils.isNoneEmpty(userCompany)) {
            var operatingCompanyList =
                    projectOptionsProvider.listOperatingCompanyByUserCompany(userCompany);
            response.addAllOperatingCompany(operatingCompanyList);
        }

        var policyTypes = projectOptionsProvider.listPolicyType();
        Optional.ofNullable(policyTypes).ifPresent(response::addAllPolicyType);

        return response.build();
    }

    @GetMapping("/options/global")
    public Message.ApiMessage getProjectGlobalOption() {
        var options = projectOptionsProvider.getGlobalOptions();
        var response = Message.ApiMessage.newBuilder().setStatus(ApiStatus.OK.toMessage());
        return response.addProjectGlobalOptions(options.toMessage()).build();
    }
}
