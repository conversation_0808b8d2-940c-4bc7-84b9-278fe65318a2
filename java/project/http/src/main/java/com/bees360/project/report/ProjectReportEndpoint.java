package com.bees360.project.report;

import com.bees360.api.Message.ApiMessage;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.project.Message.ProjectReportMergeRequest;
import com.bees360.report.Report;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/** ProjectReportEndpoint */
@RestController
@RequestMapping("${http.project.endpoint:project}")
@Import({
    ProtoHttpMessageConverterConfig.class,
    ApiExceptionHandler.class,
})
@Log4j2
public class ProjectReportEndpoint {

    private final ProjectReportManager projectReportManager;

    private final ProjectReportProcessor projectReportProcessor;

    public ProjectReportEndpoint(
            ProjectReportManager projectReportManager,
            ProjectReportProcessor projectReportProcessor) {
        this.projectReportManager = projectReportManager;
        this.projectReportProcessor = projectReportProcessor;
        log.info(
                "Created {}(projectReportManager='{}', projectReportProcessor='{}').",
                this,
                this.projectReportManager,
                this.projectReportProcessor);
    }

    /**
     * Get reports of a project.
     *
     * @param projectId id of project
     * @param type report type
     * @return A list of reports
     */
    @GetMapping("/{projectId:\\d+}/report")
    public Iterable<? extends Report> getProjectReport(
            @PathVariable String projectId, String type) {
        return projectReportManager.find(projectId, type, null);
    }

    /**
     * Get all reports including history reports of a project.
     *
     * @param projectId id of project
     * @param type report type
     * @return ApiMessage with a list of reports
     */
    @GetMapping("/{projectId:\\d+}/report/history")
    public ApiMessage getProjectReportInHistory(@PathVariable String projectId, String type) {
        var reports = projectReportManager.findInHistory(projectId, type, null);

        ApiMessage.Builder apiMessageBuilder = ApiMessage.newBuilder();
        reports.forEach((report) -> apiMessageBuilder.addReport(report.toMessage()));
        return apiMessageBuilder.build();
    }

    /**
     * Merge reports of a source project into a report with a specific type of target project.
     *
     * @param userId operator id
     * @param projectId id of project
     * @param request ProjectReportMergeRequest
     */
    @PostMapping("/{projectId:\\d+}/report/merge")
    public void reportMerge(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String projectId,
            @RequestBody ProjectReportMergeRequest request) {
        var targetProjectId =
                StringUtils.isNotBlank(request.getTargetProjectId())
                        ? request.getTargetProjectId()
                        : projectId;
        var targetReportType = request.getTargetReportType();
        var resourceIds = request.getResourceIdList();
        projectReportProcessor.mergeReport(targetProjectId, targetReportType, resourceIds, userId);
    }

    /**
     * Permanently delete report, After deletion, the report will not be displayed in history.
     *
     * @param userId operator id
     * @param projectId id of project
     * @param reportId id of report
     */
    @DeleteMapping("/{projectId:\\d+}/report/{reportId}")
    public void permanentlyDelete(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String projectId,
            @PathVariable String reportId) {
        projectReportManager.permanentlyDelete(projectId, List.of(reportId), userId);
    }
}
