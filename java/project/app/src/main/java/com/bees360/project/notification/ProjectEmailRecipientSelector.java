package com.bees360.project.notification;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/** Determine the email recipient by project id and {@code RecipientSelector}. */
@FunctionalInterface
public interface ProjectEmailRecipientSelector {

    @Getter
    @Setter
    @Accessors(chain = true)
    class RecipientSelector {
        private String type;
        // get this value if type is 'email'.
        private EmailRecipientSelector email;
        // get this value if type is 'member'.
        private RoleRecipientSelector member;
        // get this value if type is 'contact'
        private RoleRecipientSelector contact;

        /**
         * policy-type & property-type are used for policyTypeSelector{@link
         * PolicyTypeProjectEmailRecipientSelector}
         */
        private String policyType;

        private String propertyType;

        // following are recipients of the selector
        private List<String> emails;
        private List<String> members;
        private List<String> contacts;

        @Getter
        @Setter
        @Accessors(chain = true)
        public static class EmailRecipientSelector {
            private String email;
        }

        @Getter
        @Setter
        @Accessors(chain = true)
        public static class RoleRecipientSelector {
            private String role;
        }
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    class EmailRecipient {
        private String email;
    }

    Iterable<EmailRecipient> select(String projectId, RecipientSelector selector);
}
