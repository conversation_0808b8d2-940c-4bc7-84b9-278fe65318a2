package com.bees360.project.image;

import com.bees360.api.InvalidArgumentException;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

/**
 * Additional pipeline task status settings based on the ForwardingProjectImageProcessManager class
 */
@Log4j2
public class ProjectImageProcessWithSetTaskStatusManager
        extends ForwardingProjectImageProcessManager {

    private final ProjectImageProcessManager projectImageProcessManager;
    private final PipelineService pipelineService;

    private final Function<String, String> pipelineTaskKeyProvider;

    public ProjectImageProcessWithSetTaskStatusManager(
            ProjectImageProcessManager projectImageProcessManager,
            PipelineService pipelineService,
            Function<String, String> pipelineTaskKeyProvider) {
        this.projectImageProcessManager = projectImageProcessManager;
        this.pipelineService = pipelineService;
        this.pipelineTaskKeyProvider = pipelineTaskKeyProvider;
        log.info(
                "created {}(projectImageProcessManager='{}', pipelineService='{}',"
                        + " pipelineTaskKeyProvider='{}')",
                this,
                this.projectImageProcessManager,
                this.pipelineService,
                this.pipelineTaskKeyProvider);
    }

    @Override
    protected ProjectImageProcessManager delegate() {
        return projectImageProcessManager;
    }

    @Override
    public void startInferaImageJob(
            @NonNull String projectId, @NonNull String serviceKey, String userId) {
        delegate().startInferaImageJob(projectId, serviceKey, userId);
        setPipelineTaskStatus(projectId, serviceKey);
    }

    private void setPipelineTaskStatus(String projectId, String serviceKey) {
        var taskKey = pipelineTaskKeyProvider.apply(serviceKey);
        if (StringUtils.isBlank(taskKey)) {
            log.warn(
                    "Setting pipeline task status failed: task key is empty. serviceKey is '{}'",
                    serviceKey);
            return;
        }

        var status = Message.PipelineStatus.ONGOING;
        try {
            pipelineService.setTaskStatus(projectId, taskKey, status);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn(
                    "Failed to set pipeline '{}' task '{}' to '{}'.",
                    projectId,
                    taskKey,
                    status,
                    e);
        } catch (RuntimeException e) {
            log.error(
                    "Failed to set pipeline '{}' task '{}' to '{}'.",
                    projectId,
                    taskKey,
                    status,
                    e);
        }
    }
}
