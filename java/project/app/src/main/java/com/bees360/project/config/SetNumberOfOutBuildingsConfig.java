package com.bees360.project.config;

import com.bees360.event.autoconfig.EnableEventAutoRegister;
import com.bees360.image.ImageTagProvider;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.image.ProjectImageProvider;
import com.bees360.project.listener.SetNumberOfOutBuildingsListener;
import com.bees360.project.report.ProjectReportProvider;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(
        prefix = "project.app.set-number-of-outbuildings",
        name = "enabled",
        havingValue = "true")
@EnableEventAutoRegister
public class SetNumberOfOutBuildingsConfig {
    @Bean
    SetNumberOfOutBuildingsListener setNumberOfOutBuildingsListener(
            ProjectIIManager inspectionManager,
            ImageTagDictProvider imageTagDictProvider,
            ProjectImageProvider projectImageProvider,
            ImageTagProvider imageTagProvider,
            ProjectReportProvider projectReportProvider) {
        return new SetNumberOfOutBuildingsListener(
                inspectionManager,
                imageTagDictProvider,
                projectImageProvider,
                imageTagProvider,
                projectReportProvider);
    }
}
