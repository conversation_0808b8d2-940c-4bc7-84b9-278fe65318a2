package com.bees360.project.claim;

import com.bees360.address.Address;
import com.bees360.api.Entity;
import com.bees360.contract.Contract;
import com.bees360.policy.Policy;
import com.bees360.policy.PolicyManager;
import com.bees360.project.ProjectIIRepository;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import java.util.Optional;

@Log4j2
public class DefaultProjectCatastropheManager extends ForwardingProjectCatastropheManager {

    private final ProjectIIRepository projectIIRepository;
    private final PolicyManager policyManager;

    protected DefaultProjectCatastropheManager(
            ProjectCatastropheManager projectCatastropheManager,
            ProjectIIRepository projectIIRepository,
            PolicyManager policyManager) {
        super(projectCatastropheManager);
        this.policyManager = policyManager;
        this.projectIIRepository = projectIIRepository;
        log.info(
                "Created {}(projectIIRepository={}, policyManager={}).",
                this,
                this.projectIIRepository,
                this.policyManager);
    }

    @Override
    public void addProjectCatastrophe(
            String projectId,
            String catSerialNumber,
            @Nullable Integer catLevel,
            @Nullable String customerId,
            @Nullable String addressState) {
        SERIAL_NUMBER_CHECKER.accept(catSerialNumber);
        LEVEL_CHECKER.accept(catLevel);

        // when customerId or addressState is null or default value
        // fetch data from project as parameters

        var project = projectIIRepository.findById(projectId);
        if (customerId == null || DEFAULT_CUSTOMER_ID.equals(customerId)) {
            var contract = project.getContract();
            customerId =
                    Optional.ofNullable(contract)
                            .map(Contract::getInsuredBy)
                            .map(Entity::getId)
                            .orElse(DEFAULT_CUSTOMER_ID);
        }

        if (addressState == null || DEFAULT_ADDRESS_STATE.equals(addressState)) {
            var policy = project.getPolicy();
            addressState =
                    Optional.ofNullable(policy)
                            .map(Entity::getId)
                            .map(policyManager::findById)
                            .map(Policy::getAddress)
                            .map(Address::getState)
                            .orElse(DEFAULT_ADDRESS_STATE);
        }
        delegate()
                .addProjectCatastrophe(
                        projectId, catSerialNumber, catLevel, customerId, addressState);
    }
}
