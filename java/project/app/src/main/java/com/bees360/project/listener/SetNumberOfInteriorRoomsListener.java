package com.bees360.project.listener;

import static com.bees360.image.util.AttributeMessageAdapter.jsonToAttribute;

import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.image.Image;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagProvider;
import com.bees360.image.tag.ImageTag;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.image.tag.ImageTagGroupType;
import com.bees360.listener.Listener;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.image.ProjectImageProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/** count_interior_rooms pipeline task状态变为ready事件触发时统计interior rooms并插入project inspection */
@Log4j2
public class SetNumberOfInteriorRoomsListener implements Listener<PipelineTaskChanged> {

    private final ProjectIIManager inspectionManager;

    private final ImageTagDictProvider imageTagDictProvider;

    private final ProjectImageProvider projectImageProvider;

    private final PipelineService pipelineService;

    private final ImageTagProvider imageTagProvider;

    private static final String INTERIOR_ROOMS = "Interior Rooms";

    public SetNumberOfInteriorRoomsListener(
            ProjectIIManager inspectionManager,
            ImageTagDictProvider imageTagDictProvider,
            ProjectImageProvider projectImageProvider,
            PipelineService pipelineService,
            ImageTagProvider imageTagProvider) {
        this.inspectionManager = inspectionManager;
        this.imageTagDictProvider = imageTagDictProvider;
        this.projectImageProvider = projectImageProvider;
        this.pipelineService = pipelineService;
        this.imageTagProvider = imageTagProvider;
    }

    @Override
    public void execute(PipelineTaskChanged event) throws IOException {
        log.info(
                "Set project {} number of interior rooms by pipeline task ready event.",
                event.getPipelineId());

        var projectId = event.getPipelineId();
        var roomTags = getRoomTags();
        try {
            var rooms = countInteriorRooms(projectId, roomTags);
            inspectionManager.updateNumberOfInteriorRooms(projectId, rooms);
            pipelineService.setTaskStatus(projectId, event.getTaskDefKey(), PipelineStatus.DONE);
        } catch (Exception e) {
            log.error("Calculating number of interior rooms for project {} fails.", projectId, e);
            pipelineService.setTaskStatus(
                    projectId,
                    event.getTaskDefKey(),
                    PipelineStatus.ERROR,
                    "Exception occurs when calculating number of interior rooms.");
        }
    }

    /** 采用room tag和 tag attribute index 排列组合的方式计算rooms数量 */
    private Integer countInteriorRooms(String projectId, List<ImageTagEnum> roomTags) {
        Set<Long> roomsCountSet = new HashSet<>();
        Set<String> roomTagSet =
                roomTags.stream()
                        .map(tag -> String.valueOf(tag.getCode()))
                        .collect(Collectors.toSet());

        var images = projectImageProvider.findByProjectId(projectId);
        var imageTagMap =
                imageTagProvider.findByImageIds(Iterables.transform(images, Image::getId));

        // 统计rooms tag和number tag的排列组合
        for (Iterable<? extends ImageTag> tags : imageTagMap.values()) {
            var x = 0;
            var y = 0;
            for (ImageTag tag : tags) {
                var tagId = tag.getId();
                if (roomTagSet.contains(tagId)) {
                    x = Integer.parseInt(tagId);
                    y = jsonToAttribute(tag.getAttribute()).getIndex().getValue();
                }
            }

            if (x != 0) {
                roomsCountSet.add(((long) x << 16) + y);
            }
        }

        return roomsCountSet.size();
    }

    private List<ImageTagEnum> getRoomTags() {
        return Iterables.toStream(
                        imageTagDictProvider.findByGroup(
                                INTERIOR_ROOMS, ImageTagGroupType.BEES_AI.getValue()))
                .map(tag -> ImageTagEnum.valueOf(Integer.parseInt(tag.getId())))
                .collect(Collectors.toList());
    }

    private boolean imageExists(Iterable<? extends Image> images) {
        return images != null && images.iterator().hasNext();
    }
}
