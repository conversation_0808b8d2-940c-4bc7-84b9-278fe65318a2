package com.bees360.project.pipeline;

import static com.bees360.assignment.Message.AssignStrategy.OPTIMIZER;
import static com.bees360.assignment.Message.AssignStrategy.ROUND_ROBIN;
import static com.bees360.pipeline.assign.TaskAssignRecord.buildTaskToBeAssigned;

import com.bees360.assignment.Message.AssignStrategy;
import com.bees360.pipeline.assign.AssignRule;
import com.bees360.pipeline.assign.AssignTaskRecordCalculator;
import com.bees360.pipeline.assign.AssignTaskRecordOptimizer;
import com.bees360.pipeline.assign.AssigneeSchedule;
import com.bees360.pipeline.assign.PipelineAssignedTask;
import com.bees360.pipeline.assign.TaskAssignRecord;
import com.bees360.project.pipeline.config.AutoAssignConfig.AutoAssignProperties;
import com.bees360.project.pipeline.config.AutoAssignConfig.AutoAssignProperties.AutoAssignRule;
import com.bees360.project.pipeline.config.AutoAssignConfig.AutoAssignProperties.RuleItem;
import com.bees360.util.Iterables;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Log4j2
public class DefaultAssignTaskRecordCalculator implements AssignTaskRecordCalculator {
    private final Consumer<List<PipelineAssignedTask>> handler;

    private final Consumer<Iterable<PipelineAssignedTask>> assignTaskBaseDataInitializer;

    private final AutoAssignProperties autoAssignProperties;

    private final AssignTaskRecordOptimizer assignTaskRecordOptimizer;

    private final AssignmentLossFunctionFactory assignmentLossFunctionFactory;

    public DefaultAssignTaskRecordCalculator(
            Consumer<List<PipelineAssignedTask>> handler,
            Consumer<Iterable<PipelineAssignedTask>> assignTaskBaseDataInitializer,
            AutoAssignProperties autoAssignProperties,
            AssignTaskRecordOptimizer assignTaskRecordOptimizer,
            AssignmentLossFunctionFactory assignmentLossFunctionFactory) {
        this.handler = handler;
        this.assignTaskBaseDataInitializer = assignTaskBaseDataInitializer;
        this.autoAssignProperties = autoAssignProperties;
        this.assignTaskRecordOptimizer = assignTaskRecordOptimizer;
        this.assignmentLossFunctionFactory = assignmentLossFunctionFactory;
        log.info(
                "Create DefaultAssignTaskRecordCalculator :{} with handler :{} and"
                        + " assignTaskBaseDataInitializer :{} and autoAssignProperties :{} and"
                        + " assignTaskRecordOptimizer :{} and assignmentLossFunctionFactory :{}",
                this,
                this.handler,
                this.assignTaskBaseDataInitializer,
                this.autoAssignProperties,
                this.assignTaskRecordOptimizer,
                this.assignmentLossFunctionFactory);
    }

    @Data
    @AllArgsConstructor
    private static class StatusInAssignment {
        // The assigned user id.
        private final String id;
        // The workload assigned to the assigned person this time.
        private int workload;
        // The amount of work that can be allocated to the assigned person
        private final int capacity;
        // save the original position in the list
        private final int position;
        // the times that the assign change pass the assignee, used in Round Robin
        private int assignTimes;
        private double serviceTypeWeight;
        private double imageCountWeight;
        private double pilotWeight;
        // The list of tasks assigned to the assigned person this time.
        private List<PipelineAssignedTask> assignment;
        // Cache the list of tasks assigned to the assigned person in one round.
        private List<PipelineAssignedTask> assignmentCache;

        public int getWorkload() {
            var assignedWorkload =
                    this.assignment.stream().mapToInt(PipelineAssignedTask::getDifficulty).sum();
            var assignmentCache =
                    this.assignmentCache.stream()
                            .mapToInt(PipelineAssignedTask::getDifficulty)
                            .sum();
            return assignedWorkload + assignmentCache + workload;
        }

        public void addAssignTime() {
            this.assignTimes++;
        }

        // 将一轮分配完的结果进行归档操作
        public void archiving() {
            this.assignment.addAll(this.assignmentCache);
            this.assignmentCache.clear();
        }
    }

    /**
     * default function, 该方法计算的流程是： 1、填充Task的ServiceType,PilotId,ImageCount信息 2、初始化每个Task可分配人员的id
     * list即：PipelineAssignedTask#acceptableIds 3、按照2统计的人数最少的task进行分组并根据人数降序排序
     * 4、循环3中的结果进行多轮分配，每轮分配后，会经过 equalizeAllocation 进行优化，
     * 以保证每次分配的ImageCount，ServiceType，Pilot权重总和进行均衡，权重越高说明工作量越多工作越复杂， 均衡器 {@code equalizeAllocation}
     * 目的是平均每个人的工作量，, 每次分配完进行归档操作 5、将结果集转化为 Set<TaskAssignRecord> 并返回
     *
     * @param pipelines 需要分配的pipeline task list.
     * @param assignees 可分配的人员.
     * @param assignRule 分配规则.
     * @return 计算后的分配记录
     */
    @Override
    public Set<TaskAssignRecord> calculateAssignRecords(
            Iterable<PipelineAssignedTask> pipelines,
            Iterable<? extends AssigneeSchedule> assignees,
            AssignRule assignRule) {
        if (assignRule == null) {
            throw new IllegalArgumentException("The param assignRule must not be null.");
        }
        log.info("The assignRule is {}", assignRule);
        if (Iterables.isEmpty(pipelines) || Iterables.isEmpty(assignees)) {
            return Set.of();
        }
        var comparatorCode = assignRule.getAssignStrategy();
        if (comparatorCode == OPTIMIZER.getNumber()) {
            var assignResult =
                    assignTaskRecordOptimizer.optimizeAssignRecords(
                            pipelines,
                            assignees,
                            assignmentLossFunctionFactory.create(
                                    assignRule.getLossFunctionScript()));
            handler.accept(Iterables.toList(assignResult.getFailedAssignTasks()));
            return assignResult.getAssignRecord();
        }
        var assigneePosition = new AtomicInteger(0);
        // create a map to save each assignee's status during the assignment
        // Set sorted stream into a map, keep the sorted result
        var assigneeStream =
                Iterables.toStream(assignees)
                        .map(
                                as ->
                                        new StatusInAssignment(
                                                as.getUser().getId(),
                                                as.getWorkload(),
                                                as.getCapacity(),
                                                assigneePosition.getAndIncrement(),
                                                0,
                                                0,
                                                0,
                                                0,
                                                new ArrayList<>(),
                                                new ArrayList<>()));
        List<StatusInAssignment> assigneeList;
        if (comparatorCode == ROUND_ROBIN.getNumber()) {
            assigneeList = assigneeStream.collect(Collectors.toList());
        } else {
            assigneeList =
                    assigneeStream
                            .sorted(
                                    (a, b) ->
                                            CharSequence.compare(
                                                    Objects.requireNonNull(a.getId()),
                                                    Objects.requireNonNull(b.getId())))
                            .collect(Collectors.toList());
        }

        // 根据配置查询配置的AssignConfig
        var autoAssignConfig =
                autoAssignProperties.getAssignRules().stream()
                        .filter(
                                rule ->
                                        StringUtils.equalsAny(
                                                rule.getKey(),
                                                assignRule.getKey(),
                                                assignRule.getName()))
                        .findFirst()
                        .orElse(null);

        if (autoAssignConfig != null) {
            // 1、填充Task的ServiceType,PilotId,ImageCount信息
            assignTaskBaseDataInitializer.accept(pipelines);
            // 2、初始化每个Task可分配人员的id list即：PipelineAssignedTask#acceptableIds
            setAcceptableUserIds(pipelines, autoAssignConfig.getRuleItem());

            // 3、按照2统计的人数最少的task进行分组并根据人数降序排序
            Map<List<String>, List<PipelineAssignedTask>> assignsTaskMap =
                    Iterables.toStream(pipelines)
                            .collect(Collectors.groupingBy(PipelineAssignedTask::getAcceptableIds));
            var sortedMap =
                    new TreeMap<List<String>, List<PipelineAssignedTask>>(
                            Comparator.comparingInt((List<String> list) -> list.size())
                                    .thenComparing(Object::toString));
            sortedMap.putAll(assignsTaskMap);

            // 4、循环3中的结果进行多轮分配，每轮分配后，会经过 equalizeAllocation 进行优化，
            //    以保证每次分配的ImageCount，ServiceType，Pilot总和均衡
            for (var entry : sortedMap.entrySet()) {
                executeAllocation(entry, assigneeList, comparatorCode, autoAssignConfig);
            }
        } else {
            // 如果没有配置规则，直接分配归档
            assignTasks(pipelines, assigneeList, comparatorCode);
            for (var assign : assigneeList) {
                assign.archiving();
            }
        }

        // 5、将结果集转化为 Set<TaskAssignRecord> 并返回
        var assignRecords = new HashSet<TaskAssignRecord>();
        for (var assignee : assigneeList) {
            for (var assign : assignee.getAssignment()) {
                var assignedTasks =
                        assign.getTaskKey().stream()
                                .map(
                                        taskKey ->
                                                buildTaskToBeAssigned(
                                                        assignee.getId(),
                                                        assign.getPipelineId(),
                                                        taskKey))
                                .collect(Collectors.toList());
                assignRecords.addAll(assignedTasks);
            }
        }
        return assignRecords;
    }

    /**
     * 根据AssignConfig进行一次分配
     *
     * @param entry 一次分配的userIdList，TaskList数据
     * @param assigneeList 被分配人列表
     * @param comparatorCode 分配规则 {@link AssignStrategy}
     * @param autoAssignConfig 分配的配置规则
     */
    private void executeAllocation(
            Map.Entry<List<String>, List<PipelineAssignedTask>> entry,
            List<StatusInAssignment> assigneeList,
            Integer comparatorCode,
            AutoAssignRule autoAssignConfig) {
        var singleTasks = entry.getValue();
        var singleAssignIds = new HashSet<>(entry.getKey());
        var singleAssignments = new ArrayList<StatusInAssignment>();
        for (var assign : assigneeList) {
            if (singleAssignIds.contains(assign.getId())) {
                singleAssignments.add(assign);
            }
        }

        // 4.1、 分配
        assignTasks(singleTasks, singleAssignments, comparatorCode);

        // 4.2、 分配后重新计算个人的分配权重
        for (var assign : singleAssignments) {
            assign.setServiceTypeWeight(getServiceTypeWeight(assign, autoAssignConfig));
            assign.setImageCountWeight(getImageCountWeight(assign, autoAssignConfig));
            assign.setPilotWeight(getPilotWeight(assign, autoAssignConfig));
        }

        equalizeAllocation(singleAssignments, autoAssignConfig);

        // 4.3、每次分配完进行归档操作
        for (var assign : singleAssignments) {
            assign.archiving();
        }
    }

    /**
     * 根据分配规则 comparatorCode 进行分配，将分配的task设置给 StatusInAssignment 的 AssignmentCache字段， 后续会根据这个字段进行
     * equalizeAllocation 操作
     *
     * @param tasks 待分配任务列表
     * @param assignees 被分配人列表
     * @param comparatorCode 分配规则 {@link AssignStrategy}
     */
    private void assignTasks(
            Iterable<? extends PipelineAssignedTask> tasks,
            Collection<StatusInAssignment> assignees,
            Integer comparatorCode) {
        var comparator = getComparator(comparatorCode);

        var cycleIt = Iterables.cycle(assignees).iterator();
        var failedPipeline = new ArrayList<PipelineAssignedTask>();
        for (var task : tasks) {
            assignees.stream()
                    .filter(schedule -> getAvailableCapacity(schedule) >= task.getDifficulty())
                    .min(comparator)
                    .ifPresentOrElse(
                            assignee -> {
                                var userId = assignee.getId();
                                assignee.getAssignmentCache().add(task);
                                if (comparatorCode != ROUND_ROBIN.getNumber()) {
                                    return;
                                }
                                assignee.addAssignTime();
                                // every assignee between this and previous turn should increase one
                                // assign time.
                                assignee = cycleIt.next();
                                while (!assignee.getId().equals(userId)) {
                                    assignee.addAssignTime();
                                    assignee = cycleIt.next();
                                }
                            },
                            () -> failedPipeline.add(task));
        }
        if (CollectionUtils.isNotEmpty(failedPipeline)) {
            handler.accept(failedPipeline);
        }
    }

    /**
     * 订单均衡器，负责优化订单的分配，步骤： 1、查询出本次分组的最大和最小的ServiceTypeWeight、ImageCountWeight、PilotWeight值
     * 2、生成随机数，选取两个人员的待交换的两个task，并计算这两个人的Normalization权重值 {@code calculateNormalizationWeight}
     * 3、将两个人的task进行交换 4、交换后重新计算两个人的Normalization权重 5、如果Normalization权重没有降低，则将两人task换回来
     * 6、如果权重降低，则没有5操作 7、更新ServiceTypeWeight、ImageCountWeight、PilotWeight值最大最小值
     *
     * @param assignments 已分配的记录，包含已分配但未提交的订单
     * @param autoAssignConfig 自动分配规则配置
     */
    private static void equalizeAllocation(
            List<StatusInAssignment> assignments, AutoAssignRule autoAssignConfig) {
        if (assignments.size() < 2) {
            return;
        }
        // 1、查询出本次分组的最大和最小的ServiceTypeWeight、ImageCountWeight、PilotWeight值
        var maxServiceTypeWeight = assignments.get(0).getServiceTypeWeight();
        var minServiceTypeWeight = assignments.get(0).getServiceTypeWeight();
        var maxImageCountWeight = assignments.get(0).getImageCountWeight();
        var minImageCountWeight = assignments.get(0).getImageCountWeight();
        var maxPilotWeight = assignments.get(0).getPilotWeight();
        var minPilotWeight = assignments.get(0).getPilotWeight();

        for (var assign : assignments) {
            maxServiceTypeWeight = Math.max(maxServiceTypeWeight, assign.getServiceTypeWeight());
            minServiceTypeWeight = Math.min(minServiceTypeWeight, assign.getServiceTypeWeight());
            maxImageCountWeight = Math.max(maxImageCountWeight, assign.getImageCountWeight());
            minImageCountWeight = Math.min(minImageCountWeight, assign.getImageCountWeight());
            maxPilotWeight = Math.max(maxPilotWeight, assign.getPilotWeight());
            minPilotWeight = Math.min(minPilotWeight, assign.getPilotWeight());
        }

        Random random = new Random();

        for (var i = 0; i < autoAssignConfig.getOptimizationFrequency(); i++) {
            // 2、生成随机数，选取两个人员的待交换的两个task，并计算这两个人的权重值 {@code calculateWeight}
            int firstProcessorIndex = random.nextInt(assignments.size());
            int secondProcessorIndex = random.nextInt(assignments.size());
            while (secondProcessorIndex == firstProcessorIndex) {
                secondProcessorIndex = random.nextInt(assignments.size());
            }

            var firstAssignment = assignments.get(firstProcessorIndex);
            var secondAssignment = assignments.get(secondProcessorIndex);

            double currentScore =
                    calculateNormalizationWeight(
                                    firstAssignment,
                                    maxServiceTypeWeight,
                                    minServiceTypeWeight,
                                    maxImageCountWeight,
                                    minImageCountWeight,
                                    maxPilotWeight,
                                    minPilotWeight)
                            + calculateNormalizationWeight(
                                    secondAssignment,
                                    maxServiceTypeWeight,
                                    minServiceTypeWeight,
                                    maxImageCountWeight,
                                    minImageCountWeight,
                                    maxPilotWeight,
                                    minPilotWeight);

            // 3、将两个人的task进行交换
            if (!firstAssignment.getAssignmentCache().isEmpty()
                    && !secondAssignment.getAssignmentCache().isEmpty()) {
                int firstOrderIndex = random.nextInt(firstAssignment.getAssignmentCache().size());
                int secondOrderIndex = random.nextInt(secondAssignment.getAssignmentCache().size());

                // 3.1 交换订单
                var temp = firstAssignment.getAssignmentCache().get(firstOrderIndex);
                firstAssignment
                        .getAssignmentCache()
                        .set(
                                firstOrderIndex,
                                secondAssignment.getAssignmentCache().get(secondOrderIndex));
                secondAssignment.getAssignmentCache().set(secondOrderIndex, temp);

                // 4、交换后重新计算两个人的权重
                double newScore =
                        calculateNormalizationWeight(
                                        firstAssignment,
                                        maxServiceTypeWeight,
                                        minServiceTypeWeight,
                                        maxImageCountWeight,
                                        minImageCountWeight,
                                        maxPilotWeight,
                                        minPilotWeight)
                                + calculateNormalizationWeight(
                                        secondAssignment,
                                        maxServiceTypeWeight,
                                        minServiceTypeWeight,
                                        maxImageCountWeight,
                                        minImageCountWeight,
                                        maxPilotWeight,
                                        minPilotWeight);

                // 5、如果权重没有降低，则将两人task换回来
                if (newScore > currentScore) {
                    // 恢复原来的分配
                    temp = firstAssignment.getAssignmentCache().get(firstOrderIndex);
                    firstAssignment
                            .getAssignmentCache()
                            .set(
                                    firstOrderIndex,
                                    secondAssignment.getAssignmentCache().get(secondOrderIndex));
                    secondAssignment.getAssignmentCache().set(secondOrderIndex, temp);
                }

                var firstServiceTypeWeight =
                        getServiceTypeWeight(firstAssignment, autoAssignConfig);
                var firstImageCountWeight = getImageCountWeight(firstAssignment, autoAssignConfig);
                var firstPilotWeight = getPilotWeight(firstAssignment, autoAssignConfig);

                firstAssignment.setServiceTypeWeight(firstServiceTypeWeight);
                firstAssignment.setImageCountWeight(firstImageCountWeight);
                firstAssignment.setPilotWeight(firstPilotWeight);

                var secondServiceTypeWeight =
                        getServiceTypeWeight(secondAssignment, autoAssignConfig);
                var secondImageCountWeight =
                        getImageCountWeight(secondAssignment, autoAssignConfig);
                var secondPilotWeight = getPilotWeight(secondAssignment, autoAssignConfig);

                secondAssignment.setServiceTypeWeight(secondServiceTypeWeight);
                secondAssignment.setImageCountWeight(secondImageCountWeight);
                secondAssignment.setPilotWeight(secondPilotWeight);

                // 7、更新ServiceTypeWeight、ImageCountWeight、PilotWeight值最大最小值
                maxServiceTypeWeight =
                        Math.max(
                                Math.max(maxServiceTypeWeight, firstServiceTypeWeight),
                                secondServiceTypeWeight);
                maxImageCountWeight =
                        Math.max(
                                Math.max(maxImageCountWeight, firstServiceTypeWeight),
                                secondServiceTypeWeight);
                maxPilotWeight =
                        Math.max(
                                Math.max(maxPilotWeight, firstServiceTypeWeight),
                                secondServiceTypeWeight);

                minServiceTypeWeight =
                        Math.min(
                                Math.min(minServiceTypeWeight, firstServiceTypeWeight),
                                secondServiceTypeWeight);
                minImageCountWeight =
                        Math.min(
                                Math.min(minImageCountWeight, firstServiceTypeWeight),
                                secondServiceTypeWeight);
                minPilotWeight =
                        Math.min(
                                Math.min(minPilotWeight, firstServiceTypeWeight),
                                secondServiceTypeWeight);
            }
        }
    }

    /** 计算归一化权重值，具体归一化算法可网上了解 */
    private static double calculateNormalizationWeight(
            StatusInAssignment assignment,
            double maxServiceTypeWeight,
            double minServiceTypeWeight,
            double maxImageCountWeight,
            double minImageCountWeight,
            double maxPilotWeight,
            double minPilotWeight) {
        var serviceTypeWeight =
                (assignment.getServiceTypeWeight() - minServiceTypeWeight)
                        / (maxServiceTypeWeight - minServiceTypeWeight);
        var imageWeight =
                (assignment.getImageCountWeight() - minImageCountWeight)
                        / (maxImageCountWeight - minImageCountWeight);
        var pilotCountWeight =
                (assignment.getPilotWeight() - minPilotWeight) / (maxPilotWeight - minPilotWeight);
        return imageWeight + pilotCountWeight + serviceTypeWeight;
    }

    private static double getImageCountWeight(
            StatusInAssignment assignment, AutoAssignRule autoAssignConfig) {
        var imageCount =
                assignment.getAssignment().stream()
                        .mapToInt(PipelineAssignedTask::getImageCount)
                        .sum();
        imageCount +=
                assignment.getAssignmentCache().stream()
                        .mapToInt(PipelineAssignedTask::getImageCount)
                        .sum();
        return autoAssignConfig.getImageCountWeightProportion()
                * calculateDifficulty(autoAssignConfig.getImageCountWeight(), imageCount)
                * assignment.getWorkload()
                / assignment.getCapacity();
    }

    private static double getServiceTypeWeight(
            StatusInAssignment assignment, AutoAssignRule autoAssignConfig) {
        var weight = 0D;
        var serviceTypeList =
                assignment.getAssignment().stream()
                        .map(PipelineAssignedTask::getServiceTypeKey)
                        .collect(Collectors.toList());
        serviceTypeList.addAll(
                assignment.getAssignmentCache().stream()
                        .map(PipelineAssignedTask::getServiceTypeKey)
                        .collect(Collectors.toList()));
        var countMap = findIdCounts(serviceTypeList);
        for (var entry : countMap.entrySet()) {
            if (entry.getValue() > 1) {
                weight +=
                        calculateDifficulty(
                                autoAssignConfig.getServiceTypeWeight(), entry.getValue());
            }
        }
        return autoAssignConfig.getServiceTypeWeightProportion()
                * weight
                * assignment.getWorkload()
                / assignment.getCapacity();
    }

    private static double getPilotWeight(
            StatusInAssignment assignment, AutoAssignRule autoAssignConfig) {
        var weight = 0D;
        var pilotList =
                assignment.getAssignment().stream()
                        .map(PipelineAssignedTask::getPilotId)
                        .collect(Collectors.toList());
        pilotList.addAll(
                assignment.getAssignmentCache().stream()
                        .map(PipelineAssignedTask::getPilotId)
                        .collect(Collectors.toList()));
        var countMap = findIdCounts(pilotList);
        for (var entry : countMap.entrySet()) {
            if (entry.getValue() > 1) {
                weight +=
                        calculateDifficulty(
                                autoAssignConfig.getPilotCountWeight(), entry.getValue());
            }
        }
        return autoAssignConfig.getPilotCountWeightProportion()
                * weight
                * assignment.getWorkload()
                / assignment.getCapacity();
    }

    private static double calculateDifficulty(double x, int n) {
        return x * n;
    }

    private static <T> Map<T, Integer> findIdCounts(List<T> ids) {
        return ids.stream()
                .collect(Collectors.groupingBy(id -> id, Collectors.summingInt(id -> 1)));
    }

    /**
     * 设置task可分配人员的id列表 1、如果配置了明确的companyKey和serviceType配置，则允许分配 2、如果没有明确指定，但是配置了 ALL
     * 允许所有case，则需要检查exclude是否有配置 3、如果exclude有明确的companyKey和serviceType配置，则不允许分配 4、如果include不是ALL,
     * exclude是 ALL 则不允许分配 5、根据结果设置 AcceptableId
     *
     * @param tasks task list
     * @param ruleItems 人员可被分配的配置规则
     */
    private void setAcceptableUserIds(
            Iterable<PipelineAssignedTask> tasks, List<RuleItem> ruleItems) {
        for (var task : tasks) {
            for (var ruleItem : ruleItems) {
                var permit = false;
                var permitAll = false;
                var includeItems = ruleItem.getInclude();
                for (var includeItem : includeItems) {
                    var companyKeys = includeItem.getCompanyKey();
                    var serviceTypes = includeItem.getServiceType();
                    var permitCompanyAll = permitAll(companyKeys);
                    var permitServiceTypeAll = permitAll(serviceTypes);
                    var permitCompany =
                            CollectionUtils.containsAny(
                                    companyKeys, task.getInsuredKey(), task.getProcessKey());
                    var permitServiceType = serviceTypes.contains(task.getServiceTypeKey());
                    if (permitCompanyAll && permitServiceTypeAll) {
                        // 2、如果没有明确指定，但是配置了 ALL 允许所有case，则需要检查exclude是否有配置
                        permitAll = true;
                    }
                    if ((permitCompany && (permitServiceType || permitServiceTypeAll))
                            || (permitCompanyAll && permitServiceType)) {
                        // 1、如果配置了明确的companyKey和serviceType配置，则允许分配
                        permit = true;
                        break;
                    }
                }

                if (!permit && permitAll) {
                    var excludeItems = ruleItem.getExclude();
                    for (var excludeItem : excludeItems) {
                        var companyKeys = excludeItem.getCompanyKey();
                        var serviceTypes = excludeItem.getServiceType();
                        var excludeCompanyAll = permitAll(companyKeys);
                        var excludeServiceTypeAll = permitAll(serviceTypes);
                        var excludeCompany =
                                CollectionUtils.containsAny(
                                        companyKeys, task.getInsuredKey(), task.getProcessKey());
                        var excludeServiceType = serviceTypes.contains(task.getServiceTypeKey());
                        if (excludeCompanyAll && excludeServiceTypeAll) {
                            // 4、如果include不是ALL, exclude是 ALL 则不允许分配
                            permitAll = false;
                        }
                        if ((excludeCompany && (excludeServiceType || excludeServiceTypeAll))
                                || (excludeCompanyAll && excludeServiceType)) {
                            // 3、如果exclude有明确的companyKey和serviceType配置，则不允许分配
                            permitAll = false;
                            break;
                        }
                    }
                }
                if (permit || permitAll) {
                    if (CollectionUtils.isEmpty(task.getAcceptableIds())) {
                        task.setAcceptableIds(new ArrayList<>());
                    }
                    CollectionUtils.addAll(task.getAcceptableIds(), ruleItem.getUserIds());
                }
            }
            // 5、根据结果设置 AcceptableId
            if (CollectionUtils.isNotEmpty(task.getAcceptableIds())) {
                task.setAcceptableIds(
                        task.getAcceptableIds().stream()
                                .distinct()
                                .sorted()
                                .collect(Collectors.toList()));
            }
        }
    }

    private boolean permitAll(Collection<String> keys) {
        return keys.contains(AutoAssignProperties.PERMIT_ALL);
    }

    private int getAvailableCapacity(StatusInAssignment assigneeSchedule) {
        return assigneeSchedule.getCapacity() - assigneeSchedule.getWorkload();
    }

    private Comparator<StatusInAssignment> getComparator(int handlerCode) {
        var handleEnum = AssignStrategy.forNumber(handlerCode);
        if (handleEnum == null) {
            throw new IllegalArgumentException();
        }
        switch (handleEnum) {
            case ROUND_ROBIN:
                return (compare1, compare2) -> {
                    int compareAssignTimes =
                            Integer.compare(compare1.getAssignTimes(), compare2.getAssignTimes());
                    int compareAssignPosition =
                            Integer.compare(compare1.getPosition(), compare2.getPosition());
                    // If assign times is equal, use position to sort
                    return compareAssignTimes == 0 ? compareAssignPosition : compareAssignTimes;
                };
            case LEAST_WORKLOAD:
                return Comparator.comparingInt(StatusInAssignment::getWorkload);
            case MAX_CAPACITY:
                return Comparator.comparingDouble(s -> s.getWorkload() * 1.0 / s.getCapacity());
        }
        // All available situation should be in switch case, normally not reach here
        log.warn(
                "Not found correspond comparator with handler code: "
                        + handlerCode
                        + " and handle enum: "
                        + handleEnum
                        + ", using id comparator as default.");
        return Comparator.comparing(StatusInAssignment::getId);
    }
}
