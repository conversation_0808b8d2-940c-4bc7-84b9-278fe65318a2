package com.bees360.project.listener;

import com.bees360.activity.Activities;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.util.DateTimes;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;

/**
 * Create activity when project created; including created activity, note activity and attachment
 * activity
 */
@Log4j2
public class AddActivityOnProjectCreatedEvent
        extends AbstractNamedEventListener<ProjectCreatedEvent> {

    private final ProjectIIManager projectIIManager;

    private final ActivityManager activityManager;

    private final CommentManager commentManager;

    private static final String WEB_SOURCE = "WEB";

    public AddActivityOnProjectCreatedEvent(
            ProjectIIManager projectIIManager,
            ActivityManager activityManager,
            CommentManager commentManager) {
        this.projectIIManager = projectIIManager;
        this.activityManager = activityManager;
        this.commentManager = commentManager;
        log.info("Created {}(activityManager={}).", this, this.activityManager);
    }

    @Override
    public void handle(ProjectCreatedEvent event) throws IOException {
        var projectId = event.getProjectId();
        var project = projectIIManager.findById(projectId);

        // Make sure order of activities.
        // Created activity should be first of all.
        createProjectCreatedActivity(project);

        var createdAt = Instant.now();
        createProjectNoteActivity(project, createdAt);
    }

    private void createProjectCreatedActivity(ProjectII project) {
        var projectId = Long.parseLong(project.getId());
        var createdBy = project.getCreateBy().getId();
        var createdAt = project.getCreatedAt();
        var activity = Activities.createProject(projectId, createdBy, createdAt);
        activityManager.submitActivity(activity);
    }

    private void createProjectNoteActivity(ProjectII project, Instant createdAt) {
        var note = project.getNote();

        if (StringUtils.isEmpty(note)) {
            return;
        }

        commentManager.addComment(
                Comment.from(
                        com.bees360.activity.Message.CommentMessage.newBuilder()
                                .setProjectId(Long.parseLong(project.getId()))
                                .setCreatedBy(project.getCreateBy().toMessage())
                                .setContent(note)
                                .setCreatedAt(DateTimes.toTimestamp(createdAt))
                                .setSource(WEB_SOURCE)
                                .build()));
    }
}
