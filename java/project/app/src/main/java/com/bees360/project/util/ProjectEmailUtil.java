package com.bees360.project.util;

import static java.time.temporal.ChronoUnit.DAYS;

import com.bees360.activity.Comment;
import com.bees360.building.Message;
import com.bees360.customer.Customer;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectStatusEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.member.Member;
import com.bees360.project.member.RoleEnum;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.DateTimes;
import com.bees360.util.Defaults;
import com.bees360.util.Iterables;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
public class ProjectEmailUtil {

    private final ContactManager contactManager;

    private final Function<String, List<Comment>> projectContactCommentProvider;

    private final UserProvider userProvider;

    private static final DateTimeFormatter dateTimeFormatter =
            DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a z")
                    .withZone(DateTimes.DEFAULT_US_ZONE_ID);

    private static final DateTimeFormatter dateFormatter =
            DateTimeFormatter.ofPattern("MM/dd/yyyy");

    public ProjectEmailUtil(
            ContactManager contactManager,
            Function<String, List<Comment>> projectContactCommentProvider,
            UserProvider userProvider) {
        this.contactManager = contactManager;
        this.projectContactCommentProvider = projectContactCommentProvider;
        this.userProvider = userProvider;
    }

    public Map<String, Object> assembleProjectMailMap(ProjectII project, LocalDate triggerDate) {
        Map<String, Object> mailMap = new HashMap<>();
        var projectId = project.getId();
        mailMap.put(
                "projectType",
                Defaults.transformOrDefaultIfNull(
                        project.getServiceType(),
                        serviceType -> serviceType.getProjectType().getDisplay(),
                        ""));
        mailMap.put(
                "serviceType",
                Defaults.transformOrDefaultIfNull(
                        project.getServiceType(), ServiceTypeEnum::getName, ""));
        mailMap.put("location", project.getPolicy().getAddress().getAddress());
        mailMap.put(
                "namedInsured",
                Defaults.transformOrDefaultIfNull(
                        project.getContract().getInsuredBy(), Customer::getName, ""));
        mailMap.put(
                "inspectionStatus",
                ProjectStatusEnum.valueOf(project.getLatestStatus().getNumber()).getDisplay());
        mailMap.put("policyNumber", project.getPolicy().getPolicyNo());
        mailMap.put(
                "inspectionScheduled",
                Defaults.transformOrDefaultIfNull(
                        project.getInspectionScheduledTime(),
                        t -> DateTimes.toLocalDate(t).format(dateFormatter),
                        ""));

        var effectiveDate = project.getPolicy().getPolicyEffectiveDate();
        mailMap.put(
                "effectiveDate",
                Defaults.transformOrDefaultIfNull(effectiveDate, d -> d.format(dateFormatter), ""));
        mailMap.put(
                "daysPastEffective",
                Defaults.transformOrDefaultIfNull(
                        effectiveDate, d -> DAYS.between(d, triggerDate), ""));

        var orderDate = DateTimes.toLocalDate(project.getCreatedAt());
        mailMap.put(
                "orderDate",
                Defaults.transformOrDefaultIfNull(orderDate, d -> d.format(dateFormatter), ""));
        mailMap.put(
                "daysPastOrder",
                Defaults.transformOrDefaultIfNull(
                        orderDate, d -> DAYS.between(d, triggerDate), ""));

        putContactInfo(mailMap, projectId);
        setCommentList(mailMap, projectId);
        putMemberInfoByRole(mailMap, project, RoleEnum.PILOT);

        return mailMap;
    }

    private void putMemberInfoByRole(
            Map<String, Object> mailMap, ProjectII project, RoleEnum roleEnum) {
        var member =
                Iterables.toStream(project.getMember())
                        .filter(m -> Objects.equals(m.getRole(), roleEnum.getValue()))
                        .findFirst()
                        .map(Member::getUser)
                        .orElse(null);
        var role = roleEnum.getValue().toLowerCase(Locale.ROOT);
        if (member == null) {
            mailMap.put(role + "Name", "");
            mailMap.put(role + "Email", "");
            mailMap.put(role + "Phone", "");
            return;
        }
        mailMap.put(role + "Name", member.getName());
        mailMap.put(role + "Email", member.getEmail());
        mailMap.put(role + "Phone", member.getPhone());
    }

    private void putContactInfo(Map<String, Object> mailMap, String projectId) {
        var contacts =
                Iterables.toStream(contactManager.findByProjectId(projectId))
                        .collect(
                                Collectors.toMap(
                                        Contact::getRole, Function.identity(), (a, b) -> a));
        var agent = ContactRoleEnum.AGENT.getName();
        var insured = ContactRoleEnum.INSURED.getName();

        putContactInfoByRole(mailMap, agent.toLowerCase(Locale.ROOT), contacts.get(agent));
        putContactInfoByRole(mailMap, insured.toLowerCase(Locale.ROOT), contacts.get(insured));
    }

    private void putContactInfoByRole(Map<String, Object> map, String role, Contact contact) {
        if (contact == null) {
            map.put(role + "Name", StringUtils.EMPTY);
            map.put(role + "Email", StringUtils.EMPTY);
            map.put(role + "Phone", StringUtils.EMPTY);
            return;
        }
        map.put(role + "Name", contact.getFullName());
        map.put(role + "Email", contact.getPrimaryEmail());
        map.put(role + "Phone", contact.getPrimaryPhone());
    }

    private void setCommentList(Map<String, Object> mailMap, String projectId) {
        var commentList = projectContactCommentProvider.apply(projectId);

        if (CollectionUtils.isEmpty(commentList)) {
            return;
        }

        var comments = commentList.stream().map(this::convertComment).collect(Collectors.toList());
        mailMap.put("note", comments);
    }

    private CommentObject convertComment(Comment comment) {
        var creator = userProvider.findUserById(comment.getCreatedBy());

        var createdBy = Optional.ofNullable(creator).map(User::getName).orElse("");
        var createdAt =
                LocalDateTime.ofInstant(comment.getCreatedAt(), DateTimes.DEFAULT_US_ZONE_ID);
        return new CommentObject(
                createdBy, comment.getContent(), createdAt.format(dateTimeFormatter));
    }

    @Getter
    @AllArgsConstructor
    private static class CommentObject {
        private String createdBy;
        private String content;
        private String createdAt;
    }

    public static void putLOBInfo(
            Map<String, Object> variables,
            String policyType,
            com.bees360.building.Message.BuildingType propertyType,
            Function<com.bees360.building.Message.BuildingType, String> propertyTypeNameProvider,
            BiFunction<String, Message.BuildingType, String> naicsCodeProvider) {
        log.debug("Put LOB info for policyType={}, propertyType={}", policyType, propertyType);
        var propertyTypeName = propertyTypeNameProvider.apply(propertyType);
        if (StringUtils.isBlank(policyType)) {
            variables.put("LOB", propertyTypeName);
        } else {
            variables.put("LOB", StringUtils.join(List.of(policyType, propertyTypeName), " - "));
        }
        variables.put("NAICSCode", naicsCodeProvider.apply(policyType, propertyType));
    }
}
