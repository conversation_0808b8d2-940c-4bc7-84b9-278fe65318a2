package com.bees360.project.pipeline;

import com.bees360.assignment.Message;
import com.bees360.pipeline.assign.AssigneeSchedule;
import com.bees360.project.util.JSUtil;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.regex.Pattern;

@Log4j2
public class AssigneeScheduleFactory {

    private static final String PRE_ASSIGN_TASK_KEY = "preAssignTask";

    private static final Pattern REGEX_PATTERN = Pattern.compile("\\s+");

    public AssigneeSchedule create(
            Message.AssigneeScheduleResponse message,
            List<Message.AssigneeScheduleResponse.Task> preAssignTasks) {
        return new DynamicAssigneeSchedule(
                message,
                (script, assigneeSchedule) -> {
                    var jsonString = convertToJsonString(assigneeSchedule);

                    if (CollectionUtils.isNotEmpty(preAssignTasks)) {
                        var taskArray = new JsonArray();
                        for (var task : preAssignTasks) {
                            var taskString = convertToJsonString(task);
                            var taskObject = JsonParser.parseString(taskString).getAsJsonObject();
                            taskArray.add(taskObject);
                        }

                        var paramObject = JsonParser.parseString(jsonString).getAsJsonObject();
                        paramObject.add(PRE_ASSIGN_TASK_KEY, taskArray);
                        jsonString = paramObject.toString();
                    }

                    var result = JSUtil.execute(script, jsonString);
                    if (result == null) {
                        throw new IllegalArgumentException(
                                String.format(
                                        "The calculated capacity value cannot be null: script is %s"
                                                + " jsonString is %s",
                                        script, jsonString));
                    }
                    return (result instanceof Integer) ? (int) result : (int) (double) result;
                });
    }

    private String convertToJsonString(GeneratedMessageV3 message) {
        String jsonString;
        try {
            jsonString = JsonFormat.printer().print(message);
        } catch (InvalidProtocolBufferException e) {
            throw new IllegalArgumentException(
                    String.format("Conversion message to json failed: message is %s", message), e);
        }
        var matcher = REGEX_PATTERN.matcher(jsonString);
        return matcher.replaceAll("");
    }
}
