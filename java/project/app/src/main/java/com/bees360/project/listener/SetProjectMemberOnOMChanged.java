package com.bees360.project.listener;

import com.bees360.event.registry.OperationsManagerAssignedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.member.MemberManager;
import com.bees360.project.member.RoleEnum;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class SetProjectMemberOnOMChanged
        extends AbstractNamedEventListener<OperationsManagerAssignedEvent> {

    private final MemberManager memberManager;

    public SetProjectMemberOnOMChanged(MemberManager memberManager) {
        this.memberManager = memberManager;
        log.info("Created {}(memberManager={}).", this, this.memberManager);
    }

    @Override
    public void handle(OperationsManagerAssignedEvent event) throws IOException {
        memberManager.setMember(
                event.getProjectId(),
                event.getOperationsManagerId(),
                RoleEnum.OPERATIONS_MANAGER,
                event.getUpdatedBy(),
                event.getUpdateTime());
        log.info(
                "Update project {} member operations manager with user id {}",
                event.getProjectId(),
                event.getOperationsManagerId());
    }
}
