package com.bees360.project;

import com.bees360.api.InvalidArgumentException;
import com.bees360.building.Message;
import com.bees360.project.status.ProjectStatusProvider;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Supplier;

/** ProjectPolicyManager with validation features to enhance the ProjectPolicyManager. */
@Log4j2
public class ValidationProjectPolicyManager implements ProjectPolicyManager {
    private final ProjectStatusProvider projectStatusProvider;

    private final ProjectIIManager projectIIManager;
    private final BiFunction<String, Integer, Integer> policyTypePropertyTypeValidator;
    private final BuildingManager buildingManager;
    private final Supplier<Set<Integer>> denyToUpdatePolicyTypeIfStatusNotIn;

    public ValidationProjectPolicyManager(
            ProjectIIManager projectIIManager,
            BuildingManager buildingManager,
            BiFunction<String, Integer, Integer> policyTypePropertyTypeValidator,
            ProjectStatusProvider projectStatusProvider,
            Supplier<Set<Integer>> denyToUpdatePolicyTypeIfStatusNotIn) {
        this.buildingManager = buildingManager;
        this.projectIIManager = projectIIManager;
        this.policyTypePropertyTypeValidator = policyTypePropertyTypeValidator;
        this.projectStatusProvider = projectStatusProvider;
        this.denyToUpdatePolicyTypeIfStatusNotIn = denyToUpdatePolicyTypeIfStatusNotIn;
        log.info("Created {}", this);
    }

    @Override
    public void updatePolicyTypeAndPropertyType(
            @NonNull String projectId, @NonNull String policyType, @NonNull Integer propertyType) {
        log.debug(
                "try to update policy type and property type: project={}, policyType={},"
                        + " propertyType={}",
                projectId,
                policyType,
                propertyType);

        var project = projectIIManager.findById(projectId);

        validateProjectStatusWhenUpdatePolicyType(projectId);

        policyTypePropertyTypeValidator.apply(policyType, propertyType);

        projectIIManager.updateProjectPolicyType(projectId, policyType);
        buildingManager.updateBuildingType(
                project.getPolicy().getBuilding().getId(),
                Message.BuildingType.forNumber(propertyType));
    }

    private void validateProjectStatusWhenUpdatePolicyType(String projectId) {
        var statuses = projectStatusProvider.getStatusHistory(projectId);
        var statusesOnly = denyToUpdatePolicyTypeIfStatusNotIn.get();
        for (var status : statuses) {
            if (!statusesOnly.contains(status.getStatus().getNumber())) {
                throw new InvalidArgumentException(
                        "Policy Type and Property Type cannot be changed after inspection"
                            + " processing. To modify these, please cancel this project and create"
                            + " a new one.");
            }
        }
    }
}
