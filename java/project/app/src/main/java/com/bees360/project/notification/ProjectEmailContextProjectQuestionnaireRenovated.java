package com.bees360.project.notification;

import com.bees360.address.AddressProvider;
import com.bees360.codec.GsonCodec;
import com.bees360.contract.ContractManager;
import com.bees360.customer.Customer;
import com.bees360.policy.PolicyRepository;
import com.bees360.project.JooqProjectIIRepository;
import com.bees360.project.ProjectIIRepository;
import com.bees360.util.Defaults;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.gson.Gson;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.util.Map;

@Log4j2
public class ProjectEmailContextProjectQuestionnaireRenovated implements ProjectEmailContext {

    public static final String PROJECT_EMAIL_KEY_QUESTIONNAIRE_HOMEOWNER =
            "project_renovation_questionnaire_homeowner";
    public static final String PROJECT_EMAIL_KEY_QUESTIONNAIRE_PILOT =
            "project_renovation_questionnaire_pilot";

    private final String projectEmailKey;
    private final Gson gson = GsonCodec.DEFAULT_GSON_BUILDER.create();
    private final ProjectIIRepository projectIIRepository;
    private final PolicyRepository policyManager;
    private final AddressProvider addressProvider;

    private final ContractManager contractManager;

    public ProjectEmailContextProjectQuestionnaireRenovated(
            @NonNull String projectEmailKey,
            @NonNull JooqProjectIIRepository projectIIRepository,
            @NonNull PolicyRepository policyManager,
            @NonNull AddressProvider addressProvider,
            @NonNull ContractManager contractManager) {
        this.projectEmailKey = projectEmailKey;
        this.projectIIRepository = projectIIRepository;
        this.policyManager = policyManager;
        this.addressProvider = addressProvider;
        this.contractManager = contractManager;
        log.info(
                "Created"
                    + " {}(projectEmailKey={},projectIIRepository={},policyManager={},addressProvider={},contractManager={})",
                this,
                projectEmailKey,
                projectIIRepository,
                policyManager,
                addressProvider,
                contractManager);
    }

    @Override
    public String getEmailKey() {
        return projectEmailKey;
    }

    @Override
    public String getEmailVariables(String projectId) {
        var project = projectIIRepository.findById(projectId);

        var policyId = project.getPolicy().getId();
        var policy = policyManager.findById(policyId);
        Preconditions.checkArgument(
                policy != null,
                "Policy not found:Policy %s for project %s not found.",
                projectId,
                policyId);

        var address = addressProvider.findById(policy.getAddress().getId());
        var contract = contractManager.findById(project.getContract().getId());

        Map<String, Object> variables = Maps.newHashMap();
        variables.put("projectId", project.getId());
        variables.put("policyNumber", policy.getPolicyNo());
        variables.put("address", address.getAddress());

        variables.put("insuredBy", toMap(contract.getInsuredBy()));
        return gson.toJson(variables);
    }

    private Map<String, Object> toMap(Customer customer) {
        return Map.of("name", Defaults.transformOrDefaultIfNull(customer, Customer::getName, ""));
    }
}
