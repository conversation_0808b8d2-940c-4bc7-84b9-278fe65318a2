package com.bees360.project.util.easyexcel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.DateUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

public class LocalDateConverter implements Converter<LocalDate> {
    @Override
    public Class<LocalDate> supportJavaTypeKey() {
        return LocalDate.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDate convertToJavaData(
            ReadCellData<?> cellData,
            ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration)
            throws Exception {
        if (contentProperty == null || contentProperty.getDateTimeFormatProperty() == null) {
            var date = DateUtils.parseDate(cellData.getStringValue(), null);
            return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        } else {
            var date =
                    DateUtils.parseDate(
                            cellData.getStringValue(),
                            contentProperty.getDateTimeFormatProperty().getFormat());
            return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        }
    }

    @Override
    public WriteCellData<String> convertToExcelData(
            LocalDate value,
            ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration)
            throws Exception {
        if (contentProperty == null || contentProperty.getDateTimeFormatProperty() == null) {
            var instant = value.atStartOfDay(ZoneId.systemDefault()).toInstant();
            return new WriteCellData(DateUtils.format(Date.from(instant), null));
        } else {
            var instant = value.atStartOfDay(ZoneId.systemDefault()).toInstant();
            return new WriteCellData(
                    DateUtils.format(
                            Date.from(instant),
                            contentProperty.getDateTimeFormatProperty().getFormat()));
        }
    }
}
