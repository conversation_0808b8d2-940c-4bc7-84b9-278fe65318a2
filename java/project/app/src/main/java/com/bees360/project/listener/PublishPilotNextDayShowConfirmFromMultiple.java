package com.bees360.project.listener;

import com.bees360.event.EventPublisher;
import com.bees360.event.registry.MultiplePilotNextDayShowConfirm;
import com.bees360.event.registry.PilotNextDayShowConfirm;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class PublishPilotNextDayShowConfirmFromMultiple
        extends AbstractNamedEventListener<MultiplePilotNextDayShowConfirm> {
    private final EventPublisher eventPublisher;

    public PublishPilotNextDayShowConfirmFromMultiple(EventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;

        log.info("Created '{}(eventPublisher={}'", this, this.eventPublisher);
    }

    @Override
    public void handle(MultiplePilotNextDayShowConfirm event) throws IOException {
        for (PilotNextDayShowConfirm pilotNextDayShowConfirm : event.getProjectList()) {
            try {
                eventPublisher.publish(pilotNextDayShowConfirm);
            } catch (RuntimeException e) {
                log.error("Failed to publish '{}', please solve it manually.", event, e);
            }
        }
    }
}
