package com.bees360.project.config.mail;

import lombok.Data;

import org.apache.commons.collections4.map.HashedMap;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Configuration
public class ProjectNotificationSubscribeConfig {

    @Data
    @ConfigurationProperties(prefix = "project.notification")
    @EnableConfigurationProperties
    public static class ProjectNotificationProperties {
        // key: customer key
        // value: {key: email template key, value: recipient emails}
        private Map<String, Map<String, List<String>>> customerSubscription = new HashedMap<>();
        // email list
        private List<String> defaultRecipient;
        // key: Customer Key
        private Map<String, CustomerSubscription> customer = new HashedMap<>();
    }

    @Data
    public static class CustomerSubscription {
        // key: email template key
        private Map<String, CustomerNotificationProperties> subscribe = new HashedMap<>();
    }

    @Data
    public static class CustomerNotificationProperties {

        private List<Condition> conditions = new ArrayList<>();

        @Data
        public static class Condition {
            private String type;
            private String operator;
            private String field;
            private List<String> value;
        }
    }
}
