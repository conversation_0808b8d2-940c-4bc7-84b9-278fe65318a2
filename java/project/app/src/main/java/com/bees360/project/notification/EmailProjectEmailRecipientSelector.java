package com.bees360.project.notification;

import com.alibaba.excel.util.StringUtils;

import java.util.List;

/** return the email in {@code RecipientSelector}. */
public class EmailProjectEmailRecipientSelector implements ProjectEmailRecipientSelector {

    @Override
    public Iterable<EmailRecipient> select(String projectId, RecipientSelector selector) {
        if (selector.getEmail() == null || StringUtils.isEmpty(selector.getEmail().getEmail())) {
            return List.of();
        }
        var recipient = new EmailRecipient().setEmail(selector.getEmail().getEmail());
        return List.of(recipient);
    }
}
