package com.bees360.project.config;

import static com.bees360.project.Message.ProjectStatus.ASSIGNED_TO_PILOT_VALUE;
import static com.bees360.project.Message.ProjectStatus.CUSTOMER_CONTACTED_VALUE;
import static com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED_VALUE;
import static com.bees360.project.Message.ProjectStatus.RETURNED_TO_CLIENT_VALUE;
import static com.bees360.project.Message.ProjectStatus.SITE_INSPECTED_VALUE;

import com.bees360.bundle.BundleManager;
import com.bees360.bundle.Message;
import com.bees360.event.UpdateBundleStatusOnProjectStatusChanged;
import com.bees360.project.group.ProjectGroupManager;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.function.Function;

@Configuration
public class ProjectBundleConfig {
    @Bean
    Function<Integer, Message.BundleStatus> projectStatusToBundleStatus() {
        return status ->
                switch (status) {
                    case ASSIGNED_TO_PILOT_VALUE -> Message.BundleStatus.ASSIGNED_TO_PILOT;
                    case CUSTOMER_CONTACTED_VALUE -> Message.BundleStatus.CUSTOMER_CONTACTED;
                    case SITE_INSPECTED_VALUE -> Message.BundleStatus.SITE_INSPECTED;
                    case IMAGE_UPLOADED_VALUE -> Message.BundleStatus.IMAGE_UPLOADED;
                    case RETURNED_TO_CLIENT_VALUE -> Message.BundleStatus.RETURNED_TO_CLIENT;
                    default -> null;
                };
    }

    @Bean
    public UpdateBundleStatusOnProjectStatusChanged updateBundleStatusOnProjectStatusChanged(
            @Qualifier("projectStatusToBundleStatus")
                    Function<Integer, Message.BundleStatus> projectStatusToBundleStatus,
            BundleManager bundleManager,
            ProjectGroupManager projectGroupManager) {
        return new UpdateBundleStatusOnProjectStatusChanged(
                projectStatusToBundleStatus, bundleManager, projectGroupManager);
    }
}
