package com.bees360.project.config;

import com.bees360.activity.ActivityManager;
import com.bees360.beespilot.HttpPilotImageScoreManager;
import com.bees360.beespilot.PilotImageScoreManager;
import com.bees360.http.HttpClient;
import com.bees360.pilot.ProjectPilotFeedback;
import com.bees360.pilot.feedback.PilotFeedbackManager;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.image.ProjectImageProvider;
import com.bees360.project.listener.RatePilotImageScoreOnActivityAdded;
import com.bees360.project.listener.RatePilotImageScoreOnManuallyRating;
import com.bees360.project.listener.RatePilotImageScoreOnReturnToClient;
import com.bees360.user.UserProvider;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;

@Configuration
@Import({
    BeespilotConfig.class,
})
@Log4j2
@ConditionalOnProperty(
        prefix = "http.beespilot.image-score",
        name = "enabled",
        havingValue = "true")
public class PilotImageScoreManagerConfig {
    @Bean
    public PilotImageScoreManager pilotImageScoreManager(
            HttpClient beespilotHttpClient,
            @Value("${http.beespilot.project.endpoint}") String endpoint,
            @Value("${http.beespilot.image-score.path}") String imageScorePath,
            UserProvider userProvider) {
        var context = URI.create(endpoint);
        return new HttpPilotImageScoreManager(
                imageScorePath,
                context,
                beespilotHttpClient,
                id -> Objects.requireNonNull(userProvider.findUserById(id)).getEmail());
    }

    @Bean
    @ConditionalOnProperty(
            name = "http.beespilot.image-score.rate-by-activity.enabled",
            havingValue = "true",
            matchIfMissing = true)
    public RatePilotImageScoreOnActivityAdded ratePilotImageScoreOnActivityAdded(
            PilotImageScoreManager pilotImageScoreManager) {
        // 自动打分现在也会生成一条activity，不应再使用此监听器同步飞手分数。应该使用RatePilotImageScoreOnManuallyRating代替手动打分。
        return new RatePilotImageScoreOnActivityAdded(pilotImageScoreManager);
    }

    @Bean
    @ConditionalOnProperty(
            name = "http.beespilot.image-score.rate-by-activity.enabled",
            havingValue = "false")
    public RatePilotImageScoreOnManuallyRating ratePilotImageScoreOnManuallyRating(
            PilotImageScoreManager pilotImageScoreManager,
            UserProvider userProvider,
            ActivityManager activityManager) {
        return new RatePilotImageScoreOnManuallyRating(
                pilotImageScoreManager, userProvider, activityManager);
    }

    @Data
    static class ProjectImageRatingScoreProperty {
        private String feedbackId;
        private Integer score;
    }

    @Bean
    @ConfigurationProperties(prefix = "http.beespilot.image-score.rate-by-images.property")
    public List<ProjectImageRatingScoreProperty> properties() {
        return new ArrayList<>();
    }

    @Bean
    @ConditionalOnProperty(
            name = "http.beespilot.image-score.rate-by-images.enabled",
            havingValue = "true")
    public Function<ProjectPilotFeedback, Integer> imageFeedback2DeductedScoreConverter(
            List<ProjectImageRatingScoreProperty> properties) {
        log.info("get imageFeedback2PilotScoreConverter from properties :{}", properties);
        return imageFeedback ->
                properties.stream()
                        .filter(
                                property ->
                                        Objects.equals(
                                                property.getFeedbackId(),
                                                imageFeedback.getFeedbackId()))
                        .findFirst()
                        .map(ProjectImageRatingScoreProperty::getScore)
                        .orElse(0);
    }

    @Bean
    @ConditionalOnProperty(
            name = "http.beespilot.image-score.rate-by-images.enabled",
            havingValue = "true")
    public RatePilotImageScoreOnReturnToClient rateImageScoreOnReportApproved(
            ProjectIIRepository projectIIRepository,
            PilotFeedbackManager pilotFeedbackManager,
            @Qualifier("imageFeedback2DeductedScoreConverter")
                    Function<ProjectPilotFeedback, Integer> imageFeedback2DeductedScoreConverter,
            PilotImageScoreManager pilotImageScoreManager,
            ProjectImageProvider projectImageProvider,
            @Qualifier("robotUserIdSupplier") Supplier<String> robotUserIdSupplier,
            @Value("${http.beespilot.image-score.rate-by-images.allowed-service-type:}")
                    Set<ServiceTypeEnum> allowedServiceTypeSet,
            ActivityManager activityManager,
            UserProvider userProvider) {
        return new RatePilotImageScoreOnReturnToClient(
                projectIIRepository,
                pilotFeedbackManager,
                imageFeedback2DeductedScoreConverter,
                pilotImageScoreManager,
                projectImageProvider,
                robotUserIdSupplier,
                allowedServiceTypeSet,
                activityManager,
                userProvider);
    }
}
