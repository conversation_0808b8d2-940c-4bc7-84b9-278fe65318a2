package com.bees360.project.config.mail;

import static com.bees360.project.notification.ProjectEmailContextProjectQuestionnaireRenovated.PROJECT_EMAIL_KEY_QUESTIONNAIRE_HOMEOWNER;
import static com.bees360.project.notification.ProjectEmailContextProjectQuestionnaireRenovated.PROJECT_EMAIL_KEY_QUESTIONNAIRE_PILOT;

import com.bees360.address.AddressProvider;
import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.contract.JooqContractRepository;
import com.bees360.contract.config.JooqContractRepositoryConfig;
import com.bees360.customer.JooqCustomerRepository;
import com.bees360.customer.config.JooqCustomerRepositoryConfig;
import com.bees360.event.SendCustomerProjectEmailOnCloseoutReportGenerated;
import com.bees360.event.SendCustomerProjectEmailOnProjectCreated;
import com.bees360.event.SendCustomerProjectEmailOnProjectStateChangedEvent;
import com.bees360.event.SendCustomerProjectEmailOnProjectStatusChanged;
import com.bees360.event.SendCustomerProjectEmailOnQuestionnaireRenovationSubmitted;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.ProjectCanceledEvent;
import com.bees360.event.registry.ProjectClientReceivedEvent;
import com.bees360.event.registry.ProjectReturnedToClientEvent;
import com.bees360.job.JobScheduler;
import com.bees360.job.SendCustomerProjectEmailJobExecutor;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.mail.MailSenderProvider;
import com.bees360.mail.util.MailMessageFactory;
import com.bees360.policy.PolicyManager;
import com.bees360.policy.config.JooqPolicyRepositoryConfig;
import com.bees360.project.ContactManager;
import com.bees360.project.JooqProjectIIRepository;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.config.ProjectGlobalOptionsProperties;
import com.bees360.project.config.ProjectListenersConfig;
import com.bees360.project.config.ProjectRepositoryConfig;
import com.bees360.project.config.ProjectRequestCancelConfig;
import com.bees360.project.member.MemberManager;
import com.bees360.project.notification.AMProjectEmailRecipientSelector;
import com.bees360.project.notification.CompositeProjectEmailRecipientSelector;
import com.bees360.project.notification.ContactProjectEmailRecipientSelector;
import com.bees360.project.notification.CustomerProjectEmailNotify;
import com.bees360.project.notification.CustomerProjectEmailProperties;
import com.bees360.project.notification.EmailProjectEmailRecipientSelector;
import com.bees360.project.notification.MemberProjectEmailRecipientSelector;
import com.bees360.project.notification.ProjectEmailContext;
import com.bees360.project.notification.ProjectEmailContextProjectCancelled;
import com.bees360.project.notification.ProjectEmailContextProjectCompleted;
import com.bees360.project.notification.ProjectEmailContextProjectCreated;
import com.bees360.project.notification.ProjectEmailContextProjectQuestionnaireRenovated;
import com.bees360.project.notification.ProjectEmailRecipientSelector;
import com.bees360.project.notification.StateUpdatedByProjectEmailRecipientSelector;
import com.bees360.project.state.ProjectState;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.report.ReportProvider;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.bees360.util.retry.RetryProperties;
import com.google.common.base.Preconditions;

import jakarta.annotation.PostConstruct;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Log4j2
@Import({
    MailSenderConfigs.class,
    AutoRegisterJobExecutorConfig.class,
    AutoRegisterEventListenerConfig.class,
    ProjectRepositoryConfig.class,
    JooqCustomerRepositoryConfig.class,
    JooqPolicyRepositoryConfig.class,
    JooqContractRepositoryConfig.class,
    NaicsCodeConfig.class,
    ProjectGlobalOptionsProperties.class,
})
@Configuration
@ConditionalOnProperty(
        prefix = "project.app.customer-project-emails",
        name = "enabled",
        havingValue = "true")
public class CustomerProjectEmailConfig {

    @Data
    static class CustomerProjectEmailsPropertiesConfig {
        private Map<String, CustomerProjectEmailProperties> defaultProjectEmails = new HashMap<>();
        private Map<String, CustomerProjectEmailsProperties> customers = new HashMap<>();
    }

    @Data
    public static class CompanyAMEmailMapPropertiesConfig {
        private List<ProjectListenersConfig.ContactEntity> amContacts = new ArrayList<>();

        private Map<String, String> companyAMMap = new HashMap<>();

        @PostConstruct
        public void init() {
            for (ProjectListenersConfig.ContactEntity amContact : amContacts) {
                List<String> companyKeys = amContact.getCompanyKey();
                for (String companyKey : companyKeys) {
                    companyAMMap.put(companyKey, amContact.getPrimaryEmail());
                }
            }
        }
    }

    @Data
    static class CustomerProjectEmailsProperties {
        private Map<String, CustomerProjectEmailProperties> projectEmails = new HashMap<>();
    }

    @Bean
    @RefreshableConfigurationProperties(prefix = "project.app.customer-project-emails")
    CustomerProjectEmailsPropertiesConfig customerProjectEmailsPropertiesConfig() {
        return new CustomerProjectEmailsPropertiesConfig();
    }

    @Bean
    @RefreshableConfigurationProperties(prefix = "project.app.customer")
    CompanyAMEmailMapPropertiesConfig companyAMMappingPropertiesConfig() {
        return new CompanyAMEmailMapPropertiesConfig();
    }

    @Bean
    ProjectEmailRecipientSelector projectEmailRecipientSelector(
            MemberManager memberManager,
            ContactManager contactManager,
            @Qualifier("grpcProjectManager") ProjectIIManager grpcProjectManager,
            UserProvider userProvider,
            CompanyAMEmailMapPropertiesConfig properties) {
        Map<String, ProjectEmailRecipientSelector> map = new HashMap<>();
        map.put("email", new EmailProjectEmailRecipientSelector());
        map.put("member", new MemberProjectEmailRecipientSelector(memberManager));
        map.put("contact", new ContactProjectEmailRecipientSelector(contactManager));
        map.put(
                "am",
                new AMProjectEmailRecipientSelector(
                        grpcProjectManager,
                        companyKey -> properties.getCompanyAMMap().get(companyKey)));
        map.put(
                "stateUpdateBy",
                new StateUpdatedByProjectEmailRecipientSelector(grpcProjectManager, userProvider));
        return new CompositeProjectEmailRecipientSelector(map);
    }

    @Bean
    BiFunction<String, String, CustomerProjectEmailProperties>
            customerProjectEmailsPropertiesProvider(
                    CustomerProjectEmailsPropertiesConfig properties) {
        return (customerKey, projectEmailKey) -> {
            var defaultProperties = properties.getDefaultProjectEmails().get(projectEmailKey);
            var customerProperties =
                    Optional.ofNullable(properties.getCustomers().get(customerKey))
                            .map(CustomerProjectEmailsProperties::getProjectEmails)
                            .map(map -> map.get(projectEmailKey))
                            .orElse(null);
            if (defaultProperties == null && customerProperties == null) {
                return null;
            }
            var emailProperties = new CustomerProjectEmailProperties();
            Boolean isSubscribed = null;
            if (defaultProperties != null) {
                isSubscribed = defaultProperties.isSubscribed();
                emailProperties.setTemplateKey(defaultProperties.getTemplateKey());
                emailProperties.getToSelector().addAll(defaultProperties.getToSelector());
                emailProperties.getCcSelector().addAll(defaultProperties.getCcSelector());
                emailProperties.getBccSelector().addAll(defaultProperties.getBccSelector());
            }
            if (customerProperties != null) {
                isSubscribed =
                        Optional.ofNullable(customerProperties.getSubscribed())
                                .orElse(isSubscribed);
                if (StringUtils.isNotBlank(customerProperties.getTemplateKey())) {
                    emailProperties.setTemplateKey(customerProperties.getTemplateKey());
                }
                // to
                if (customerProperties.isToSelectorOverrideDefault()) {
                    emailProperties.setToSelector(customerProperties.getToSelector());
                } else {
                    emailProperties.getToSelector().addAll(customerProperties.getToSelector());
                }
                // cc
                if (customerProperties.isCcSelectorOverrideDefault()) {
                    emailProperties.setCcSelector(customerProperties.getCcSelector());
                } else {
                    emailProperties.getCcSelector().addAll(customerProperties.getCcSelector());
                }
                // bcc
                if (customerProperties.isBccSelectorOverrideDefault()) {
                    emailProperties.setBccSelector(customerProperties.getBccSelector());
                } else {
                    emailProperties.getBccSelector().addAll(customerProperties.getBccSelector());
                }
            }

            emailProperties.setSubscribed(isSubscribed);
            return emailProperties;
        };
    }

    @Bean("customerProjectEmailsJobRetryProperties")
    RetryProperties customerProjectEmailsJobRetryProperties() {
        return new RetryProperties().setRetryCount(5).setRetryDelay(Duration.ofMinutes(5));
    }

    @Bean
    Function<String, CustomerProjectEmailNotify> customerProjectEmailNotifyProvider(
            JooqProjectIIRepository jooqProjectIIRepository,
            JooqCustomerRepository jooqCustomerRepository,
            JobScheduler jobScheduler,
            @Qualifier("customerProjectEmailsJobRetryProperties")
                    RetryProperties jobRetryProperties) {
        var map = new ConcurrentHashMap<String, CustomerProjectEmailNotify>();
        log.info(
                "Created"
                    + " customerProjectEmailNotifyProvider(jooqProjectIIRepository={},jooqCustomerRepository={},jobScheduler={})",
                jooqProjectIIRepository,
                jooqCustomerRepository,
                jobScheduler);
        return mailSender ->
                map.computeIfAbsent(
                        mailSender,
                        key ->
                                new CustomerProjectEmailNotify(
                                        mailSender,
                                        jooqProjectIIRepository,
                                        jooqCustomerRepository,
                                        jobScheduler,
                                        jobRetryProperties));
    }

    @Bean
    Predicate<String> claimProjectEmailPredicate(JooqProjectIIRepository jooqProjectIIRepository) {
        return projectId -> {
            var project = jooqProjectIIRepository.findById(projectId);
            return ProjectTypeEnum.CLAIM == project.getProjectType()
                    && project.getServiceType() != ServiceTypeEnum.SCHEDULING_ONLY;
        };
    }

    @Bean
    Predicate<String> underwritingProjectEmailPredicate(
            JooqProjectIIRepository jooqProjectIIRepository) {
        return projectId -> {
            var project = jooqProjectIIRepository.findById(projectId);
            return ProjectTypeEnum.UNDERWRITING == project.getProjectType()
                    && project.getServiceType() != ServiceTypeEnum.SCHEDULING_ONLY;
        };
    }

    @Bean
    Predicate<String> isProjectCanceledPredicate(JooqProjectIIRepository jooqProjectIIRepository) {
        return projectId -> {
            var project = jooqProjectIIRepository.findById(projectId);
            return project.isCanceled();
        };
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.customer-project-emails.trigger",
            name = "project_completed_underwriting",
            havingValue = "project-status:returned-to-client")
    SendCustomerProjectEmailOnProjectStatusChanged<ProjectReturnedToClientEvent>
            sendCustomerProjectEmailOnReturnedToClient(
                    @Qualifier("customerProjectEmailNotifyProvider")
                            Function<String, CustomerProjectEmailNotify>
                                    customerProjectEmailNotifyProvider,
                    @Qualifier("underwritingProjectEmailPredicate")
                            Predicate<String> underwritingProjectEmailPredicate,
                    @Qualifier("isProjectCanceledPredicate")
                            Predicate<String> isProjectCanceledPredicate) {

        var notify = customerProjectEmailNotifyProvider.apply("no-reply-sender");

        Predicate<String> projectEmailProjectIdPredicate =
                projectId ->
                        underwritingProjectEmailPredicate.test(projectId)
                                && !isProjectCanceledPredicate.test(projectId);

        return new SendCustomerProjectEmailOnProjectStatusChanged<ProjectReturnedToClientEvent>(
                ProjectEmailContextProjectCompleted.PROJECT_EMAIL_KEY_UNDERWRITING_COMPLETED,
                projectEmailProjectIdPredicate,
                notify,
                null) {};
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.customer-project-emails.trigger",
            name = "project_completed_claim",
            havingValue = "project-status:client-received")
    SendCustomerProjectEmailOnProjectStatusChanged<ProjectClientReceivedEvent>
            sendCustomerProjectEmailOnProjectStatusChanged(
                    @Qualifier("customerProjectEmailNotifyProvider")
                            Function<String, CustomerProjectEmailNotify>
                                    customerProjectEmailNotifyProvider,
                    @Qualifier("claimProjectEmailPredicate")
                            Predicate<String> claimProjectEmailPredicate,
                    @Qualifier("isProjectCanceledPredicate")
                            Predicate<String> isProjectCanceledPredicate) {

        var notify = customerProjectEmailNotifyProvider.apply("no-reply-sender");

        Predicate<String> projectEmailProjectIdPredicate =
                projectId ->
                        claimProjectEmailPredicate.test(projectId)
                                && !isProjectCanceledPredicate.test(projectId);

        return new SendCustomerProjectEmailOnProjectStatusChanged<ProjectClientReceivedEvent>(
                ProjectEmailContextProjectCompleted.PROJECT_EMAIL_KEY_CLAIM_COMPLETED,
                projectEmailProjectIdPredicate,
                notify,
                null) {};
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.request-cancel",
            name = "enable",
            havingValue = "true")
    SendCustomerProjectEmailOnProjectStatusChanged<ProjectCanceledEvent>
            sendCustomerProjectEmailOnProjectCanceled(
                    @Qualifier("claimProjectEmailPredicate")
                            Predicate<String> claimProjectEmailPredicate,
                    @Qualifier("customerProjectEmailNotifyProvider")
                            Function<String, CustomerProjectEmailNotify>
                                    customerProjectEmailNotifyProvider,
                    @Qualifier("getRequestCancelCreatorEmailByProjectId")
                            Function<String, List<User>> getRequestCancelCreatorEmailByProjectId) {
        var notify = customerProjectEmailNotifyProvider.apply("no-reply-sender");
        return new SendCustomerProjectEmailOnProjectStatusChanged<ProjectCanceledEvent>(
                ProjectEmailContextProjectCancelled.PROJECT_EMAIL_KEY_PROJECT_CANCELLED_CLAIM,
                claimProjectEmailPredicate,
                notify,
                getRequestCancelCreatorEmailByProjectId) {};
    }

    @Bean("revertCancelPredictor")
    public Predicate<ProjectState> revertCancelPredictor(
            ProjectRequestCancelConfig.RequestCancelProperties requestCancelProperties) {
        return currentState -> {
            if (!currentState
                    .getState()
                    .equals(Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN)) {
                return false;
            }
            return currentState
                    .getStateChangeReason()
                    .getDisplayText()
                    .equals(requestCancelProperties.getRevertCancelReason());
        };
    }

    @Bean
    public Function<String, List<User>> getRequestCancelCreatorEmailByProjectId(
            @Qualifier("jooqProjectStateManager") ProjectStateManager jooqProjectStateManager,
            ProjectRequestCancelConfig.RequestCancelProperties requestCancelProperties,
            UserProvider userProvider) {
        return projectId ->
                Iterables.toStream(jooqProjectStateManager.findStateHistoryByProjectId(projectId))
                        .filter(
                                projectState ->
                                        (projectState
                                                        .getState()
                                                        .equals(
                                                                Message.ProjectMessage.ProjectState
                                                                        .ProjectStateEnum
                                                                        .PROJECT_PAUSE)
                                                && projectState
                                                        .getStateChangeReason()
                                                        .getDisplayText()
                                                        .equals(
                                                                requestCancelProperties
                                                                        .getPendingReason())))
                        .max(Comparator.comparing(ProjectState::getUpdatedAt))
                        .map(ProjectState::getUpdatedBy)
                        .map(userProvider::getUser)
                        .map(Collections::singletonList)
                        .orElse(null);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.request-cancel",
            name = "enable",
            havingValue = "true")
    SendCustomerProjectEmailOnProjectStateChangedEvent sendCustomerClaimProjectEmailOnRevertCancel(
            @Qualifier("customerProjectEmailNotifyProvider")
                    Function<String, CustomerProjectEmailNotify> customerProjectEmailNotifyProvider,
            @Qualifier("revertCancelPredictor") Predicate<ProjectState> revertCancelPredictor,
            @Qualifier("getRequestCancelCreatorEmailByProjectId")
                    Function<String, List<User>> getRequestCancelCreatorEmailByProjectId,
            @Qualifier("claimProjectEmailPredicate") Predicate<String> claimProjectEmailPredicate) {
        var notify = customerProjectEmailNotifyProvider.apply("no-reply-sender");
        return new SendCustomerProjectEmailOnProjectStateChangedEvent(
                ProjectEmailContextProjectCancelled.PROJECT_EMAIL_KEY_PROJECT_REVERT_CANCEL_CLAIM,
                notify,
                revertCancelPredictor,
                claimProjectEmailPredicate,
                getRequestCancelCreatorEmailByProjectId);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.request-cancel",
            name = "enable",
            havingValue = "true")
    SendCustomerProjectEmailOnProjectStateChangedEvent
            sendCustomerUnderwritingProjectEmailOnRevertCancel(
                    @Qualifier("customerProjectEmailNotifyProvider")
                            Function<String, CustomerProjectEmailNotify>
                                    customerProjectEmailNotifyProvider,
                    @Qualifier("revertCancelPredictor")
                            Predicate<ProjectState> revertCancelPredictor,
                    @Qualifier("getRequestCancelCreatorEmailByProjectId")
                            Function<String, List<User>> getRequestCancelCreatorEmailByProjectId,
                    @Qualifier("underwritingProjectEmailPredicate")
                            Predicate<String> underwritingProjectEmailPredicate) {
        var notify = customerProjectEmailNotifyProvider.apply("no-reply-sender");
        return new SendCustomerProjectEmailOnProjectStateChangedEvent(
                ProjectEmailContextProjectCancelled
                        .PROJECT_EMAIL_KEY_PROJECT_REVERT_CANCEL_UNDERWRTING,
                notify,
                revertCancelPredictor,
                underwritingProjectEmailPredicate,
                getRequestCancelCreatorEmailByProjectId);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.request-cancel",
            name = "enable",
            havingValue = "true")
    SendCustomerProjectEmailOnProjectStateChangedEvent
            sendCustomerClaimProjectEmailOnProjectRequestCancel(
                    @Qualifier("customerProjectEmailNotifyProvider")
                            Function<String, CustomerProjectEmailNotify>
                                    customerProjectEmailNotifyProvider,
                    @Qualifier("requestCancelPredictor")
                            Predicate<ProjectState> requestCancelPredictor,
                    @Qualifier("getRequestCancelCreatorEmailByProjectId")
                            Function<String, List<User>> getRequestCancelCreatorEmailByProjectId,
                    @Qualifier("claimProjectEmailPredicate")
                            Predicate<String> claimProjectEmailPredicate) {
        var notify = customerProjectEmailNotifyProvider.apply("no-reply-sender");
        return new SendCustomerProjectEmailOnProjectStateChangedEvent(
                ProjectEmailContextProjectCancelled.PROJECT_EMAIL_KEY_PROJECT_REQUEST_CANCEL_CLAIM,
                notify,
                requestCancelPredictor,
                claimProjectEmailPredicate,
                getRequestCancelCreatorEmailByProjectId);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.request-cancel",
            name = "enable",
            havingValue = "true")
    SendCustomerProjectEmailOnProjectStateChangedEvent
            sendCustomerUnderwritingProjectEmailOnProjectRequestCancel(
                    @Qualifier("customerProjectEmailNotifyProvider")
                            Function<String, CustomerProjectEmailNotify>
                                    customerProjectEmailNotifyProvider,
                    @Qualifier("requestCancelPredictor")
                            Predicate<ProjectState> requestCancelPredictor,
                    @Qualifier("getRequestCancelCreatorEmailByProjectId")
                            Function<String, List<User>> getRequestCancelCreatorEmailByProjectId,
                    @Qualifier("underwritingProjectEmailPredicate")
                            Predicate<String> underwritingProjectEmailPredicate) {
        var notify = customerProjectEmailNotifyProvider.apply("no-reply-sender");
        return new SendCustomerProjectEmailOnProjectStateChangedEvent(
                ProjectEmailContextProjectCancelled
                        .PROJECT_EMAIL_KEY_PROJECT_REQUEST_CANCEL_UNDERWRTING,
                notify,
                requestCancelPredictor,
                underwritingProjectEmailPredicate,
                getRequestCancelCreatorEmailByProjectId);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.request-cancel",
            name = "enable",
            havingValue = "true")
    SendCustomerProjectEmailOnCloseoutReportGenerated
            sendCustomerUnderwritingProjectEmailOnCloseoutReportGenerated(
                    @Qualifier("customerProjectEmailNotifyProvider")
                            Function<String, CustomerProjectEmailNotify>
                                    customerProjectEmailNotifyProvider,
                    @Qualifier("underwritingProjectEmailPredicate")
                            Predicate<String> underwritingProjectEmailPredicate,
                    @Qualifier("getRequestCancelCreatorEmailByProjectId")
                            Function<String, List<User>> getRequestCancelCreatorEmailByProjectId,
                    ReportProvider reportProvider) {
        var notify = customerProjectEmailNotifyProvider.apply("no-reply-sender");
        return new SendCustomerProjectEmailOnCloseoutReportGenerated(
                ProjectEmailContextProjectCancelled.PROJECT_EMAIL_KEY_PROJECT_CANCELLED_UNDERWRTING,
                notify,
                underwritingProjectEmailPredicate,
                reportProvider,
                getRequestCancelCreatorEmailByProjectId);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.customer-project-emails.trigger",
            name = "project_created",
            havingValue = "event:project_created_event")
    SendCustomerProjectEmailOnProjectCreated sendCustomerProjectEmailOnProjectCreatedEvent(
            @Qualifier("customerProjectEmailNotifyProvider")
                    Function<String, CustomerProjectEmailNotify>
                            customerProjectEmailNotifyProvider) {
        var notify = customerProjectEmailNotifyProvider.apply("client-mail-sender");
        return new SendCustomerProjectEmailOnProjectCreated("project_created", notify);
    }

    @Bean
    SendCustomerProjectEmailJobExecutor sendCustomerProjectEmailJobExecutor(
            ProjectEmailRecipientSelector projectEmailRecipientSelector,
            @Qualifier("customerProjectEmailsPropertiesProvider")
                    BiFunction<String, String, CustomerProjectEmailProperties> provider,
            MailSenderProvider mailSenderProvider,
            MailMessageFactory clientMailMessageFactory,
            List<ProjectEmailContext> projectEmailContexts) {
        var map =
                projectEmailContexts.stream()
                        .collect(
                                Collectors.toMap(
                                        ProjectEmailContext::getEmailKey, Function.identity()));
        BinaryOperator<String> mailVariablesProvider =
                (projectEmailKey, projectId) -> {
                    var context = map.get(projectEmailKey);
                    Preconditions.checkArgument(
                            context != null,
                            "Project email context not found: Not context found for key %s.",
                            projectEmailKey);
                    return context.getEmailVariables(projectId);
                };
        return new SendCustomerProjectEmailJobExecutor(
                projectEmailRecipientSelector,
                mailVariablesProvider,
                provider,
                mailSenderProvider,
                clientMailMessageFactory);
    }

    @Configuration
    @Import({
        ProjectEmailContextProjectCreated.class,
    })
    static class CustomerProjectEmailContextConfig {

        @Bean
        ProjectEmailContextProjectCompleted projectEmailContextProjectCompletedUnderwriting(
                JooqProjectIIRepository projectIIRepository,
                @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                AddressProvider addressProvider,
                UserProvider userProvider,
                JooqContractRepository jooqContractRepository,
                ContactManager contactManager,
                @Qualifier("naicsCodeProvider")
                        BiFunction<String, com.bees360.building.Message.BuildingType, String>
                                naicsCodeProvider,
                @Qualifier("propertyTypeNameProvider")
                        Function<com.bees360.building.Message.BuildingType, String>
                                propertyTypeNameProvider) {
            return new ProjectEmailContextProjectCompleted(
                    ProjectEmailContextProjectCompleted.PROJECT_EMAIL_KEY_UNDERWRITING_COMPLETED,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    userProvider,
                    jooqContractRepository,
                    contactManager,
                    naicsCodeProvider,
                    propertyTypeNameProvider);
        }

        @Bean
        ProjectEmailContextProjectCompleted projectEmailContextProjectCompletedClaim(
                JooqProjectIIRepository projectIIRepository,
                @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                AddressProvider addressProvider,
                UserProvider userProvider,
                JooqContractRepository jooqContractRepository,
                ContactManager contactManager,
                @Qualifier("naicsCodeProvider")
                        BiFunction<String, com.bees360.building.Message.BuildingType, String>
                                naicsCodeProvider,
                @Qualifier("propertyTypeNameProvider")
                        Function<com.bees360.building.Message.BuildingType, String>
                                propertyTypeNameProvider) {
            return new ProjectEmailContextProjectCompleted(
                    ProjectEmailContextProjectCompleted.PROJECT_EMAIL_KEY_CLAIM_COMPLETED,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    userProvider,
                    jooqContractRepository,
                    contactManager,
                    naicsCodeProvider,
                    propertyTypeNameProvider);
        }

        @Bean
        ProjectEmailContextProjectCancelled projectEmailContextClaimProjectRequestCancellation(
                JooqProjectIIRepository projectIIRepository,
                @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                AddressProvider addressProvider,
                JooqContractRepository jooqContractRepository,
                ContactManager contactManager,
                @Qualifier("getRequestCancelCreatorEmailByProjectId")
                        Function<String, List<User>> getRequestCancelCreatorEmailByProjectId,
                @Qualifier("naicsCodeProvider")
                        BiFunction<String, com.bees360.building.Message.BuildingType, String>
                                naicsCodeProvider,
                @Qualifier("propertyTypeNameProvider")
                        Function<com.bees360.building.Message.BuildingType, String>
                                propertyTypeNameProvider) {
            return new ProjectEmailContextProjectCancelled(
                    ProjectEmailContextProjectCancelled
                            .PROJECT_EMAIL_KEY_PROJECT_REQUEST_CANCEL_CLAIM,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    jooqContractRepository,
                    contactManager,
                    getRequestCancelCreatorEmailByProjectId,
                    naicsCodeProvider,
                    propertyTypeNameProvider);
        }

        @Bean
        ProjectEmailContextProjectCancelled
                projectEmailContextUnderwritingProjectRequestCancellation(
                        JooqProjectIIRepository projectIIRepository,
                        @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                        AddressProvider addressProvider,
                        JooqContractRepository jooqContractRepository,
                        ContactManager contactManager,
                        @Qualifier("getRequestCancelCreatorEmailByProjectId")
                                Function<String, List<User>>
                                        getRequestCancelCreatorEmailByProjectId,
                        @Qualifier("naicsCodeProvider")
                                BiFunction<
                                                String,
                                                com.bees360.building.Message.BuildingType,
                                                String>
                                        naicsCodeProvider,
                        @Qualifier("propertyTypeNameProvider")
                                Function<com.bees360.building.Message.BuildingType, String>
                                        propertyTypeNameProvider) {
            return new ProjectEmailContextProjectCancelled(
                    ProjectEmailContextProjectCancelled
                            .PROJECT_EMAIL_KEY_PROJECT_REQUEST_CANCEL_UNDERWRTING,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    jooqContractRepository,
                    contactManager,
                    getRequestCancelCreatorEmailByProjectId,
                    naicsCodeProvider,
                    propertyTypeNameProvider);
        }

        @Bean
        ProjectEmailContextProjectCancelled projectEmailContextClaimProjectCancellation(
                JooqProjectIIRepository projectIIRepository,
                @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                AddressProvider addressProvider,
                JooqContractRepository jooqContractRepository,
                ContactManager contactManager,
                @Qualifier("getRequestCancelCreatorEmailByProjectId")
                        Function<String, List<User>> getRequestCancelCreatorEmailByProjectId,
                @Qualifier("naicsCodeProvider")
                        BiFunction<String, com.bees360.building.Message.BuildingType, String>
                                naicsCodeProvider,
                @Qualifier("propertyTypeNameProvider")
                        Function<com.bees360.building.Message.BuildingType, String>
                                propertyTypeNameProvider) {

            return new ProjectEmailContextProjectCancelled(
                    ProjectEmailContextProjectCancelled.PROJECT_EMAIL_KEY_PROJECT_CANCELLED_CLAIM,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    jooqContractRepository,
                    contactManager,
                    getRequestCancelCreatorEmailByProjectId,
                    naicsCodeProvider,
                    propertyTypeNameProvider);
        }

        @Bean
        ProjectEmailContextProjectCancelled projectEmailContextUnderwritingProjectCancellation(
                JooqProjectIIRepository projectIIRepository,
                @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                AddressProvider addressProvider,
                JooqContractRepository jooqContractRepository,
                ContactManager contactManager,
                @Qualifier("getRequestCancelCreatorEmailByProjectId")
                        Function<String, List<User>> getRequestCancelCreatorEmailByProjectId,
                @Qualifier("naicsCodeProvider")
                        BiFunction<String, com.bees360.building.Message.BuildingType, String>
                                naicsCodeProvider,
                @Qualifier("propertyTypeNameProvider")
                        Function<com.bees360.building.Message.BuildingType, String>
                                propertyTypeNameProvider) {
            return new ProjectEmailContextProjectCancelled(
                    ProjectEmailContextProjectCancelled
                            .PROJECT_EMAIL_KEY_PROJECT_CANCELLED_UNDERWRTING,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    jooqContractRepository,
                    contactManager,
                    getRequestCancelCreatorEmailByProjectId,
                    naicsCodeProvider,
                    propertyTypeNameProvider);
        }

        @Bean
        ProjectEmailContextProjectCancelled projectEmailContextClaimProjectRevertCancellation(
                JooqProjectIIRepository projectIIRepository,
                @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                AddressProvider addressProvider,
                JooqContractRepository jooqContractRepository,
                ContactManager contactManager,
                @Qualifier("getRequestCancelCreatorEmailByProjectId")
                        Function<String, List<User>> getRequestCancelCreatorEmailByProjectId,
                @Qualifier("naicsCodeProvider")
                        BiFunction<String, com.bees360.building.Message.BuildingType, String>
                                naicsCodeProvider,
                @Qualifier("propertyTypeNameProvider")
                        Function<com.bees360.building.Message.BuildingType, String>
                                propertyTypeNameProvider) {
            return new ProjectEmailContextProjectCancelled(
                    ProjectEmailContextProjectCancelled
                            .PROJECT_EMAIL_KEY_PROJECT_REVERT_CANCEL_CLAIM,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    jooqContractRepository,
                    contactManager,
                    getRequestCancelCreatorEmailByProjectId,
                    naicsCodeProvider,
                    propertyTypeNameProvider);
        }

        @Bean
        ProjectEmailContextProjectCancelled
                projectEmailContextUnderwritingProjectRevertCancellation(
                        JooqProjectIIRepository projectIIRepository,
                        @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                        AddressProvider addressProvider,
                        JooqContractRepository jooqContractRepository,
                        ContactManager contactManager,
                        @Qualifier("getRequestCancelCreatorEmailByProjectId")
                                Function<String, List<User>>
                                        getRequestCancelCreatorEmailByProjectId,
                        @Qualifier("naicsCodeProvider")
                                BiFunction<
                                                String,
                                                com.bees360.building.Message.BuildingType,
                                                String>
                                        naicsCodeProvider,
                        @Qualifier("propertyTypeNameProvider")
                                Function<com.bees360.building.Message.BuildingType, String>
                                        propertyTypeNameProvider) {
            return new ProjectEmailContextProjectCancelled(
                    ProjectEmailContextProjectCancelled
                            .PROJECT_EMAIL_KEY_PROJECT_REVERT_CANCEL_UNDERWRTING,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    jooqContractRepository,
                    contactManager,
                    getRequestCancelCreatorEmailByProjectId,
                    naicsCodeProvider,
                    propertyTypeNameProvider);
        }
    }

    @Configuration
    static class SendCustomerProjectEmailOnQuestionnaireRenovationSubmittedConfig {

        @Bean
        @ConditionalOnProperty(
                prefix = "project.app.customer-project-emails.trigger",
                name = "project_renovation_questionnaire",
                havingValue = "event:renovation_questionnaire_submitted")
        SendCustomerProjectEmailOnQuestionnaireRenovationSubmitted
                sendCustomerProjectEmailOnQuestionnaireRenovationPilotSubmitted(
                        @Qualifier("customerProjectEmailNotifyProvider")
                                Function<String, CustomerProjectEmailNotify> provider) {
            var notify = provider.apply("no-reply-sender");
            var submitterToEmailKey =
                    Map.of(
                            "homeowner", PROJECT_EMAIL_KEY_QUESTIONNAIRE_HOMEOWNER,
                            "pilot", PROJECT_EMAIL_KEY_QUESTIONNAIRE_PILOT);
            return new SendCustomerProjectEmailOnQuestionnaireRenovationSubmitted(
                    submitterToEmailKey, notify);
        }

        @Bean
        ProjectEmailContextProjectQuestionnaireRenovated
                projectEmailContextProjectQuestionnaireRenovatedHomeowner(
                        JooqProjectIIRepository projectIIRepository,
                        @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                        AddressProvider addressProvider,
                        JooqContractRepository jooqContractRepository) {
            return new ProjectEmailContextProjectQuestionnaireRenovated(
                    PROJECT_EMAIL_KEY_QUESTIONNAIRE_HOMEOWNER,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    jooqContractRepository);
        }

        @Bean
        ProjectEmailContextProjectQuestionnaireRenovated
                projectEmailContextProjectQuestionnaireRenovatedPilot(
                        JooqProjectIIRepository projectIIRepository,
                        @Qualifier("jooqFullPolicyManager") PolicyManager policyManager,
                        AddressProvider addressProvider,
                        JooqContractRepository jooqContractRepository) {
            return new ProjectEmailContextProjectQuestionnaireRenovated(
                    PROJECT_EMAIL_KEY_QUESTIONNAIRE_PILOT,
                    projectIIRepository,
                    policyManager,
                    addressProvider,
                    jooqContractRepository);
        }
    }
}
