package com.bees360.project;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.config.GrpcActivityClientConfig;
import com.bees360.activity.config.GrpcCommentClientConfig;
import com.bees360.address.AddressManager;
import com.bees360.address.config.GrpcAddressAirspaceManagerConfig;
import com.bees360.address.config.GrpcAddressFlyZoneManagerConfig;
import com.bees360.address.config.GrpcAddressManagerConfig;
import com.bees360.apolloconfig.config.ApolloClientConfig;
import com.bees360.attachment.AttachmentManager;
import com.bees360.attachment.FillResourceAttachmentManager;
import com.bees360.attachment.GrpcAttachmentService;
import com.bees360.attachment.config.JooqAttachmentRepositoryConfig;
import com.bees360.auth.config.JwtResourceServerConfig;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.building.GrpcBuildingService;
import com.bees360.building.config.JooqBuildingRepositoryConfig;
import com.bees360.bundle.config.BundleActivityConfig;
import com.bees360.bundle.config.BundleBeespilotConfig;
import com.bees360.bundle.config.BundleDeliveryConfig;
import com.bees360.bundle.config.BundleEmailConfig;
import com.bees360.bundle.config.BundleServiceConfig;
import com.bees360.bundle.config.BundleStateConfig;
import com.bees360.config.ActuatorSecurityConfig;
import com.bees360.contact.config.ContactRecordConfig;
import com.bees360.contract.ContractManager;
import com.bees360.contract.ContractServiceManager;
import com.bees360.contract.GrpcContractService;
import com.bees360.contract.config.JooqContractRepositoryConfig;
import com.bees360.contract.util.FillLogoResourceUrlContractManager;
import com.bees360.customer.GrpcCustomerService;
import com.bees360.customer.config.JooqCustomerPolicyTypeManagerConfig;
import com.bees360.customer.config.JooqCustomerRepositoryConfig;
import com.bees360.customer.config.JooqDivisionRepositoryConfig;
import com.bees360.event.CreateJobToCreateProjectOnProjectIntegrationCreatedEvent;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.http.config.CorsConfig;
import com.bees360.image.config.GrpcImageClientConfig;
import com.bees360.image.config.GrpcImageGroupClientConfig;
import com.bees360.image.config.GrpcImageTagClientConfig;
import com.bees360.image.config.GrpcImageTagDictClientConfig;
import com.bees360.integration.IntegrationSummaryConfig;
import com.bees360.job.CompressPdfByTronJob;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.PdfCompressJob;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.om.GrpcOperationsManagerService;
import com.bees360.om.config.JooqOperationsManagerRepositoryConfig;
import com.bees360.om.config.OperationsManagerServiceConfig;
import com.bees360.pilot.config.PilotFeedbackManagerConfig;
import com.bees360.pilot.feedback.GrpcPilotFeedbackService;
import com.bees360.pilot.feedback.config.JooqPilotFeedbackRepositoryConfig;
import com.bees360.pipeline.GrpcAssignRuleService;
import com.bees360.pipeline.GrpcAssigneeService;
import com.bees360.pipeline.GrpcPipelineDefService;
import com.bees360.pipeline.GrpcPipelineService;
import com.bees360.pipeline.GrpcProjectPipelineConfigService;
import com.bees360.pipeline.GrpcTaskAssignRecordService;
import com.bees360.pipeline.JooqPipelineConfig;
import com.bees360.pipeline.JooqProjectPipelineConfigService;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.config.GrpcPipelineClientConfig;
import com.bees360.pipeline.config.GrpcPipelineDefClientConfig;
import com.bees360.pipeline.config.SetPipelineStateOnStageStateChangedConfig;
import com.bees360.pipeline.todo.PipelineTodoRepositoryConfig;
import com.bees360.pipeline.todo.listener.config.PipelineTodoListenerConfig;
import com.bees360.pipeline.util.PipelineResetUpdaterByChannelService;
import com.bees360.policy.FullPolicyManager;
import com.bees360.policy.GrpcPolicyService;
import com.bees360.policy.PolicyManager;
import com.bees360.policy.config.JooqPolicyRepositoryConfig;
import com.bees360.project.airspace.config.JooqProjectAirspaceManagerConfig;
import com.bees360.project.attachment.config.JooqProjectAttachmentRepositoryConfig;
import com.bees360.project.claim.ProjectCatastropheConfig;
import com.bees360.project.config.AddTagOnAIReworkEventConfig;
import com.bees360.project.config.AppGlobalConfigProperties;
import com.bees360.project.config.AssembleProjectSummaryConfig;
import com.bees360.project.config.AutoPauseReopenProjectListenersConfig;
import com.bees360.project.config.AutoProjectTagConfig;
import com.bees360.project.config.AutoReportConfig;
import com.bees360.project.config.AutoTagProjectConfig;
import com.bees360.project.config.BeesPilotBatchConfig;
import com.bees360.project.config.BeespilotImageTagConfig;
import com.bees360.project.config.ChangeProjectStatusConfig;
import com.bees360.project.config.CloseProjectJobConfig;
import com.bees360.project.config.CreateActivityOnProjectStatusEventConfig;
import com.bees360.project.config.CustomerPdfReportSizeConfig;
import com.bees360.project.config.EventConfig;
import com.bees360.project.config.EventPublishConfig;
import com.bees360.project.config.GenerateInvoiceConfig;
import com.bees360.project.config.GenerateInvoiceJobListenersConfig;
import com.bees360.project.config.GrpcProjectClientConfig;
import com.bees360.project.config.GrpcProjectGroupServiceConfig;
import com.bees360.project.config.GrpcProjectIIMangerConfig;
import com.bees360.project.config.GrpcProjectMemberClientConfig;
import com.bees360.project.config.GrpcProjectPolicyManagerConfig;
import com.bees360.project.config.GrpcProjectStateManagerConfig;
import com.bees360.project.config.InferaImageAnnotationConfig;
import com.bees360.project.config.JooqInspectionCodeManagerConfig;
import com.bees360.project.config.MemberTaskOwnerConfig;
import com.bees360.project.config.NotifyImageAutoImportConfig;
import com.bees360.project.config.ParentChildProjectStatusConfig;
import com.bees360.project.config.PeregrineLambdaListenerConfig;
import com.bees360.project.config.PhotoSheetJsonDataProviderConfig;
import com.bees360.project.config.PilotImageScoreManagerConfig;
import com.bees360.project.config.PipelineAssignmentConfig;
import com.bees360.project.config.PipelineListenersConfig;
import com.bees360.project.config.PipelineStatusCheckConfig;
import com.bees360.project.config.PipelineTaskTransitionsConfig;
import com.bees360.project.config.ProjectAddressAirspaceConfig;
import com.bees360.project.config.ProjectAirspaceConfig;
import com.bees360.project.config.ProjectBeespilotConfig;
import com.bees360.project.config.ProjectBundleConfig;
import com.bees360.project.config.ProjectCloseoutConfig;
import com.bees360.project.config.ProjectCreationConfig;
import com.bees360.project.config.ProjectHoverConfig;
import com.bees360.project.config.ProjectImageAddressListenerConfig;
import com.bees360.project.config.ProjectImageConfig;
import com.bees360.project.config.ProjectInvoiceGeneratorConfig;
import com.bees360.project.config.ProjectInvoiceManagerConfig;
import com.bees360.project.config.ProjectListenersConfig;
import com.bees360.project.config.ProjectMemberConfig;
import com.bees360.project.config.ProjectOptionsConfig;
import com.bees360.project.config.ProjectPipelineRoleConfig;
import com.bees360.project.config.ProjectPipelineTaskConfig;
import com.bees360.project.config.ProjectPrimaryContactConfig;
import com.bees360.project.config.ProjectReportAutoApproveConfig;
import com.bees360.project.config.ProjectReportAutoApproveOnTaskChangedConfig;
import com.bees360.project.config.ProjectRepositoryConfig;
import com.bees360.project.config.ProjectRequestCancelConfig;
import com.bees360.project.config.ProjectReturnedOnReportApprovedConfig;
import com.bees360.project.config.ProjectSecureResourcePoolConfig;
import com.bees360.project.config.ProjectServiceUpgradeConfig;
import com.bees360.project.config.ProjectStateConfig;
import com.bees360.project.config.ProjectStateTriggerConfig;
import com.bees360.project.config.ProjectStatisticConfig;
import com.bees360.project.config.ScheduleJobOnPipelineReadyConfig;
import com.bees360.project.config.SendPilotFeedbackJobExecutorConfig;
import com.bees360.project.config.SetNumberOfInteriorRoomsConfig;
import com.bees360.project.config.SetNumberOfOutBuildingsConfig;
import com.bees360.project.config.SetPipelineTaskOnCreateMagicPlanConfig;
import com.bees360.project.config.SetPipelineTaskOnLongDistancePilotsOnlyConfig;
import com.bees360.project.config.SetPipelineTaskStatusOnInspectionScheduledTimeChangedConfig;
import com.bees360.project.config.SetProjectInvoiceStatusListenerConfig;
import com.bees360.project.config.StatisticProviderConfig;
import com.bees360.project.config.SystemUserConfig;
import com.bees360.project.config.ThirdPartyConfig;
import com.bees360.project.config.TwilioServiceConfig;
import com.bees360.project.config.UpdatePropertyOnProjectCreatedConfig;
import com.bees360.project.config.mail.CustomerProjectEmailConfig;
import com.bees360.project.config.mail.DailyDaysOldEmailConfig;
import com.bees360.project.config.mail.DaysPassPolicyDateEmailConfig;
import com.bees360.project.config.mail.MailSenderConfigs;
import com.bees360.project.config.mail.ProjectCompleteNotificationConfig;
import com.bees360.project.config.mail.ScheduledProjectStatisticsMailConfig;
import com.bees360.project.config.mail.SendCommercialProjectCreatedEmailConfig;
import com.bees360.project.config.mail.SendContactUsEmailConfig;
import com.bees360.project.config.mail.SendJoinTheHiveEmailConfig;
import com.bees360.project.creator.ClaimClaimNoAndAddressDuplicationValidator;
import com.bees360.project.creator.UnderwritingInspectionNoAddressDuplicationValidator;
import com.bees360.project.image.GrpcProjectImageProcessService;
import com.bees360.project.information.GrpcProjectInformationService;
import com.bees360.project.information.config.JooqProjectMessageRepositoryConfig;
import com.bees360.project.inspection.GrpcProjectInspectionCodeService;
import com.bees360.project.invoice.GrpcProjectInvoiceManagerService;
import com.bees360.project.invoice.GrpcProjectInvoiceService;
import com.bees360.project.invoice.SetProjectPaidOnProjectInvoiceReceiptChanged;
import com.bees360.project.invoice.SetProjectUnpaidOnProjectInvoiceAdded;
import com.bees360.project.invoice.UpdateProjectInvoiceReceiptOnInvoicePaymentReceived;
import com.bees360.project.listener.CompressPdfReportOnReportGroupAdded;
import com.bees360.project.listener.SendJoinTheHiveEmailListener;
import com.bees360.project.listener.SetPipelineDoneOnImageTagFinish;
import com.bees360.project.listener.SetPipelineDoneOnReportGroupAdded;
import com.bees360.project.listener.SetProjectMemberOnOMChanged;
import com.bees360.project.listener.SetProjectScoreOnScoreUpdated;
import com.bees360.project.listener.SetStageOwnerOnMemberChanged;
import com.bees360.project.listener.SetTaskOwnerOnProjectMemberChanged;
import com.bees360.project.listener.SetTimeLineOnInitialCustomerContacted;
import com.bees360.project.listener.SubmitActivityWhenProjectStateChangedListener;
import com.bees360.project.member.JooqProjectMemberRepository;
import com.bees360.project.member.UserFillProjectMemberManager;
import com.bees360.project.participant.ProjectParticipantProvider;
import com.bees360.project.participant.UserFillProjectParticipantProvider;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.project.report.DefaultProjectReportProcessor;
import com.bees360.project.report.GenerateReportConfig;
import com.bees360.project.report.MergeReportConfig;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.project.report.ProjectReportProcessor;
import com.bees360.project.report.config.ProjectReportJobManagerConfig;
import com.bees360.project.state.GrpcProjectStateChangeReasonService;
import com.bees360.project.state.GrpcStateChangeReasonGroupService;
import com.bees360.project.util.easyexcel.ExcelUploadUtil;
import com.bees360.redis.config.codec.RedisProtoCodecConfig;
import com.bees360.report.ReportGroupManager;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportProcessor;
import com.bees360.report.ReportProvider;
import com.bees360.report.config.GrpcReportGroupManagerConfig;
import com.bees360.report.config.GrpcReportManagerClientConfig;
import com.bees360.report.config.GrpcReportProcessorClientConfig;
import com.bees360.report.job.CompressPdfJobFactory;
import com.bees360.resource.ResourceGetUrlProvider;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.resource.config.GrpcResourceClientConfig;
import com.bees360.scheduled.job.GrpcCronScheduledJobService;
import com.bees360.scheduled.job.config.JooqCronScheduledJobRepositoryConfig;
import com.bees360.todo.GrpcTodoService;
import com.bees360.todo.JooqTodoManager;
import com.bees360.todo.UserFilledTodoManager;
import com.bees360.todo.config.JooqTodoRepositoryConfig;
import com.bees360.todo.config.TodoConfig;
import com.bees360.user.User;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;
import com.bees360.user.config.GrpcGroupManagerConfig;
import com.bees360.user.config.GrpcUserKeyProviderConfig;
import com.bees360.user.config.GrpcUserProviderConfig;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.Duration;
import java.util.Comparator;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;

@Import({
    AssembleProjectSummaryConfig.class,
    SystemUserConfig.class,

    // grpc client
    GrpcProjectClientConfig.class,
    GrpcActivityClientConfig.class,
    GrpcGroupManagerConfig.class,
    GrpcCommentClientConfig.class,
    GrpcAddressManagerConfig.class,
    GrpcAddressAirspaceManagerConfig.class,
    GrpcImageTagClientConfig.class,
    GrpcImageTagDictClientConfig.class,
    GrpcUserProviderConfig.class,
    GrpcImageGroupClientConfig.class,
    GrpcImageClientConfig.class,
    GrpcReportManagerClientConfig.class,
    GrpcReportGroupManagerConfig.class,
    GrpcReportProcessorClientConfig.class,
    GrpcUserKeyProviderConfig.class,
    GrpcAddressFlyZoneManagerConfig.class,
    GrpcPipelineClientConfig.class,
    GrpcPipelineDefClientConfig.class,
    GrpcClientConfig.class,
    GrpcProjectStateManagerConfig.class,

    // grpc server
    GrpcProjectTagService.class,
    GrpcOperationsManagerService.class,
    GrpcProjectPipelineConfigService.class,
    GrpcPipelineService.class,
    GrpcPipelineDefService.class,
    GrpcTodoService.class,
    GrpcProjectIIService.class,
    GrpcPolicyService.class,
    GrpcContractService.class,
    GrpcBuildingService.class,
    GrpcProjectContactService.class,
    GrpcProjectCatastropheService.class,
    GrpcCustomerService.class,
    GrpcProjectInvoiceService.class,
    GrpcProjectParticipantService.class,
    GrpcAssignRuleService.class,
    GrpcAssigneeService.class,
    GrpcPilotFeedbackService.class,
    GrpcProjectOperationTagService.class,
    GrpcProjectInspectionCodeService.class,
    GrpcProjectStateChangeReasonService.class,
    GrpcStateChangeReasonGroupService.class,
    GrpcProjectDaysOldService.class,
    GrpcTaskAssignRecordService.class,
    GrpcProjectInvoiceManagerService.class,
    GrpcExternalIntegrationManagerService.class,
    GrpcProjectPaymentService.class,
    GrpcAttachmentService.class,
    GrpcProjectInformationService.class,
    GrpcCronScheduledJobService.class,
    GrpcProjectAirspaceService.class,
    GrpcProjectGroupServiceConfig.class,
    GrpcProjectImageProcessService.class,
    GrpcProjectProcessStatusService.class,
    GrpcProjectCancellationService.class,
    GrpcProjectMemberClientConfig.class,

    // default implements
    ProjectRepositoryConfig.class,
    JooqInspectionCodeManagerConfig.class,
    JooqOperationsManagerRepositoryConfig.class,
    JooqProjectPipelineConfigService.class,
    JooqPipelineConfig.class,
    SetPipelineStateOnStageStateChangedConfig.class,
    JooqTodoRepositoryConfig.class,
    JooqPolicyRepositoryConfig.class,
    JooqContractRepositoryConfig.class,
    JooqBuildingRepositoryConfig.class,
    JooqCustomerRepositoryConfig.class,
    JooqDivisionRepositoryConfig.class,
    JooqCustomerPolicyTypeManagerConfig.class,
    JooqPilotFeedbackRepositoryConfig.class,
    JooqAttachmentRepositoryConfig.class,
    JooqProjectMessageRepositoryConfig.class,
    JooqCronScheduledJobRepositoryConfig.class,
    JooqProjectAirspaceManagerConfig.class,
    JooqProjectAttachmentRepositoryConfig.class,

    // config
    RedisProtoCodecConfig.class,
    ProjectImageConfig.class,
    AppGlobalConfigProperties.class,
    EventConfig.class,
    ProjectPipelineTaskConfig.class,
    TwilioServiceConfig.class,
    GrpcResourceClientConfig.class,
    ApacheHttpClientConfig.class,
    RabbitEventPublisher.class,
    RabbitJobScheduler.class,
    PipelineAssignmentConfig.class,
    CustomerPdfReportSizeConfig.class,
    ScheduleJobOnPipelineReadyConfig.class,
    PhotoSheetJsonDataProviderConfig.class,
    PipelineTaskTransitionsConfig.class,
    PipelineStatusCheckConfig.class,
    UpdatePropertyOnProjectCreatedConfig.class,
    TodoConfig.class,
    ProjectCreationConfig.class,
    InferaImageAnnotationConfig.class,
    ProjectInvoiceManagerConfig.class,
    ProjectInvoiceGeneratorConfig.class,
    GenerateInvoiceJobListenersConfig.class,
    GenerateInvoiceConfig.class,
    ProjectReturnedOnReportApprovedConfig.class,
    ParentChildProjectStatusConfig.class,
    SetProjectInvoiceStatusListenerConfig.class,
    ProjectReportAutoApproveConfig.class,
    ProjectReportAutoApproveOnTaskChangedConfig.class,
    GenerateReportConfig.class,
    AutoTagProjectConfig.class,
    AutoProjectTagConfig.class,
    ProjectCloseoutConfig.class,
    CloseProjectJobConfig.class,
    ProjectRequestCancelConfig.class,
    ProjectCatastropheConfig.class,
    MergeReportConfig.class,
    ProjectReportJobManagerConfig.class,
    SendCommercialProjectCreatedEmailConfig.class,

    // event
    SetPipelineDoneOnImageTagFinish.class,
    SendJoinTheHiveEmailListener.class,
    UpdateProjectInvoiceReceiptOnInvoicePaymentReceived.class,
    SetProjectPaidOnProjectInvoiceReceiptChanged.class,
    SetProjectUnpaidOnProjectInvoiceAdded.class,
    PipelineTodoListenerConfig.class,
    CompressPdfReportOnReportGroupAdded.class,
    ProjectStateTriggerConfig.class,
    CreateActivityOnProjectStatusEventConfig.class,
    SetProjectScoreOnScoreUpdated.class,
    SetStageOwnerOnMemberChanged.class,
    SetTaskOwnerOnProjectMemberChanged.class,
    MemberTaskOwnerConfig.class,
    ProjectMemberConfig.class,
    EventPublishConfig.class,
    SetPipelineTaskOnLongDistancePilotsOnlyConfig.class,
    SetPipelineTaskOnCreateMagicPlanConfig.class,
    SetPipelineTaskStatusOnInspectionScheduledTimeChangedConfig.class,
    AddTagOnAIReworkEventConfig.class,
    ProjectPrimaryContactConfig.class,
    ProjectAddressAirspaceConfig.class,
    ProjectAirspaceConfig.class,
    AutoPauseReopenProjectListenersConfig.class,
    PeregrineLambdaListenerConfig.class,
    SetNumberOfOutBuildingsConfig.class,
    SetNumberOfInteriorRoomsConfig.class,

    // job
    AutoRegisterJobExecutorConfig.class,
    RabbitJobDispatcher.class,

    // jooq
    JooqConfig.class,
    ExceptionTranslateInterceptor.class,
    PipelineTodoRepositoryConfig.class,
    JooqExternalIntegrationManager.class,
    JooqIntegrationFormManager.class,

    // mail
    MailSenderConfigs.class,
    ScheduledProjectStatisticsMailConfig.class,
    StatisticProviderConfig.class,
    SendJoinTheHiveEmailConfig.class,
    SendContactUsEmailConfig.class,
    ProjectCompleteNotificationConfig.class,
    DailyDaysOldEmailConfig.class,
    DaysPassPolicyDateEmailConfig.class,
    CustomerProjectEmailConfig.class,

    // pilot-feedback
    SendPilotFeedbackJobExecutorConfig.class,
    PilotFeedbackManagerConfig.class,

    // om
    SetProjectMemberOnOMChanged.class,
    OperationsManagerServiceConfig.class,

    // batch
    BeesPilotBatchConfig.class,

    // hover
    ProjectHoverConfig.class,

    // Du
    ProjectBeespilotConfig.class,

    // contact record
    ContactRecordConfig.class,
    ProjectListenersConfig.class,

    // secure resource
    ProjectSecureResourcePoolConfig.class,
    CorsConfig.class,

    // project state
    ProjectStateConfig.class,

    // invoice
    ExcelUploadUtil.class,
    ProjectStatisticConfig.class,

    // pipeline
    PipelineListenersConfig.class,
    ProjectPipelineRoleConfig.class,
    // timeline
    SetTimeLineOnInitialCustomerContacted.class,

    // options
    ProjectOptionsConfig.class,
    // images
    ProjectImageAddressListenerConfig.class,
    NotifyImageAutoImportConfig.class,

    // pilot image
    PilotImageScoreManagerConfig.class,
    IntegrationSummaryConfig.class,
    BeespilotImageTagConfig.class,
    ThirdPartyConfig.class,
    ChangeProjectStatusConfig.class,

    // bundle
    BundleServiceConfig.class,
    GrpcProjectPolicyManagerConfig.class,
    GrpcProjectIIMangerConfig.class,
    ProjectBundleConfig.class,
    BundleActivityConfig.class,
    BundleDeliveryConfig.class,
    BundleEmailConfig.class,
    BundleBeespilotConfig.class,
    BundleStateConfig.class,

    // apolloconfig
    ApolloClientConfig.class,
    AutoReportConfig.class,
    ProjectServiceUpgradeConfig.class,
    JwtResourceServerConfig.class,
    ActuatorSecurityConfig.class,
})
@EnableEncryptableProperties
@EnableConfigurationProperties
@ApplicationAutoConfig
@EnableTransactionManagement
@EnableWebSecurity
@Log4j2
public class ProjectApp {
    public static void main(final String[] args) {
        ExitableSpringApplication.run(ProjectApp.class, args);
    }

    @Bean
    public FullPolicyManager jooqFullPolicyManager(
            PolicyManager jooqPolicyRepository,
            BuildingManager jooqBuildingRepository,
            AddressManager grpcAddressManager) {
        return new FullPolicyManager(
                jooqPolicyRepository, grpcAddressManager, jooqBuildingRepository);
    }

    @Bean
    @ConditionalOnMissingBean(value = ResourceUrlProvider.class)
    public ResourceUrlProvider resourceUrlProvider(ResourcePool resourcePool) {
        return resourcePool.asResourceUrlProvider();
    }

    @Bean
    public ContractManager jooqFillLogoResourceUrlContractManager(
            ContractManager jooqContractRepository, ResourceUrlProvider resourceUrlProvider) {
        return new FillLogoResourceUrlContractManager(jooqContractRepository, resourceUrlProvider);
    }

    @Bean
    public ContractManager grpcContractManager(ContractManager jooqContractRepository) {
        return jooqContractRepository;
    }

    @Bean
    public IntegrationFormManager grpcServiceIntegrationFormManager(
            JooqIntegrationFormManager jooqIntegrationFormManager) {
        return jooqIntegrationFormManager;
    }

    @Bean
    ContractServiceManager grpcContractServiceManager(
            ContractServiceManager jooqContractServiceRepository) {
        return jooqContractServiceRepository;
    }

    @Bean
    public AttachmentManager fillResourceAttachmentManager(
            ResourcePool resourcePool,
            ResourceGetUrlProvider resourceGetUrlProvider,
            AttachmentManager jooqAttachmentManager) {
        return new FillResourceAttachmentManager(
                jooqAttachmentManager, resourcePool, resourceGetUrlProvider);
    }

    @Bean("grpcTodoManager")
    public UserFilledTodoManager userFilledTodoManager(
            JooqTodoManager todoManager, UserProvider userProvider) {
        return new UserFilledTodoManager(todoManager, userProvider);
    }

    @Bean
    @ConditionalOnMissingBean(name = "projectReportManager")
    public ProjectReportManager projectReportManager(
            ReportGroupManager reportGroupManager, ReportManager reportManager) {
        return new DefaultProjectReportManager(reportGroupManager, reportManager);
    }

    @Bean
    public ProjectReportProcessor projectReportProcessor(ReportProcessor reportProcessor) {
        return new DefaultProjectReportProcessor(reportProcessor);
    }

    @Bean(name = {"memberManager", "projectMemberManager", "userFillProjectMemberManager"})
    public UserFillProjectMemberManager userFillProjectMemberManager(
            DSLContext dslContext, UserProvider userProvider, UserKeyProvider userKeyProvider) {
        return new UserFillProjectMemberManager(dslContext, userProvider, userKeyProvider);
    }

    @Bean
    public MemberFillProjectIIRepository memberFillProjectIIRepository(
            JooqProjectMemberRepository memberManager,
            JooqProjectIIRepository jooqProjectIIRepository) {
        return new MemberFillProjectIIRepository(jooqProjectIIRepository, memberManager);
    }

    @Bean
    public PolicyFillProjectIIManager policyFillProjectIIManager(
            @Qualifier(value = "memberFillProjectIIRepository")
                    ProjectIIManager memberFillProjectIIRepository,
            FullPolicyManager jooqFullPolicyManager) {
        return new PolicyFillProjectIIManager(memberFillProjectIIRepository, jooqFullPolicyManager);
    }

    @Bean
    public ContractFillProjectIIManager contractAndPolicyFillProjectIIManager(
            @Qualifier(value = "policyFillProjectIIManager")
                    ProjectIIManager policyFillProjectIIManager,
            ContractManager jooqContractRepository) {
        return new ContractFillProjectIIManager(policyFillProjectIIManager, jooqContractRepository);
    }

    @Bean(name = {"grpcProjectManager"})
    @Primary
    @ConditionalOnProperty(
            prefix = "bees360.feature.web-forwarding",
            name = "enabled",
            havingValue = "false",
            matchIfMissing = true)
    public ProjectIIManager checkDuplicationProjectManager(
            DSLContext dslContext,
            @Qualifier(value = "contractAndPolicyFillProjectIIManager")
                    ProjectIIManager contractAndPolicyFillProjectIIManager) {
        var claimDuplicationValidator = new ClaimClaimNoAndAddressDuplicationValidator(dslContext);
        var underwritingDuplicationValidator =
                new UnderwritingInspectionNoAddressDuplicationValidator(dslContext);

        var checkDuplicationProjectCreator =
                new CheckDuplicationProjectCreator(
                        contractAndPolicyFillProjectIIManager,
                        claimDuplicationValidator,
                        underwritingDuplicationValidator);
        return new CreationForwardingProjectManager(
                contractAndPolicyFillProjectIIManager, checkDuplicationProjectCreator);
    }

    @Bean(name = {"grpcProjectManager"})
    @Primary
    @ConditionalOnProperty(
            prefix = "bees360.feature.web-forwarding",
            name = "enabled",
            havingValue = "true")
    public ProjectIIManager webForwardingCheckDuplicationProjectManager(
            DSLContext dslContext,
            @Qualifier(value = "contractAndPolicyFillProjectIIManager")
                    ProjectIIManager contractAndPolicyFillProjectIIManager,
            GrpcProjectIIManager grpcProjectIIManager) {
        var claimDuplicationValidator = new ClaimClaimNoAndAddressDuplicationValidator(dslContext);
        var underwritingDuplicationValidator =
                new UnderwritingInspectionNoAddressDuplicationValidator(dslContext);

        var checkDuplicationProjectCreator =
                new CheckDuplicationProjectCreator(
                        contractAndPolicyFillProjectIIManager,
                        claimDuplicationValidator,
                        underwritingDuplicationValidator);
        var manager =
                new CreationForwardingProjectManager(
                        contractAndPolicyFillProjectIIManager, checkDuplicationProjectCreator);
        return new WebForwardingProjectIIManager(grpcProjectIIManager, manager);
    }

    @Bean
    @Primary
    public UserFillProjectParticipantProvider userFillProjectParticipantManager(
            UserProvider userProvider, ProjectParticipantProvider projectParticipantProvider) {
        return new UserFillProjectParticipantProvider(userProvider, projectParticipantProvider);
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "bees360.feature.open-close",
            name = {"enabled"},
            havingValue = "true")
    SubmitActivityWhenProjectStateChangedListener submitActivityWhenProjectStateChangedListener(
            ActivityManager activityManager) {
        final String defaultUser = "10000";
        return new SubmitActivityWhenProjectStateChangedListener(activityManager, defaultUser);
    }

    @Bean
    SetPipelineDoneOnReportGroupAdded setPipelineDoneOnReportGroupAdded(
            PipelineTaskTransitionsConfig pipelineTaskTransitionsConfig,
            PipelineService pipelineService,
            ReportProvider reportProvider) {
        return new SetPipelineDoneOnReportGroupAdded(
                pipelineTaskTransitionsConfig.getReportGenerateTrigger(),
                pipelineService,
                reportProvider);
    }

    @Bean
    CompressPdfJobFactory compressPdfJobFactory(
            @Value("${report.compress.util.key}") String compressUtilKey) {
        log.info("report compress job util is:{}", compressUtilKey);

        var pdfTronSwitchOn = StringUtils.equals(compressUtilKey, "pdfTron");
        var ghostScriptSwitchOn = StringUtils.equals(compressUtilKey, "ghostScript");

        Preconditions.checkArgument(
                pdfTronSwitchOn || ghostScriptSwitchOn, "report compress util should be provide");

        return (jobId, maxOutputSize, inputFile, outputFile) -> {
            Job job;
            if (pdfTronSwitchOn) {
                job = CompressPdfByTronJob.getInstance(jobId, maxOutputSize, outputFile, inputFile);
            } else {
                job = PdfCompressJob.getInstance(jobId, maxOutputSize, outputFile, inputFile);
            }
            return RetryableJob.of(job, 6, Duration.ofSeconds(10), 1.5f);
        };
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "bees360.feature.integration.create-project",
            name = {"disable"},
            havingValue = "false",
            matchIfMissing = true)
    CreateJobToCreateProjectOnProjectIntegrationCreatedEvent
            createProjectOnProjectIntegrationCreatedEvent(JobScheduler jobScheduler) {
        return new CreateJobToCreateProjectOnProjectIntegrationCreatedEvent(jobScheduler);
    }

    @Bean
    Supplier<String> robotUserIdSupplier(
            UserProvider userProvider,
            @Value("${project.app.user.robot-email}") String robotUserEmail) {

        Supplier<String> robotUserIdSupplier =
                () -> {
                    var robotUser =
                            Iterables.toList(userProvider.findUserByEmail(robotUserEmail)).stream()
                                    .min(Comparator.comparing(User::getId))
                                    .orElse(null);
                    if (robotUser == null) {
                        var message =
                                String.format(
                                        "Robot User with email %s not found.", robotUserEmail);
                        throw new IllegalStateException(message);
                    }
                    return robotUser.getId();
                };
        log.info(
                "Created {}(userProvider={},robotUserEmail={})",
                robotUserIdSupplier,
                userProvider,
                robotUserEmail);
        return robotUserIdSupplier;
    }

    @Bean({"pipelineService"})
    @ConditionalOnProperty(
            prefix = "grpc.client.pipelineService",
            name = "disabled",
            havingValue = "false")
    PipelineService grpcClientAsPipelineService(
            PipelineService grpcPipelineClient,
            @Qualifier(value = "channelUpdaterProvider")
                    Function<Message.TaskAssignChannelEnum, String> channelUpdaterProvider) {
        return new PipelineResetUpdaterByChannelService(grpcPipelineClient, channelUpdaterProvider);
    }

    @Bean(name = {"channelUpdaterProvider"})
    Function<Message.TaskAssignChannelEnum, String> channelUpdaterProvider(
            @Qualifier(value = "systemUserSupplier") Supplier<String> systemUserSupplier) {
        return channel -> {
            if (Objects.equals(channel, Message.TaskAssignChannelEnum.AUTO_ASSIGN_MANUAL)) {
                return systemUserSupplier.get();
            }
            return null;
        };
    }

    @Bean({"pipelineService"})
    @ConditionalOnProperty(
            prefix = "grpc.client.pipelineService",
            name = "disabled",
            havingValue = "true",
            matchIfMissing = true)
    PipelineService jooqPipelineServiceAsPipelineService(
            PipelineService jooqPipelineService,
            @Qualifier(value = "channelUpdaterProvider")
                    Function<Message.TaskAssignChannelEnum, String> channelUpdaterProvider) {
        return new PipelineResetUpdaterByChannelService(
                jooqPipelineService, channelUpdaterProvider);
    }

    @Bean({"grpcPipelineService"})
    @ConditionalOnMissingBean(name = "grpcPipelineService")
    PipelineService pipelineServiceAsGrpcPipelineService(PipelineService pipelineService) {
        return pipelineService;
    }
}
