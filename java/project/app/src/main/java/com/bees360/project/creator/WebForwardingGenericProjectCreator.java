package com.bees360.project.creator;

import com.bees360.attachment.BatchAttachment;
import com.bees360.project.GenericProjectCreator;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectII;

import lombok.extern.log4j.Log4j2;

import org.springframework.transaction.annotation.Transactional;

@Log4j2
public class WebForwardingGenericProjectCreator extends ForwardingGenericProjectCreator {

    private final GenericProjectCreator webGenericProjectCreator;

    public WebForwardingGenericProjectCreator(
            GenericProjectCreator genericProjectCreator,
            GenericProjectCreator webGenericProjectCreator) {
        super(genericProjectCreator);
        this.webGenericProjectCreator = webGenericProjectCreator;
    }

    @Transactional
    @Override
    public ProjectII create(
            ProjectCreationRequest project,
            boolean allowDuplication,
            String creationChannel,
            BatchAttachment attachment) {
        var projectRequest = new BasicProjectCreationRequest(project);
        var result = delegate().create(project, allowDuplication, creationChannel, attachment);

        projectRequest.setProjectId(result.getId());
        webGenericProjectCreator.create(projectRequest, allowDuplication, creationChannel);
        log.debug("Project Creation: finish create project {}.", result.getId());
        return result;
    }
}
