package com.bees360.project.listener;

import com.bees360.activity.Activities;
import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Comment;
import com.bees360.activity.Message;
import com.bees360.event.registry.ProjectStatusHistoryInserted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.ProjectStatusEnum;
import com.bees360.util.DateTimes;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

@Log4j2
public class CreateActivityOnProjectStatusEvent
        extends AbstractNamedEventListener<ProjectStatusHistoryInserted> {

    private final ActivityManager activityManager;

    private static final String ACTIVITY_SOURCE_WEB = "WEB";

    public CreateActivityOnProjectStatusEvent(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={}).", this, this.activityManager);
    }

    @Override
    public void handle(ProjectStatusHistoryInserted event) throws IOException {
        var projectId = Long.parseLong(event.getProjectId());
        var creator = event.getUpdatedBy();
        var status = event.getStatus();
        var comment = event.getComment();
        var updatedAt = event.getUpdatedAt();

        if (com.bees360.project.Message.ProjectStatus.PROJECT_CREATED.getNumber() == status) {
            /** Project_Created status activity added by {@link AddActivityOnProjectCreatedEvent} */
            return;
        }

        var activity =
                Activities.changeProjectField(
                        projectId,
                        creator,
                        Message.ActivityMessage.Field.newBuilder()
                                .setName(Message.ActivityMessage.FieldName.STATUS.name())
                                .setValue(ProjectStatusEnum.valueOf(status).getDisplay())
                                .build(),
                        updatedAt);

        var builder = activity.toMessage().toBuilder();
        if (StringUtils.isNotBlank(comment)) {
            var commentMsg =
                    Comment.from(
                                    projectId,
                                    creator,
                                    comment,
                                    DateTimes.toTimestamp(updatedAt),
                                    DateTimes.toTimestamp(updatedAt))
                            .toMessage();
            builder.setComment(commentMsg);
        }

        activity = Activity.of(builder.setSource(ACTIVITY_SOURCE_WEB).build());
        activityManager.submitActivity(activity);
        log.info("Successfully created activity for project status changed event {}", event);
    }
}
