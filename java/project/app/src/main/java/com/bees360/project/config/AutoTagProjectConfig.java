package com.bees360.project.config;

import com.bees360.event.AutoTagOrderRcReportOnProjectCreatedEvent;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.project.JooqProjectIIRepository;
import com.bees360.project.tag.Message.ProjectTagType;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagManager;
import com.bees360.rabbit.config.RabbitApiConfig;

import jakarta.annotation.PostConstruct;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Supplier;

@Log4j2
@Import({
    // getBees360TagByTitleAndType
    ProjectTagCommonTagConfig.class,
    SystemUserConfig.class,
    RabbitApiConfig.class,
    RabbitEventDispatcher.class,
    AutoRegisterEventListenerConfig.class,
})
@Configuration
@ConditionalOnProperty(prefix = "project.app.auto-tag", name = "enabled", havingValue = "true")
@EnableConfigurationProperties
public class AutoTagProjectConfig {

    @Bean
    @ConditionalOnProperty(
            prefix = "project.app.auto-tag.order-rc-report",
            name = "trigger-by",
            havingValue = "event:project-created")
    AutoTagOrderRcReportOnProjectCreatedEvent autoTagOrderRcReportOnProjectCreatedEvent(
            ProjectTagManager projectTagManager,
            JooqProjectIIRepository jooqProjectIIRepository,
            @Value("${project.app.auto-tag.order-rc-report.condition.supplemental-services:}")
                    Set<String> expectedSupplementalServices,
            @Qualifier("systemUserSupplier") Supplier<String> systemUserSupplier,
            @Qualifier("getBees360TagByTitleAndType")
                    BiFunction<String, ProjectTagType, ProjectTag> getBees360TagByTitleAndType) {
        return new AutoTagOrderRcReportOnProjectCreatedEvent(
                jooqProjectIIRepository,
                projectTagManager,
                getBees360TagByTitleAndType,
                expectedSupplementalServices,
                systemUserSupplier);
    }

    @PostConstruct
    void echo() {
        log.info("Created {}", this);
    }
}
