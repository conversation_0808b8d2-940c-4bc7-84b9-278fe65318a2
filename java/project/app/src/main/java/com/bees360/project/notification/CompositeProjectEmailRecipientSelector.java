package com.bees360.project.notification;

import com.google.common.base.Preconditions;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.util.Map;

@Log4j2
public class CompositeProjectEmailRecipientSelector implements ProjectEmailRecipientSelector {
    private final Map<String, ProjectEmailRecipientSelector> typeSelectors;

    public CompositeProjectEmailRecipientSelector(
            @NonNull Map<String, ProjectEmailRecipientSelector> typeSelectors) {
        this.typeSelectors = typeSelectors;
        log.info("Created{}(typeSelectors={})", this, typeSelectors);
    }

    @Override
    public Iterable<EmailRecipient> select(String projectId, RecipientSelector selector) {
        Preconditions.checkArgument(
                typeSelectors.containsKey(selector.getType()),
                "Selector type not found:Selector type {} not found.",
                selector.getType());
        return typeSelectors.get(selector.getType()).select(projectId, selector);
    }
}
