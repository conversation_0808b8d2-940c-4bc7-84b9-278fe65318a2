package com.bees360.project.listener;

import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.util.PipelineTaskChangedListeners;
import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.ProjectAutoReportJob;
import com.bees360.job.util.EventTriggeredJob;
import com.bees360.pipeline.Message;
import com.bees360.report.ReportTypeEnum;

import lombok.extern.log4j.Log4j2;

import java.time.Duration;
import java.util.List;

@Log4j2
public class GenerateAutoReportOnPipeLineReady extends EventTriggeredJob<PipelineTaskChanged> {

    public static final String EVENT_LISTENER_QUEUE_PREFIX = "generate_auto_report_on/";

    private final String taskKey;

    private final ReportTypeEnum reportType;

    private final List<String> targetFactorKeys;
    private final Type targetImageType;
    private final Integer retryCount;
    private final Duration retryDelay;
    private final Float retryDelayIncreaseFactor;

    public GenerateAutoReportOnPipeLineReady(
            String taskKey,
            ReportTypeEnum reportType,
            List<String> targetFactorKeys,
            Type targetImageType,
            JobScheduler jobScheduler,
            Integer retryCount,
            Duration retryDelay,
            Float retryDelayIncreaseFactor) {
        super(jobScheduler);
        this.taskKey = taskKey;
        this.reportType = reportType;
        this.targetFactorKeys = targetFactorKeys;
        this.targetImageType = targetImageType;
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.retryDelayIncreaseFactor = retryDelayIncreaseFactor;
        log.info(
                "Created {}(routingKey={}, taskKey={},"
                        + " reportType={}, targetFactorKeys={}, targetImageType={},"
                        + " retryCount={}, retryDelay={}, retryDelayIncreaseFactor={}).",
                this,
                this.getRoutingKey(),
                this.taskKey,
                this.reportType,
                this.targetFactorKeys,
                this.targetImageType,
                this.retryCount,
                this.retryDelay,
                this.retryDelayIncreaseFactor);
    }

    @Override
    public String getName() {
        return EVENT_LISTENER_QUEUE_PREFIX + getRoutingKey();
    }

    @Override
    public String getRoutingKey() {
        return PipelineTaskChangedListeners.getEventName(taskKey, Message.PipelineStatus.READY);
    }

    @Override
    public Job convert(PipelineTaskChanged event) {
        log.info("Received event {}.", event);
        var projectId = event.getPipelineId();
        var updatedBy = event.getUpdatedBy();
        var payload =
                new ProjectAutoReportJob(
                        projectId,
                        updatedBy,
                        taskKey,
                        reportType,
                        targetFactorKeys,
                        targetImageType);
        return RetryableJob.of(
                JobPayloads.encode(payload), retryCount, retryDelay, retryDelayIncreaseFactor);
    }
}
