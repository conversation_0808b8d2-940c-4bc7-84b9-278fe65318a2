package com.bees360.project.notification;

import com.bees360.address.AddressProvider;
import com.bees360.building.Message;
import com.bees360.codec.GsonCodec;
import com.bees360.contract.ContractManager;
import com.bees360.customer.Customer;
import com.bees360.policy.PolicyRepository;
import com.bees360.project.ContactManager;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.JooqProjectIIRepository;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.util.ProjectEmailUtil;
import com.bees360.user.UserProvider;
import com.bees360.util.Defaults;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.gson.Gson;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

@Log4j2
public class ProjectEmailContextProjectCompleted implements ProjectEmailContext {

    public static final String PROJECT_EMAIL_KEY_CLAIM_COMPLETED = "project_completed_claim";
    public static final String PROJECT_EMAIL_KEY_UNDERWRITING_COMPLETED =
            "project_completed_underwriting";

    private final String projectEmailKey;
    private final Gson gson = GsonCodec.DEFAULT_GSON_BUILDER.create();
    private final ProjectIIRepository projectIIRepository;
    private final PolicyRepository policyManager;
    private final AddressProvider addressProvider;
    private final UserProvider userProvider;

    private final ContractManager contractManager;

    private final ContactManager contactManager;

    private final BiFunction<String, Message.BuildingType, String> naicsCodeProvider;

    private final Function<Message.BuildingType, String> propertyTypeNameProvider;

    public ProjectEmailContextProjectCompleted(
            @NonNull String projectEmailKey,
            @NonNull JooqProjectIIRepository projectIIRepository,
            @NonNull PolicyRepository policyManager,
            @NonNull AddressProvider addressProvider,
            @NonNull UserProvider userProvider,
            @NonNull ContractManager contractManager,
            ContactManager contactManager,
            BiFunction<String, Message.BuildingType, String> naicsCodeProvider,
            Function<Message.BuildingType, String> propertyTypeNameProvider) {
        this.projectEmailKey = projectEmailKey;
        this.projectIIRepository = projectIIRepository;
        this.policyManager = policyManager;
        this.addressProvider = addressProvider;
        this.userProvider = userProvider;
        this.contractManager = contractManager;
        this.contactManager = contactManager;
        this.naicsCodeProvider = naicsCodeProvider;
        this.propertyTypeNameProvider = propertyTypeNameProvider;
        log.info(
                "Created"
                    + " {}(projectEmailKey={},projectIIRepository={},policyManager={},addressProvider={},userProvider={},contractManager={},naicsCodeProvider={})",
                this,
                projectEmailKey,
                projectIIRepository,
                policyManager,
                addressProvider,
                userProvider,
                contractManager,
                naicsCodeProvider);
    }

    @Override
    public String getEmailKey() {
        return projectEmailKey;
    }

    @Override
    public String getEmailVariables(String projectId) {
        var project = projectIIRepository.findById(projectId);

        var policyId = project.getPolicy().getId();
        var policy = policyManager.findById(policyId);
        Preconditions.checkArgument(
                policy != null,
                "Policy not found:Policy %s for project %s not found.",
                projectId,
                policyId);

        var address = addressProvider.findById(policy.getAddress().getId());
        var creator = userProvider.getUser(project.getCreateBy().getId());
        var contract = contractManager.findById(project.getContract().getId());

        Map<String, Object> variables = Maps.newHashMap();
        variables.put("projectId", project.getId());
        variables.put("username", creator.getName());
        variables.put("policyNumber", policy.getPolicyNo());
        variables.put("isRenewal", policy.isRenewal());
        variables.put("inspectionNumber", project.getInspectionNo());
        variables.put("address", address.getAddress());

        variables.put("insuredBy", toMap(contract.getInsuredBy()));
        variables.put("processedBy", toMap(contract.getProcessedBy()));
        var policyType = policy.getType();
        var propertyType = policy.getBuilding().getType();
        ProjectEmailUtil.putLOBInfo(
                variables, policyType, propertyType, propertyTypeNameProvider, naicsCodeProvider);
        putInsuredInfo(variables, projectId);
        return gson.toJson(variables);
    }

    private void putInsuredInfo(Map<String, Object> variables, String projectId) {
        var contacts = contactManager.findByProjectId(projectId);

        Iterables.toStream(contacts)
                .filter(c -> StringUtils.equals(ContactRoleEnum.INSURED.getName(), c.getRole()))
                .findFirst()
                .ifPresent(
                        insured -> {
                            variables.put("insuredName", insured.getFullName());
                        });
    }

    private Map<String, Object> toMap(Customer customer) {
        return Map.of("name", Defaults.transformOrDefaultIfNull(customer, Customer::getName, ""));
    }
}
