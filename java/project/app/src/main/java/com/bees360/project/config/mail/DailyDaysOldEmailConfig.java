package com.bees360.project.config.mail;

import com.bees360.activity.Comment;
import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.building.Message;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.job.JobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.job.registry.SendDaysOldEmailJob;
import com.bees360.mail.MailSender;
import com.bees360.mail.util.MailMessageFactory;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectDaysOldProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.config.ProjectContactCommentProviderConfig;
import com.bees360.project.executor.DailyDaysOldEmailExecutor;
import com.bees360.project.listener.DailyDaysOldEmailListener;
import com.bees360.project.util.ProjectEmailUtil;
import com.bees360.user.UserProvider;
import com.google.common.base.Preconditions;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Log4j2
@Import({
    ProjectContactCommentProviderConfig.class,
    MailSenderConfigs.class,
    AutoRegisterJobExecutorConfig.class,
    AutoRegisterEventListenerConfig.class,
    NaicsCodeConfig.class,
})
@Configuration
@ConditionalOnProperty(prefix = "mail.daily-days-old", name = "enabled", havingValue = "true")
@EnableConfigurationProperties
public class DailyDaysOldEmailConfig {

    @Data
    @Configuration
    @RefreshableConfigurationProperties(prefix = "mail.daily-days-old.company-property")
    static class DailyDaysOldEmailProperties {
        private List<DailyDaysOldEmailProperty> properties;

        @Data
        static class DailyDaysOldEmailProperty {
            private String companyKey;
            private List<String> recipients = new ArrayList<>();
            private List<String> cc = new ArrayList<>();
            private List<Integer> daysOld = new ArrayList<>();
        }
    }

    @Bean
    public DailyDaysOldEmailListener dailyDaysOldEmailListener(
            JobScheduler jobScheduler, DailyDaysOldEmailProperties properties) {
        Function<String, SendDaysOldEmailJob> daysOldEmailJobProvider =
                key -> {
                    var property =
                            properties.getProperties().stream()
                                    .filter(p -> p.getCompanyKey().equals(key))
                                    .findFirst()
                                    .orElse(null);
                    if (property == null) {
                        log.warn("Cannot find days old email configuration for company {}.", key);
                        return null;
                    }
                    return new SendDaysOldEmailJob(
                            property.getCompanyKey(),
                            property.getRecipients(),
                            property.getCc(),
                            property.getDaysOld());
                };

        Supplier<List<String>> companyListProvider =
                () ->
                        properties.getProperties().stream()
                                .map(
                                        DailyDaysOldEmailProperties.DailyDaysOldEmailProperty
                                                ::getCompanyKey)
                                .collect(Collectors.toList());
        return new DailyDaysOldEmailListener(
                jobScheduler, companyListProvider, daysOldEmailJobProvider);
    }

    @Bean
    public DailyDaysOldEmailExecutor dailyDaysOldEmailExecutor(
            MailMessageFactory mailMessageFactory,
            MailSender backendSender,
            @Qualifier("grpcProjectManager") ProjectIIManager projectIIManager,
            ProjectDaysOldProvider projectDaysOldProvider,
            ContactManager contactManager,
            UserProvider userProvider,
            @Value("${mail.daily-days-old.project-context}") String projectContext,
            @Qualifier("projectContactCommentProvider")
                    Function<String, List<Comment>> projectContactCommentProvider,
            @Qualifier("naicsCodeProvider")
                    BiFunction<String, Message.BuildingType, String> naicsCodeProvider,
            @Qualifier("propertyTypeNameProvider")
                    Function<com.bees360.building.Message.BuildingType, String>
                            propertyTypeNameProvider) {
        Preconditions.checkArgument(
                StringUtils.isNoneEmpty(projectContext),
                "Project context for daily days old email can not be empty.");
        var projectEmailUtil =
                new ProjectEmailUtil(contactManager, projectContactCommentProvider, userProvider);
        return new DailyDaysOldEmailExecutor(
                mailMessageFactory,
                backendSender,
                projectIIManager,
                projectDaysOldProvider,
                () -> projectContext,
                projectEmailUtil,
                naicsCodeProvider,
                propertyTypeNameProvider);
    }
}
