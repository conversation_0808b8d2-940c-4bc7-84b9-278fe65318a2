package com.bees360.project.notification;

import com.bees360.building.Message;
import com.bees360.policy.Policy;
import com.bees360.project.Building;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.member.Member;
import com.bees360.project.member.MemberManager;
import com.bees360.user.User;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Log4j2
public class PolicyTypeProjectEmailRecipientSelector implements ProjectEmailRecipientSelector {
    public static final String SELECTOR_TYPE = "policyType";

    private final ProjectIIRepository projectIIRepository;
    private final MemberManager memberManager;
    private final ContactManager contactManager;

    public PolicyTypeProjectEmailRecipientSelector(
            ProjectIIRepository projectIIRepository,
            MemberManager memberManager,
            ContactManager contactManager) {
        this.projectIIRepository = projectIIRepository;
        this.memberManager = memberManager;
        this.contactManager = contactManager;
        log.info(
                "Created {}(projectIIRepository={}, memberManager={}, contactManager={}).",
                this,
                this.projectIIRepository,
                this.memberManager,
                this.contactManager);
    }

    @Override
    public Iterable<EmailRecipient> select(String projectId, RecipientSelector selector) {
        log.debug(
                "Try select policy type recipient for project {} with selector properties {}.",
                projectId,
                selector);
        if (!StringUtils.equals(SELECTOR_TYPE, selector.getType())) {
            log.debug(
                    "Selector type not matched {}, return empty recipient for project {}.",
                    selector.getType(),
                    projectId);
            return List.of();
        }

        var policyType = selector.getPolicyType();
        var propertyType = selector.getPropertyType();
        // if type is empty, match all
        var policyTypeMatched = StringUtils.isBlank(policyType);
        var propertyTypeMatched = StringUtils.isBlank(propertyType);

        // if both type and property type are empty, return empty
        if (policyTypeMatched && propertyTypeMatched) {
            log.debug(
                    "Both type and property type are empty, return empty recipient for project {}.",
                    projectId);
            return List.of();
        }

        var project = projectIIRepository.findById(projectId);
        var policy = project.getPolicy();
        var projectPolicyType = Optional.ofNullable(policy).map(Policy::getType).orElse(null);
        var projectPropertyType =
                Optional.ofNullable(policy)
                        .map(Policy::getBuilding)
                        .map(Building::getType)
                        .orElse(null);

        if (!policyTypeMatched) {
            policyTypeMatched = StringUtils.equalsIgnoreCase(projectPolicyType, policyType);
        }

        if (!propertyTypeMatched) {
            var buildingType =
                    StringUtils.isNumeric(propertyType)
                            ? Message.BuildingType.forNumber(Integer.parseInt(propertyType))
                            : Message.BuildingType.valueOf(propertyType);
            propertyTypeMatched = buildingType != null && buildingType.equals(projectPropertyType);
        }

        log.debug(
                "Policy type matched: {}, property type matched: {} for project {}.",
                policyTypeMatched,
                propertyTypeMatched,
                projectId);

        if (!policyTypeMatched || !propertyTypeMatched) {
            log.debug(
                    "Both type and property type are not matched, return empty recipient for"
                            + " project {}.",
                    projectId);
            return List.of();
        }

        var recipients = new ArrayList<String>();
        CollectionUtils.addAll(recipients, getEmails(selector));
        CollectionUtils.addAll(recipients, getMemberRecipientEmails(projectId, selector));
        CollectionUtils.addAll(recipients, getContactRecipientEmails(projectId, selector));
        return recipients.stream()
                .distinct()
                .map(email -> new EmailRecipient().setEmail(email))
                .toList();
    }

    private List<String> getEmails(RecipientSelector selector) {
        if (CollectionUtils.isEmpty(selector.getEmails())) {
            return List.of();
        }
        return selector.getEmails();
    }

    private List<String> getMemberRecipientEmails(String projectId, RecipientSelector selector) {
        if (CollectionUtils.isEmpty(selector.getMembers())) {
            return List.of();
        }

        var members = selector.getMembers().toArray(new String[0]);
        var projectMembers = memberManager.findByProjectIds(List.of(projectId)).get(projectId);
        return Iterables.toStream(projectMembers)
                .filter(member -> StringUtils.equalsAnyIgnoreCase(member.getRole(), members))
                .map(Member::getUser)
                .map(User::getEmail)
                .toList();
    }

    private List<String> getContactRecipientEmails(String projectId, RecipientSelector selector) {
        if (CollectionUtils.isEmpty(selector.getContacts())) {
            return List.of();
        }

        var contacts = selector.getContacts().toArray(new String[0]);
        var projectContacts = contactManager.findByProjectId(projectId);
        return Iterables.toStream(projectContacts)
                .filter(contact -> StringUtils.equalsAnyIgnoreCase(contact.getRole(), contacts))
                .map(Contact::getPrimaryEmail)
                .toList();
    }
}
