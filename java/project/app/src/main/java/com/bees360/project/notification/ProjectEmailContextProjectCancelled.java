package com.bees360.project.notification;

import com.bees360.address.AddressProvider;
import com.bees360.building.Message;
import com.bees360.codec.GsonCodec;
import com.bees360.contract.ContractManager;
import com.bees360.customer.Customer;
import com.bees360.policy.PolicyRepository;
import com.bees360.project.ContactManager;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.util.ProjectEmailUtil;
import com.bees360.user.User;
import com.bees360.util.CollectionUtils;
import com.bees360.util.Defaults;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.gson.Gson;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;

@Log4j2
public class ProjectEmailContextProjectCancelled implements ProjectEmailContext {

    public static final String PROJECT_EMAIL_KEY_PROJECT_CANCELLED_CLAIM =
            "project_cancelled_claim";

    public static final String PROJECT_EMAIL_KEY_PROJECT_CANCELLED_UNDERWRTING =
            "project_cancelled_underwriting";

    public static final String PROJECT_EMAIL_KEY_PROJECT_REVERT_CANCEL_CLAIM =
            "revert_project_cancel_claim";

    public static final String PROJECT_EMAIL_KEY_PROJECT_REVERT_CANCEL_UNDERWRTING =
            "revert_project_cancel_underwriting";

    public static final String PROJECT_EMAIL_KEY_PROJECT_REQUEST_CANCEL_CLAIM =
            "request_project_cancel_claim";

    public static final String PROJECT_EMAIL_KEY_PROJECT_REQUEST_CANCEL_UNDERWRTING =
            "request_project_cancel_underwriting";

    private final String projectEmailKey;

    private final ProjectIIRepository projectIIRepository;

    private final PolicyRepository policyManager;

    private final AddressProvider addressProvider;

    private final ContractManager contractManager;

    private final ContactManager contactManager;

    private final Function<String, List<User>> getRequestCancellerByProjectId;

    private final Gson gson = GsonCodec.DEFAULT_GSON_BUILDER.create();
    private final BiFunction<String, Message.BuildingType, String> naicsCodeProvider;
    private final Function<Message.BuildingType, String> propertyTypeNameProvider;

    public ProjectEmailContextProjectCancelled(
            String projectEmailKey,
            ProjectIIRepository projectIIRepository,
            PolicyRepository policyManager,
            AddressProvider addressProvider,
            ContractManager contractManager,
            ContactManager contactManager,
            Function<String, List<User>> getRequestCancellerByProjectId,
            BiFunction<String, Message.BuildingType, String> naicsCodeProvider,
            Function<Message.BuildingType, String> propertyTypeNameProvider) {
        this.projectEmailKey = projectEmailKey;
        this.projectIIRepository = projectIIRepository;
        this.policyManager = policyManager;
        this.addressProvider = addressProvider;
        this.contractManager = contractManager;
        this.contactManager = contactManager;
        this.getRequestCancellerByProjectId = getRequestCancellerByProjectId;
        this.naicsCodeProvider = naicsCodeProvider;
        this.propertyTypeNameProvider = propertyTypeNameProvider;
        log.info(
                "Created"
                    + " {}(projectEmailKey={},projectIIRepository={},policyManager={},addressProvider={},contractManager={},contactManager={},getRequestCancellerByProjectId={},naicsCodeProvider={},propertyTypeNameProvider={})",
                this,
                projectEmailKey,
                projectIIRepository,
                policyManager,
                addressProvider,
                contractManager,
                contactManager,
                getRequestCancellerByProjectId,
                naicsCodeProvider,
                propertyTypeNameProvider);
    }

    @Override
    public String getEmailKey() {
        return projectEmailKey;
    }

    @Override
    public String getEmailVariables(String projectId) {
        var project = projectIIRepository.findById(projectId);

        var policyId = project.getPolicy().getId();
        var policy = policyManager.findById(policyId);
        Preconditions.checkArgument(
                policy != null,
                "Policy not found:Policy %s for project %s not found.",
                projectId,
                policyId);

        var address = addressProvider.findById(policy.getAddress().getId());
        var contract = contractManager.findById(project.getContract().getId());
        var reason = project.getCurrentState().getComment();

        if (Objects.equals(projectEmailKey, PROJECT_EMAIL_KEY_PROJECT_CANCELLED_CLAIM)
                || Objects.equals(
                        projectEmailKey, PROJECT_EMAIL_KEY_PROJECT_CANCELLED_UNDERWRTING)) {
            reason = project.getCurrentState().getStateChangeReason().getDisplayText();
        }

        List<User> users = getRequestCancellerByProjectId.apply(projectId);

        String operatorName =
                Optional.ofNullable(users)
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(userList -> userList.stream().findFirst())
                        .map(User::getName)
                        .orElse(null);

        Map<String, Object> variables = Maps.newHashMap();
        variables.put("operatorName", operatorName);
        variables.put("projectId", project.getId());
        variables.put("policyNumber", policy.getPolicyNo());
        variables.put("inspectionNo", project.getInspectionNo());
        variables.put("address", address.getAddress());
        variables.put("reason", reason);
        variables.put("insuredBy", toMap(contract.getInsuredBy()));
        var policyType = policy.getType();
        var propertyType = policy.getBuilding().getType();
        ProjectEmailUtil.putLOBInfo(
                variables, policyType, propertyType, propertyTypeNameProvider, naicsCodeProvider);
        putInsuredInfo(variables, projectId);
        return gson.toJson(variables);
    }

    private void putInsuredInfo(Map<String, Object> variables, String projectId) {
        var contacts = contactManager.findByProjectId(projectId);

        Iterables.toStream(contacts)
                .filter(c -> StringUtils.equals(ContactRoleEnum.INSURED.getName(), c.getRole()))
                .findFirst()
                .ifPresent(
                        insured -> {
                            variables.put("insuredName", insured.getFullName());
                        });
    }

    private Map<String, Object> toMap(Customer customer) {
        return Map.of("name", Defaults.transformOrDefaultIfNull(customer, Customer::getName, ""));
    }
}
