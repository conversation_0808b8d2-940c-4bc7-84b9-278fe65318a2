package com.bees360.project.listener;

import com.bees360.event.registry.ProjectCreate;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.claim.ClaimTypeEnum;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagManager;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Auto add 'Hail Report Needed' tag according to the applied rules. As it will produce {@link
 * com.bees360.event.registry.ProjectTagAdded} event and lead to pipeline task changed, it listens
 * on 'ProjectCreate' to make sure pipeline has been created when operating.
 */
@Log4j2
public class AddHailTagOnProjectCreatedEvent extends AbstractNamedEventListener<ProjectCreate> {

    private final ProjectIIManager projectIIManager;

    private final ProjectTagManager projectTagManager;

    private final Predicate<String> autoHailTagPredicate;

    private final Supplier<Pattern> hailNeededRegexSupplier;

    private final Function<String, Pattern> lossDescriptionRegexProvider;

    private final Supplier<String> systemUserSupplier;

    private final Supplier<String> bees360CustomerSupplier;

    private static final String title = "Hail report NEEDED";

    public AddHailTagOnProjectCreatedEvent(
            ProjectIIManager projectIIManager,
            ProjectTagManager projectTagManager,
            Predicate<String> autoHailTagPredicate,
            Supplier<Pattern> hailNeededRegexSupplier,
            Function<String, Pattern> lossDescriptionRegexProvider,
            Supplier<String> systemUserSupplier,
            Supplier<String> bees360CustomerSupplier) {
        this.projectIIManager = projectIIManager;
        this.projectTagManager = projectTagManager;
        this.autoHailTagPredicate = autoHailTagPredicate;
        this.hailNeededRegexSupplier = hailNeededRegexSupplier;
        this.lossDescriptionRegexProvider = lossDescriptionRegexProvider;
        this.systemUserSupplier = systemUserSupplier;
        this.bees360CustomerSupplier = bees360CustomerSupplier;
        log.info("Created {}(projectTagManager={}).", this, this.projectTagManager);
    }

    @Override
    public void handle(ProjectCreate event) throws IOException {
        var projectId = event.getProject().getId();
        var project = projectIIManager.findById(projectId);
        var serviceType = project.getServiceType();
        var claimType = project.getClaimType();
        if (project.getProjectType() != ProjectTypeEnum.CLAIM
                || Objects.equals(ServiceTypeEnum.POST_CONSTRUCTION_AUDIT, serviceType)) {
            return;
        }

        var claimNote = project.getNote();
        var insuranceCompany = project.getContract().getInsuredBy().getCompanyKey();

        var hailReportNeeded = checkNeedHailReport(claimNote, insuranceCompany, claimType);
        if (hailReportNeeded) {
            addProjectHailTag(project.getId());
        }
    }

    private boolean checkNeedHailReport(
            String claimNote, String insuranceCompany, ClaimTypeEnum claimType) {

        // read if customer support auto hail tag added.
        if (!autoHailTagPredicate.test(insuranceCompany)) {
            return false;
        }

        // hail and hail&wind will add hail_report_needed_tag directly
        if (List.of(ClaimTypeEnum.HAIL, ClaimTypeEnum.HAIL_WIND).contains(claimType)) {
            return true;
        }

        if (Objects.isNull(insuranceCompany) || StringUtils.isEmpty(claimNote)) {
            return false;
        }

        var hailNeededPattern = hailNeededRegexSupplier.get();
        var lossDescPattern = lossDescriptionRegexProvider.apply(insuranceCompany);
        if (Objects.isNull(hailNeededPattern)) {
            return false;
        }

        // if lossDescription pattern not found, match complete note.
        if (Objects.isNull(lossDescPattern)) {
            return hailNeededPattern.matcher(claimNote).find();
        }
        Matcher lossDescMatcher = lossDescPattern.matcher(claimNote + "\n\n");
        if (lossDescMatcher.find()) {
            String lossDescription = lossDescMatcher.group("LossDescription");
            return hailNeededPattern.matcher(lossDescription).find();
        }
        return false;
    }

    private void addProjectHailTag(String projectId) {
        List<? extends ProjectTag> projectTags =
                IterableUtils.toList(
                        projectTagManager.findByTitle(
                                title,
                                bees360CustomerSupplier.get(),
                                com.bees360.project.tag.Message.ProjectTagType.CLAIM));
        if (!CollectionUtils.isEmpty(projectTags)) {
            projectTagManager.addProjectTag(
                    projectId, List.of(projectTags.get(0).getId()), systemUserSupplier.get(), "AI");
            log.info("Auto add TagHailReportNeeded tag successfully, projectId {}", projectId);
        }
    }
}
