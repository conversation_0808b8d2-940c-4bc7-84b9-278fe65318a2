package com.bees360.project.config;

import com.bees360.assignment.Message;
import com.bees360.es.ESApi;
import com.bees360.es.config.ESConfig;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.mail.MailSender;
import com.bees360.mail.util.MailMessageFactory;
import com.bees360.pipeline.assign.PipelineTaskFilter;
import com.bees360.project.ContractFillProjectIIManager;
import com.bees360.project.image.ProjectImageProvider;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.user.GroupProvider;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.indices.CreateIndexRequest;
import org.opensearch.client.indices.GetIndexRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.nio.charset.StandardCharsets;
import java.util.function.Predicate;

@DirtiesContext
@SpringBootTest(classes = PipelineAssignmentConfigTest.Config.class)
@TestPropertySource(
        properties = {
            "spring.config.location = classpath:application-test.yml",
        })
@ImportAutoConfiguration(RefreshAutoConfiguration.class)
class PipelineAssignmentConfigTest {

    @Import({
        ESConfig.class,
        RabbitApiConfig.class,
        RabbitEventPublisher.class,
        RabbitEventDispatcher.class,
        AppGlobalConfigProperties.class,
        PipelineAssignmentConfig.class,
    })
    @Configuration
    static class Config {
        @MockBean GroupProvider groupProvider;

        @MockBean UserKeyProvider userKeyProvider;

        @MockBean UserProvider userProvider;

        @MockBean MailSender mailSender;

        @MockBean MailMessageFactory mailMessageFactory;

        @MockBean ProjectImageProvider projectImageProvider;

        @MockBean(name = "projectIIManager")
        public ContractFillProjectIIManager projectIIManager;
    }

    @Autowired Predicate<String> lossFunctionScriptLegalityPredicate;

    @Autowired Predicate<PipelineTaskFilter> filterScriptLegalityPredicate;

    @Autowired ESApi esApi;

    @SneakyThrows
    @Test
    void testLossFunctionScriptLegalityPredicate() {
        // The correct script test returns a value of type int.
        String script =
                IOUtils.resourceToString(
                        "auto-assign-test-loss-function-script.js",
                        StandardCharsets.UTF_8,
                        PipelineAssignmentConfigTest.class.getClassLoader());
        Assertions.assertTrue(lossFunctionScriptLegalityPredicate.test(script));
        // The wrong script test returns a value of type string.
        Assertions.assertFalse(lossFunctionScriptLegalityPredicate.test("userId"));
    }

    @Test
    void testFilterFullScriptLegalityPredicate() {
        var indexName = "project_data";
        var request = new GetIndexRequest(indexName);
        var indexExists =
                esApi.apply(client -> client.indices().exists(request, RequestOptions.DEFAULT));
        if (!indexExists) {
            var createRequest = new CreateIndexRequest(indexName);
            esApi.apply(client -> client.indices().create(createRequest, RequestOptions.DEFAULT));
        }
        // Test the correct full script, No outermost queries are allowed.
        var correctFullScript =
                "{\n"
                    + "  \"query\": {\n"
                    + "    \"terms\": {\n"
                    + "      \"project_id\": [\n"
                    + "        \"1726539959810\",\n"
                    + "        \"1726539990824\"\n"
                    + "      ]\n"
                    + "    }\n"
                    + "  },\n"
                    + "  \"script_fields\": {\n"
                    + "    \"acceptable_user_id\": {\n"
                    + "      \"script\": {\n"
                    + "        \"lang\": \"painless\",\n"
                    + "        \"source\": \"return ['1726539959810', '1726539990824'];\"\n"
                    + "      }\n"
                    + "    },\n"
                    + "    \"task_for_assignment\": {\n"
                    + "      \"script\": {\n"
                    + "        \"lang\": \"painless\",\n"
                    + "        \"source\": \"ArrayList tasks = [];ArrayList assignmentTasks = [];if"
                    + " (params['_source'].containsKey('pipeline') &&"
                    + " params['_source']['pipeline'].containsKey('pipeline_task')) {tasks ="
                    + " params['_source']['pipeline']['pipeline_task'];}for (int i = 0; i < 2 && i"
                    + " < tasks.length; i++)"
                    + " {tasks[i].assignment_difficulty=1;assignmentTasks.add(tasks[i]);}return"
                    + " assignmentTasks;\"\n"
                    + "      }\n"
                    + "    }\n"
                    + "  }\n"
                    + "}";
        var correctTaskFilter =
                PipelineTaskFilter.from(
                        Message.PipelineTaskFilter.newBuilder()
                                .setFullFilter(correctFullScript)
                                .build());
        Assertions.assertTrue(filterScriptLegalityPredicate.test(correctTaskFilter));
        // Test the wrong full script, No outermost queries are allowed.
        var wrongFullScript = "{\"terms\":{\"project_id\":[\"40\",\"51\"]}}";
        var wrongTaskFilter =
                PipelineTaskFilter.from(
                        Message.PipelineTaskFilter.newBuilder()
                                .setFullFilter(wrongFullScript)
                                .build());
        Assertions.assertFalse(filterScriptLegalityPredicate.test(wrongTaskFilter));
    }

    @Test
    void testFilterTaskAndPipelineScriptLegalityPredicate() {
        // Test the correct pipeline script and task script.
        var correctTaskScript = "(pipeline_task_def.key = 'tag_elevation_overviews')";
        var correctPipelineScript = "(customer.key in ('American Family'))";
        var correctTaskFilter =
                PipelineTaskFilter.from(
                        Message.PipelineTaskFilter.newBuilder()
                                .setPipelineFilter(correctPipelineScript)
                                .setTaskFilter(correctTaskScript)
                                .build());
        Assertions.assertTrue(filterScriptLegalityPredicate.test(correctTaskFilter));

        // Test the wrong pipeline script and task script.
        var wrongTaskScript = "(pipeline_task_def.key_a = 'tag_elevation_overviews')";
        var wrongPipelineScript = "(customer.key_a in ('American Family'))";
        var wrongTaskFilter =
                PipelineTaskFilter.from(
                        Message.PipelineTaskFilter.newBuilder()
                                .setPipelineFilter(wrongPipelineScript)
                                .setTaskFilter(wrongTaskScript)
                                .build());
        Assertions.assertFalse(filterScriptLegalityPredicate.test(wrongTaskFilter));
    }
}
