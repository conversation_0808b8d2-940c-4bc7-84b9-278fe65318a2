package com.bees360.project;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.BaseActivityUtil;
import com.bees360.activity.Message.ActivityMessage.ActionType;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.CronTriggerDailyAt9AmCst;
import com.bees360.job.JobScheduler;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.mail.MailMessage;
import com.bees360.mail.MailSender;
import com.bees360.project.config.mail.DailyDaysOldEmailConfig;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@SpringBootTest(classes = DailyDaysOldEmailITest.Config.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
@ImportAutoConfiguration(RefreshAutoConfiguration.class)
class DailyDaysOldEmailITest {

    @Configuration
    @Import({
        DailyDaysOldEmailConfig.class,
    })
    static class Config {

        @Bean
        InMemoryEventPublisher inMemoryEventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }

        @Bean
        InMemoryJobScheduler inMemoryJobScheduler() {
            return new InMemoryJobScheduler(MoreExecutors.directExecutor());
        }
    }

    @MockBean(name = "grpcProjectManager")
    private ProjectIIManager projectIIManager;

    @MockBean(name = "backendSender")
    private MailSender mailSender;

    @MockBean private ProjectDaysOldProvider projectDaysOldProvider;
    @MockBean private ContactManager contactManager;
    @MockBean private UserProvider userProvider;
    @MockBean private ActivityManager activityManager;
    @Autowired private EventPublisher eventPublisher;
    @Autowired private JobScheduler jobScheduler;
    private final Gson gson = new Gson();

    @Test
    void testSendDailyDaysOldEmail() {
        mockData();
        CronTriggerDailyAt9AmCst event = new CronTriggerDailyAt9AmCst();
        event.setTriggerTime(Instant.now().plus(1, ChronoUnit.DAYS));

        eventPublisher.publish(event);

        // should schedule a SendDaysOldEmailJob and a EmailJob
        var mailMessageCaptor = ArgumentCaptor.forClass(MailMessage.class);
        Mockito.verify(mailSender, Mockito.times(1)).send(mailMessageCaptor.capture());
        var mailMessage = mailMessageCaptor.getValue();
        Assertions.assertTrue(mailMessage.getRecipients().contains("<EMAIL>"));
        Assertions.assertTrue(mailMessage.getCc().isEmpty());
        var variablesJson = mailMessage.toMessage().getVariablesJson();
        var variables = gson.fromJson(variablesJson, JsonObject.class);

        Assertions.assertNotNull(variables.get("LOB"));
        Assertions.assertNotNull(variables.get("NAICSCode"));
    }

    private void mockData() {
        var projectId = String.valueOf(RandomUtils.nextInt(10000, 100000));
        Mockito.when(projectDaysOldProvider.findProjectByDaysOldQuery(any()))
                .thenReturn(List.of(projectId));
        var originalProject = RandomProjectUtil.randomProject();
        var creatorId = String.valueOf(RandomUtils.nextInt(10000, 100000));
        var creator =
                com.bees360.user.Message.UserMessage.newBuilder()
                        .setId(creatorId)
                        .setEmail(RandomStringUtils.randomAlphabetic(8))
                        .build();
        var project =
                ProjectII.from(
                        originalProject.toMessage().toBuilder()
                                .setServiceType(Message.ServiceType.EXPRESS_UNDERWRITING)
                                .setId(projectId)
                                .setCreateBy(creator)
                                .setLatestStatus(Message.ProjectStatus.CUSTOMER_CONTACTED)
                                .clearInspection()
                                .build());
        Preconditions.checkArgument(Objects.nonNull(project));
        Mockito.when(projectIIManager.findAllById(eq(Set.of(projectId))))
                .thenAnswer(e -> List.of(project));
        var contact = RandomProjectUtil.randomContact();
        Mockito.when(contactManager.findByProjectId(projectId)).thenAnswer(e -> contact);
        Mockito.when(userProvider.findUserById(creatorId)).thenAnswer(e -> User.from(creator));
        var activity = createActionComment(ActionType.CONTACT, Set.of("WEB"));

        Mockito.when(activityManager.getActivities(any())).thenAnswer(e -> List.of(activity));
        Mockito.when(userProvider.findUserById(Mockito.anyList()))
                .thenAnswer(
                        e -> List.of(User.from(activity.toMessage().getComment().getCreatedBy())));
    }

    private Activity createActionComment(ActionType actionType, Set<String> visibility) {
        return Activity.from(
                BaseActivityUtil.randomActivityComment(actionType.name()).toMessage().toBuilder()
                        .addAllVisibility(visibility)
                        .build());
    }
}
