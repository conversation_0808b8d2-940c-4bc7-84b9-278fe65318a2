package com.bees360.project.report;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;

import com.bees360.codec.UniversalCodec;
import com.bees360.estintel.FactorValue;
import com.bees360.estintel.FactorValueProvider;
import com.bees360.estintel.Message;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.registry.ProjectPipelineTaskChanged;
import com.bees360.job.JobScheduler;
import com.bees360.map.BasicMap;
import com.bees360.map.util.InMemoryMap;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.PipelineService;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.report.Report;
import com.bees360.report.util.InMemoryReport;
import com.bees360.report.util.InMemoryReportResource;
import com.bees360.report.util.InMemoryReportSummary;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.gson.JsonParser;
import com.google.protobuf.util.JsonFormat;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

@SpringBootTest
@DirtiesContext
public class MergeFSRAndHoverOnPipelineTaskReadyTest {

    private static final String EVENT_ROUTING_KEY =
            "pipeline_task_changed.status_changed.merge_fsr_and_hover.ready";

    @Configuration
    @Import({
        InMemoryEventPublisher.class,
        MergeFSRAndHoverOnPipelineTaskReady.class,
    })
    static class Config {

        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @Bean
        Supplier<String> systemUserSupplier() {
            return () -> "10000";
        }

        @Bean
        BasicMap<String, String> jobKey2ProjectIdMap() {
            return new InMemoryMap<>();
        }

        @Bean
        JobScheduler jobScheduler() {
            return mock(JobScheduler.class);
        }

        @Bean
        ProjectReportProvider projectReportProvider() {
            return mock(ProjectReportProvider.class);
        }

        @Bean
        FactorValueProvider factorValueProvider() {
            return mock(FactorValueProvider.class);
        }

        @Bean
        PipelineService pipelineService() {
            return mock(PipelineService.class);
        }
    }

    @Autowired private JobScheduler jobScheduler;

    @Autowired private ProjectReportProvider projectReportProvider;

    @Autowired private FactorValueProvider factorValueProvider;

    @Autowired private PipelineService pipelineService;

    @Autowired private InMemoryEventPublisher inMemoryEventPublisher;

    @Autowired private MergeFSRAndHoverOnPipelineTaskReady listener;

    @Test
    @DirtiesContext
    void testMergeShouldScheduleJob() {
        mockFactorValueProvider("merge-fsr-hover-hover-factor-value.json");
        mockProjectReportProvider();
        Mockito.when(jobScheduler.schedule(Mockito.any())).thenReturn(null);

        inMemoryEventPublisher.enlist(listener);

        var event = getEvent("123", "10000");
        inMemoryEventPublisher.publish(event);

        inMemoryEventPublisher.publish(EVENT_ROUTING_KEY, UniversalCodec.INSTANCE.encode(event));
        inMemoryEventPublisher.delist(listener);

        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(any());
    }

    @Test
    @DirtiesContext
    void testAutoMergeNoMainShouldSkipAndSetTaskError() {
        mockFactorValueProvider("merge-fsr-hover-hover-factor-value-none-main.json");
        mockProjectReportProvider();

        Mockito.doNothing()
                .when(pipelineService)
                .setTaskStatus(anyString(), anyString(), any(), anyString());

        inMemoryEventPublisher.enlist(listener);

        var event = getEvent("123", "10000");
        inMemoryEventPublisher.publish(event);

        inMemoryEventPublisher.publish(EVENT_ROUTING_KEY, UniversalCodec.INSTANCE.encode(event));
        inMemoryEventPublisher.delist(listener);

        Mockito.verify(pipelineService, Mockito.times(1))
                .setTaskStatus(anyString(), anyString(), any(), anyString());
        Mockito.verify(jobScheduler, Mockito.times(0)).schedule(any());
    }

    @Test
    @DirtiesContext
    void testManualMergeNoMainShouldScheduleJob() {
        mockFactorValueProvider("merge-fsr-hover-hover-factor-value-none-main.json");
        mockProjectReportProvider();

        inMemoryEventPublisher.enlist(listener);

        var event = getEvent("123", "100001");
        inMemoryEventPublisher.publish(event);

        inMemoryEventPublisher.publish(EVENT_ROUTING_KEY, UniversalCodec.INSTANCE.encode(event));
        inMemoryEventPublisher.delist(listener);

        Mockito.verify(pipelineService, Mockito.times(0))
                .setTaskStatus(anyString(), anyString(), any(), anyString());
        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(any());
    }

    @Test
    @DirtiesContext
    void testMergeNoFSRShouldSkipAndSetTaskError() {
        Mockito.when(projectReportProvider.find(anyString(), anyString()))
                .thenAnswer(a -> List.of());

        Mockito.doNothing()
                .when(pipelineService)
                .setTaskStatus(anyString(), anyString(), any(), anyString());

        inMemoryEventPublisher.enlist(listener);

        var event = getEvent("123", "10000");
        inMemoryEventPublisher.publish(event);

        inMemoryEventPublisher.publish(EVENT_ROUTING_KEY, UniversalCodec.INSTANCE.encode(event));
        inMemoryEventPublisher.delist(listener);

        Mockito.verify(pipelineService, Mockito.times(1))
                .setTaskStatus(anyString(), anyString(), any(), anyString());
        Mockito.verify(jobScheduler, Mockito.times(0)).schedule(any());
    }

    @Test
    @DirtiesContext
    void testMergeNoHoverShouldSkipAndSetTaskError() {
        Mockito.when(factorValueProvider.findByQuery(any())).thenAnswer(a -> List.of());
        mockProjectReportProvider();
        Mockito.doNothing()
                .when(pipelineService)
                .setTaskStatus(anyString(), anyString(), any(), anyString());

        inMemoryEventPublisher.enlist(listener);

        var event = getEvent("123", "10000");
        inMemoryEventPublisher.publish(event);

        inMemoryEventPublisher.publish(EVENT_ROUTING_KEY, UniversalCodec.INSTANCE.encode(event));
        inMemoryEventPublisher.delist(listener);

        Mockito.verify(factorValueProvider, Mockito.times(1)).findByQuery(any());
        Mockito.verify(pipelineService, Mockito.times(1))
                .setTaskStatus(anyString(), anyString(), any(), anyString());
        Mockito.verify(jobScheduler, Mockito.times(0)).schedule(any());
    }

    @Test
    @DirtiesContext
    void testAlreadyMergedShouldSkipAndSetTaskDone() {
        mockFactorValueProvider("merge-fsr-hover-hover-factor-value.json");
        mockProjectReportProvider(
                "{\n"
                        + "  \"test\": \"123\",\n"
                        + "  \"fsr\": \"test summary\",\n"
                        + "  \"hover\": {\n"
                        + "    \"hoverTla\": [\n"
                        + "      {\n"
                        + "        \"version\": \"1.0\"\n"
                        + "      }\n"
                        + "    ]\n"
                        + "  }\n"
                        + "}");
        Mockito.when(jobScheduler.schedule(Mockito.any())).thenReturn(null);
        Mockito.doNothing()
                .when(pipelineService)
                .setTaskStatus("123", "merge_fsr_and_hover", PipelineStatus.DONE, null);

        inMemoryEventPublisher.enlist(listener);

        var event = getEvent("123", "10000");
        inMemoryEventPublisher.publish(event);

        inMemoryEventPublisher.publish(EVENT_ROUTING_KEY, UniversalCodec.INSTANCE.encode(event));
        inMemoryEventPublisher.delist(listener);

        Mockito.verify(pipelineService, Mockito.times(1))
                .setTaskStatus("123", "merge_fsr_and_hover", PipelineStatus.DONE, null);
        Mockito.verify(jobScheduler, Mockito.times(0)).schedule(any());
    }

    private ProjectPipelineTaskChanged getEvent(String projectId, String updatedBy) {
        var event = new ProjectPipelineTaskChanged();
        event.setPipelineId(projectId);
        event.setTaskDefKey("merge_fsr_and_hover");
        event.setUpdatedBy(updatedBy);
        event.setOldState(
                PipelineTaskChanged.State.newBuilder().status(PipelineStatus.PENDING).build());
        event.setState(PipelineTaskChanged.State.newBuilder().status(PipelineStatus.READY).build());
        return event;
    }

    private void mockProjectReportProvider() {
        mockProjectReportProvider("{\"test\":\"123\",\"fsr\":\"test summary\"}");
    }

    private void mockProjectReportProvider(String summary) {
        var reportSummary = new InMemoryReportSummary();
        reportSummary.setVersion("1.0.0");
        reportSummary.setSummary(summary);
        var report = new InMemoryReport();
        report.setSummary(reportSummary);
        var reportResource = new InMemoryReportResource();
        reportResource.setUrl("report/test.pdf");
        reportResource.setType(Type.COMPRESSED);
        report.setResources(List.of(reportResource));
        var reportList = new ArrayList<Report>();
        reportList.add(report);
        Mockito.when(projectReportProvider.find(anyString(), anyString()))
                .thenAnswer(a -> reportList);
    }

    private void mockFactorValueProvider(String sourceUrl) {
        Mockito.when(factorValueProvider.findByQuery(any()))
                .thenAnswer(
                        a -> {
                            var json = loadResource(sourceUrl);
                            var factorValues = new ArrayList<FactorValue>();
                            var jsonArray = JsonParser.parseString(json).getAsJsonArray();
                            for (var element : jsonArray) {
                                var jsonObject = element.getAsJsonObject();
                                var factorValueBuilder = Message.FactorValueMessage.newBuilder();
                                JsonFormat.parser()
                                        .merge(jsonObject.toString(), factorValueBuilder);
                                factorValues.add(FactorValue.from(factorValueBuilder.build()));
                            }
                            return factorValues;
                        });
    }

    @SneakyThrows
    private static String loadResource(String resourceFileName) {
        return IOUtils.resourceToString(
                resourceFileName,
                StandardCharsets.UTF_8,
                MergeFSRAndHoverOnPipelineTaskReadyTest.class.getClassLoader());
    }
}
