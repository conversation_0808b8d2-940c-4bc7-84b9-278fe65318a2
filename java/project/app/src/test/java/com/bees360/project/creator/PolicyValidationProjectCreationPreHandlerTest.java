package com.bees360.project.creator;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.bees360.api.InvalidArgumentException;
import com.bees360.building.Message.BuildingType;
import com.bees360.customer.Customer;
import com.bees360.customer.CustomerProvider;
import com.bees360.customer.Message.CustomerMessage;
import com.bees360.project.BuildingManager;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.config.ProjectPolicyValidationConfig;
import com.bees360.project.status.ProjectStatusProvider;
import com.google.protobuf.util.JsonFormat;

import lombok.SneakyThrows;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.function.BiPredicate;

/** testing for {@link PolicyValidationProjectCreationPreHandler} */
@SpringBootTest(classes = {PolicyValidationProjectCreationPreHandlerTest.Config.class})
@ActiveProfiles("validation-policy-type")
public class PolicyValidationProjectCreationPreHandlerTest {

    @Configuration
    @Import({ProjectPolicyValidationConfig.class})
    static class Config {}

    @MockitoBean ProjectIIManager projectIIManager;
    @MockitoBean BuildingManager buildingManager;
    @MockitoBean ProjectStatusProvider projectStatusProvider;
    @MockitoBean CustomerProvider customerProvider;

    @Autowired PolicyValidationProjectCreationPreHandler policyValidationProjectCreationPreHandler;

    private static final String CUSTOMER_ID_2357 = "2357";
    private static final String CUSTOMER_ID_1063 = "1063";
    private static final String COMMERCIAL_POLICY = "Commercial Property Insurance";
    private static final String FAKE_POLICY = "Fake - Commercial Property Insurance";
    private static final String OPENAPI_CHANNEL = "OPENAPI";
    private static final String WEB_CHANNEL = "WEB";
    private static final String UNKNOWN_CHANNEL = "UNKNOWN";

    private static final BuildingType GAS_STATION = BuildingType.forNumber(21); // Gas Station
    private static final BuildingType GENERAL_COMMERCIAL =
            BuildingType.forNumber(22); // General Commercial

    @BeforeEach
    void setUp() {
        setupCustomer(CUSTOMER_ID_2357, "Swyfft Underwriting");
        setupCustomer(CUSTOMER_ID_1063, "Another Customer");
    }

    private void setupCustomer(String customerId, String companyKey) {
        when(customerProvider.findById(eq(customerId)))
                .thenReturn(
                        Customer.of(
                                CustomerMessage.newBuilder()
                                        .setId(customerId)
                                        .setKey(companyKey)
                                        .build()));
    }

    @Test
    void testValidateAndLoadData_policyTypePropertyTypePassed() {
        var request = createTestRequest(CUSTOMER_ID_2357, COMMERCIAL_POLICY, 21);

        assertEquals(GAS_STATION, request.getPolicy().getBuilding().getType());
        policyValidationProjectCreationPreHandler.validateAndLoadData(request, OPENAPI_CHANNEL);
        assertEquals(GAS_STATION, request.getPolicy().getBuilding().getType());
    }

    @Test
    void testValidateAndLoadData_OPENAPI_policyTypeNotMatchPropertyType() {
        var request = createTestRequest(CUSTOMER_ID_2357, COMMERCIAL_POLICY);

        assertEquals(
                BuildingType.RESIDENTIAL_SINGLE_FAMILY,
                request.getPolicy().getBuilding().getType());

        var exception =
                assertThrows(
                        InvalidArgumentException.class,
                        () ->
                                policyValidationProjectCreationPreHandler.validateAndLoadData(
                                        request, OPENAPI_CHANNEL));

        assertEquals(
                "Invalid house type for this policy. Please update and retry.",
                exception.getMessage());
    }

    @Test
    void testValidateAndLoadData_OPENAPI_policyTypeInvalid() {
        var request = createTestRequest(CUSTOMER_ID_2357, FAKE_POLICY);

        var exception =
                assertThrows(
                        InvalidArgumentException.class,
                        () ->
                                policyValidationProjectCreationPreHandler.validateAndLoadData(
                                        request, OPENAPI_CHANNEL));

        assertEquals(
                "Invalid policy Type. Please check the acceptable values and try again.",
                exception.getMessage());
    }

    @Test
    void testValidateAndLoadData_OPENAPI_emptyPolicyTypeOkIfCustomerNoRequire() {
        var request = createTestRequest(CUSTOMER_ID_1063, "", BuildingType.GARAGE.getNumber());

        policyValidationProjectCreationPreHandler.validateAndLoadData(request, OPENAPI_CHANNEL);

        assertEquals(BuildingType.GARAGE, request.getPolicy().getBuilding().getType());
    }

    @Test
    void testValidateAndLoadData_OPENAPI_emptyPolicyTypeInvalid() {
        var request = createTestRequest(CUSTOMER_ID_2357, "");

        var exception =
                assertThrows(
                        InvalidArgumentException.class,
                        () ->
                                policyValidationProjectCreationPreHandler.validateAndLoadData(
                                        request, OPENAPI_CHANNEL));

        assertEquals("Missing Policy Type. Please fill in and try again.", exception.getMessage());
    }

    @Test
    void testValidateAndLoadData_OPENAPI_failIfEmptyPropertyTypeWithCustomerValidation() {
        var request = createTestRequest(CUSTOMER_ID_2357, COMMERCIAL_POLICY);

        var exception =
                assertThrows(
                        InvalidArgumentException.class,
                        () ->
                                policyValidationProjectCreationPreHandler.validateAndLoadData(
                                        request, OPENAPI_CHANNEL));

        assertEquals(
                "Invalid house type for this policy. Please update and retry.",
                exception.getMessage());
    }

    @Test
    void testValidateAndLoadData_WEB_successIfPolicyTypeEmptyWithoutCustomerValidation() {
        var request = createTestRequest(CUSTOMER_ID_1063, "");

        policyValidationProjectCreationPreHandler.validateAndLoadData(request, OPENAPI_CHANNEL);
    }

    @Test
    void testValidateAndLoadData_OPENAPI_failIfPolicyTypeEmpty() {
        var request = createTestRequest(CUSTOMER_ID_2357, "");

        var exception =
                assertThrows(
                        InvalidArgumentException.class,
                        () ->
                                policyValidationProjectCreationPreHandler.validateAndLoadData(
                                        request, OPENAPI_CHANNEL));

        assertEquals("Missing Policy Type. Please fill in and try again.", exception.getMessage());
    }

    @Test
    void testValidateAndLoadData_WEB_successIfEmptyPolicyTypeWithoutCustomerValidation() {
        var request = createTestRequest(CUSTOMER_ID_1063, "");

        policyValidationProjectCreationPreHandler.validateAndLoadData(request, OPENAPI_CHANNEL);
    }

    @Test
    void testValidateAndLoadData_WEB_policyTypeNotMatchPropertyType() {
        var request = createTestRequest(CUSTOMER_ID_2357, COMMERCIAL_POLICY);

        var exception =
                assertThrows(
                        InvalidArgumentException.class,
                        () ->
                                policyValidationProjectCreationPreHandler.validateAndLoadData(
                                        request, WEB_CHANNEL));

        assertEquals(
                "Invalid property type for this policy type. Please update and retry.",
                exception.getMessage());
    }

    @Test
    void testValidateAndLoadData_WEB_policyTypeInvalid() {
        var request = createTestRequest(CUSTOMER_ID_2357, FAKE_POLICY);

        var exception =
                assertThrows(
                        InvalidArgumentException.class,
                        () ->
                                policyValidationProjectCreationPreHandler.validateAndLoadData(
                                        request, "WEB"));

        assertEquals(
                "Invalid policy Type. Please check the acceptable values and try again.",
                exception.getMessage());
    }

    @Test
    void testValidateAndLoadData_failWithMissingHouseTypeIfBuildingTypeUnrecognized() {
        var request = createTestRequest(CUSTOMER_ID_2357, COMMERCIAL_POLICY, -1);

        assertEquals(BuildingType.UNRECOGNIZED, request.getPolicy().getBuilding().getType());
        var exception =
                assertThrows(
                        InvalidArgumentException.class,
                        () ->
                                policyValidationProjectCreationPreHandler.validateAndLoadData(
                                        request, OPENAPI_CHANNEL));

        assertEquals(
                "Missing Type of House. Please fill in and try again.", exception.getMessage());
    }

    @Test
    void testValidateAndLoadData_failWithMissiongHouseTypeIfBuildingTypeUnknown() {
        var request = createTestRequest(CUSTOMER_ID_2357, COMMERCIAL_POLICY, 100);

        assertEquals(
                BuildingType.UNKNOWN_BUILDING_TYPE, request.getPolicy().getBuilding().getType());
        var exception =
                assertThrows(
                        InvalidArgumentException.class,
                        () ->
                                policyValidationProjectCreationPreHandler.validateAndLoadData(
                                        request, OPENAPI_CHANNEL));
        assertEquals(
                "Missing Type of House. Please fill in and try again.", exception.getMessage());
    }

    @Test
    void testValidateAndLoadData_creationChannelNotMatch() {
        var request = createTestRequest("1063", "Policy Type", BuildingType.COMMERCIAL.getNumber());
        policyValidationProjectCreationPreHandler.validateAndLoadData(request, UNKNOWN_CHANNEL);
    }

    @Test
    void testValidateAndLoadData_insuredByNotMatchCondition() {
        var request = createTestRequest(CUSTOMER_ID_1063, null);
        policyValidationProjectCreationPreHandler.validateAndLoadData(request, OPENAPI_CHANNEL);
    }

    private BasicProjectCreationRequest createTestRequest(String customerId, String policyType) {
        return createTestRequest(customerId, policyType, null);
    }

    private BasicProjectCreationRequest createTestRequest(
            String customerId, String policyType, Integer buildingType) {

        var requestJson =
                """
               {
                   "project_type": 2,
                   "contract": {
                       "insured_by": { "id": "%s" }
                   },
                   "policy": {
                       "type": "%s",
                       "building": { "type": %s }
                   }
               }
           """
                        .formatted(customerId, policyType, buildingType);
        return genRequest(requestJson);
    }

    @SneakyThrows
    private BasicProjectCreationRequest genRequest(String requestJson) {
        var builder = ProjectMessage.newBuilder();
        JsonFormat.parser().merge(requestJson, builder);
        return new BasicProjectCreationRequest(ProjectCreationRequest.from(builder.build()));
    }

    @Autowired
    @Qualifier("customerFeaturePredicate")
    BiPredicate<String, String> customerFeaturePredicate;

    @Test
    void testCustomerFeaturePredicate() {
        assertTrue(customerFeaturePredicate.test("Fake Customer", "always-true"));
        assertFalse(customerFeaturePredicate.test("Fake Customer", "always-false"));
        assertFalse(customerFeaturePredicate.test("Fake Customer", "fake-feature"));

        assertTrue(customerFeaturePredicate.test("Swyfft Underwriting", "always-true"));
        assertFalse(customerFeaturePredicate.test("Swyfft Underwriting", "always-false"));
        assertFalse(customerFeaturePredicate.test("Swyfft Underwriting", "fake-feature"));
        assertTrue(
                customerFeaturePredicate.test(
                        "Swyfft Underwriting", "valid-policy-type-property-type-in-OPENAPI"));

        assertFalse(customerFeaturePredicate.test("Test Sample", "global-value"));
        assertTrue(customerFeaturePredicate.test("Swyfft Underwriting", "global-value"));
        // using config of [*]
        assertTrue(customerFeaturePredicate.test("New Customer", "global-value"));
    }
}
