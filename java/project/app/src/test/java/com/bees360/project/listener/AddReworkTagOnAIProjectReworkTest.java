package com.bees360.project.listener;

import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.ProjectReworkEvent;
import com.bees360.project.base.Message;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagManager;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

import java.util.concurrent.Executor;

@SpringBootTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DirtiesContext
public class AddReworkTagOnAIProjectReworkTest {
    @Configuration
    @Import({
        InMemoryEventPublisher.class,
        AutoRegisterEventListenerConfig.class,
    })
    static class Config {
        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @Bean
        AddReworkTagOnAIProjectRework addReworkTagOnAIProjectRework(
                ProjectTagManager projectTagManager) {
            return new AddReworkTagOnAIProjectRework(projectTagManager, "REWORK NEEDED", "2000");
        }
    }

    @Autowired private EventPublisher eventPublisher;
    @MockBean private ProjectTagManager projectTagManager;

    @BeforeEach
    private void setUp() {

        var reworkNeededTag =
                ProjectTag.of(
                        com.bees360.project.tag.Message.ProjectTagMessage.newBuilder()
                                .setId("3")
                                .setTitle("REWORK NEEDED")
                                .build());
        Mockito.when(
                        projectTagManager.findByTitleEquals(
                                Mockito.anyString(),
                                Mockito.anyString(),
                                Mockito.any(com.bees360.project.tag.Message.ProjectTagType.class)))
                .thenReturn(reworkNeededTag);
        Mockito.when(
                        projectTagManager.addProjectTag(
                                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(1);
    }

    @Test
    public void testReceiveEventNotFromAIShouldNotAddTag() {
        var projectId = String.valueOf(RandomUtils.nextLong());
        var event = new ProjectReworkEvent();

        event.setProjectId(projectId);
        event.setUpdatedBy(RandomStringUtils.randomAlphabetic(8));
        event.setProjectType(Message.ProjectType.forNumber(RandomUtils.nextInt(1, 3)));
        event.setUpdatedVia("IO");
        eventPublisher.publish(event);
        Mockito.verify(projectTagManager, Mockito.times(0))
                .addProjectTag(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void testReceiveEventFromAIShouldAddTag() {
        var projectId = String.valueOf(RandomUtils.nextLong());
        var event = new ProjectReworkEvent();

        event.setProjectId(projectId);
        event.setUpdatedBy(RandomStringUtils.randomAlphabetic(8));
        event.setProjectType(Message.ProjectType.forNumber(RandomUtils.nextInt(1, 3)));
        event.setUpdatedVia("AI");
        eventPublisher.publish(event);
        Mockito.verify(projectTagManager, Mockito.times(1))
                .addProjectTag(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }
}
