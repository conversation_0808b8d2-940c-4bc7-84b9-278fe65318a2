package com.bees360.project.config.mail;

import static org.junit.jupiter.api.Assertions.*;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.building.Message;

import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.util.function.BiFunction;

@SpringBootTest(classes = NaicsCodeConfigTest.Config.class)
@ApplicationAutoConfig(exclude = {GrpcServerFactoryAutoConfiguration.class})
@ActiveProfiles({"test"})
public class NaicsCodeConfigTest {

    @Import({
        NaicsCodeConfig.class,
    })
    public static class Config {}

    @Autowired private BiFunction<String, Message.BuildingType, String> naicsCodeProvider;

    @Test
    void testExactPolicyAndPropertyMatch() {
        String result =
                naicsCodeProvider.apply(
                        "Commercial Property Insurance", Message.BuildingType.HABITATIONAL);
        assertEquals("531110", result);
    }

    @Test
    void testWildcardPolicyMatch() {
        String result =
                naicsCodeProvider.apply(
                        "Invalid Policy", Message.BuildingType.RESIDENTIAL_SINGLE_FAMILY);
        assertEquals("236115", result);
    }

    @Test
    void testNullPolicyType() {
        String result = naicsCodeProvider.apply(null, Message.BuildingType.COMMERCIAL);
        assertEquals("531120", result);
    }

    @Test
    void testEmptyPolicyType() {
        String result = naicsCodeProvider.apply("", Message.BuildingType.COMMERCIAL);
        assertEquals("531120", result);
    }

    @Test
    void testFullFallbackChain() {
        assertEquals(
                "531110",
                naicsCodeProvider.apply(
                        "Commercial Property Insurance", Message.BuildingType.HABITATIONAL));

        assertEquals(
                "236115",
                naicsCodeProvider.apply(
                        "Invalid Policy", Message.BuildingType.RESIDENTIAL_SINGLE_FAMILY));
    }
}
