package com.bees360.project;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.twilio.ConversationApi;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;

import java.util.Optional;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {
            ApacheHttpClientConfig.class,
            TestProjectAppConfig.class,
            ProjectApp.class,
        },
        properties = "GRPC_SERVER_PORT=9881")
@ActiveProfiles("disable-twilio")
@DirtiesContext
class ProjectAppDisableTwilioTest {

    @Autowired private Optional<ConversationApi> conversationApi;

    @Test
    void testLoadContext() {
        assertTrue(conversationApi.isEmpty());
    }
}
