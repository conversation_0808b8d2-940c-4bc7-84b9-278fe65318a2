package com.bees360.project.listener;

import static org.mockito.ArgumentMatchers.eq;

import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.project.RandomProjectUtil;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.report.Message;
import com.bees360.report.RandomReport;
import com.bees360.report.ReportTypeEnum;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(classes = ProjectReportAutoApproveTest.Config.class)
@TestPropertySource(
        properties = "spring.config.location = classpath:application-projectReportAutoApprove.yaml")
public class NewProjectReportAutoApproveTest extends ProjectReportAutoApproveTest {

    @Test
    void testAutoApproveWhenExpressInspectionInvoice() {
        var projectId = RandomProjectUtil.getRandomId();
        var reportId = RandomReport.randomId();
        mockData(projectId, "Test Company", reportId, ReportTypeEnum.INV);

        var project = projectManager.findById(projectId);
        Mockito.when(project.getServiceType()).thenAnswer(e -> ServiceTypeEnum.EXPRESS_INSPECTION);

        var event = new ReportGroupAdded();
        event.setReportId(reportId);
        event.setGroupKey(projectId);
        event.setGroupType("GROUP_PROJECT");
        eventPublisher.publish(event);

        Mockito.verify(reportManager, Mockito.times(1))
                .updateStatus(eq(reportId), eq(Message.ReportMessage.Status.APPROVED));
    }
}
