package com.bees360.project;

import com.bees360.address.AddressDataService;
import com.bees360.address.GrpcAddressService;
import com.bees360.address.JooqAddressRepository;
import com.bees360.project.config.GrpcProjectStatisticsClientConfig;
import com.bees360.resource.InMemoryResourceRepository;
import com.bees360.resource.ProjectResourceUploader;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.resource.SecureProjectResourceUploader;
import com.bees360.resource.util.ForwardingResourcePool;
import com.bees360.user.InMemoryUserKeyProvider;
import com.bees360.user.InMemoryUserRepository;
import com.bees360.user.UserProvider;

import org.checkerframework.checker.nullness.qual.Nullable;
import org.jooq.DSLContext;
import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;

@Import({
    GrpcProjectStatisticsClientConfig.class,
})
@Configuration
public class TestProjectAppConfig {

    static final String PROTOCOL = "http://";

    static class TestResourcePool extends ForwardingResourcePool implements ResourceUrlProvider {
        private final ResourcePool resourcePool;

        public TestResourcePool(ResourcePool resourcePool) {
            this.resourcePool = resourcePool;
        }

        @Override
        public URL getGetUrl(String key) {
            try {
                return URI.create(PROTOCOL + key).toURL();
            } catch (MalformedURLException e) {
                throw new IllegalStateException(e);
            }
        }

        @Override
        public URL getPutUrl(String key, @Nullable ResourceMetadata metadata) {
            try {
                return URI.create(PROTOCOL + key).toURL();
            } catch (MalformedURLException e) {
                throw new IllegalStateException(e);
            }
        }

        @Override
        protected ResourcePool delegate() {
            return resourcePool;
        }
    }

    @Bean
    ResourcePool inmemResourcePool() {
        return new InMemoryResourceRepository(URI.create("inmem://test"));
    }

    @Bean
    ResourcePool projectResourcePool(ResourcePool inmemResourcePool) {
        return inmemResourcePool;
    }

    @Bean
    @Primary
    UserProvider userProvider() {
        return new InMemoryUserRepository();
    }

    @Bean
    @Primary
    public InMemoryUserKeyProvider userKeyProvider() {
        return new InMemoryUserKeyProvider(new InMemoryUserRepository());
    }

    @Bean
    TestResourcePool testResourcePool(ResourcePool inmemResourcePool) {
        return new TestResourcePool(inmemResourcePool);
    }

    @Bean
    ProjectResourceUploader projectResourceUploader(
            TestResourcePool testResourcePool, ProjectSecurityProvider securityProvider) {

        return new SecureProjectResourceUploader(
                testResourcePool, testResourcePool, securityProvider);
    }

    @Bean
    GrpcAddressService grpcAddressService(DSLContext dslContext) {
        var addressDataService = Mockito.mock(AddressDataService.class);
        var addressRepository = new JooqAddressRepository(dslContext);
        return new GrpcAddressService(addressDataService, addressRepository);
    }
}
