package com.bees360.project.pipeline;

import com.bees360.assignment.Message;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.util.List;

public class AssignmentLossFunctionFactoryTest {

    @SneakyThrows
    @Test
    public void assignCorrectScriptShouldSuccess() {
        String script =
                IOUtils.resourceToString(
                        "auto-assign-test-loss-function-script.js",
                        StandardCharsets.UTF_8,
                        AssignmentLossFunctionFactoryTest.class.getClassLoader());
        var testData =
                List.of(
                        Message.AssignmentMessage.newBuilder()
                                .setUserId("10000")
                                .setCapacity(1)
                                .addAllDifficulty(List.of(1, 2))
                                .build(),
                        Message.AssignmentMessage.newBuilder()
                                .setUserId("10001")
                                .setCapacity(2)
                                .addAllDifficulty(List.of(3, 4))
                                .build());
        var lossFunction = new AssignmentLossFunctionFactory().create(script);
        var result = lossFunction.computeLoss(testData);
        Assertions.assertEquals(4D, result);
    }

    @Test
    public void assignErrorScriptShouldThrowException() {
        String script = "var difficultySum = param.reduce(function(sum, obj) {}";
        var testData =
                List.of(
                        Message.AssignmentMessage.newBuilder()
                                .setUserId("10000")
                                .setCapacity(1)
                                .addAllDifficulty(List.of(1, 2))
                                .build());
        var lossFunction = new AssignmentLossFunctionFactory().create(script);
        Assertions.assertThrows(
                IllegalArgumentException.class, () -> lossFunction.computeLoss(testData));
    }

    @Test
    public void assignScriptReturnNullShouldThrowException() {
        String script = "var difficultySum = 0;";
        var testData =
                List.of(
                        Message.AssignmentMessage.newBuilder()
                                .setUserId("10000")
                                .setCapacity(1)
                                .addAllDifficulty(List.of(1, 2))
                                .build());
        var lossFunction = new AssignmentLossFunctionFactory().create(script);
        Assertions.assertThrows(
                IllegalArgumentException.class, () -> lossFunction.computeLoss(testData));
    }
}
