package com.bees360.project.listener;

import com.bees360.event.registry.ResourceKeyAliasBind;
import com.bees360.job.JobScheduler;
import com.bees360.mail.MailSender;
import com.bees360.mail.util.MailMessageFactory;
import com.bees360.project.executor.ContactUsEmailJobExecutor;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.google.protobuf.ByteString;

import jakarta.annotation.PostConstruct;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.annotation.DirtiesContext;

import java.io.IOException;
import java.util.List;

@SpringBootTest
@DirtiesContext
public class SendContactUsEmailListenerTest {

    @Configuration
    static class Config {}

    @Mock private MailMessageFactory mailMessageFactory;

    @Mock private MailSender mailSender;

    @Mock private JobScheduler jobScheduler;

    @Mock private ResourcePool resourcePool;

    @Mock private List<String> receipts;

    private ContactUsEmailJobExecutor executor;

    private SendContactUsEmailListener listener;

    private Resource resource;

    @PostConstruct
    void init() {
        listener = new SendContactUsEmailListener(jobScheduler, resourcePool);
        executor = new ContactUsEmailJobExecutor(mailMessageFactory, mailSender, receipts);
        resource = generateJson();
        Mockito.when(resourcePool.get(Mockito.anyString())).thenReturn(resource);
    }

    @Test
    void testSendContactUsEmail() throws IOException {
        var alias = "2151da2412da4141442shn1.json";
        var key = "contactus/2141ad4212.json";
        ResourceKeyAliasBind event = new ResourceKeyAliasBind();
        event.setKey(key);
        event.setAlias(alias);

        Assertions.assertTrue(listener.filter(event));
        var job = listener.convert(event);
        Assertions.assertNotNull(job);

        executor.execute(job);
        Mockito.verify(mailSender, Mockito.times(1)).send(Mockito.any());
    }

    Resource generateJson() {
        var content =
                "{\"fullName\":\"Groot\",\"email\":\"<EMAIL>\",\"message\":\"I am"
                        + " Groot!\"}";
        return Resource.of(ByteString.copyFromUtf8(content));
    }
}
