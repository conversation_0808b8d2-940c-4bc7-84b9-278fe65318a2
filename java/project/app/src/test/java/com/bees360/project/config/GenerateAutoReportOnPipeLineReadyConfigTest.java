package com.bees360.project.config;

import static com.bees360.image.RandomImageApiUtil.randomImageApiMessage;
import static com.bees360.project.RandomProjectUtil.randomProject;
import static com.bees360.project.listener.GenerateAutoReportOnPipeLineReady.EVENT_LISTENER_QUEUE_PREFIX;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.bees360.codec.GsonCodec;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.util.PipelineTaskChangedListeners;
import com.bees360.image.ImageApiQueryResponse;
import com.bees360.job.JobScheduler;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.Message;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.RandomProjectUtil;
import com.bees360.project.executor.ProjectAutoReportJobExecutor;
import com.bees360.project.image.ProjectImageApiProvider;
import com.bees360.project.listener.GenerateAutoReportOnPipeLineReady;
import com.bees360.thirdparty.ThirdPartyManager;
import com.google.common.util.concurrent.MoreExecutors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.util.List;

@SpringBootTest
@TestPropertySource(
        properties = {
            "spring.config.location = classpath:application-test.yml",
        })
public class GenerateAutoReportOnPipeLineReadyConfigTest {

    @Configuration
    @Import({
        GenerateAutoReportOnPipeLineReadyConfig.class,
    })
    static class Config {
        @MockBean JobScheduler jobScheduler;

        @Bean
        public InMemoryEventPublisher inMemoryEventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }

        @Bean
        ProjectImageApiProvider projectImageApiProvider() {
            var projectImageApiProvider = Mockito.mock(ProjectImageApiProvider.class);

            var image1 = randomImageApiMessage();
            var image2 = randomImageApiMessage();
            var imageList = List.of(image1, image2);
            var apiResponse =
                    ImageApiQueryResponse.from(
                            com.bees360.image.Message.ImageApiQueryResponse.newBuilder()
                                    .addAllImage(imageList)
                                    .build());
            Mockito.when(projectImageApiProvider.findByProjectId(Mockito.any()))
                    .thenAnswer(e -> apiResponse);
            return projectImageApiProvider;
        }

        @Bean
        ProjectIIManager policyFillProjectIIManager() {
            var policyFillProjectIIManager = Mockito.mock(ProjectIIManager.class);
            when(policyFillProjectIIManager.findById(any()))
                    .thenReturn(
                            ProjectII.from(
                                    Message.ProjectMessage.newBuilder()
                                            .setPolicy(RandomProjectUtil.randomPolicy().toMessage())
                                            .build()));
            return policyFillProjectIIManager;
        }

        @Bean
        ThirdPartyManager thirdPartyManager() {
            return Mockito.mock(ThirdPartyManager.class);
        }

        @Bean
        PipelineService pipelineService() {
            return Mockito.mock(PipelineService.class);
        }
    }

    GsonCodec gson = GsonCodec.INSTANCE;

    @Autowired EventPublisher eventPublisher;

    @Autowired ThirdPartyManager thirdPartyManager;

    @Autowired PipelineService pipelineService;

    @Autowired JobScheduler jobScheduler;

    @Autowired List<GenerateAutoReportOnPipeLineReady> generateAutoReportOnPipeLineReady;

    @Autowired ProjectAutoReportJobExecutor executor;

    @BeforeEach
    void setUp() {
        Mockito.reset(thirdPartyManager);
        Mockito.reset(pipelineService);
        Mockito.reset(jobScheduler);
    }

    @Test
    void testGenerateAutoReportOnPipeLineReadyListenerName() {
        generateAutoReportOnPipeLineReady.forEach(
                listener -> {
                    assertEquals(
                            EVENT_LISTENER_QUEUE_PREFIX + listener.getRoutingKey(),
                            listener.getName());
                });
    }

    @Test
    void testGenerateAutoHLOReportOnPipeLineReady() throws IOException {
        var projectId = randomProject();

        var event = new PipelineTaskChanged();
        event.setTaskDefKey("auto_generate_hlor");
        event.setPipelineId(projectId.getId());

        var state = new PipelineTaskChanged.State();
        state.setStatus(com.bees360.pipeline.Message.PipelineStatus.READY);
        event.setState(state);

        eventPublisher.publish(
                PipelineTaskChangedListeners.getEventName(
                        event.getTaskDefKey(), event.getState().getStatus()),
                gson.encode(event));

        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(any());

        // test generateAutoHLOReport on ProjectAutoReportJobExecutor
        var job = generateAutoReportOnPipeLineReady.get(0).convert(event);
        executor.execute(job);

        Mockito.verify(thirdPartyManager, Mockito.times(1)).send(any());
        Mockito.verify(pipelineService, Mockito.times(1)).setTaskStatus(any(), any(), any(), any());
    }

    @Test
    void testGenerateAutoFSRRCEReportOnPipeLineReady() throws IOException {
        var projectId = randomProject();

        var event = new PipelineTaskChanged();
        event.setTaskDefKey("auto_generate_fsr");
        event.setPipelineId(projectId.getId());

        var state = new PipelineTaskChanged.State();
        state.setStatus(com.bees360.pipeline.Message.PipelineStatus.READY);
        event.setState(state);

        eventPublisher.publish(
                PipelineTaskChangedListeners.getEventName(
                        event.getTaskDefKey(), event.getState().getStatus()),
                gson.encode(event));

        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(any());

        // test generateAutoFSRRCEReport on ProjectAutoReportJobExecutor
        var job = generateAutoReportOnPipeLineReady.get(0).convert(event);
        executor.execute(job);

        Mockito.verify(thirdPartyManager, Mockito.times(1)).send(any());
        Mockito.verify(pipelineService, Mockito.times(1)).setTaskStatus(any(), any(), any(), any());
    }

    @Test
    void testIllegalPipeLineReady() {
        var projectId = randomProject();

        var event = new PipelineTaskChanged();
        event.setTaskDefKey("auto_generate_illegal_report");
        event.setPipelineId(projectId.getId());

        var state = new PipelineTaskChanged.State();
        state.setStatus(com.bees360.pipeline.Message.PipelineStatus.READY);
        event.setState(state);

        eventPublisher.publish(
                PipelineTaskChangedListeners.getEventName(
                        event.getTaskDefKey(), event.getState().getStatus()),
                gson.encode(event));

        Mockito.verify(jobScheduler, Mockito.times(0)).schedule(any());
        Mockito.verify(thirdPartyManager, Mockito.times(0)).send(any());
        Mockito.verify(pipelineService, Mockito.times(0)).setTaskStatus(any(), any(), any(), any());
    }
}
