package com.bees360.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.when;

import com.bees360.address.AddressManager;
import com.bees360.contact.ContactRecord;
import com.bees360.contact.ContactRecordProvider;
import com.bees360.contact.Message.ContactRecordMessage;
import com.bees360.contact.util.ContactRecordUtil.ContactRecordRole;
import com.bees360.hazardhub.AddressEnhancedProperty;
import com.bees360.hazardhub.AddressHazardHubTestUtil;
import com.bees360.hazardhub.AddressReplacementCosts;
import com.bees360.hazardhub.AddressRisks;
import com.bees360.image.Image;
import com.bees360.image.ImageTagManager;
import com.bees360.image.tag.ImageTag;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;
import com.bees360.project.Message.ServiceType;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.RandomProjectUtil;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.image.ProjectImageProvider;
import com.bees360.project.member.Member;
import com.bees360.project.member.MemberManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.project.state.AbstractProjectState;
import com.bees360.project.state.ProjectState;
import com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage;
import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportSummary;
import com.bees360.report.ReportTypeEnum;
import com.bees360.user.Message.UserMessage;
import com.bees360.user.User;
import com.bees360.util.DateTimes;
import com.bees360.util.Functions;
import com.google.common.collect.Maps;
import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest(
        properties =
                "spring.config.location ="
                        + " classpath:application-DefaultIntegrationSummaryProviderTest.yml",
        classes = DefaultIntegrationSummaryProviderTest.Config.class)
@SpringJUnitConfig
@DirtiesContext
class DefaultIntegrationSummaryProviderTest {

    @Configuration
    @Import({
        IntegrationSummaryConfig.class,
    })
    static class Config {

        @MockBean public ProjectIIRepository projectIIRepository;

        @MockBean public ProjectImageProvider projectImageProvider;

        @MockBean public ImageTagManager imageTagManager;

        @MockBean public ProjectReportManager projectReportManager;

        @MockBean public AddressManager addressManager;

        @MockBean public MemberManager memberManager;

        @MockBean public ContactRecordProvider contactRecordProvider;

        @MockBean public ContactManager contactManager;
    }

    @Autowired private ProjectIIRepository projectIIRepository;
    @Autowired private ProjectImageProvider projectImageProvider;
    @Autowired private ImageTagManager imageTagManager;
    @Autowired private ProjectReportProvider projectReportProvider;
    @Autowired private AddressManager addressManager;
    @Autowired private MemberManager memberManager;
    @Autowired private ContactRecordProvider contactRecordProvider;
    @Autowired private ContactManager contactManager;

    private final IntegrationSummaryProvider integrationSummaryProvider;

    @Autowired
    public DefaultIntegrationSummaryProviderTest(
            IntegrationSummaryProvider defaultIntegrationSummaryProvider) {
        this.integrationSummaryProvider = defaultIntegrationSummaryProvider;
    }

    @SneakyThrows
    @Test
    void testCollectSummary() {
        // mock
        var closedTime = Timestamps.parse("2024-07-01T10:00:20.021-05:00");
        var project =
                initProjectSummary(
                        ServiceTypeEnum.FULL_ADJUSTMENT,
                        createProjectState(
                                ProjectStateEnum.PROJECT_CLOSE, "Client request.", closedTime),
                        "with-hazard-hub");

        var projectId = project.getId();
        initSummary(projectId);
        initImage();
        initHazardHubData();
        var pilotUser = createUser("The Pilot");
        mockMembers(
                projectId,
                Map.of(RoleEnum.PILOT, pilotUser, RoleEnum.REVIEWER, createUser("The Reviewer")));

        var agentContact = createContact("Lovegood", ContactRoleEnum.AGENT);
        mockContact(
                projectId, List.of(agentContact, createContact("Luke", ContactRoleEnum.INSURED)));

        var insuredContactRecord = createContactRecord("12345", ContactRecordRole.INSURED);
        var agentContactRecord = createContactRecord("67890", ContactRecordRole.AGENT);
        mockContactRecord(
                projectId,
                List.of(
                        insuredContactRecord,
                        agentContactRecord,
                        createContactRecord("00001", ContactRecordRole.PHD_CENTER)));

        // run...
        var integrationSummary = integrationSummaryProvider.collectByProjectId(projectId);

        // verify
        Assertions.assertNotNull(integrationSummary);
        Assertions.assertNotEquals(
                com.bees360.integration.Message.IntegrationSummaryMessage.ProjectSummary
                        .getDefaultInstance(),
                integrationSummary.getProjectSummary());
        Assertions.assertNotEquals(
                com.bees360.openapi.Message.ReportMessage.Summary.getDefaultInstance(),
                integrationSummary.getReportSummary());
        assertAddress(integrationSummary.getProjectSummary().getAddress());

        var projectSummary = integrationSummary.getProjectSummary();
        assertEquals(
                DateTimes.toTimestamp(project.getInspectionScheduledTime()),
                projectSummary.getInspectionScheduledTime());
        assertEquals(project.getServiceType().getName(), projectSummary.getServiceType());
        assertEquals(pilotUser.toMessage(), projectSummary.getPilot());
        assertEquals(
                List.of(ReportTypeEnum.DAR.getDisplay(), ReportTypeEnum.PIR.getDisplay()),
                projectSummary.getReportTypeList());
        assertEquals("Client request.", projectSummary.getCloseReason());
        assertEquals(closedTime, projectSummary.getClosedTime());
        assertEquals(
                List.of(insuredContactRecord.toMessage(), agentContactRecord.toMessage()),
                projectSummary.getContactRecordList());
        assertEquals(agentContact.toMessage(), projectSummary.getAgent());
    }

    @SneakyThrows
    @Test
    void testCollectSummaryWithoutHazardHub() {
        // mock
        var closedTime = Timestamps.parse("2024-07-02T10:00:20.021-05:00");
        var project =
                initProjectSummary(
                        ServiceTypeEnum.COMMERCIAL_UNDERWRITING,
                        createProjectState(
                                ProjectStateEnum.PROJECT_CLOSE, "Client request.", closedTime),
                        "without-hazard-hub");

        var projectId = project.getId();
        initSummary(projectId);
        initImage();
        initHazardHubData();
        var pilotUser = createUser("The Pilot");
        mockMembers(
                projectId,
                Map.of(RoleEnum.PILOT, pilotUser, RoleEnum.REVIEWER, createUser("The Reviewer")));

        var agentContact = createContact("Lovegood", ContactRoleEnum.AGENT);
        mockContact(
                projectId, List.of(agentContact, createContact("Luke", ContactRoleEnum.INSURED)));

        var insuredContactRecord = createContactRecord("12345", ContactRecordRole.INSURED);
        var agentContactRecord = createContactRecord("67890", ContactRecordRole.AGENT);
        mockContactRecord(
                projectId,
                List.of(
                        insuredContactRecord,
                        agentContactRecord,
                        createContactRecord("00001", ContactRecordRole.PHD_CENTER)));

        // run...
        var integrationSummary = integrationSummaryProvider.collectByProjectId(projectId);

        // verify
        Assertions.assertNotNull(integrationSummary);
        Assertions.assertNotEquals(
                com.bees360.integration.Message.IntegrationSummaryMessage.ProjectSummary
                        .getDefaultInstance(),
                integrationSummary.getProjectSummary());
        Assertions.assertNotEquals(
                com.bees360.openapi.Message.ReportMessage.Summary.getDefaultInstance(),
                integrationSummary.getReportSummary());
        Assertions.assertNotNull(integrationSummary.getProjectSummary().getAddress());
        Assertions.assertEquals(
                com.bees360.address.Message.AddressMessage.HazardHub.getDefaultInstance(),
                integrationSummary.getProjectSummary().getAddress().getHazardHub());
    }

    private User createUser(String userName) {
        return User.from(UserMessage.newBuilder().setName(userName).build());
    }

    private Contact createContact(String name, ContactRoleEnum role) {
        return Contact.from(
                ProjectMessage.Contact.newBuilder()
                        .setRole(role.getName())
                        .setFullName(name)
                        .build());
    }

    private ContactRecord createContactRecord(String phoneName, ContactRecordRole role) {
        return ContactRecord.from(
                ContactRecordMessage.newBuilder()
                        .setPhoneNumber(phoneName)
                        .setContactRecipient(role.name())
                        .build());
    }

    @SneakyThrows
    private ProjectState createProjectState(
            ProjectStateEnum projectState, String changeReasonDisplayText, Timestamp updatedAt) {
        return AbstractProjectState.from(
                ProjectMessage.ProjectState.newBuilder()
                        .setState(projectState)
                        .setUpdatedAt(updatedAt)
                        .setStateChangeReason(
                                ProjectStateChangeReasonMessage.newBuilder()
                                        .setDisplayText(changeReasonDisplayText)
                                        .build())
                        .build());
    }

    private void mockMembers(String projectId, Map<RoleEnum, User> members) {
        var roleToMember =
                Maps.transformEntries(
                        members,
                        (r, u) ->
                                new Member() {
                                    @Override
                                    public User getUser() {
                                        return u;
                                    }

                                    @Override
                                    public String getRole() {
                                        return r.name();
                                    }
                                });
        when(memberManager.findMemberByProjectIdAndRole(anyList(), any(RoleEnum.class)))
                .thenAnswer(
                        args -> {
                            Iterable<String> projectIds = args.getArgument(0);
                            RoleEnum role = args.getArgument(1);
                            var map = new HashMap<String, Member>();
                            for (var id : projectIds) {
                                if (StringUtils.equals(projectId, id)
                                        && roleToMember.containsKey(role)) {
                                    map.put(id, roleToMember.get(role));
                                }
                            }
                            return map;
                        });
    }

    private void mockContactRecord(String projectId, List<ContactRecord> contactRecords) {
        when(contactRecordProvider.findByProjectId(eq(projectId), isNull()))
                .thenAnswer(args -> contactRecords);
    }

    private void mockContact(String projectId, List<Contact> contacts) {
        when(contactManager.findByProjectId(eq(projectId))).thenAnswer(args -> contacts);
    }

    private ProjectII initProjectSummary(
            ServiceTypeEnum serviceType, ProjectState projectState, String insuredBy) {
        var projectBuilder = RandomProjectUtil.randomProject().toMessage().toBuilder();
        projectBuilder.setServiceType(ServiceType.forNumber(serviceType.getCode()));
        projectBuilder.setCurrentState(projectState.toMessage());
        var contractBuilder = projectBuilder.getContract().toBuilder();
        var insuredByBuilder = contractBuilder.getInsuredBy().toBuilder();
        Functions.acceptIfNotNull(insuredByBuilder::setKey, insuredBy);
        contractBuilder.setInsuredBy(insuredByBuilder.build());
        projectBuilder.setContract(contractBuilder.build());

        var finalProject = ProjectII.from(projectBuilder.build());
        when(projectIIRepository.findById(eq(finalProject.getId()))).thenAnswer(e -> finalProject);
        return finalProject;
    }

    private void initSummary(String projectId) {
        var report = Mockito.mock(Report.class);
        var summary = TestIntegrationSummaryUtil.randomReportSummary();
        var reportSummary = Mockito.mock(ReportSummary.class);
        when(projectReportProvider.find(
                        eq(projectId), eq("FUR"), eq(Message.ReportMessage.Status.APPROVED)))
                .thenAnswer(e -> List.of(report));
        when(report.getSummary()).thenAnswer(e -> reportSummary);
        when(reportSummary.getSummary()).thenAnswer(e -> summary.getSummary());
    }

    private void initImage() {
        var image = Mockito.mock(Image.class);
        when(projectImageProvider.findByProjectId(Mockito.any())).thenAnswer(e -> List.of(image));
        var imageMessage = TestIntegrationSummaryUtil.randomImage();
        when(image.toMessage()).thenAnswer(e -> imageMessage);
        var tag = Mockito.mock(ImageTag.class);
        when(imageTagManager.findByImageIds(Mockito.any()))
                .thenAnswer(e -> Map.of(RandomStringUtils.randomAlphabetic(12), List.of(tag)));
    }

    private void initHazardHubData() {
        var hazardHub = AddressHazardHubTestUtil.getStaticHazardHub();
        when(addressManager.findAddressRisks(Mockito.any()))
                .thenAnswer(
                        e ->
                                AddressRisks.AddressRisksBuilder.newBuilder()
                                        .setRisksJson(hazardHub.getRisksJson())
                                        .build());
        when(addressManager.findAddressEnhancedProperty(Mockito.any()))
                .thenAnswer(
                        e ->
                                AddressEnhancedProperty.AddressEnhancedPropertyBuilder.newBuilder()
                                        .setEnhancedPropertyJson(
                                                hazardHub.getEnhancedPropertyJson())
                                        .build());
        when(addressManager.findAddressReplacementCosts(Mockito.any()))
                .thenAnswer(
                        e ->
                                AddressReplacementCosts.AddressReplacementCostsBuilder.newBuilder()
                                        .setReplacementCostsJson(
                                                hazardHub.getReplacementCostsJson())
                                        .build());
    }

    private void assertAddress(com.bees360.address.Message.AddressMessage address) {
        Assertions.assertNotNull(address);
        var hazardHub = address.getHazardHub();
        Assertions.assertTrue(StringUtils.isNotEmpty(hazardHub.getRisksJson()));
        Assertions.assertTrue(StringUtils.isNotEmpty(hazardHub.getEnhancedPropertyJson()));
        Assertions.assertTrue(StringUtils.isNotEmpty(hazardHub.getReplacementCostsJson()));
    }
}
