package com.bees360.project.config.mail;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyIterable;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.activity.CommentManager;
import com.bees360.address.Address;
import com.bees360.address.AddressManager;
import com.bees360.address.Message.AddressMessage;
import com.bees360.contract.Contract;
import com.bees360.contract.JooqContractRepository;
import com.bees360.contract.Message.ContractMessage;
import com.bees360.customer.Customer;
import com.bees360.customer.JooqCustomerRepository;
import com.bees360.customer.Message.CustomerMessage;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.SendCustomerProjectEmailOnQuestionnaireRenovationSubmitted;
import com.bees360.event.registry.BeespilotProjectQuestionnaireRenovationSubmittedEvent;
import com.bees360.event.registry.BeespilotProjectQuestionnaireRenovationSubmittedEvent.Question;
import com.bees360.event.registry.ProjectCanceledEvent;
import com.bees360.event.registry.ProjectClientReceivedEvent;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.event.registry.ProjectReportAdded;
import com.bees360.event.registry.ProjectReturnedToClientEvent;
import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.mail.MailMessage;
import com.bees360.mail.MailSender;
import com.bees360.mail.MailSenderProvider;
import com.bees360.mail.Message;
import com.bees360.policy.JooqPolicyRepository;
import com.bees360.policy.Message.PolicyMessage;
import com.bees360.policy.Policy;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.JooqProjectIIRepository;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.Message.ServiceType;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.base.Message.ProjectType;
import com.bees360.project.config.ProjectRequestCancelConfig;
import com.bees360.project.member.Member;
import com.bees360.project.member.MemberManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.notification.CustomerProjectEmailProperties;
import com.bees360.project.notification.ProjectEmailContextProjectCancelled;
import com.bees360.project.state.AbstractProjectState;
import com.bees360.project.state.JooqProjectStateManager;
import com.bees360.project.state.ProjectState;
import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.report.ReportProvider;
import com.bees360.report.util.InMemoryReport;
import com.bees360.user.Message.UserMessage;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.BoolValue;

import jakarta.annotation.PostConstruct;

import lombok.SneakyThrows;

import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

@SpringBootTest(
        classes = {
            CustomerProjectEmailConfig.class,
            CustomerProjectEmailConfigTest.Config.class,
        })
@ActiveProfiles({"CustomerProjectEmailConfigTest", "test"})
@EnableConfigurationProperties
@ImportAutoConfiguration(RefreshAutoConfiguration.class)
class CustomerProjectEmailConfigTest {

    @Configuration
    @Import(ProjectRequestCancelConfig.class)
    static class Config {

        @Bean
        InMemoryEventPublisher inMemoryEventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }

        @Bean
        InMemoryJobScheduler inMemoryJobScheduler() {
            return new InMemoryJobScheduler(MoreExecutors.directExecutor());
        }

        @Bean
        UserProvider userProvider() {
            var userProvider = Mockito.mock(UserProvider.class);
            when(userProvider.getUser(anyString()))
                    .thenAnswer(
                            arg -> {
                                String userId = arg.getArgument(0);
                                return User.from(
                                        UserMessage.newBuilder()
                                                .setId(userId)
                                                .setName("Bob")
                                                .setEmail("<EMAIL>")
                                                .build());
                            });
            when(userProvider.findUserByEmail(anyString()))
                    .thenAnswer(
                            arg -> {
                                String userId = arg.getArgument(0);
                                return User.from(
                                        UserMessage.newBuilder()
                                                .setId(userId)
                                                .setName("Bob")
                                                .setEmail("<EMAIL>")
                                                .build());
                            });
            return userProvider;
        }

        @MockBean CommentManager commentManager;

        @Bean
        MemberManager memberManager() {
            var manager = Mockito.mock(MemberManager.class);
            var user = User.from(UserMessage.newBuilder().setEmail(EMAIL_CREATOR).build());
            Function<String, Member> memberCreator =
                    role ->
                            new Member() {
                                @Override
                                public User getUser() {
                                    return user;
                                }

                                @Override
                                public String getRole() {
                                    return role;
                                }
                            };
            when(manager.findMemberByProjectIdAndRole(anyIterable(), any(RoleEnum.class)))
                    .thenAnswer(
                            args -> {
                                Iterable<String> projectIds = args.getArgument(0);
                                RoleEnum role = args.getArgument(1);
                                var projectId = projectIds.iterator().next();
                                if (StringUtils.equalsAny(
                                        projectId,
                                        CLAIM_PROJECT_ID_WITH_CUSTOMER_FAKE_TWO,
                                        UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR)) {
                                    return Map.of(projectId, memberCreator.apply(role.getValue()));
                                }
                                return Map.of();
                            });
            return manager;
        }

        @Bean
        ContactManager contactManager() {
            var mgr = Mockito.mock(ContactManager.class);
            var adjuster =
                    Contact.ContactBuilder.newBuilder()
                            .setPrimaryEmail(EMAIL_CONTACT_ADJUSTER)
                            .setRole("adjuster")
                            .build();
            var claimDirector =
                    Contact.ContactBuilder.newBuilder()
                            .setPrimaryEmail(EMAIL_CONTACT_CLAIM_DIRECTOR)
                            .setRole("Claim Director")
                            .build();
            var insured =
                    Contact.ContactBuilder.newBuilder()
                            .setRole(ContactRoleEnum.INSURED.getName())
                            .setFullName("The insured")
                            .build();
            Iterable<? extends Contact> contacts = List.of(adjuster, claimDirector, insured);
            when(mgr.findByProjectId(anyString())).thenAnswer(args -> contacts);
            return mgr;
        }

        @Bean
        AddressManager addressManager() {
            var addressManager = Mockito.mock(AddressManager.class);
            var address =
                    Address.from(
                            AddressMessage.newBuilder().setAddress("This is address.").build());
            doReturn(address).when(addressManager).findById(anyString());
            return addressManager;
        }

        @Bean
        ReportProvider reportProvider() {
            var reportProvider = Mockito.mock(ReportProvider.class);
            var ICR_REPORTID = "ICR_REPORTID";
            var ICRReport = new InMemoryReport();
            ICRReport.setId(ICR_REPORTID);
            ICRReport.setType("19");
            Mockito.doReturn(ICRReport).when(reportProvider).get(ICR_REPORTID);

            var report = new InMemoryReport();
            var MP_REPORTID = "MP_REPORTID";
            report.setId(MP_REPORTID);
            report.setType("26");
            Mockito.doReturn(report).when(reportProvider).get(MP_REPORTID);
            return reportProvider;
        }

        @Bean
        @Primary
        MailSenderProvider primaryMailSenderProvider(MailSenderProvider mailSenderProvider) {
            return new SpyableMailSenderProvider(mailSenderProvider);
        }

        @SpyBean JooqProjectIIRepository jooqProjectIIRepository;

        @Bean("grpcProjectManager")
        ProjectIIManager grpcProjectManager(JooqProjectIIRepository jooqProjectIIRepository) {
            return jooqProjectIIRepository;
        }

        @Bean("grpcProjectStateManager")
        ProjectStateManager grpcProjectStateManager(
                JooqProjectStateManager jooqProjectStateManager) {
            return jooqProjectStateManager;
        }
    }

    private static final String EMAIL_CREATOR = "<EMAIL>";

    private static final String EMAIL_CONTACT_ADJUSTER = "<EMAIL>";

    private static final String EMAIL_CONTACT_CLAIM_DIRECTOR = "<EMAIL>";

    private static final String EMAIL_CLIENT_DESIGNATED = "<EMAIL>";

    private static final String PROJECT_ID_WITHOUT_CREATOR = "10012";

    private static final String CLAIM_PROJECT_ID_WITH_CUSTOMER_FAKE_TWO = "10014";

    private static final String PROJECT_ID_WITHOUT_EMAIL_PROPERTIES = "10016";

    private static final String UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR = "10019";
    private static final String CLAIM_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR = "10020";

    private static final String UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_SCHEDULING_ONLY = "10021";
    private static final String CLAIM_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_SCHEDULING_ONLY = "10022";

    private static final String UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_REQUEST_CANCEL_ONLY =
            "10023";

    private static final String CLAIM_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_REQUEST_CANCEL_ONLY =
            "10024";

    private static final String NOT_PAUSE_REASON = "NOT PAUSE REASON";

    private static final String CANCEL_REASON = "CANCEL REASON";

    private static final String BEES360_REASON = "bees360 reason";

    private static final String OPEN_REASON = "open reason";

    @Autowired private EventPublisher eventPublisher;

    @SpyBean MailSenderProvider mailSenderProvider;

    @Autowired JooqProjectIIRepository jooqProjectIIRepository;

    @Autowired ProjectRequestCancelConfig.RequestCancelProperties requestCancelProperties;

    @SpyBean
    @Qualifier("jooqProjectStateManager")
    ProjectStateManager projectStateManager;

    @Autowired ProjectStateChangeReasonManager projectStateChangeReasonManager;

    @Autowired
    @Qualifier("customerProjectEmailsPropertiesProvider")
    BiFunction<String, String, CustomerProjectEmailProperties>
            customerProjectEmailsPropertiesProvider;

    @SpyBean JooqCustomerRepository jooqCustomerRepository;

    @SpyBean JooqPolicyRepository jooqPolicyRepository;

    @SpyBean JooqContractRepository jooqContractRepository;

    @PostConstruct
    void postConstruct() {
        setUpProjectIIRepository(jooqProjectIIRepository);
        setUpCustomerRepository(jooqCustomerRepository);
        setUpPolicyRepository(jooqPolicyRepository);
        when(jooqContractRepository.findById(anyString()))
                .thenReturn(
                        Contract.from(
                                ContractMessage.newBuilder()
                                        .setInsuredBy(
                                                CustomerMessage.newBuilder()
                                                        .setName("The Customer"))
                                        .build()));
    }

    private void setUpPolicyRepository(JooqPolicyRepository repository) {
        var policy =
                Policy.from(
                        PolicyMessage.newBuilder()
                                .setPolicyNo("POLICYNUMBER")
                                .setAddress(AddressMessage.newBuilder().setId("10001").build())
                                .build());
        when(repository.findById(anyString())).thenReturn(policy);
    }

    void setUpCustomerRepository(JooqCustomerRepository repository) {
        var idMapKey =
                Map.of(
                        "1", "Frankenmuth Insurance",
                        "2", "Fake Two",
                        "3", "Fake Three",
                        "4", "Fake With Email Selector",
                        "5", "Fake With Request Cancel Email Selector");
        Answer<Customer> answer =
                arg -> {
                    String id = arg.getArgument(0);
                    var key = idMapKey.get(id);
                    if (key == null) {
                        return null;
                    }
                    return Customer.of(CustomerMessage.newBuilder().setId(id).setKey(key).build());
                };
        when(repository.findById(anyString())).thenAnswer(answer);
    }

    void setUpProjectIIRepository(ProjectIIRepository repository) {

        var projectCustomerMap =
                Map.of(
                        PROJECT_ID_WITHOUT_CREATOR,
                        "1",
                        CLAIM_PROJECT_ID_WITH_CUSTOMER_FAKE_TWO,
                        "2",
                        PROJECT_ID_WITHOUT_EMAIL_PROPERTIES,
                        "3",
                        UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR,
                        "4",
                        CLAIM_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR,
                        "4",
                        UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_SCHEDULING_ONLY,
                        "4",
                        CLAIM_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_SCHEDULING_ONLY,
                        "4",
                        UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_REQUEST_CANCEL_ONLY,
                        "5",
                        CLAIM_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_REQUEST_CANCEL_ONLY,
                        "5");
        Answer<ProjectII> answer =
                args -> {
                    String projectId = args.getArgument(0);
                    var customerId = projectCustomerMap.get(projectId);
                    var customer =
                            CustomerMessage.newBuilder()
                                    .setId(customerId)
                                    .setKey("mock_company")
                                    .build();
                    var contract =
                            ContractMessage.newBuilder()
                                    .setId("9901")
                                    .setInsuredBy(customer)
                                    .build();
                    var createdBy = UserMessage.newBuilder().setId("20025").build();
                    var policy =
                            PolicyMessage.newBuilder()
                                    .setId(projectId)
                                    .setAddress(
                                            AddressMessage.newBuilder()
                                                    .setAddress("A full address"))
                                    .build();
                    var projectType = ProjectType.CLAIM_TYPE;
                    var serviceType = ServiceType.FULL_ADJUSTMENT;
                    if (Long.parseLong(projectId) % 2 == 1) {
                        projectType = ProjectType.UNDERWRITING_TYPE;
                        serviceType = ServiceType.ROOF_ONLY_UNDERWRITING;
                    }
                    if (StringUtils.equalsAny(
                            projectId,
                            UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_SCHEDULING_ONLY,
                            CLAIM_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_SCHEDULING_ONLY)) {
                        serviceType = ServiceType.SCHEDULING_ONLY;
                    }

                    var projectState =
                            ProjectMessage.ProjectState.newBuilder()
                                    .setComment("bees360 reason")
                                    .setStateChangeReason(
                                            com.bees360.project.statechangereason.Message
                                                    .ProjectStateChangeReasonMessage.newBuilder()
                                                    .setDisplayText(CANCEL_REASON)
                                                    .setKey(CANCEL_REASON)
                                                    .build());

                    return ProjectII.from(
                            ProjectMessage.newBuilder()
                                    .setId(projectId)
                                    .setServiceType(serviceType)
                                    .setProjectType(projectType)
                                    .setContract(contract)
                                    .setCreateBy(createdBy)
                                    .setPolicy(policy)
                                    .setCurrentState(projectState)
                                    .build());
                };
        when(repository.findById(anyString())).thenAnswer(answer);
    }

    @SneakyThrows
    @Test
    void testShouldNoMailSendIfWithoutProjectEmailsPropertiesConfig() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");

        var event = new ProjectCreatedEvent();
        event.setProjectId(PROJECT_ID_WITHOUT_EMAIL_PROPERTIES);
        eventPublisher.publish(event);
        verify(clientMailSender, times(0)).send(any(MailMessage.class));
    }

    @SneakyThrows
    @Test
    void testShouldNoMailSendIfWithoutMemberFound() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");

        var event = new ProjectCreatedEvent();
        event.setProjectId(PROJECT_ID_WITHOUT_CREATOR);
        eventPublisher.publish(event);
        verify(clientMailSender, times(0)).send(any(MailMessage.class));
    }

    @Test
    void testSkipEmailIfNotSubscribed() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");

        var returned = new ProjectClientReceivedEvent();
        returned.setProjectId(CLAIM_PROJECT_ID_WITH_CUSTOMER_FAKE_TWO);
        eventPublisher.publish(returned);

        verify(clientMailSender, times(0)).send(any(MailMessage.class));
    }

    @SneakyThrows
    @Test
    void testWithCustomerTemplateKeyAndAdditionalToCcBcc() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");

        var event = new ProjectCreatedEvent();
        event.setProjectId(UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR);
        eventPublisher.publish(event);

        ArgumentCaptor<MailMessage> argument = ArgumentCaptor.forClass(MailMessage.class);
        verify(clientMailSender).send(argument.capture());

        var expectedVariables =
                "{'address':'This is address.',"
                        + "'creatorEmail':'<EMAIL>',"
                        + "'creatorName':'Bob',"
                        + "'projectId':'10019',"
                        + "'_trigger':{}}";
        var expected =
                Message.MailMessage.newBuilder()
                        .setTemplateKey("new_project_created")
                        .addRecipients(EMAIL_CREATOR)
                        .addRecipients(EMAIL_CLIENT_DESIGNATED)
                        .addCc(EMAIL_CONTACT_CLAIM_DIRECTOR)
                        .addBcc(EMAIL_CLIENT_DESIGNATED)
                        .setVariablesJson(expectedVariables.replace('\'', '"'))
                        .build();

        assertMailMessageEquals(expected, argument.getValue().toMessage());
    }

    @Test
    void testSendProjectCompletedEmailWhenUWReturnedToClient() {
        var noReplyMailSender = Mockito.mock(MailSender.class);
        doReturn(noReplyMailSender).when(mailSenderProvider).get("no-reply-sender");

        var returned = new ProjectReturnedToClientEvent();
        returned.setProjectId(UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR);
        eventPublisher.publish(returned);

        ArgumentCaptor<MailMessage> argument = ArgumentCaptor.forClass(MailMessage.class);
        verify(noReplyMailSender).send(argument.capture());

        var expectedVariableJson =
                "{'address':'This is address.',"
                        + "'policyNumber':'POLICYNUMBER',"
                        + "'processedBy':{'name':''},"
                        + "'projectId':'10019',"
                        + "'insuredBy':{'name':'The Customer'},"
                        + "'username':'Bob',"
                        + "'insuredName':'The insured',"
                        + "'_trigger':{}}";
        var expected =
                Message.MailMessage.newBuilder()
                        .setTemplateKey("project_completed_underwriting")
                        .addRecipients(EMAIL_CREATOR)
                        .setVariablesJson(expectedVariableJson.replace('\'', '"'))
                        .build();

        assertMailMessageEquals(expected, argument.getValue().toMessage());
    }

    @Test
    void testSendProjectCompletedEmailWhenClaimClientReceived() {
        var noReplyMailSender = Mockito.mock(MailSender.class);
        doReturn(noReplyMailSender).when(mailSenderProvider).get("no-reply-sender");

        var returned = new ProjectClientReceivedEvent();
        returned.setProjectId(CLAIM_PROJECT_ID_WITH_CUSTOMER_FAKE_TWO);
        eventPublisher.publish(returned);

        ArgumentCaptor<MailMessage> argument = ArgumentCaptor.forClass(MailMessage.class);
        verify(noReplyMailSender).send(argument.capture());

        var expectedVariableJson =
                "{'address':'This is address.',"
                        + "'policyNumber':'POLICYNUMBER',"
                        + "'processedBy':{'name':''},"
                        + "'projectId':'10014',"
                        + "'insuredBy':{'name':'The Customer'},"
                        + "'username':'Bob',"
                        + "'insuredName':'The insured',"
                        + "'_trigger':{}}";
        var expected =
                Message.MailMessage.newBuilder()
                        .setTemplateKey("project_completed_claim")
                        .addRecipients(EMAIL_CLIENT_DESIGNATED)
                        .addRecipients(EMAIL_CREATOR)
                        .addCc(EMAIL_CONTACT_CLAIM_DIRECTOR)
                        .setVariablesJson(expectedVariableJson.replace('\'', '"'))
                        .build();

        assertMailMessageEquals(expected, argument.getValue().toMessage());
    }

    @Test
    void testSchedulingOnlyWontSendProjectCompletedEmailWhenClaimClientReceived() {
        var noReplyMailSender = Mockito.mock(MailSender.class);
        doReturn(noReplyMailSender).when(mailSenderProvider).get("no-reply-sender");

        var returned = new ProjectClientReceivedEvent();
        returned.setProjectId(CLAIM_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_SCHEDULING_ONLY);
        eventPublisher.publish(returned);

        verify(noReplyMailSender, times(0)).send(any(MailMessage.class));
    }

    @Test
    void testSchedulingOnlyWontSendProjectCompletedEmailWhenUWReturnedToClient() {
        var noReplyMailSender = Mockito.mock(MailSender.class);
        doReturn(noReplyMailSender).when(mailSenderProvider).get("no-reply-sender");

        var returned = new ProjectReturnedToClientEvent();
        returned.setProjectId(UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_SCHEDULING_ONLY);
        eventPublisher.publish(returned);

        verify(noReplyMailSender, times(0)).send(any(MailMessage.class));
    }

    @Test
    void testOverrideDefaultSelector() {
        var emailProperties =
                customerProjectEmailsPropertiesProvider.apply("TWFG", "fake_email_topic");

        assertEquals(1, emailProperties.getToSelector().size());
        var selector = emailProperties.getToSelector().iterator().next();
        assertEquals("email", selector.getType());
        assertEquals("<EMAIL>", selector.getEmail().getEmail());

        assertEquals(1, emailProperties.getCcSelector().size());
        selector = emailProperties.getCcSelector().iterator().next();
        assertEquals("member", selector.getType());
        assertEquals("CREATOR", selector.getMember().getRole());

        assertEquals(1, emailProperties.getBccSelector().size());
        selector = emailProperties.getBccSelector().iterator().next();
        assertEquals("contact", selector.getType());
        assertEquals("Underwriting Director", selector.getContact().getRole());
    }

    @Test
    void testSendCustomerProjectEmailOnQuestionnaireRenovationSubmittedHomeowner() {
        testSendCustomerProjectEmailOnQuestionnaireRenovationSubmitted(
                "homeowner", "project_renovation_questionnaire_homeowner", true, true);
    }

    @Test
    void testSendCustomerProjectEmailOnQuestionnaireRenovationSubmittedPilot() {
        testSendCustomerProjectEmailOnQuestionnaireRenovationSubmitted(
                "pilot", "project_renovation_questionnaire_pilot", true, true);
    }

    @Test
    void testSendCustomerProjectEmailOnQuestionnaireRenovationSubmittedUnknown() {
        testSendCustomerProjectEmailOnQuestionnaireRenovationSubmitted("unknown", "", true, false);
    }

    @Test
    void testSendCustomerProjectEmailOnQuestionnaireRenovationSubmittedNotAlert() {
        testSendCustomerProjectEmailOnQuestionnaireRenovationSubmitted(
                "homeowner", "", false, false);
    }

    @Test
    void testSendCustomerProjectEmailOnQuestionnaireRenovationSubmittedFailIfNoQuestions() {

        var noReplyMailSender = Mockito.mock(MailSender.class);
        doReturn(noReplyMailSender).when(mailSenderProvider).get("no-reply-sender");

        var event =
                new BeespilotProjectQuestionnaireRenovationSubmittedEvent()
                        .setProjectId(PROJECT_ID_WITHOUT_CREATOR)
                        .setSubmitter("homeowner")
                        .setData(
                                Map.of(
                                        "renovationsAlert",
                                        true,
                                        "renovationsAlertFake",
                                        "Fake Value"))
                        // Deliberately changing the order of the questions
                        .setQuestions(List.of());

        eventPublisher.publish(event);
        verify(noReplyMailSender, never()).send(any(MailMessage.class));
    }

    @Test
    void testSendCustomerProjectEmailOnProjectStateChangedEventFailIfNotProjectPause() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");

        var currentState =
                AbstractProjectState.from(
                        com.bees360.project.Message.ProjectMessage.ProjectState.newBuilder()
                                .setState(
                                        ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE)
                                .build());
        var event = new ProjectStateChangedEvent();
        event.setCurrentState(currentState);
        eventPublisher.publish(event);

        verify(clientMailSender, never()).send(any());
    }

    @Test
    void testSendCustomerProjectEmailOnProjectStateChangedEventFailIfNotPauseReason() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");

        var stateChangedReason =
                com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage
                        .newBuilder()
                        .setDisplayText(NOT_PAUSE_REASON)
                        .setKey(NOT_PAUSE_REASON)
                        .build();
        var curState =
                AbstractProjectState.from(
                        ProjectMessage.ProjectState.newBuilder()
                                .setState(
                                        ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_PAUSE)
                                .setStateChangeReason(stateChangedReason)
                                .build());
        var event = new ProjectStateChangedEvent();
        event.setCurrentState(curState);
        eventPublisher.publish(event);

        verify(clientMailSender, never()).send(any());
    }

    @Test
    void testSendCustomerProjectEmailOnProjectStateChangedEvent() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("no-reply-sender");
        mockProjectHistoryState();
        var stateChangedReason =
                com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage
                        .newBuilder()
                        .setDisplayText(requestCancelProperties.getPendingReason())
                        .build();
        var curState =
                AbstractProjectState.from(
                        ProjectMessage.ProjectState.newBuilder()
                                .setState(
                                        ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_PAUSE)
                                .setStateChangeReason(stateChangedReason)
                                .setComment(BEES360_REASON)
                                .build());
        var event = new ProjectStateChangedEvent();
        event.setCurrentState(curState);
        event.setProjectId(
                Long.parseLong(UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_REQUEST_CANCEL_ONLY));
        eventPublisher.publish(event);

        ArgumentCaptor<MailMessage> argument = ArgumentCaptor.forClass(MailMessage.class);
        verify(clientMailSender).send(argument.capture());

        var variableJson =
                "{'reason':'bees360 reason','address':'This is"
                    + " address.','policyNumber':'POLICYNUMBER','operatorName':'Bob','projectId':'10023','insuredBy':{'name':'The"
                    + " Customer'},'_trigger':{}, 'insuredName': 'The insured'}";

        var expected =
                Message.MailMessage.newBuilder()
                        .setTemplateKey(
                                ProjectEmailContextProjectCancelled
                                        .PROJECT_EMAIL_KEY_PROJECT_REQUEST_CANCEL_UNDERWRTING)
                        .addRecipients("<EMAIL>")
                        .addAllCc(List.of("<EMAIL>"))
                        .addBcc("<EMAIL>")
                        .setVariablesJson(variableJson.replace('\'', '"'))
                        .build();
        assertMailMessageEquals(expected, argument.getValue().toMessage());
    }

    @Test
    void testSendCustomerProjectEmailOnUnPausedEventFailIfNotUnpauseReason() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");

        var stateChangedReason =
                com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage
                        .newBuilder()
                        .setDisplayText(NOT_PAUSE_REASON)
                        .setKey(NOT_PAUSE_REASON)
                        .build();
        var curState =
                AbstractProjectState.from(
                        ProjectMessage.ProjectState.newBuilder()
                                .setState(ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN)
                                .setStateChangeReason(stateChangedReason)
                                .build());
        var event = new ProjectStateChangedEvent();
        event.setCurrentState(curState);
        event.setProjectId(
                Long.parseLong(UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_REQUEST_CANCEL_ONLY));
        eventPublisher.publish(event);

        verify(clientMailSender, never()).send(any());
    }

    void testSendCustomerProjectEmailOnUnPausedEvent() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");
        mockProjectHistoryState();
        var stateChangedReason =
                com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage
                        .newBuilder()
                        .setDisplayText(requestCancelProperties.getRevertCancelReason())
                        .setKey(requestCancelProperties.getRevertCancelReason())
                        .build();
        var curState =
                AbstractProjectState.from(
                        ProjectMessage.ProjectState.newBuilder()
                                .setState(ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN)
                                .setStateChangeReason(stateChangedReason)
                                .build());
        var event = new ProjectStateChangedEvent();
        event.setCurrentState(curState);
        event.setProjectId(
                Long.parseLong(UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_REQUEST_CANCEL_ONLY));
        eventPublisher.publish(event);

        ArgumentCaptor<MailMessage> argument = ArgumentCaptor.forClass(MailMessage.class);
        verify(clientMailSender).send(argument.capture());

        var variableJson =
                "{'reason':'bees360 reason','address':'This is"
                    + " address.','policyNumber':'POLICYNUMBER','operatorName':'Bob','projectId':'10024','insuredBy':{'name':'The"
                    + " Customer'},'_trigger':{}}";

        var expected =
                Message.MailMessage.newBuilder()
                        .setTemplateKey(
                                ProjectEmailContextProjectCancelled
                                        .PROJECT_EMAIL_KEY_PROJECT_REVERT_CANCEL_CLAIM)
                        .addRecipients("<EMAIL>")
                        .addAllCc(List.of("<EMAIL>", "<EMAIL>"))
                        .addBcc("<EMAIL>")
                        .setVariablesJson(variableJson.replace('\'', '"'))
                        .build();
    }

    @Test
    void testSendCustomerProjectEmailOnCloseoutReportGeneratedFailIfNotReport() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");

        var event = new ProjectReportAdded();
        event.setReportId("no_report_id");
        eventPublisher.publish(event);
        verify(clientMailSender, never()).send(any());
    }

    @Test
    void testSendCustomerProjectEmailOnCloseoutReportGeneratedFailIfNotICRReport() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("client-mail-sender");

        var event = new ProjectReportAdded();
        event.setReportId("MP_REPORTID");
        eventPublisher.publish(event);
        verify(clientMailSender, never()).send(any());
    }

    @Test
    void testSendCustomerProjectEmailOnProjectCanceled() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("no-reply-sender");
        mockProjectHistoryState();

        var event = new ProjectCanceledEvent();
        event.setProjectId(CLAIM_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_REQUEST_CANCEL_ONLY);
        event.setStatus(110);

        eventPublisher.publish(event);

        ArgumentCaptor<MailMessage> argument = ArgumentCaptor.forClass(MailMessage.class);

        verify(clientMailSender).send(argument.capture());

        var variableJson =
                "{'reason':'CANCEL REASON','address':'This is"
                    + " address.','policyNumber':'POLICYNUMBER','operatorName':'Bob','projectId':'10024','insuredBy':{'name':'The"
                    + " Customer'},'_trigger':{}, 'insuredName': 'The insured'}'";

        var expected =
                Message.MailMessage.newBuilder()
                        .setTemplateKey(
                                ProjectEmailContextProjectCancelled
                                        .PROJECT_EMAIL_KEY_PROJECT_CANCELLED_CLAIM)
                        .addRecipients("<EMAIL>")
                        .addAllCc(List.of("<EMAIL>", "<EMAIL>"))
                        .addBcc("<EMAIL>")
                        .setVariablesJson(variableJson.replace('\'', '"'))
                        .build();
        assertMailMessageEquals(expected, argument.getValue().toMessage());
    }

    @Test
    void testSendCustomerProjectEmailOnCloseoutReportGenerated() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("no-reply-sender");

        mockProjectHistoryState();

        var event = new ProjectReportAdded();
        event.setGroupKey(UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR_REQUEST_CANCEL_ONLY);
        event.setReportId("ICR_REPORTID");
        eventPublisher.publish(event);

        ArgumentCaptor<MailMessage> argument = ArgumentCaptor.forClass(MailMessage.class);
        verify(clientMailSender).send(argument.capture());

        var variableJson =
                "{'reason':'CANCEL REASON','address':'This is"
                    + " address.','policyNumber':'POLICYNUMBER','operatorName':'Bob','projectId':'10023','insuredBy':{'name':'The"
                    + " Customer'},'_trigger':{}, 'insuredName': 'The insured'}";

        var expected =
                Message.MailMessage.newBuilder()
                        .setTemplateKey(
                                ProjectEmailContextProjectCancelled
                                        .PROJECT_EMAIL_KEY_PROJECT_CANCELLED_UNDERWRTING)
                        .addRecipients("<EMAIL>")
                        .addAllCc(List.of("<EMAIL>"))
                        .addBcc("<EMAIL>")
                        .setVariablesJson(variableJson.replace('\'', '"'))
                        .build();
        assertMailMessageEquals(expected, argument.getValue().toMessage());
    }

    /**
     * Testing {@link SendCustomerProjectEmailOnQuestionnaireRenovationSubmitted} and {@link
     * BeespilotProjectQuestionnaireRenovationSubmittedEvent}
     */
    void testSendCustomerProjectEmailOnQuestionnaireRenovationSubmitted(
            String submitter,
            String projectEmailKey,
            boolean renovationsAlert,
            boolean expecteSendMail) {

        var noReplyMailSender = Mockito.mock(MailSender.class);
        doReturn(noReplyMailSender).when(mailSenderProvider).get("no-reply-sender");

        var question1 = new Question().setIndex(1).setQuestionTitle("Q1").setAnswerText("A1");
        var question2 = new Question().setIndex(2).setQuestionTitle("Q2").setAnswerText("A2");

        var event =
                new BeespilotProjectQuestionnaireRenovationSubmittedEvent()
                        .setProjectId(PROJECT_ID_WITHOUT_CREATOR)
                        .setSubmitter(submitter)
                        .setData(
                                Map.of(
                                        "renovationsAlert",
                                        renovationsAlert,
                                        "renovationsAlertFake",
                                        "Fake Value"))
                        // Deliberately changing the order of the questions
                        .setQuestions(List.of(question2, question1));

        eventPublisher.publish(event);
        if (!expecteSendMail) {
            verify(noReplyMailSender, never()).send(any(MailMessage.class));
            return;
        }

        ArgumentCaptor<MailMessage> argument = ArgumentCaptor.forClass(MailMessage.class);
        verify(noReplyMailSender).send(argument.capture());

        var variableJson =
                "{'address':'This is address.',"
                        + "'policyNumber':'POLICYNUMBER',"
                        + "'projectId':'10012','insuredBy':{'name':'The Customer'},"
                        + "'_trigger':{'questions':["
                        + "{'index':1,'question_title':'Q1','answer_text':'A1'},"
                        + "{'index':2,'question_title':'Q2','answer_text':'A2'}]}"
                        + "}";

        var expected =
                Message.MailMessage.newBuilder()
                        .setTemplateKey(projectEmailKey)
                        .addRecipients("<EMAIL>")
                        .setVariablesJson(variableJson.replace('\'', '"'))
                        .build();

        assertMailMessageEquals(expected, argument.getValue().toMessage());
    }

    @SneakyThrows
    private void assertMailMessageEquals(Message.MailMessage expected, Message.MailMessage actual) {
        assertEquals(expected.getTemplateKey(), actual.getTemplateKey());
        assertEquals(
                Sets.newHashSet(expected.getRecipientsList()),
                Sets.newHashSet(actual.getRecipientsList()));
        assertEquals(Sets.newHashSet(expected.getCcList()), Sets.newHashSet(actual.getCcList()));
        assertEquals(Sets.newHashSet(expected.getBccList()), Sets.newHashSet(actual.getBccList()));
        JSONAssert.assertEquals(expected.getVariablesJson(), actual.getVariablesJson(), true);
    }

    private void mockProjectHistoryState() {
        var state = Mockito.mock(ProjectState.class);
        var projectStateChangeReason = Mockito.mock(ProjectStateChangeReason.class);
        when(projectStateChangeReason.getDisplayText())
                .thenReturn(requestCancelProperties.getPendingReason());

        when(state.getState())
                .thenReturn(
                        com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum
                                .PROJECT_PAUSE);
        when(state.getUpdatedBy()).thenReturn("");
        when(state.getStateChangeReason()).thenReturn(projectStateChangeReason);
        doReturn(List.of(state)).when(projectStateManager).findStateHistoryByProjectId(any());
    }

    @Test
    void testRenewalFlagIncludedInEmailVariables() {
        var clientMailSender = Mockito.mock(MailSender.class);
        doReturn(clientMailSender).when(mailSenderProvider).get("no-reply-sender");

        // Mock policy with renewal flag set to true
        when(jooqPolicyRepository.findById(anyString()))
                .thenReturn(
                        Policy.from(
                                PolicyMessage.newBuilder()
                                        .setPolicyNo("POLICYNUMBER")
                                        .setAddress(
                                                AddressMessage.newBuilder().setId("10001").build())
                                        .setIsRenewal(BoolValue.of(true))
                                        .build()));

        var event = new ProjectReturnedToClientEvent();
        event.setProjectId(UW_PROJECT_ID_FAKE_WITH_EMAIL_SELECTOR);
        eventPublisher.publish(event);

        ArgumentCaptor<MailMessage> argument = ArgumentCaptor.forClass(MailMessage.class);
        verify(clientMailSender).send(argument.capture());

        // Verify that isRenewal=true is in the variables JSON
        String variablesJson = argument.getValue().toMessage().getVariablesJson();
        assertTrue(variablesJson.contains("\"isRenewal\":true"));

        // Reset the mock and test with renewal=false
        Mockito.reset(clientMailSender, jooqPolicyRepository);
        doReturn(clientMailSender).when(mailSenderProvider).get("no-reply-sender");

        when(jooqPolicyRepository.findById(anyString()))
                .thenReturn(
                        Policy.from(
                                PolicyMessage.newBuilder()
                                        .setPolicyNo("POLICYNUMBER")
                                        .setAddress(
                                                AddressMessage.newBuilder().setId("10001").build())
                                        .setIsRenewal(BoolValue.of(false))
                                        .build()));

        eventPublisher.publish(event);

        argument = ArgumentCaptor.forClass(MailMessage.class);
        verify(clientMailSender).send(argument.capture());

        // Verify that isRenewal=false is in the variables JSON
        variablesJson = argument.getValue().toMessage().getVariablesJson();
        assertTrue(variablesJson.contains("\"isRenewal\":false"));
    }
}
