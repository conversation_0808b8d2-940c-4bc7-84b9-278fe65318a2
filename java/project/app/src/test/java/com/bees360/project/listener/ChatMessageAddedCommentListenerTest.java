package com.bees360.project.listener;

import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.activity.CommentQuery;
import com.bees360.event.EventListener;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.ChatMessageAdded;
import com.bees360.event.registry.ChatMessageAdded.Attachment;

import org.apache.commons.lang3.RandomStringUtils;
import org.jmock.lib.concurrent.DeterministicScheduler;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

@SpringBootTest
@TestPropertySource(
        properties = {
            "spring.config.location = classpath:application-test.yml",
            "spring.main.allow-circular-references=true"
        })
@DirtiesContext
public class ChatMessageAddedCommentListenerTest {

    @Configuration
    @Import(
            value = {
                ChatMessageAddedCommentListener.class,
            })
    static class Config {
        @Bean
        public CommentManager commentManager() {
            return new TestCommentManager();
        }

        @Autowired EventListener eventListener;

        @Bean
        public DeterministicScheduler deterministicScheduler() {
            return new DeterministicScheduler();
        }

        @Bean
        public InMemoryEventPublisher eventPublisher(
                DeterministicScheduler deterministicScheduler) {
            InMemoryEventPublisher eventPublisher =
                    new InMemoryEventPublisher(deterministicScheduler);
            eventPublisher.enlist(eventListener);
            return eventPublisher;
        }
    }

    @Autowired private EventPublisher eventPublisher;

    @Autowired private CommentManager commentManager;

    @Autowired private DeterministicScheduler deterministicScheduler;

    @Test
    void testAddComment() {
        ChatMessageAdded event = new ChatMessageAdded();
        String projectId = String.valueOf(new Random().nextInt());
        String message = "I am a test message.";
        event.setSenderId(RandomStringUtils.randomAlphabetic(6));
        event.setMessage(message);
        event.setProjectId(projectId);
        event.setSource("BEES TEST");

        eventPublisher.publish(event);
        deterministicScheduler.runUntilIdle();
        Comment comment = commentManager.findById(projectId);
        Assertions.assertEquals(message, comment.getContent());
    }

    @Test
    void testAddCommentWithAttachments() {
        ChatMessageAdded event = new ChatMessageAdded();
        String projectId = String.valueOf(new Random().nextInt());
        String message = "I am a attachments test message.";
        Attachment attachment = new Attachment();
        attachment.setUrl("testUrl");
        attachment.setFilename("testFile");
        event.setSenderId(RandomStringUtils.randomAlphabetic(6));
        event.setMessage(message);
        event.setProjectId(projectId);
        event.setSource("BEES TEST");
        event.setAttachments(List.of(attachment));

        eventPublisher.publish(event);
        deterministicScheduler.runUntilIdle();
        Comment comment = commentManager.findById(projectId);
        Assertions.assertEquals(message, comment.getContent());
        Assertions.assertNotNull(comment.getAttachment().iterator().next());
    }

    static class TestCommentManager implements CommentManager {
        Map<String, Comment> commentMap = new HashMap<>();

        @Override
        public Comment findById(String id) {
            return commentMap.get(id);
        }

        @Override
        public List<? extends Comment> getComments(CommentQuery commentQuery) {
            return null;
        }

        @Override
        public String addComment(Comment comment) {
            String id = String.valueOf(comment.getProjectId());
            commentMap.put(id, comment);
            return id;
        }

        @Override
        public String updateComment(Comment comment) {
            return null;
        }

        @Override
        public void deleteById(String id) {}
    }
}
