package com.bees360.project.invoice;

import com.bees360.event.registry.ProjectInvoiceReceiptChanged;
import com.bees360.invoice.Invoice;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.project.JooqProjectInvoiceManager;
import com.bees360.project.JooqProjectInvoiceReceiptManager;
import com.bees360.project.Message;
import com.bees360.project.ProjectPaymentManager;
import com.bees360.util.DateTimes;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@DirtiesContext
@Import({JooqProjectInvoiceReceiptManager.class, DataSourceAutoConfiguration.class})
@SpringBootTest(classes = JooqConfig.class)
class SetProjectPaidOnProjectInvoiceReceiptChangedTest {

    @Autowired ProjectInvoiceReceiptManager invoiceReceiptManager;

    @MockBean JooqProjectInvoiceManager projectInvoiceManager;

    @MockBean ProjectPaymentManager projectPaymentManager;

    private SetProjectPaidOnProjectInvoiceReceiptChanged listener;

    @BeforeEach
    void invoicePaymentReceivedShouldSetProjectsPaid() {
        listener =
                new SetProjectPaidOnProjectInvoiceReceiptChanged(
                        projectInvoiceManager, projectPaymentManager);
    }

    @Test
    void testPublishProjectPaidOnInvoicePaymentShouldSucceed() throws IOException {
        BigDecimal amountToPaid = BigDecimal.valueOf(300);
        List<String> projectIds =
                IntStream.range(0, 10)
                        .mapToObj(i -> RandomStringUtils.randomNumeric(8, 12))
                        .collect(Collectors.toList());
        String invoiceNO = createProjectInvoice(amountToPaid, projectIds);
        ProjectInvoiceReceiptChanged event = randomReceiptEvent(invoiceNO, amountToPaid, true);

        listener.handle(event);
        Mockito.verify(projectPaymentManager, Mockito.atLeastOnce()).setProjectsPaid(projectIds);
    }

    @Test
    void testPartialPaymentShouldNotTriggerProjectPaid() throws IOException {
        BigDecimal amountToPaid = BigDecimal.valueOf(300);
        List<String> projectIds =
                IntStream.range(0, 10)
                        .mapToObj(i -> RandomStringUtils.randomNumeric(8, 12))
                        .collect(Collectors.toList());
        String invoiceNO = createProjectInvoice(amountToPaid, projectIds);

        BigDecimal amountReceived = BigDecimal.valueOf(200);
        ProjectInvoiceReceiptChanged event = randomReceiptEvent(invoiceNO, amountReceived, false);
        listener.handle(event);

        Mockito.verify(projectPaymentManager, Mockito.never()).setProjectsPaid(projectIds);
    }

    String createProjectInvoice(BigDecimal amountToPaid, List<String> projectIds) {
        String invoiceNO = RandomStringUtils.randomAlphanumeric(32);
        ProjectInvoice projectInvoice = mockProjectInvoice(invoiceNO, amountToPaid, projectIds);

        Mockito.when(projectInvoiceManager.findByInvoiceNo(invoiceNO)).thenReturn(projectInvoice);
        invoiceReceiptManager.initializeReceipt(invoiceNO);
        return invoiceNO;
    }

    ProjectInvoiceReceiptChanged randomReceiptEvent(
            String invoiceNO, BigDecimal amountReceived, boolean cleared) {
        String checkNo = RandomStringUtils.randomAlphanumeric(32);
        LocalDate clearDate = LocalDate.now().minusDays(RandomUtils.nextInt(20, 60));
        var event = new ProjectInvoiceReceiptChanged();
        var builder =
                Message.ProjectMessage.Invoice.Receipt.newBuilder()
                        .setInvoiceNo(invoiceNO)
                        .setCheckNo(checkNo)
                        .setAmountPaid(amountReceived.doubleValue());
        if (cleared) {
            builder.setClearedDate(DateTimes.toProtoDate(clearDate));
        }
        event.setReceipt(ProjectInvoiceReceipt.from(builder.build()));
        return event;
    }

    ProjectInvoice mockProjectInvoice(
            String invoiceNO, BigDecimal totalToBePaid, List<String> projectIds) {
        com.bees360.invoice.Message.InvoiceMessage invoiceMsg =
                com.bees360.invoice.Message.InvoiceMessage.newBuilder()
                        .setInvoiceNo(invoiceNO)
                        .setTotal(totalToBePaid.doubleValue())
                        .build();
        Invoice invoice = Invoice.from(invoiceMsg);
        return new MockProjectInvoice(
                invoiceNO,
                invoice,
                RandomUtils.nextBoolean(),
                RandomStringUtils.randomAlphanumeric(16),
                null,
                "",
                projectIds);
    }
}
