project:
  app:
    auto-project-tag:
      enabled: true
      enables:
        add-on-project-created: true
      add-on-project-created:
        - on-supplemental-service: [ 'RC Report' ]
          on-project-type: [ 'UNDERWRITING' ]
          tag: [ 182 ]
          tag-lookup:
            title: Order RC Report
            type: UNDERWRITING
          updated-via: IO
        - on-service-type: [ 'EXTERIOR_UNDERWRITING' ]
          tag: 181
          updated-via: WEB
        - on-project-type: [ 'CLAIM' ]
          tag: 180
          updated-via: AI
        - on-service-type: [ 'EXPRESS_UNDERWRITING' ]
          tag: 179
          updated-via: AI
