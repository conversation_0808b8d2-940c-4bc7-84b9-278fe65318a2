grpc:
  server:
    port: ${GRPC_SERVER_PORT:9821}
project.app.customer-project-emails:
  enabled: true
  trigger:
    # 开启邮件的发送
    project_completed_underwriting: "project-status:returned-to-client"
    # 开启邮件的发送
    project_completed_claim: "project-status:client-received"
    project_renovation_questionnaire: "event:renovation_questionnaire_submitted"
  # 配置默认的邮件，以及邮件的接收人
  default-project-emails:
    project_renovation_questionnaire_homeowner:
      subscribed: true
      to-selector:
        - type: email
          email:
            email: <EMAIL>
    project_renovation_questionnaire_pilot:
      subscribed: true
      to-selector:
        - type: email
          email:
            email: <EMAIL>
    fake_email_topic:
      to-selector:
        - type: email
          email:
            email: <EMAIL>
        - type: member
          member:
            role: FAKE
        - type: contact
          contact:
            role: DIRECTOR
      cc-selector:
        - type: email
          email:
            email: <EMAIL>
        - type: member
          member:
            role: FAKE
        - type: contact
          contact:
            role: DIRECTOR
      bcc-selector:
        - type: email
          email:
            email: <EMAIL>
        - type: member
          member:
            role: FAKE
        - type: contact
          contact:
            role: DIRECTOR

  customers:
    "[Fake Two]":
      project-emails:
        project_created:
          subscribed: true
          # 增加新的邮件接收人
          to-selector:
            - type: member
              member:
                role: CREATOR
        project_completed_claim:
          subscribed: true
          # 增加新的邮件接收人
          to-selector:
            - type: email
              email:
                email: <EMAIL>

    "[Fake With Email Selector]":
      project-emails:
        project_created:
          subscribed: true
          # 替换了默认的邮件模板
          template-key: new_project_created
          # 增加新的to接收人
          to-selector:
            - type: email
              email:
                email: <EMAIL>
          # 增加新的cc接收人
          cc-selector:
            - type: contact
              contact:
                role: "Claim Director"
          # 增加新的bcc接收人
          bcc-selector:
            - type: email
              email:
                email: <EMAIL>
        project_completed_underwriting:
          # 设置该公司接收该邮件，没有配置的 project_completed_claim 则沿用默认配置中的不接收
          subscribed: true
    "[Fake With Policy Type Selector]":
      project-emails:
        project_completed_underwriting:
          subscribed: true
          template-key: project_completed_underwriting
          to-selector:
            - type: policyType
              policy-type: "Commercial Property Insurance"
              emails:
                - "CommercialPolicy@test"
            - type: policyType
              policy-type: "Homeowners Insurance"
              emails:
                - "ResidentialPolicy@test"
              members:
                - "Creator"
            - type: policyType
              property-type: "COMMERCIAL"
              emails:
                - "CommercialProperty@test"
              contacts:
                - "Insured"
            - type: policyType
              policy-type: "Commercial Property Insurance"
              property-type: "COMMERCIAL"
              emails:
                - "CommercialPolicyAndProperty@test"
              members:
                - "Creator"
              contacts:
                - "Insured"
    "[Fake With Request Cancel Email Selector]":
      project-emails:
        request_project_cancel_claim:
          subscribed: true
          template-key: request_project_cancel_claim
          cc-selector:
            - type: am
            - type: contact
              contact:
                role: "Claim Director"
          bcc-selector:
            - type: email
              email:
                email: <EMAIL>
        request_project_cancel_underwriting:
          subscribed: true
          template-key: request_project_cancel_underwriting
          cc-selector:
            - type: am
            - type: contact
              contact:
                role: "Underwriting Director"
          bcc-selector:
            - type: email
              email:
                email: <EMAIL>
        revert_project_cancel_claim:
          subscribed: true
          template-key: revert_project_cancel_claim
          cc-selector:
            - type: am
            - type: contact
              contact:
                role: "Claim Director"
          bcc-selector:
            - type: email
              email:
                email: <EMAIL>
        revert_project_cancel_underwriting:
          subscribed: true
          template-key: revert_project_cancel_underwriting
          cc-selector:
            - type: am
            - type: contact
              contact:
                role: "Underwriting Director"
          bcc-selector:
            - type: email
              email:
                email: <EMAIL>
        project_cancelled_claim:
          subscribed: true
          template-key: project_cancelled_claim
          to-selector:
            - type: stateUpdateBy
          cc-selector:
            - type: am
            - type: contact
              contact:
                role: "Claim Director"
          bcc-selector:
            - type: email
              email:
                email: <EMAIL>
        project_cancelled_underwriting:
          subscribed: true
          template-key: project_cancelled_underwriting
          to-selector:
            - type: stateUpdateBy
          cc-selector:
            - type: am
            - type: contact
              contact:
                role: "Underwriting Director"
          bcc-selector:
            - type: email
              email:
                email: <EMAIL>
    "[TWFG]":
      project-emails:
        fake_email_topic:
          to-selector-override-default: true
          to-selector:
            - type: email
              email:
                email: <EMAIL>
          cc-selector-override-default: true
          cc-selector:
            - type: member
              member:
                role: CREATOR
          bcc-selector-override-default: true
          bcc-selector:
            - type: contact
              contact:
                role: Underwriting Director

mail:
  senders:
    backend-sender: <EMAIL>
    join-the-hive-sender: <EMAIL>
    claim-sender: <EMAIL>
    client-mail-sender: <EMAIL>
