spring:
  main:
    allow-circular-references: true
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver

grpc:
  server:
    port: ${GRPC_SERVER_PORT:9898}
  client-config:
    wait-for-ready-enable: false
  client:
    thirdPartyService:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    imageManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectReportJobManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStatisticService:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectTagManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStatusManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    resourcePool:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    addressManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
    hiveLocationManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
    addressFlyZoneTypeManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
    projectGroupManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      negotiationType: plaintext
    projectImageProcessManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      negotiationType: plaintext
    reportManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      negotiationType: plaintext
    projectStateManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      negotiationType: plaintext
    estintelManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      negotiationType: plaintext
    nonBlockingLambdaExecutionManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      negotiationType: plaintext
    projectCatastropheManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      negotiationType: plaintext
    projectQuizManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

oauth:
  clients:
    beespilot:
      token-endpoint: https://test.beespilot.io/fn/oauth/token
      client-id: bees360-backend
      client-secret: 40tpjq4XwQeNCqri
      method: POST

server:
  port: 9999

rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

redis:
  client:
    host: redis
    port: 6379
    database: 0
  job2project:
    name: jobKey2projectId
    expiry: P7d

pipeline:
  statemachine:
    initial: PENDING
    transitions:
      # from PENDING
      - source: PENDING
        target: READY
        event: CONDITION_READY
      # from READY
      - source: READY
        event: START
        target: ONGOING
      - source: READY
        event: FINISH
        target: DONE
      - source: READY
        event: EXCEPTION
        target: ERROR
      - source: READY
        target: PENDING
        event: RESET
      # from ongoing
      - source: ONGOING
        event: FINISH
        target: DONE
      - source: ONGOING
        event: EXCEPTION
        target: ERROR
      - source: ONGOING
        event: RESET
        target: PENDING
      # from DONE
      - source: DONE
        event: CONDITION_READY
        target: READY
      - source: DONE
        event: EXCEPTION
        target: ERROR
      - source: DONE
        event: RESET
        target: PENDING
        guard:
          disabled-task-keys:
            - "test_task"
      # from ERROR
      - source: ERROR
        event: FINISH
        target: DONE
      - source: ERROR
        event: START
        target: ONGOING
      - source: ERROR
        event: CONDITION_READY
        target: READY
      - source: ERROR
        event: RESET
        target: PENDING
      - source: ERROR
        event: IGNORE
        target: IGNORED
      # from IGNORED
      - source: IGNORED
        event: RESET
        target: PENDING
      - source: IGNORED
        event: CONDITION_READY
        target: READY
  tasks:
    - key: test_task
      name: 'Test Task'
      stage: 30
      prereq-task-def-key: []
      next-task-def-key: [init_task]
      add-trigger:
        - tag-id: 98
          tag-changed-type: ADD
          version-suffix: _add_on_tag_add
        - tag-id: 98
          tag-changed-type: REMOVED
          version-suffix: _add_on_tag_remove
        - feedback-regex: ^.*feedback.*$
          service-type: [QUICK_INSPECT]
          version-suffix: _feedback
        - flyZone-type: [ RESTRICTED_ZONE ]
          service-type: [ QUICK_INSPECT ]
          version-suffix: _restricted
        - interior-damage: true
          service-type: [ FOUR_POINT ]
          insured_by: [insuredBy]
          processed_by: [processedBy]
          version-suffix: _interior
      status-trigger:
        - tag-id: 98
          tag-changed-type: REMOVED
          status: DONE
        - feedback-regex: ^.*feedback.*$
          status: ERROR
        - call-result-regex: ^.*test call.*$
          status: ERROR
        - project-status: PROJECT_REWORK
          status: ERROR
        - state: PROJECT_CLOSE
          status: DONE
        - added-operation-tag-id: 22
          status: ERROR
        - removed-operation-tag-id: 22
          status: DONE
        - trigger-status: READY
          service-type: [ QUICK_INSPECT ]
          status: ONGOING
        - flyZone-type: [ RESTRICTED_ZONE ]
          service-type: [ QUICK_INSPECT ]
          status: ERROR
        - trigger-status: READY
          source-key: source_key
          service-type: [ QUICK_INSPECT ]
          status: ONGOING
    - key: test_to_remove
      name: 'Test To be removed'
      stage: 0
      remove-trigger:
        - tag-id: 98
          tag-changed-type: ADD
          version-suffix: _remove_on_tag_add
        - tag-id: 98
          tag-changed-type: REMOVED
          version-suffix: _remove_on_tag_remove
        - feedback-regex: ^.*feedback.*$
          service-type: [ QUICK_INSPECT ]
          version-suffix: _remove_on_feedback
        - flyZone-type: [ RESTRICTED_ZONE ]
          service-type: [ QUICK_INSPECT ]
          version-suffix: _remove_on_restricted
        - interior-damage: true
          service-type: [ FOUR_POINT ]
          version-suffix: _remove_on_interior
    - key: assign-trigger-test
      assign-trigger:
        role: PROCESSOR
        owner-on-initialization: test-owner
  key-role:
    taskRoleStatus:
      - key: test_set_member
        status: [ READY, ONGOING ]
        role: SCHEDULER
        serviceType: [ QUICK_INSPECT, FULL_ADJUSTMENT, POST_CONSTRUCTION_AUDIT, PREMIUM_FOUR_POINT ]

      - key: test_set_task_owner
        status: [ READY, ONGOING ]
        role: FIELD_COORDINATOR
        serviceType: [ QUICK_INSPECT, FULL_ADJUSTMENT, POST_CONSTRUCTION_AUDIT, PREMIUM_FOUR_POINT ]


      - role: FIELD_COORDINATOR
        stage:
          - 10
        serviceType: [ ROOF_ONLY, EXTERIOR, FOUR_POINT, FOUR_POINT_SELF, QUICK_INSPECT, FULL_ADJUSTMENT,
                       PREMIUM_FOUR_POINT, POST_CONSTRUCTION_AUDIT ]
  task:
    transitions:
      report-generate-trigger:
        # MagicPlan report
        "MP": generate_cla
  error-remove-task:
    removeOnErrorTaskKeys: [ test_remove_task_owner ]
  precheck:
    enable: false
    checkers:
      # hosta task checker
      hosta-task-taged-image-checker:
        mysql-image-provider:
          enable: false
        enable: false
        tasks:
          - task-key: tag_room_overviews
            properties:
              min-image-count: 2
              image-tag-groups:
                - image-tag-ids:
                    - 201
                    - 1511
                  image-tag-category: LOCATION
                  exclude-image-tag-ids:
                    - 505
                    - 1518
                - image-tag-ids:
                    - 201
                    - 1510
                  image-tag-category: LOCATION
                - image-tag-ids:
                    - 201
                    - 1509
                  image-tag-category: LOCATION
  auto-assign:
    assign-rules:
      - key: "test-assign-config"
        optimization-frequency: 10000
        service-type-weight: 1
        image-count-weight: 1
        pilot-count-weight: 1
        service-type-weight-proportion: 0.45
        image-count-weight-proportion: 0.45
        pilot-count-weight-proportion: 0.1
        rule-item:
          - user-ids: [ "<EMAIL>" ]
            include:
              - company-key: [ "Insurance Risk Services, Inc." ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
          - user-ids: [ "<EMAIL>", "<EMAIL>" ]
            include:
              - company-key: [ "Security First Insurance", "SageSure Insurance", "Allied Trust Insurance" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
          - user-ids: [ "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>", "<EMAIL>" ]
            include:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "Insurance Risk Services, Inc." ]
                service-type: [ "ALL" ]
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
          - user-ids: [ "<EMAIL>" ]
            include:
              - company-key: [ "Olympus Insurance" ]
                service-type: [ "Premium 4-Point Underwriting" ]
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "Insurance Risk Services, Inc.", "Vault Insurance", "Amwins Insurance" ]
                service-type: [ "ALL" ]
              - company-key: [ "ALL" ]
                service-type: [ "Premium 4-Point Underwriting" ]
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
          - user-ids: [ "<EMAIL>" ]
            include:
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
              - company-key: [ "Swyfft Underwriting" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
          - user-ids: [ "<EMAIL>" ]
            include:
              - company-key: [ "Berkley One Insurance", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
              - company-key: [ "Swyfft Underwriting" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
          - user-ids: [ "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>", "<EMAIL>" ]
            include:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
              - company-key: [ "ALL" ]
                service-type: [ "Premium 4-Point Underwriting" ]
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
              - company-key: [ "Insurance Risk Services, Inc." ]
                service-type: [ "ALL" ]
      - key: "test-assign-not-optimized"
        optimization-frequency: 0
        service-type-weight: 45
        image-count-weight: 45
        pilot-count-weight: 10
        rule-item:
          - user-ids: [ "<EMAIL>" ]
            include:
              - company-key: [ "Insurance Risk Services, Inc." ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
          - user-ids: [ "<EMAIL>", "<EMAIL>" ]
            include:
              - company-key: [ "Security First Insurance", "SageSure Insurance", "Allied Trust Insurance" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
          - user-ids: [ "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>", "<EMAIL>" ]
            include:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "Insurance Risk Services, Inc." ]
                service-type: [ "ALL" ]
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
          - user-ids: [ "<EMAIL>" ]
            include:
              - company-key: [ "Olympus Insurance" ]
                service-type: [ "Premium 4-Point Underwriting" ]
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "Insurance Risk Services, Inc.", "Vault Insurance", "Amwins Insurance" ]
                service-type: [ "ALL" ]
              - company-key: [ "ALL" ]
                service-type: [ "Premium 4-Point Underwriting" ]
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
          - user-ids: [ "<EMAIL>" ]
            include:
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
              - company-key: [ "Swyfft Underwriting" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
          - user-ids: [ "<EMAIL>" ]
            include:
              - company-key: [ "Berkley One Insurance", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
              - company-key: [ "Swyfft Underwriting" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
          - user-ids: [ "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>", "<EMAIL>" ]
            include:
              - company-key: [ "ALL" ]
                service-type: [ "ALL" ]
            exclude:
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
              - company-key: [ "ALL" ]
                service-type: [ "Premium 4-Point Underwriting" ]
              - company-key: [ "Berkley One Insurance", "Swyfft", "Centauri Insurance" ]
                service-type: [ "Roof Only Underwriting" ]
              - company-key: [ "Insurance Risk Services, Inc." ]
                service-type: [ "ALL" ]

report:
  merge:
    type-config:
      # target Type CAL, merge types GLR, NR, INV, DAR, PIR, HPR, MP, PLNR, CUBI, HTLA
      CLA: [DAR, PIR]
      FSR_RCE: [FUR, NR]
    fsr-and-hover:
      enable: true
  service-type-report:
    EXTERIOR:
      report-types: [FUR]
      editor-expand-reports: [INV, CHR, NR]
    HIGH_LEVEL_OVERVIEW:
      report-types: [HLOR]
      editor-expand-reports: [INV, CHR]
  job:
    template:
      "HLOR": High-Level Overview Report
  pipeline:
    task:
      report-task-map:
        "HLOR": generate_hlor

bees360:
  feature:
    open-close:
      enabled: true
project:
  app:
    naics-code:
      mapping:
        "[Commercial Property Insurance]":
          HABITATIONAL: "531110"
          RESTAURANT: "722511"
          GARAGE: "811111"
          GAS_STATION: "447110"
          GENERAL_COMMERCIAL: "531120"
        "[Homeowners Insurance]":
          OTHER: "531390"
        "[Farm & Ranch Insurance]":
          OTHER: "112120"
        "Default":
          RESIDENTIAL_SINGLE_FAMILY: "236115"
          RESIDENTIAL_CONDO: "236115"
          RESIDENTIAL_TOWNHOUSE: "236115"
          RESIDENTIAL_MULTI_FAMILY: "236116"
          RESIDENTIAL_APARTMENTS: "236116"
          COMMERCIAL: "531120"
          SINGLE_FAMILY_DETACHED: "236115"
          SINGLE_FAMILY_ATTACHED: "236115"
          DUPLEX: "236115"
          TRIPLEX: "236115"
          ROW_HOME: "236115"
          TWO_FAMILY_HOME: "236115"
          THREE_FAMILY_HOME: "236115"
          FOUR_FAMILY_HOME: "236115"
          MOBILE_HOME: "321991"
          CONDOMINIUM_DETACHED: "236115"
    assemble-project-summary:
      enabled: true
    auto-report:
      enabled: true
      auto-approve-by-task:
        - pipeline-task-key: "approve_fur"
          report-type: FUR
          target-task-owner-id: 123
          approved-user-id: 123
        - pipeline-task-key: "approve_ror"
          report-type: ROR
          target-task-owner-id: 123
          approved-user-id: 123
      auto-approve-by-task-owner-changed:
        - pipeline-task-key: "approve_fur"
          report-type: FUR
          target-task-owner-id: 123
          approved-user-id: 123
        - pipeline-task-key: "approve_ror"
          report-type: ROR
          target-task-owner-id: 123
          approved-user-id: 123
      auto-reports:
        - pipeline-task-key: "auto_generate_hlor"
          report-type: HLOR
          target-factor-keys: [ "YEAR_BUILT", "LOT_SIZE" ]
        - pipeline-task-key: "auto_generate_fsr"
          report-type: FSR_RCE
          target-factor-keys: [ "YEAR_BUILT", "LOT_SIZE" ]
      auto-approve:
        "[HLOR]": # HLOR
          service-type: "HIGH_LEVEL_OVERVIEW"
    invoice:
      enable-parent-child: true
      enable-invoice-status: true
      cancellation-fee-map:
        "[Erroneous Assignment]":
          - Insured Withdraws Claims
          - Insured Unreachable for Multiple Attempts
        "[Full Inspection (denied on location)]":
          - Insured Declines Drone Inspection
          - Insured Refuses to Schedule
      auto-generate:
        enable-block-customer: true
        block-customer:
          - Test Block Company
    service-type:
      map:
        11: # scheduling only
          required-reports: [ SOS ]
        1: # full assignment
          required-reports: [ DAR, PIR ]
        9: # white glove
          required-reports: [ FUR ]
        4:
          required-reports: [ FUR]
    status:
      auto-returned-to-client:
        enabled: true
        on-service-types: [ 11 ]
      enable-returned-to-client: true
      enable-parent-child: true
    creation:
      enable-created-event: true
    image:
      upload-address-image-to-project:
        enabled: true
    contact:
      assign-trigger:
        enable: true
        contacts:
          - full-name: ALAN WANG1
            primary-email: ALAN <EMAIL>
            primary-phone: 1234561234
            role: CLAIM_DIRECTOR
            project-type: [ CLAIM ]
            company-key: [ "CLAIM_COMPANY" ]
          - full-name: ALAN WANG2
            primary-email: LAN <EMAIL>
            primary-phone: 1234564321
            role: UNDEWRITING_DIRECTOR
            project-type: [ UNDERWRITING ]
            company-key: [ "UW_COMPANY" ]
    set-task-owner-on-du-changed:
      enabled: true
    set-scheduler-on-du-changed:
      enabled: true
    set-member-on-beespilot-member-changed:
      enabled: true
    completed:
      CLAIM:
        need-report-type: 1, 15
      UNDERWRITING:
        include-report-type: 17, 18, 19
    state:
      check-change-state:
        enabled: true
      param-unify-change-state:
        enabled: true
      auto-pause-reopen:
        enabled: true
        pause-reason: "POLICY_EFFECTIVE_DATE_NOT_REACHED"
        reopen-reason: "POLICY_EFFECTIVE_DATE_REACHED"
        customer-list: ["Auto Pause Company"]
      close-condition:
        underwriting:
          stay-open-customer-list: ["testCompany"]
        claim:
          stay-open-customer-list: ["testCompany"]
        customer-report-condition:
          # 针对如下公司针对如下公司, 若 required-report-type 配置的报告已经 approve 但 optional-report-type 配置的却没有时，需要强制 close
          "[Amwins Insurance]":
            EXTERIOR:
              # 18: FUR (Full-scope Underwriting Report)
              required-report-type: [18]
              # 42: FSR_RCE (Full-scope Underwriting Report with Recovery Cost Estimate)
              optional-report-type: [42]
            FOUR_POINT:
              required-report-type: [18]
              optional-report-type: [42]
            FOUR_POINT_SELF:
              required-report-type: [18]
              optional-report-type: [42]
            PREMIUM_FOUR_POINT:
              required-report-type: [18]
              optional-report-type: [42]
            WHITE_GLOVE:
              required-report-type: [18]
              optional-report-type: [42]
          "[Tower Hill Insurance Group]":
            EXTERIOR:
              required-report-type: [18]
              optional-report-type: [42]
            FOUR_POINT:
              required-report-type: [18]
              optional-report-type: [42]
            FOUR_POINT_SELF:
              required-report-type: [18]
              optional-report-type: [42]
            PREMIUM_FOUR_POINT:
              required-report-type: [18]
              optional-report-type: [42]
            WHITE_GLOVE:
              required-report-type: [18]
              optional-report-type: [42]
          "[Olympus Insurance]":
            EXTERIOR:
              required-report-type: [18]
              optional-report-type: [42]
            FOUR_POINT:
              required-report-type: [18]
              optional-report-type: [42]
            FOUR_POINT_SELF:
              required-report-type: [18]
              optional-report-type: [42]
            PREMIUM_FOUR_POINT:
              required-report-type: [18]
              optional-report-type: [42]
            WHITE_GLOVE:
              required-report-type: [18]
              optional-report-type: [42]
    request-cancel:
      enable: true
      status-cancel-reason:
        PROJECT_CREATED: Client-Requested Cancellation
        CUSTOMER_CONTACTED: Client-Requested Cancellation
        ASSIGNED_TO_PILOT: Client-Requested Cancellation
        SITE_INSPECTED: Insured Denied：Onsite
        IMAGE_UPLOADED: Cancellation after inspection completed
      pending-reason: Cancellation request is under review
      activity-comment: submitted a cancellation request. The cancellation request is under review.
      revert-cancel-reason: The cancellation request has been withdrawn
      web-user-id: 10000
      todo-content: request cancel

    customer:
      am-contacts:
        - full-name: ALAN WANG1
          primary-email: <EMAIL>
          company-key: [ "mock_company" ]
        - full-name: ALAN WANG2
          primary-email: LAN <EMAIL>
          company-key: [ "UW_COMPANY" ]
      hail-needed-regex: "(?i)(?:^|\\W)(hail)(?:$|\\W)"
      dataset:
        - id: Insurance Company
          contract:
            processed-by-mapper:
              - policy-no-prefix: Test
                company: 10043
          policy-type-extract-regex: "Policy Type:(?<PolicyType>(.+))\r?\n"
          property-type-mapper:
            source-policy-type:
              - bop hab
            target-property-type: COMMERCIAL
          auto-tag-hail-needed: true
          lossDescExtractRegex: "Loss Description:\r?\n(?<LossDescription>(.+\r?\n)+)\r?\n"
          note-by-policy-no-map:
            "SB": "I am test note."
        - id: No Hail Needed Company
          auto-tag-hail-needed: false
    report:
      auto-approve:
        "[25]": # INV
          - "Test Company"
    close-out:
      enabled: true
      delivery-task:
        - deliver_report_to_test
        - deliver_project_to_test
      customer:
        "[Test Company]":
          enabled: true
    global-options:
      property-type:
        0:
          name: "Residential-Single Family"
        1:
          name: "Residential-Condo"
        2:
          name: "Residential-Townhouse"
        3:
          name: "Residential-Multi Family"
        4:
          name: "Residential-Apartments"
        5:
          name: "Commercial"
        6:
          name: "Single Family Detached"
        7:
          name: "Single Family Attached"
        8:
          name: "Duplex"
        9:
          name: "Triplex"
        10:
          name: "Row Home"
        11:
          name: "2-Family Home"
        12:
          name: "3-Family Home"
        13:
          name: "4-Family Home"
        14:
          name: "Mobile Home"
        15:
          name: "Condominium Detached"
        16:
          name: "Other"
        17:
          name: "Commercial-Apartment"
        18:
          name: "Habitational"
        19:
          name: "Restaurant"
        20:
          name: "Garage"
        21:
          name: "Gas Station"
        22:
          name: "General Commercial"
  image:
    auto-import:
      sourceProjectTypes: [ ADDRESS, INSURED_BY ]
      companyPolicyRequirement:
        "[Swyfft Underwriting]": .*00$
      allow-renewal: true
    check-project-image-resource:
      enabled: true
      needCheckImageResourceTypes: ORIGIN, LARGE, MIDDLE, SMALL
      retry:
        retry-count: 30
        retry-delay: PT30S
        retry-delay-increase-factor: 1

  notification:
    customer-subscription:
      company-key1:
        project_completed:
          - <EMAIL>
          - <EMAIL>
      company-key2:
        project_completed:
    default-recipient: <EMAIL>
    features:
      project-completed:
        enabled: true
  job:
    close-project:
      enabled: true
      underwriting-close-reason: 'CLIENT CANCELLED'
      claim-close-reason: 'CANCELLATION BY CLIENT - UNKNOWN'
      service-type-close-reason-mapping:
        'WHITE_GLOVE': 'CANCELLATION BY CLIENT - UNKNOWN'
        'PREMIUM_FOUR_POINT': 'CANCELLATION BY CLIENT - UNKNOWN'
  peregrine:
    whitelist:
      projectId:
        - "10086"
        - "10010"
        - "10000"
      policyNoRegex: "^.*00$"
      state:
        - "California"
        - "New York"
        - "Texas"
      projectStatus:
        - "PROJECT_CREATED"
        - "IMAGE_UPLOADED"
        - "ESTIMATE_COMPLETED"
      processorId:
        - "10000"
        - "10001"
    lambda:
      - insuredBy:
          - "Swyfft Underwriting"
        serviceType:
          - "4-Point Underwriting"
          - "Premium 4-Point Underwriting"
        lambda-chain:
          - key: com.bees360.estintel.project.fsr.swyfftExterior.AUTOFILL_FORM_DEFAULT
            force: false
          - key: com.bees360.estintel.project.SYNC_IMAGE_RESOURCE
            force: true
          - key: com.bees360.estintel.project.SYNC_IMAGE_TAG
            force: false

beespilot:
  image:
    tag:
      enabled: true
      category-map:
        1: [ADDRESS_VERIFICATION]
        2: [FRONT, ELEVATION]
        3: [REAR, ELEVATION]
        4: [LEFT, ELEVATION]
        5: [RIGHT, ELEVATION]
        6: [INTERIOR]
        7: [DETACHED_GARAGE]
        8: [OTHER_STRUCTURE]
        9: [CONTENT]
        10: [ROOF, OVERVIEW]
        11: [ROOF]
        12: [ROOF, CLOSEUP]
        13: [ROOF]
        14: [ELECTRICAL_PANEL, INTERIOR]
        15: [SUPPLY_LINES, INTERIOR]
        16: [WATER_HEATER, INTERIOR]
        17: [HAZARDS]
        18: [ZIGZAG, ROOF]
        19: [ROOF_LAYER, ROOF]
        20: [INTERIOR, OVERVIEW]
        21: [INTERIOR, FLOOR, OVERVIEW]
        22: [INTERIOR, CEILING, OVERVIEW]
        23: [DAMAGE, INTERIOR]
        24: [OTHER_ITEMS, INTERIOR]
        29: [ALARM, INTERIOR]
        30: [GENERATOR, INTERIOR]
        31: [WATER_SHUTOFF_VALUE, INTERIOR]
        32: [DRY_HYDRANTS, INTERIOR]
        33: [HOME_SPRINKLERS, INTERIOR]
        34: [FIRE_EXTINGUISHERS, INTERIOR]
        35: [FIRE_PROOF_CABINETS, INTERIOR]
        36: [FLAMMABLE_RAG, INTERIOR]
        37: [NO_SMOKING_SIGNS, INTERIOR]
        39: [HAZARDOUS_ADJACENT_PROPERTY]

mail:
  senders:
    backend-sender: <EMAIL>
  daily-days-old:
    enabled: true
    company-property:
      properties:
        - companyKey: testCompany
          recipients: [ "<EMAIL>" ]
          cc: []
          daysOld: [ 10, 20 ]
    project-context: test
  days-pass-policy-date:
    enabled: true
    customer-map:
      "[testCompany]":
        days: [25, 45]
        email-template: days_pass_policy_date
        recipients: [ "<EMAIL>" ]
        cc: [ "<EMAIL>" ]
    project-context: localhost/project/

twilio:
  auth:
    accountSid: fake
    token: fake
  chatServiceSid: fake

customer:
  report:
    auto-generate:
      "[Test Company]":
        RETURNED_TO_CLIENT:
          - INV
        PROJECT_CANCELED:
          - INV
      "[New Invoice Company]":
        RETURNED_TO_CLIENT:
          - INV
        PROJECT_CANCELED:
          - INV
      "[Underwriting Company]":
        RETURNED_TO_CLIENT:
          - INV
      "[No New Invoice Company]":
        RETURNED_TO_CLIENT:
          - INV
infera:
  image:
    service-config:
      annotate_vent:
        pipeline-task-key: sent_image_to_beesense_vent
        image-filter:
          - include-tags: [ ROOF, CLOSEUP ]
            exclude-tags: [ ROOF_LAYER ]
            include-tag-categories: [ SCOPE, COMPONENT ]
            exclude-tag-categories: [ LOCATION ]
          - include-tags: [ ROOF, ZIGZAG ]
            exclude-tags: [ ROOF_LAYER ]
            include-tag-categories: [ SCOPE, COMPONENT ]
            exclude-tag-categories: [ LOCATION ]
    retry-count: 3
    retry-delay: PT2S
    retry-delay-increase-factor: 1.5

project.app.customer-project-emails:
  enabled: true
  trigger:
    project_completed_underwriting: "project-status:returned-to-client"
    project_completed_claim: "project-status:client-received"
  customers:
    "[Mississippi Windstorm Underwriting Association]":
      project_completed_claim:
        # add more to recipients
        to-selector:
          - type: email
            email:
              email: <EMAIL>

project.app.auto-tag:
  enabled: true
  order-rc-report:
    trigger-by: "event:project-created"

es:
  host: elasticsearch
  port: 9200

lambda:
  client:
    aws:
      enabled: true
      client-secret: secret
      functions:
        assemble-project-summary: localhost:8080/
      retry:
        max-attempts: 3
logging:
  level:
    com.bees360.job.AssembleProjectSummaryJobExecutor: DEBUG

handlebar:
  template:
    enabled: true
    resource-prefix: "cdn://private/prod/handlerbar/"
