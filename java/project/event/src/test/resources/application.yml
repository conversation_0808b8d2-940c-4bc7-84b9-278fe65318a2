rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password
pipeline:
  tasks:
    - key: test_task
      name: 'Test Task'
      stage: 30
      prereq-task-def-key: []
      next-task-def-key: [init_task]
      add-trigger:
        - tag-id: 98
          tag-changed-type: ADD
      status-trigger:
        - tag-id: 98
          tag-changed-type: REMOVED
          status: DONE
    - key: assign-trigger-test
      assign-trigger:
        role: PROCESSOR
        owner-on-initialization: test-owner
spring:
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver

postgres:
  event-names:
    - contact_changed_event
