package com.bees360.event.registry;

import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.PostgresEventDispatcher;
import com.bees360.event.PostgresEventListener;
import com.bees360.event.config.PostgresEventDispatcherConfig;
import com.bees360.event.registry.PostgresEventListenerConfig.Config.Properties;
import com.bees360.event.util.EventListeners;
import com.bees360.jooq.config.JooqConfig;

import jakarta.annotation.PostConstruct;

import lombok.Data;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.concurrent.Executors;

@Configuration
@Import({
    PostgresEventDispatcherConfig.class,
    JooqConfig.class,
    DataSourceAutoConfiguration.class,
    TransactionAutoConfiguration.class,
    DataSourceTransactionManagerAutoConfiguration.class,
    PostgresEventListenerConfig.Config.class,
})
@EnableConfigurationProperties
public class PostgresEventListenerConfig {

    @Configuration
    public static class Config {

        @Bean({"eventPublisher", "eventDispatcher"})
        InMemoryEventPublisher inMemoryEventPublisher() {
            return new InMemoryEventPublisher(Executors.newFixedThreadPool(6));
        }

        @Bean
        Properties properties() {
            return new Properties();
        }

        @Data
        @ConfigurationProperties("postgres")
        public static class Properties {
            private List<String> eventNames;
        }
    }

    @Autowired private PostgresEventDispatcher postgresEventDispatcher;

    @Autowired private EventPublisher eventPublisher;

    @Autowired private Properties properties;

    @PostConstruct
    public void assembleEventDispatcher() {
        for (var name : properties.getEventNames()) {
            postgresEventDispatcher.enlist(EventListeners.forwardToPublisher(name, eventPublisher));
        }
        postgresEventDispatcher.enlist(new PostgresEventListener("publish_event", eventPublisher));
    }
}
