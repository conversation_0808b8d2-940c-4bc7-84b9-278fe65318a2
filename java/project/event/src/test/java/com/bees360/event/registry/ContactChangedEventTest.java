package com.bees360.event.registry;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.bees360.event.EventDispatcher;
import com.bees360.event.MockUnnamedEventListener;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.project.Contact;
import com.bees360.project.JooqProjectContactRepository;
import com.bees360.project.Message;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.util.Functions;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;

import java.time.Duration;
import java.util.List;

@Log4j2
@SpringBootTest(
        classes = {
            JooqConfig.class,
            DataSourceAutoConfiguration.class,
            TransactionAutoConfiguration.class,
            DataSourceTransactionManagerAutoConfiguration.class,
        })
@Import({
    JooqProjectContactRepository.class,
    RabbitApiConfig.class,
    RabbitJobDispatcher.class,
    RabbitJobScheduler.class,
    RabbitEventDispatcher.class,
    RabbitEventPublisher.class
})
public class ContactChangedEventTest {

    private final JooqProjectContactRepository jooqProjectContactRepository;
    private final EventDispatcher eventDispatcher;

    public ContactChangedEventTest(
            @Autowired JooqProjectContactRepository jooqProjectContactRepository,
            @Autowired EventDispatcher eventDispatcher) {
        this.jooqProjectContactRepository = jooqProjectContactRepository;
        this.eventDispatcher = eventDispatcher;
        log.info(
                "Created {}(jooqProjectContactRepository={},eventDispatcher={})",
                this,
                jooqProjectContactRepository,
                eventDispatcher);
    }

    @Test
    void testPublishEvent() {
        final var listener = new MockUnnamedEventListener<ContactChangedEvent>() {};
        eventDispatcher.enlist(listener);
        final var projectId = RandomUtils.nextInt(1000000, 10000000) + "";
        final var userId = RandomUtils.nextInt(1000000, 10000000) + "";
        final var userIdRemoveOpt = RandomUtils.nextInt(1000000, 10000000) + "";

        final var contact = createContact(null);

        final var contactId = jooqProjectContactRepository.addContact(projectId, contact, userId);
        final var contactExpected =
                Contact.from(contact.toMessage().toBuilder().setId(contactId).build());
        listener.accept(
                event -> assertEvent(event, projectId, userId, null, contactExpected),
                Duration.ofSeconds(10));

        final var newContact = createContact(contactId);
        jooqProjectContactRepository.updateContact(newContact, userId);
        var expectedFullName =
                WordUtils.capitalizeFully(
                        String.join(
                                " ",
                                newContact.getFirstName(),
                                newContact.getMiddleName(),
                                newContact.getLastName()));
        final var newContactExpected =
                Contact.from(
                        newContact.toMessage().toBuilder().setFullName(expectedFullName).build());
        listener.accept(
                event -> assertEvent(event, projectId, userId, contactExpected, newContactExpected),
                Duration.ofSeconds(10));

        jooqProjectContactRepository.removeContact(contactId, userIdRemoveOpt);
        listener.accept(
                event -> assertEvent(event, projectId, userIdRemoveOpt, newContactExpected, null),
                Duration.ofSeconds(10));
    }

    private Contact createContact(String contactId) {
        var builder = Message.ProjectMessage.Contact.newBuilder();
        Functions.acceptIfNotNull(builder::setId, contactId);
        builder.setRole(RandomStringUtils.randomAlphanumeric(6));
        builder.setFullName(WordUtils.capitalizeFully(RandomStringUtils.randomAlphanumeric(6)));
        builder.setFirstName(RandomStringUtils.randomAlphanumeric(6));
        builder.setMiddleName(RandomStringUtils.randomAlphanumeric(6));
        builder.setLastName(RandomStringUtils.randomAlphanumeric(6));
        builder.setPrimaryEmail(RandomStringUtils.randomAlphanumeric(6) + "@9realms.co");
        builder.addAllOtherEmail(List.of(RandomStringUtils.randomAlphanumeric(6) + "@9realms.co"));
        builder.setPrimaryPhone(RandomStringUtils.randomNumeric(8));
        builder.addAllOtherPhone(List.of(RandomStringUtils.randomNumeric(8)));
        builder.setIsPrimary(true);
        return Contact.from(builder.build());
    }

    private void assertEvent(
            ContactChangedEvent event,
            String projectIdExpected,
            String updatedByExpected,
            Contact oldValueExpected,
            Contact newValueExpected) {
        assertEquals(projectIdExpected, event.getProjectId());
        assertEquals(updatedByExpected, event.getUpdatedBy());
        assertNotNull(event.getUpdatedAt());
        assertContactEquals(oldValueExpected, event.getOldValue());
        assertContactEquals(newValueExpected, event.getNewValue());
    }

    private void assertContactEquals(Contact expected, Contact actual) {
        if (expected == actual) {
            return;
        }
        if (expected == null || actual == null) {
            throw new AssertionError(
                    (String.format("Expected %s, but actual %s.", expected, actual)));
        }
        assertEquals(expected.getId(), actual.getId());
        assertEquals(expected.getRole(), actual.getRole());
        assertEquals(expected.getFullName(), actual.getFullName());
        assertEquals(WordUtils.capitalizeFully(expected.getFirstName()), actual.getFirstName());
        assertEquals(WordUtils.capitalizeFully(expected.getMiddleName()), actual.getMiddleName());
        assertEquals(WordUtils.capitalizeFully(expected.getLastName()), actual.getLastName());
        assertEquals(expected.getPrimaryPhone(), actual.getPrimaryPhone());
        assertEquals(expected.getOtherPhone(), actual.getOtherPhone());
        assertEquals(expected.getPrimaryEmail(), actual.getPrimaryEmail());
        assertEquals(expected.getOtherEmail(), actual.getOtherEmail());
        assertEquals(expected.isPrimary(), actual.isPrimary());
    }
}
