package com.bees360.event.registry;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.bees360.address.Address;
import com.bees360.address.Message.AddressMessage;
import com.bees360.building.Message.BuildingMessage;
import com.bees360.codec.UniversalCodec;
import com.bees360.policy.Policy.PolicyBuilder;
import com.bees360.project.Building;
import com.google.protobuf.ByteString;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;

public class ProjectPolicyUpdatedEventDecodeTest {

    @SneakyThrows
    @Test
    void testDecodePolicyUpdatedEvent() {
        var json =
                IOUtils.resourceToString(
                        "PolicyUpdatedEventDecodeTest.json",
                        StandardCharsets.UTF_8,
                        this.getClass().getClassLoader());
        var event =
                UniversalCodec.INSTANCE.decode(
                        ByteString.copyFromUtf8(json), ProjectPolicyUpdatedEvent.class);

        var expected = new ProjectPolicyUpdatedEvent();
        expected.setProjectIds(
                List.of(
                        "1876098439",
                        "1821187673",
                        "1859268302",
                        "1875954869",
                        "1803749563",
                        "1838235637"));
        expected.setOldPolicy(
                PolicyBuilder.newBuilder()
                        .setId("1")
                        .setPolicyNo("12345690")
                        .setPolicyEffectiveDate(LocalDate.of(2023, 5, 1))
                        .setBuilding(Building.from(BuildingMessage.newBuilder().setId("7").build()))
                        .setAddress(Address.from(AddressMessage.newBuilder().setId("2").build()))
                        .build());
        expected.setNewPolicy(
                PolicyBuilder.newBuilder()
                        .setId("7")
                        .setPolicyNo("731573d")
                        .setPolicyEffectiveDate(LocalDate.of(2024, 7, 2))
                        .setBuilding(
                                Building.from(BuildingMessage.newBuilder().setId("11").build()))
                        .setAddress(Address.from(AddressMessage.newBuilder().setId("8").build()))
                        .setType("Homeowners Gold Star")
                        .setIsRenewal(true)
                        .build());

        assertEquals(expected.getOldPolicy().toMessage(), event.getOldPolicy().toMessage());
        assertEquals(expected.getNewPolicy().toMessage(), event.getNewPolicy().toMessage());
    }
}
