package com.bees360.project.listener;

import com.bees360.activity.Activities;
import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Comment;
import com.bees360.event.registry.ProjectStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.Message;
import com.bees360.project.status.OpenApiProjectStatusEnum;
import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;

import lombok.NonNull;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/** 监听项目状态变更事件并生成对应的活动记录 */
public class EmitActivityOnStatusChanged extends AbstractNamedEventListener<ProjectStatusChanged> {

    private final ActivityManager activityManager;
    private static final String ACTIVITY_SOURCE_WEB = "WEB";

    public EmitActivityOnStatusChanged(@NonNull ActivityManager activityManager) {
        this.activityManager = activityManager;
    }

    @Override
    public void handle(ProjectStatusChanged event) throws IOException {
        String statusName =
                OpenApiProjectStatusEnum.valueOfProjectStatus(
                                Message.ProjectStatus.forNumber(event.getStatus()))
                        .getName();
        emitActivity(event.getProjectId(), event.getUpdatedBy(), statusName, event.getComment());
    }

    private void emitActivity(String projectId, String updateBy, String status, String comment) {
        activityManager.submitActivity(generateActivity(projectId, updateBy, status, comment));
    }

    private Activity generateActivity(
            String projectId, String updateBy, String status, String comment) {
        Activity activity =
                Activities.changeProjectField(
                        Long.parseLong(projectId),
                        updateBy,
                        com.bees360.activity.Message.ActivityMessage.Field.newBuilder()
                                .setName(
                                        com.bees360.activity.Message.ActivityMessage.FieldName
                                                .STATUS
                                                .name())
                                .setValue(status)
                                .build());
        activity =
                Activity.of(
                        activity.toMessage().toBuilder().setSource(ACTIVITY_SOURCE_WEB).build());
        if (StringUtils.isNotBlank(comment)) {
            Timestamp now = Timestamps.fromMillis(System.currentTimeMillis());
            var commentMsg =
                    Comment.from(Long.parseLong(projectId), updateBy, comment, now, now)
                            .toMessage();
            activity = Activity.of(activity.toMessage().toBuilder().setComment(commentMsg).build());
        }
        return activity;
    }
}
