package com.bees360.event.registry;

import com.bees360.codec.ProtoGsonDecoder;
import com.bees360.project.Project;
import com.google.gson.annotations.JsonAdapter;

import lombok.Data;

/** 当项目正常完成或者异常关闭时会发送这个事件, 这个事件表示项目已经关闭且不需要再进行处理，如需要处理会现发送 {@link ProjectReopenedEvent} */
@Event
@Data
public class ProjectClosedEvent {
    @JsonAdapter(value = ProtoGsonDecoder.class)
    private Project project;
}
