package com.bees360.event.registry;

import com.bees360.codec.ProtoGsonDecoder;
import com.bees360.policy.Policy;
import com.google.gson.annotations.JsonAdapter;

import lombok.Data;

import java.util.List;

@Event
@Data
public class ProjectPolicyUpdatedEvent {

    private List<String> projectIds;

    @JsonAdapter(ProtoGsonDecoder.class)
    private Policy oldPolicy;

    @JsonAdapter(ProtoGsonDecoder.class)
    private Policy newPolicy;
}
