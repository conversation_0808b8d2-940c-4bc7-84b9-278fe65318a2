package com.bees360.event.registry;

import com.bees360.codec.ProtoGsonDecoder;
import com.bees360.project.Message;
import com.bees360.project.state.AbstractProjectState;
import com.google.gson.annotations.JsonAdapter;

import lombok.Data;

@Event
@Data
public class ProjectStateChangedEvent {

    private long projectId;

    @JsonAdapter(value = ProtoGsonDecoder.class)
    private AbstractProjectState currentState;

    private Message.ProjectMessage.ProjectState.ProjectStateEnum oldState;
}
