package com.bees360.event.registry;

import com.google.gson.annotations.SerializedName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Event
public class InspectionDueDateChanged {
    @SerializedName(value = "projectId")
    private String projectId; // 项目ID, 不能为空。

    @SerializedName(value = "dueDate")
    private Long dueDate; // 检测最后期限，可以为null。

    @SerializedName(value = "updatedTime")
    private long updatedTime; // 更新时间，不能为空

    @SerializedName(value = "updatedBy")
    private String updatedBy; // 更新用户

    @SerializedName(value = "reason")
    private Reason reason; // 更新原因, 可为null

    @Data
    public static class Reason {
        private String reason;
        private String details;
    }
}
