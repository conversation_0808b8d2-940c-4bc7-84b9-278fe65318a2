package com.bees360.event.registry;

import lombok.Data;

@Deprecated
@Event
@Data
public class ProjectMemberChangedEvent {
    /** 发出该事件的项目 */
    private String projectId;
    /** 触发该事件的用户ID */
    private String operator;
    /** 角色 */
    private String auth;
    /** 原本担任该角色的用户ID。当值为空时，表示第一次分配。 */
    private String userFrom;
    /** 修改之后担任该角色的用户ID。当值为空时，表示取消该角色分配。 */
    private String userTo;
}
