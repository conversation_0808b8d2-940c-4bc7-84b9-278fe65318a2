package com.bees360.event.registry;

import lombok.Data;
import lombok.ToString;

import java.time.Instant;

/** 每次插入project_status_history一条新的记录都会触发该事件 */
@Event
@ToString
@Data
public class ProjectStatusHistoryInserted {

    private String projectId;

    /**
     * @see com.bees360.project.Message.ProjectStatus
     */
    private Integer status;

    private String updatedBy;

    private Instant updatedAt;

    private String comment;
}
