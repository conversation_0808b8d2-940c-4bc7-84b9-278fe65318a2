package com.bees360.kpi;

import com.bees360.AbstractKPIProvider;
import com.bees360.api.Message.ApiMessage;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import java.time.Instant;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;

@Log4j2
public class CachedKPIProvider extends AbstractKPIProvider {
    private final KPIProviderRedisMap kpiProviderMap;
    @Getter private final ZoneId zoneId;

    public CachedKPIProvider(KPIProviderRedisMap kpiProviderMap, ZoneId zoneId) {
        this.kpiProviderMap = kpiProviderMap;
        this.zoneId = zoneId;
    }

    @Override
    public KPI getKPISnapshot(Instant startTime, Instant endTime, String userId) {
        Instant now = Instant.now();
        if (endTime.isAfter(now)) {
            return kpiProviderMap.getKPISnapshot(startTime, endTime, userId);
        }
        List<? extends KPI> list =
                Iterables.toList(
                        get(
                                Message.KPISnapshotQueryMessage.newBuilder()
                                        .setStartTime(DateTimes.toTimestamp(startTime))
                                        .setEndTime(DateTimes.toTimestamp(endTime))
                                        .setUserId(userId)
                                        .build()));
        return list.size() > 0 ? list.get(0) : DEFAULT_INSTANCE.apply(userId);
    }

    @Override
    public Iterable<? extends KPI> getKPISnapshot(Instant startTime, Instant endTime) {
        Instant now = Instant.now();
        if (endTime.isAfter(now)) {
            return kpiProviderMap.getKPISnapshot(startTime, endTime);
        }
        return get(
                Message.KPISnapshotQueryMessage.newBuilder()
                        .setStartTime(DateTimes.toTimestamp(startTime))
                        .setEndTime(DateTimes.toTimestamp(endTime))
                        .build());
    }

    Iterable<? extends KPI> get(Message.KPISnapshotQueryMessage query) {
        ApiMessage apiMessage = kpiProviderMap.get(query);
        if (apiMessage != null) {
            return Iterables.transform(apiMessage.getKpiList(), KPI::from);
        }
        return Collections.emptyList();
    }
}
