package com.bees360.om;

import com.bees360.map.RedisMap;
import com.bees360.project.Message.OperationsManagerMessage;
import com.bees360.util.Iterables;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Equator;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
public class CachedOperationsManagerService extends ForwardingOperationsManagerProvider {

    private final RedisMap<String, List<ByteString>> redisMap;

    private final Equator<OperationsManager> equator;

    public CachedOperationsManagerService(
            RedisMap<String, List<ByteString>> redisMap,
            OperationsManagerProvider operationsManagerService) {
        super(operationsManagerService);
        this.redisMap = redisMap;
        equator = new OperationsManagerEquator();
        log.info(
                "Created {}(redisMap={}, operationsManagerService={}).",
                this,
                this.redisMap,
                this.delegate());
    }

    public Iterable<? extends OperationsManager> getAllOperationsManager() {
        return delegate().getAllOperationsManager();
    }

    public Iterable<? extends OperationsManager> getAllOperationsManagerKPI(
            Instant startTime, Instant endTime) {
        var key = generateKey(startTime, endTime);
        if (!redisMap.containsKey(key)) {
            return getAndUpdateCache(startTime, endTime);
        }

        var cache =
                redisMap.get(key).stream()
                        .map(
                                byteString -> {
                                    try {
                                        return OperationsManager.from(
                                                OperationsManagerMessage.parseFrom(byteString));
                                    } catch (InvalidProtocolBufferException e) {
                                        var msg =
                                                String.format(
                                                        "Failed to parse OperationsManager object"
                                                                + " from redis cache '%s'.",
                                                        byteString);
                                        throw new IllegalStateException(msg, e);
                                    }
                                })
                        .collect(Collectors.toList());
        var operationsManagerList = Iterables.toList(delegate().getAllOperationsManager());

        var isUpdated = !CollectionUtils.isEqualCollection(cache, operationsManagerList, equator);

        if (isUpdated) {
            return getAndUpdateCache(startTime, endTime);
        }

        // 将缓存的结果合并kpi中部分需要实时计算的指标
        return mergeProjectsCountByTag(cache);
    }

    private Iterable<? extends OperationsManager> getAndUpdateCache(
            Instant startTime, Instant endTime) {
        var key = generateKey(startTime, endTime);
        var result = delegate().getAllOperationsManagerKPI(startTime, endTime);

        var byteString =
                Iterables.toStream(result)
                        .map(OperationsManager::toByteString)
                        .collect(Collectors.toList());
        redisMap.put(key, byteString);
        log.info("Update operations-manager beeskpi redis cache and return for {}.", key);
        return result;
    }

    private String generateKey(Instant start, Instant end) {
        return start.toEpochMilli() + "-" + end.toEpochMilli();
    }

    private Iterable<? extends OperationsManager> mergeProjectsCountByTag(
            Iterable<? extends OperationsManager> operationsManagerList) {
        var oms = delegate().getAllOperationsManager();
        return Stream.concat(Iterables.toStream(operationsManagerList), Iterables.toStream(oms))
                .collect(
                        Collectors.toMap(
                                object -> {
                                    var om = (OperationsManager) object;
                                    return om.getUser().getId();
                                },
                                object -> (OperationsManager) object,
                                (omA, omB) -> {
                                    var builder = omA.toMessage().toBuilder();
                                    builder.mergeFrom(omB.toMessage());
                                    return OperationsManager.from(builder.build());
                                }))
                .values();
    }

    static class OperationsManagerEquator implements Equator<OperationsManager> {
        @Override
        public boolean equate(OperationsManager o1, OperationsManager o2) {
            return StringUtils.equals(o1.getUser().getId(), o2.getUser().getId())
                    && CollectionUtils.isEqualCollection(
                            Iterables.toCollection(o1.getManageStates()),
                            Iterables.toCollection(o2.getManageStates()));
        }

        @Override
        public int hash(OperationsManager o) {
            return 0;
        }
    }
}
