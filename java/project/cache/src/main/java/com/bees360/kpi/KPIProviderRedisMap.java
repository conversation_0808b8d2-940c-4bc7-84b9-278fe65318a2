package com.bees360.kpi;

import com.bees360.map.RedisMap;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;

import jakarta.annotation.Nullable;

import org.apache.logging.log4j.util.Strings;
import org.redisson.api.LocalCachedMapOptions;
import org.redisson.api.RedissonClient;
import org.redisson.api.map.MapLoader;

import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.List;

public class KPIProviderRedisMap
        extends RedisMap<Message.KPISnapshotQueryMessage, com.bees360.api.Message.ApiMessage>
        implements KPIProvider {
    private final KPIProvider kpiProvider;

    public KPIProviderRedisMap(
            RedissonClient client,
            String name,
            KPIProvider kpiProvider,
            @Nullable Duration expiry) {
        super(client, name, expiry);
        this.kpiProvider = kpiProvider;
    }

    @Override
    protected void customizeOptions(
            LocalCachedMapOptions<
                            Message.KPISnapshotQueryMessage, com.bees360.api.Message.ApiMessage>
                    options) {
        options.loader(
                new MapLoader<>() {
                    @Override
                    public com.bees360.api.Message.ApiMessage load(
                            Message.KPISnapshotQueryMessage key) {
                        return com.bees360.api.Message.ApiMessage.newBuilder()
                                .addAllKpi(
                                        Iterables.transform(loadKPIFromSource(key), KPI::toMessage))
                                .build();
                    }

                    @Override
                    public Iterable<Message.KPISnapshotQueryMessage> loadAllKeys() {
                        return Collections.emptyList();
                    }
                });
    }

    private Iterable<? extends KPI> loadKPIFromSource(
            Message.KPISnapshotQueryMessage snapshotQueryMessage) {
        if (Strings.isNotBlank(snapshotQueryMessage.getUserId())) {
            return List.of(
                    kpiProvider.getKPISnapshot(
                            DateTimes.toInstant(snapshotQueryMessage.getStartTime()),
                            DateTimes.toInstant(snapshotQueryMessage.getEndTime()),
                            snapshotQueryMessage.getUserId()));
        } else {
            return kpiProvider.getKPISnapshot(
                    DateTimes.toInstant(snapshotQueryMessage.getStartTime()),
                    DateTimes.toInstant(snapshotQueryMessage.getEndTime()));
        }
    }

    @Override
    public KPI getKPISnapshot(Instant startTime, Instant endTime, String userId) {
        return kpiProvider.getKPISnapshot(startTime, endTime, userId);
    }

    @Override
    public Iterable<? extends KPI> getKPISeries(
            int periodStart, int periodEnd, Message.PeriodUnit unit, String userId) {
        return kpiProvider.getKPISeries(periodStart, periodEnd, unit, userId);
    }

    @Override
    public Iterable<? extends KPI> getKPISnapshot(Instant startTime, Instant endTime) {
        return kpiProvider.getKPISnapshot(startTime, endTime);
    }
}
