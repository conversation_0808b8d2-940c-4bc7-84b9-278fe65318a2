package com.bees360.om;

import com.bees360.map.RedisMap;
import com.bees360.project.AbstractOperationsManagerProviderTest;
import com.bees360.project.Message.OperationsManagerMessage;
import com.bees360.redis.config.RedissonConfig;
import com.bees360.user.InMemoryUserRepository;
import com.bees360.user.Message.UserMessage;
import com.bees360.user.User;
import com.bees360.user.UserRepository;
import com.bees360.util.Iterables;
import com.google.protobuf.ByteString;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@SpringBootTest
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class CachedOperationsManagerServiceTest extends AbstractOperationsManagerProviderTest {

    @Configuration
    @Import({
        RedissonConfig.class,
        InMemoryUserRepository.class,
        SimpleOperationsManagerRepository.class
    })
    static class Config {

        @Bean
        @Primary
        public OperationsManagerProvider operationsManagerService(
                OperationsManagerProvider operationsManagerProvider,
                RedissonClient redissonClient) {
            var redisMap =
                    new RedisMap<String, List<ByteString>>(
                            redissonClient, "om-beeskpi-cache-test", Duration.ofSeconds(3));
            return new CachedOperationsManagerService(redisMap, operationsManagerProvider);
        }
    }

    private final OperationsManagerRepository operationsManagerRepository;

    protected CachedOperationsManagerServiceTest(
            @Autowired OperationsManagerRepository operationsManagerRepository,
            @Autowired OperationsManagerProvider operationsManagerService,
            @Autowired UserRepository userRepository) {
        super(operationsManagerService, userRepository);
        this.operationsManagerRepository = operationsManagerRepository;
    }

    @Test
    @Override
    public void testGetAllOperationsManagerKPI() {
        var time = Instant.now().plusSeconds(new Random().nextInt(1000));
        var userId = RandomStringUtils.randomNumeric(10);
        userRepository.save(User.from(UserMessage.newBuilder().setId(userId).build()));

        var states = List.of("NY", "DC");
        saveOM(userId, states);

        operationsManagerProvider.getAllOperationsManagerKPI(time, time);
        var oms =
                Iterables.toStream(operationsManagerProvider.getAllOperationsManagerKPI(time, time))
                        .filter(om -> userId.equals(om.getUser().getId()))
                        .collect(Collectors.toList());
        Assertions.assertEquals(1, oms.size());
        assertEquals(userId, oms.get(0));
    }

    @Test
    void testUpdateOMMessageShouldUpdateData() {
        var time = Instant.now().plusSeconds(new Random().nextInt(1000));
        var userA = RandomStringUtils.randomNumeric(10);
        userRepository.save(User.from(UserMessage.newBuilder().setId(userA).build()));

        var states = List.of("NY", "DC");
        saveOM(userA, states);

        var oms =
                Iterables.toStream(operationsManagerProvider.getAllOperationsManagerKPI(time, time))
                        .filter(om -> userA.equals(om.getUser().getId()))
                        .collect(Collectors.toList());
        Assertions.assertEquals(1, oms.size());
        assertEquals(userA, oms.get(0));

        var userB = RandomStringUtils.randomNumeric(10);
        userRepository.save(User.from(UserMessage.newBuilder().setId(userB).build()));

        states = List.of("TX", "LA");
        saveOM(userB, states);
        oms =
                Iterables.toStream(operationsManagerProvider.getAllOperationsManagerKPI(time, time))
                        .filter(
                                om ->
                                        userA.equals(om.getUser().getId())
                                                || userB.equals(om.getUser().getId()))
                        .collect(Collectors.toList());
        Assertions.assertEquals(2, oms.size());
    }

    @Override
    protected void saveOM(String userId, List<String> states) {
        operationsManagerRepository.save(userId, states);
    }

    private OperationsManager create() {
        return OperationsManager.from(
                OperationsManagerMessage.newBuilder()
                        .setUser(
                                UserMessage.newBuilder()
                                        .setId(RandomStringUtils.randomAlphabetic(8)))
                        .addAllManageStates(List.of(RandomStringUtils.randomAlphabetic(2)))
                        .build());
    }
}
