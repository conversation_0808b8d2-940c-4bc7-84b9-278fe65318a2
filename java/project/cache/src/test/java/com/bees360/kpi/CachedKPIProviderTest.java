package com.bees360.kpi;

import static com.bees360.util.TimeZones.DEFAULT_US_TIMEZONE_ID;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.impl.ForwardingActivityManager;
import com.bees360.api.Message.ApiMessage;
import com.bees360.kpi.config.CachedKPIProviderConfig;
import com.bees360.map.RedisMap;
import com.bees360.user.Pilot;
import com.bees360.user.User;
import com.bees360.util.Calendars;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@SpringBootTest
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class CachedKPIProviderTest extends AbstractKPIProviderTest {
    @Import(value = {CachedKPIProviderConfig.class})
    @Configuration
    static class Config {
        @Bean
        ActivityManager activityManager() {
            return new ForwardingActivityManager() {
                @Override
                protected ActivityManager delegate() {
                    return activityManager;
                }
            };
        }

        @Bean
        CachedKPIProvider cachedOMKPIProvider(Map<Message.KPINamespace, KPIProvider> map) {
            return (CachedKPIProvider) map.get(Message.KPINamespace.OM);
        }
    }

    static final String KPI_NAMESPACE = "kpi/operations-manager";
    static ActivityManager activityManager = getActivityManager();

    public CachedKPIProviderTest(
            @Autowired ActivityManager activityManager,
            @Autowired CachedKPIProvider cachedOMKPIProvider,
            @Autowired RedissonClient redissonClient) {
        super(activityManager, cachedOMKPIProvider);
        redisMap = new RedisMap<>(redissonClient, KPI_NAMESPACE, Duration.ofMinutes(1));
        this.cachedKPIProvider = cachedOMKPIProvider;
    }

    private final KPIProvider cachedKPIProvider;
    private final RedisMap<Message.KPISnapshotQueryMessage, ApiMessage> redisMap;

    @BeforeEach
    public void clean() {
        activityManager = getActivityManager();
    }

    @Test
    public void testGetKPISeriesNumbersWithMonth() {
        super.testGetKPISeriesNumbersWithMonth();
    }

    @Test
    public void testGetKPISeriesNumbersWithWeek() {
        super.testGetKPISeriesNumbersWithWeek();
    }

    @Test
    public void testGetOperationsManagerKPIWithImageQuality() {
        super.testGetOperationsManagerKPIWithImageQuality();
    }

    @Test
    public void testGetOperationsManagerKPIWithTurnAround() {
        super.testGetOperationsManagerKPIWithTurnAround();
    }

    @Test
    public void testGetOperationsManagerKPI() {
        super.testGetOperationsManagerKPI();
    }

    @Test
    public void testGetPassedKPISnapshotShouldBeCached() {
        Pilot pilot = randomPilot();
        User user = pilot.getOperationsManager();
        String userId = user.getId();
        Instant startOfLastMonth = Calendars.getStartOfMonth(-1, DEFAULT_US_TIMEZONE_ID);
        Instant endOfLastMonth = Calendars.getEndOfMonth(-1, DEFAULT_US_TIMEZONE_ID);
        randomImageQualityActivity(pilot, startOfLastMonth);
        randomImageQualityActivity(startOfLastMonth);
        KPI kpi = cachedKPIProvider.getKPISnapshot(startOfLastMonth, endOfLastMonth, userId);
        Assertions.assertNotEquals(
                kpi.toMessage(),
                Message.KPIMessage.getDefaultInstance().toBuilder().setUserId(userId).build(),
                "The first kpi should not be empty");

        ApiMessage apiMessage =
                redisMap.get(
                        Message.KPISnapshotQueryMessage.newBuilder()
                                .setStartTime(DateTimes.toTimestamp(startOfLastMonth))
                                .setEndTime(DateTimes.toTimestamp(endOfLastMonth))
                                .setUserId(userId)
                                .build());
        assertEquals(kpi, KPI.from(apiMessage.getKpi(0)));

        // search without useId
        Map<String, ? extends KPI> kpiMap =
                Iterables.toStream(
                                cachedKPIProvider.getKPISnapshot(startOfLastMonth, endOfLastMonth))
                        .collect(Collectors.toMap(KPI::getUserId, Function.identity()));
        ApiMessage apiMessage2 =
                redisMap.get(
                        Message.KPISnapshotQueryMessage.newBuilder()
                                .setStartTime(DateTimes.toTimestamp(startOfLastMonth))
                                .setEndTime(DateTimes.toTimestamp(endOfLastMonth))
                                .build());
        Assertions.assertEquals(2, apiMessage2.getKpiCount());

        for (Message.KPIMessage kpiMessage : apiMessage2.getKpiList()) {
            assertEquals(KPI.from(kpiMessage), kpiMap.get(kpiMessage.getUserId()));
        }
    }

    @Test
    public void testGetPassedKPISeriesShouldBeCached() {
        Pilot pilot = randomPilot();
        User user = pilot.getOperationsManager();
        String userId = user.getId();
        Instant startOfLastMonth = Calendars.getStartOfMonth(-1, DEFAULT_US_TIMEZONE_ID);
        Instant endOfLastMonth = Calendars.getEndOfMonth(-1, DEFAULT_US_TIMEZONE_ID);
        randomImageQualityActivity(pilot, startOfLastMonth);
        randomImageQualityActivity(startOfLastMonth);
        KPI kpi =
                Iterables.toList(
                                cachedKPIProvider.getKPISeries(
                                        -1, -1, Message.PeriodUnit.MONTH, userId))
                        .get(0);
        Assertions.assertEquals(userId, kpi.getUserId());
        Assertions.assertEquals(-1, kpi.getPeriodNum());
        Assertions.assertEquals(Message.PeriodUnit.MONTH, kpi.getPeriodUnit());
        Assertions.assertNotEquals(
                kpi.getImageQuality().toMessage(), Message.MetricsMessage.getDefaultInstance());
        ApiMessage apiMessage =
                redisMap.get(
                        Message.KPISnapshotQueryMessage.newBuilder()
                                .setStartTime(DateTimes.toTimestamp(startOfLastMonth))
                                .setEndTime(DateTimes.toTimestamp(endOfLastMonth))
                                .setUserId(userId)
                                .build());
        assertEquals(kpi, KPI.from(apiMessage.getKpi(0)));
    }

    @Test
    public void testGetFutureKPISnapshotShouldNotBeCache() {
        Pilot pilot = randomPilot();
        User user = pilot.getOperationsManager();
        String userId = user.getId();
        Instant startOfCurrentMonth = Calendars.getStartOfMonth(0, DEFAULT_US_TIMEZONE_ID);
        Instant endOfCurrentMonth = Calendars.getEndOfMonth(0, DEFAULT_US_TIMEZONE_ID);
        randomImageQualityActivity(pilot, startOfCurrentMonth);
        randomImageQualityActivity(startOfCurrentMonth);
        KPI kpi = cachedKPIProvider.getKPISnapshot(startOfCurrentMonth, endOfCurrentMonth, userId);
        Assertions.assertNotEquals(
                kpi.toMessage(),
                Message.KPIMessage.getDefaultInstance().toBuilder().setUserId(userId).build(),
                "The first kpi should not be empty");

        // search current month should not be cached
        ApiMessage apiMessage =
                redisMap.get(
                        Message.KPISnapshotQueryMessage.newBuilder()
                                .setStartTime(DateTimes.toTimestamp(startOfCurrentMonth))
                                .setEndTime(DateTimes.toTimestamp(endOfCurrentMonth))
                                .setUserId(userId)
                                .build());
        Assertions.assertNull(apiMessage);
    }
}
