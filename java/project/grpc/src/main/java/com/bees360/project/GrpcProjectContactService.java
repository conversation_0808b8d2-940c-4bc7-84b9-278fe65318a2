package com.bees360.project;

import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.apache.commons.lang3.StringUtils;

@GrpcService
@Log4j2
@RequiredArgsConstructor
public class GrpcProjectContactService
        extends ProjectContactServiceGrpc.ProjectContactServiceImplBase {

    private final ContactManager projectContactManager;

    @Override
    public void saveContact(
            Message.SaveProjectContactRequest request,
            StreamObserver<StringValue> responseObserver) {
        log.info("Save project contact, project {} request {}", request.getProjectId(), request);
        var contact = Contact.of(request.getContact());
        var result =
                StringUtils.isEmpty(contact.getId())
                        ? projectContactManager.addContact(
                                request.getProjectId(), contact, request.getOpUserId())
                        : projectContactManager.updateContact(contact, request.getOpUserId());
        responseObserver.onNext(StringValue.newBuilder().setValue(result).build());
        responseObserver.onCompleted();
    }

    @Override
    public void removeContact(
            Message.RemoveProjectContactRequest request, StreamObserver<Empty> responseObserver) {
        log.info("Remove contact, contactId {}", request);
        projectContactManager.removeContact(request.getContactId(), request.getOpUserId());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void findByProjectId(
            StringValue request, StreamObserver<Message.ProjectMessage.Contact> responseObserver) {
        var result = projectContactManager.findByProjectId(request.getValue());
        result.forEach(projectContact -> responseObserver.onNext(projectContact.toMessage()));
        responseObserver.onCompleted();
    }
}
