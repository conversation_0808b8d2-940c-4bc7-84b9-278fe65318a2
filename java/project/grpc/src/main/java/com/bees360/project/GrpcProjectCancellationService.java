package com.bees360.project;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.project.Message.CancellationRequest;
import com.bees360.project.state.ProjectRequestCancellationManager;
import com.google.protobuf.BoolValue;

import io.grpc.stub.StreamObserver;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Import;

@GrpcService
@Import({ExceptionTranslateInterceptor.class})
public class GrpcProjectCancellationService
        extends ProjectCancellationServiceGrpc.ProjectCancellationServiceImplBase {

    private final ProjectRequestCancellationManager projectRequestCancellationManager;

    public GrpcProjectCancellationService(
            @Qualifier("defaultProjectRequestCancellationManager")
                    ProjectRequestCancellationManager projectRequestCancellationManager) {
        this.projectRequestCancellationManager = projectRequestCancellationManager;
    }

    @Override
    public void requestCancel(
            CancellationRequest request, StreamObserver<BoolValue> responseObserver) {
        var isSuccess =
                projectRequestCancellationManager.requestCancel(
                        request.getUserId(),
                        request.getProjectId(),
                        request.getCancelOption(),
                        request.getComment());
        responseObserver.onNext(BoolValue.of(isSuccess));
        responseObserver.onCompleted();
    }
}
