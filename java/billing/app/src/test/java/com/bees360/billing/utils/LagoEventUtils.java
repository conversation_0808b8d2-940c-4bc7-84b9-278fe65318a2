package com.bees360.billing.utils;

import com.bees360.event.registry.LagoEvent;
import com.bees360.lago.JSON;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/** 获取webhookJson并转换为LagoEvent */
public class LagoEventUtils {

    private static final Gson gson = JSON.getGson().newBuilder().serializeNulls().create();

    public static LagoEvent createLagoEvent(String resourceName) throws IOException {
        var jsonStr = getResourceString(resourceName);
        var jsonObject = gson.fromJson(jsonStr, JsonObject.class);
        var webhookType = jsonObject.get("webhook_type").getAsString();
        var objectType = jsonObject.get("object_type").getAsString();
        var object = gson.toJson(jsonObject.get(objectType));
        return new LagoEvent(webhookType, objectType, object);
    }

    /** 用于获取resources/路径下的各类响应json文件 */
    public static String getResourceString(String resourceName) throws IOException {
        return IOUtils.resourceToString(
                resourceName, StandardCharsets.UTF_8, LagoEventUtils.class.getClassLoader());
    }
}
