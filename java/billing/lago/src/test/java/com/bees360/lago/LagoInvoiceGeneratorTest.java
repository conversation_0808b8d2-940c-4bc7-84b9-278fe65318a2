package com.bees360.lago;

import static com.bees360.lago.config.LagoApiConfig.GSON;
import static com.bees360.lago.utils.MockWebServerUtils.getResourceString;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;

import com.bees360.lago.api.EventsApi;
import com.bees360.lago.config.LagoHttpClientConfig;
import com.bees360.lago.model.EventBatchV2Input;
import com.google.gson.JsonObject;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;
import java.util.UUID;

@SpringBootTest(classes = LagoInvoiceGeneratorTest.Config.class)
@ActiveProfiles("test")
public class LagoInvoiceGeneratorTest {

    @Import({LagoHttpClientConfig.class})
    @Configuration
    static class Config {}

    @Test
    void testSubmitInvoiceGeneration() throws IOException, ApiException {
        var eventsApi = mock(EventsApi.class);
        var invoiceGenerator = new LagoInvoiceGenerator(LagoApi.of(eventsApi), GSON);
        var requestObject =
                GSON.fromJson(getResourceString("event_batch_v2_request.json"), JsonObject.class);
        var eventsObject = requestObject.get("events").getAsJsonObject();
        var subscriptionId = eventsObject.get("external_subscription_id").getAsString();
        var customerId = eventsObject.get("external_customer_id").getAsString();
        var projectId = UUID.randomUUID().toString();
        var taxCode = RandomStringUtils.randomNumeric(5);
        var billData = GSON.toJson(eventsObject.get("bill_data"));

        doAnswer(
                        args -> {
                            EventBatchV2Input input = args.getArgument(0);
                            assertEquals(
                                    subscriptionId, input.getEvents().getExternalSubscriptionId());
                            assertEquals(customerId, input.getEvents().getExternalCustomerId());
                            var metadataObject =
                                    GSON.fromJson(
                                            GSON.toJson(input.getEvents().getMetadata()),
                                            JsonObject.class);
                            assertEquals(projectId, metadataObject.get("project_id").getAsString());
                            assertEquals(taxCode, metadataObject.get("tax_code").getAsString());
                            assertEquals(billData, GSON.toJson(input.getEvents().getBillData()));
                            return null;
                        })
                .when(eventsApi)
                .createBatchEventsV2(any(EventBatchV2Input.class));
        assertDoesNotThrow(
                () ->
                        invoiceGenerator.submitInvoiceGeneration(
                                projectId, subscriptionId, customerId, billData, taxCode));
    }
}
