package com.bees360.lago.utils;

import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;

import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class MockWebServerUtils {

    public static String getResourceString(String resourcePath) {
        try {
            return new String(
                    new ClassPathResource(resourcePath).getInputStream().readAllBytes(),
                    StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static MockWebServer setupMockWebServer() {
        var server = new MockWebServer();
        server.setDispatcher(
                new Dispatcher() {
                    @Override
                    public MockResponse dispatch(RecordedRequest request)
                            throws InterruptedException {
                        if (request.getPath().contains("/api/v1/subscriptions?")) {
                            return new MockResponse()
                                    .setResponseCode(200)
                                    .setBody(getResourceString("list_subscriptions_response.json"));
                        } else if (request.getPath().contains("/api/v1/subscriptions/")) {
                            String path = request.getPath();
                            if (path.contains("1e64a002-528c-4df0-8a3e-f1455264bc6a")) {
                                return new MockResponse()
                                        .setResponseCode(200)
                                        .setBody(
                                                getResourceString(
                                                        "retrieve_subscription_pekin_insurance_response.json"));
                            } else if (path.contains("f9fe4744-aec4-4c07-a9f3-d3a74c6e4294")) {
                                return new MockResponse()
                                        .setResponseCode(200)
                                        .setBody(
                                                getResourceString(
                                                        "retrieve_subscription_plan1_response.json"));
                            } else if (path.contains("15a05c41-ca92-4ccb-831c-f54380026493")) {
                                return new MockResponse()
                                        .setResponseCode(200)
                                        .setBody(
                                                getResourceString(
                                                        "retrieve_subscription_plan1_2_response.json"));
                            }
                        } else if (request.getPath().contains("/api/v1/billable_metrics")) {
                            return new MockResponse()
                                    .setResponseCode(200)
                                    .setBody(
                                            getResourceString(
                                                    "test_find_all_billable_metrics_response.json"));
                        } else if (request.getPath().contains("/api/v1/events/batch/v2")) {
                            return new MockResponse().setResponseCode(200);
                        }
                        return new MockResponse().setResponseCode(404);
                    }
                });
        return server;
    }
}
