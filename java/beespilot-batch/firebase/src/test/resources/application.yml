firebase:
  credential:
    type: service_account
    project_id: test
    private_key_id: none
    private_key: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    client_email: <EMAIL>
    client_id: none
    auth_uri: https://accounts.google.com/o/oauth2/auth
    token_uri: https://oauth2.googleapis.com/token
    auth_provider_x509_cert_url: https://www.googleapis.com/oauth2/v1/certs
    client_x509_cert_url: https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>
  listener:
    collection: pilot
  firestore:
    emulator-host: firestore-emulator:8080

spring:
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver
  jooq:
    sql-dialect: POSTGRES
logging:
  level:
    org.jooq: DEBUG
