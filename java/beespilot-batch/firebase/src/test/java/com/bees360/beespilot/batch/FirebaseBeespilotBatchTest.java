package com.bees360.beespilot.batch;

import static com.bees360.jooq.persistent.beespilotbatch.Tables.BEESPILOT_BATCH;
import static com.bees360.jooq.persistent.beespilotbatch.Tables.BEESPILOT_BATCH_ITEM;

import com.bees360.beespilot.batch.config.JooqBeesPilotBatchRepositoryConfig;
import com.bees360.beespilot.batch.util.BatchEntity;
import com.bees360.beespilot.batch.util.BatchEntity.BatchItem;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.ChannelInMemoryEventPublisher;
import com.bees360.event.EventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.firebase.DefaultFirebaseListenerManager;
import com.bees360.firebase.FirebaseApi;
import com.bees360.firebase.FirebaseListener;
import com.bees360.firebase.FirebaseListenerCheckpointManager;
import com.bees360.firebase.FirebaseListenerManager;
import com.bees360.firebase.config.FirebaseConfig;
import com.bees360.firebase.domain.Batch;
import com.bees360.firebase.listener.CheckpointCollectionChangedListener;
import com.bees360.jooq.persistent.beespilotbatch.tables.records.BeespilotBatchRecord;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@DirtiesContext
@SpringBootTest
@ApplicationAutoConfig
@SpringJUnitConfig
public class FirebaseBeespilotBatchTest {
    @Configuration
    @Import(
            value = {
                ChannelInMemoryEventPublisher.class,
                FirebaseConfig.class,
                JooqBeesPilotBatchRepositoryConfig.class,
                FirebaseBatchChangedListener.class,
                AutoRegisterEventListenerConfig.class,
                FirebaseListenerCheckpointManager.class,
            })
    static class Config {
        @Bean
        FirebaseListener batchChangedListener(
                EventPublisher eventPublisher,
                Firestore firestore,
                FirebaseListenerCheckpointManager firebaseListenerCheckpointManager) {
            String collectionName = Batch.COLLECTION_NAME;
            Query query = firestore.collection(collectionName);
            String queryString = query.toProto().getStructuredQuery().toString();
            return new CheckpointCollectionChangedListener(
                    eventPublisher,
                    (f) -> query,
                    firebaseListenerCheckpointManager,
                    firebaseListenerCheckpointManager.getCheckpoint(
                            collectionName, queryString, "updatedAt", 0L));
        }

        @Bean
        FirebaseApi firebaseApi(Firestore firestore) {
            return new FirebaseApi(firestore);
        }

        @Bean
        FirebaseListenerManager firebaseCollectionRelayListener(
                FirebaseListener firebaseListener, Firestore firestore) {
            DefaultFirebaseListenerManager firebaseListenerManager =
                    new DefaultFirebaseListenerManager(firestore);
            firebaseListenerManager.register(firebaseListener);
            return firebaseListenerManager;
        }

        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired FirebaseApi firebaseApi;
    @Autowired DSLContext dsl;

    @Test
    void testFirebaseAddBatchShouldUpdatePostgres() throws InterruptedException {
        var batch = randomBatch();
        FirebaseApi.exec(
                () ->
                        firebaseApi
                                .collection("batch")
                                .document(batch.getBatchNo())
                                .create(getBatchMap(batch)));
        Thread.sleep(1000);
        assertEqual(batch.getBatchNo(), batch);
    }

    @Test
    void testFirebaseAddBatchWithItemNoExtraPayShouldUpdatePostgres() throws InterruptedException {
        var batch = randomBatchWithNoItemPay();
        FirebaseApi.exec(
                () ->
                        firebaseApi
                                .collection("batch")
                                .document(batch.getBatchNo())
                                .create(getBatchMap(batch)));
        Thread.sleep(1000);
        assertEqual(batch.getBatchNo(), batch);
    }

    @Test
    void testFirebaseUpdateBatchShouldUpdatePostgres() throws InterruptedException {
        var batch = randomBatch();
        var batchId = batch.getBatchNo();
        FirebaseApi.exec(
                () -> firebaseApi.collection("batch").document(batchId).create(getBatchMap(batch)));
        var updatedBatch = randomBatch(batchId, batch.getBatchItems());

        FirebaseApi.exec(
                () ->
                        firebaseApi
                                .collection("batch")
                                .document(batchId)
                                .set(getBatchMap(updatedBatch)));

        sleep();
        assertEqual(batch.getBatchNo(), updatedBatch);
    }

    @Test
    void testFirebaseDeleteBatchShouldUpdatePostgres() throws InterruptedException {
        var batch = randomBatch();
        var batchNo = batch.getBatchNo();
        FirebaseApi.exec(
                () -> firebaseApi.collection("batch").document(batchNo).create(getBatchMap(batch)));
        sleep();
        assertEqual(batch.getBatchNo(), batch);
        FirebaseApi.exec(
                () ->
                        firebaseApi
                                .collection("batch")
                                .document(batchNo)
                                .update(
                                        Map.of(
                                                "isDeleted",
                                                true,
                                                "updatedAt",
                                                Instant.now().toEpochMilli())));
        sleep();
        var record = getBatchRecord(batchNo);
        Assertions.assertNull(record);
    }

    /**
     * 更新firebase之后数据要同步到Postgres,即使没有updateAt字段，因为后台该值是通过 {@linkplain
     * QueryDocumentSnapshot#getUpdateTime()}获取的entity的更新时间的
     */
    @Test
    void testFirebaseDeleteBatchWithoutUpdateAtFiledShouldUpdatePostgres()
            throws InterruptedException {
        var batch = randomBatch();
        var batchNo = batch.getBatchNo();
        FirebaseApi.exec(
                () -> firebaseApi.collection("batch").document(batchNo).create(getBatchMap(batch)));
        sleep();
        assertEqual(batch.getBatchNo(), batch);
        FirebaseApi.exec(
                () ->
                        firebaseApi
                                .collection("batch")
                                .document(batchNo)
                                .update(Map.of("isDeleted", true)));
        sleep();
        var record = getBatchRecord(batchNo);
        Assertions.assertNull(record);
    }

    @Test
    void testFirebaseDeleteBatchItemShouldUpdatePostgres() throws InterruptedException {
        var batch = randomBatch();
        var batchNo = batch.getBatchNo();
        var item1 = batch.getBatchItems().get(0);
        FirebaseApi.exec(
                () -> firebaseApi.collection("batch").document(batchNo).create(getBatchMap(batch)));
        sleep();
        assertEqual(batch.getBatchNo(), batch);
        FirebaseApi.exec(
                () ->
                        firebaseApi
                                .collection("batch")
                                .document(batchNo)
                                .update(
                                        Map.of(
                                                "updatedAt",
                                                Instant.now().toEpochMilli(),
                                                "project." + item1.getProjectId() + ".isDeleted",
                                                true)));
        sleep();
        var record = getBatchItemRecord(batchNo, item1.getProjectId());
        Assertions.assertNull(record);
    }

    BeespilotBatchRecord getBatchRecord(String batchNo) {
        return dsl.selectFrom(BEESPILOT_BATCH)
                .where(BEESPILOT_BATCH.BATCH_NO.eq(batchNo).and(BEESPILOT_BATCH.DELETED.eq(false)))
                .fetchOne();
    }

    String getBatchItemRecord(String batchNo, String projectId) {
        return dsl.select(BEESPILOT_BATCH_ITEM.ID)
                .from(BEESPILOT_BATCH_ITEM)
                .join(BEESPILOT_BATCH)
                .on(BEESPILOT_BATCH.ID.eq(BEESPILOT_BATCH_ITEM.BEESPILOT_BATCH_ID))
                .where(
                        BEESPILOT_BATCH_ITEM
                                .PROJECT_ID
                                .eq(projectId)
                                .and(BEESPILOT_BATCH_ITEM.DELETED.eq(false))
                                .and(BEESPILOT_BATCH.DELETED.eq(false))
                                .and(BEESPILOT_BATCH.BATCH_NO.eq(batchNo)))
                .fetchOne(BEESPILOT_BATCH_ITEM.ID);
    }

    void assertEqual(String exceptBatchNo, BatchEntity actualBatch) {
        var record =
                dsl.selectFrom(BEESPILOT_BATCH)
                        .where(BEESPILOT_BATCH.BATCH_NO.eq(exceptBatchNo))
                        .fetchOne();
        Assertions.assertNotNull(record);
        var batchId = record.getId();
        var extraPay = record.getExtraPay();
        var basePay = record.getBasePay();
        Assertions.assertEquals(
                basePay, actualBatch.getBasePay(), "The batch base pay should match.");
        Assertions.assertEquals(
                extraPay, actualBatch.getExtraPay(), "The batch extra pay should match.");
        assertEqual(batchId, actualBatch.getBatchItems());
    }

    void assertEqual(String exceptBatchId, List<BatchItem> batchItems) {
        var recordMap =
                dsl.selectFrom(BEESPILOT_BATCH_ITEM)
                        .where(BEESPILOT_BATCH_ITEM.BEESPILOT_BATCH_ID.eq(exceptBatchId))
                        .fetchMap(BEESPILOT_BATCH_ITEM.PROJECT_ID);
        Assertions.assertNotNull(recordMap);
        Assertions.assertEquals(batchItems.size(), recordMap.size());
        for (BatchItem actualBatchItem : batchItems) {
            var record = recordMap.get(actualBatchItem.getProjectId());
            Assertions.assertEquals(
                    record.getBasePay(),
                    actualBatchItem.getBasePay(),
                    "The base pay should match.");
            Assertions.assertEquals(
                    record.getExtraPay(),
                    actualBatchItem.getExtraPay(),
                    "The extra pay should match.");
        }
    }

    BatchEntity randomBatch() {
        return randomBatch(
                RandomStringUtils.randomAlphabetic(9),
                List.of(randomBatchItem(), randomBatchItem()));
    }

    BatchEntity randomBatchWithNoItemPay() {
        return BatchEntity.toBuilder()
                .batchNo(RandomStringUtils.randomAlphabetic(9))
                .basePay(randomPay())
                .extraPay(randomPay())
                .batchItems(List.of(randomBatchItemNoPay()))
                .build();
    }

    BatchEntity randomBatch(String batchNo, List<BatchItem> items) {
        items =
                items.stream()
                        .map(
                                item ->
                                        BatchItem.toBuilder()
                                                .isDeleted(item.isDeleted())
                                                .projectId(item.getProjectId())
                                                .basePay(randomPay())
                                                .extraPay(randomPay())
                                                .build())
                        .collect(Collectors.toList());
        return BatchEntity.toBuilder()
                .batchNo(batchNo)
                .basePay(randomPay())
                .extraPay(randomPay())
                .batchItems(items)
                .build();
    }

    BatchItem randomBatchItem() {
        return BatchItem.toBuilder()
                .projectId(String.valueOf(RandomUtils.nextLong()))
                .extraPay(randomPay())
                .basePay(randomPay())
                .build();
    }

    BatchItem randomBatchItemNoPay() {
        return BatchItem.toBuilder().projectId(String.valueOf(RandomUtils.nextLong())).build();
    }

    Map<String, Object> getBatchMap(BatchEntity batch) {

        return Map.of(
                "basePay", batch.getBasePay(),
                "extraPay", batch.getExtraPay(),
                "isDeleted", false,
                "createdAt", Instant.now().toEpochMilli(),
                "updatedAt", Instant.now().toEpochMilli(),
                "project",
                        batch.getBatchItems().stream()
                                .collect(
                                        Collectors.toMap(
                                                BatchItem::getProjectId, this::getBatchItemMap)));
    }

    Map<String, Object> getBatchItemMap(BatchItem batchItem) {
        var map = new HashMap<String, Object>();
        map.put("isDeleted", false);
        map.put("updateTime", Instant.now().toEpochMilli());
        Optional.ofNullable(batchItem.getBasePay())
                .ifPresent(basePay -> map.put("basePay", basePay));
        Optional.ofNullable(batchItem.getExtraPay())
                .ifPresent(extraPay -> map.put("extraPay", extraPay));
        return map;
    }

    float randomPay() {
        return RandomUtils.nextInt(0, 10000) / 10.0F;
    }

    private void sleep() throws InterruptedException {
        Thread.sleep(3000);
    }
}
