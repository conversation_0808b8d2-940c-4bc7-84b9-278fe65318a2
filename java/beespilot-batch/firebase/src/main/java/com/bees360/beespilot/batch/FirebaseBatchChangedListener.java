package com.bees360.beespilot.batch;

import com.bees360.beespilot.batch.util.BatchEntity;
import com.bees360.beespilot.batch.util.BatchEntity.BatchItem;
import com.bees360.event.registry.FirebaseBatchChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.firebase.domain.Batch;
import com.bees360.util.DateTimes;
import com.google.gson.Gson;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.stream.Collectors;

/** 监听Firebase批次变更事件并更新本地批次数据 */
@Log4j2
public class FirebaseBatchChangedListener extends AbstractNamedEventListener<FirebaseBatchChanged> {
    private final BeesPilotBatchRepository batchManager;

    public FirebaseBatchChangedListener(BeesPilotBatchRepository batchManager) {
        this.batchManager = batchManager;
        log.info("Created '{}(batchManager={}'", this, this.batchManager);
    }

    private static final Gson gson = new Gson();

    @Override
    public void handle(FirebaseBatchChanged event) throws IOException {
        event.getDocumentsChanged().stream()
                .map(json -> gson.fromJson(json, Batch.class))
                .forEach(this::updateBatch);
    }

    private void updateBatch(Batch batch) {
        var batchNo = batch.getId();
        var updateEpochMilli =
                DateTimes.toEpochMilli(batch.getUpdateTime().toSqlTimestamp().toLocalDateTime());
        if (batch.isRemoved() || batch.isDeleted()) {
            batchManager.deleteByBatchNo(batchNo, updateEpochMilli);
            return;
        }

        var projects = batch.getProject();
        if (projects == null) {
            return;
        }
        var items =
                projects.entrySet().stream()
                        .map(
                                e ->
                                        BatchItem.toBuilder()
                                                .projectId(e.getKey())
                                                .basePay(e.getValue().getBasePay())
                                                .extraPay(e.getValue().getExtraPay())
                                                .isDeleted(e.getValue().isDeleted())
                                                .build())
                        .collect(Collectors.toList());
        var batchEntity =
                BatchEntity.toBuilder()
                        .batchNo(batchNo)
                        .basePay(batch.getBasePay())
                        .extraPay(batch.getExtraPay())
                        .batchItems(items)
                        .build();
        batchManager.save(batchEntity, updateEpochMilli);
    }
}
