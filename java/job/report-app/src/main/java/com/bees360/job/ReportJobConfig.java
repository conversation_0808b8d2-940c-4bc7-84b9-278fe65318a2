package com.bees360.job;

import static com.bees360.job.GroupType.GROUP_PROJECT;
import static com.bees360.job.ReportJobNames.CHAIN_PDF_COMPRESS_JOB_NAME;
import static com.bees360.job.ReportJobNames.CHAIN_PDF_SPLIT_JOB_NAME;
import static com.bees360.job.ReportJobNames.GENERATE_DPS_JOB_NAME;
import static com.bees360.job.ReportJobNames.GENERATE_MERGE_PDF_REPORT;
import static com.bees360.job.ReportJobNames.GENERATE_MPS_JOB_NAME;
import static com.bees360.job.ReportJobNames.GENERATE_PDF_REPORT;
import static com.bees360.job.ReportJobNames.GENERATE_REPORT_DOCUMENT;
import static com.bees360.job.ReportJobNames.GENERATE_REPORT_RESOURCE;
import static com.bees360.job.ReportJobNames.UNARCHIVE_AND_SAVE_REPORT;

import com.bees360.job.autoconfig.EnableJobAutoRegister;
import com.bees360.job.command.CommandJobExecutor;
import com.bees360.job.command.GenericCommandChainJobExecutor;
import com.bees360.job.command.HtmlToPdfJobExecutor;
import com.bees360.job.registry.GenericCommandJob;
import com.bees360.job.registry.PostGenReportDocProcessJob;
import com.bees360.job.registry.SaveReportJob;
import com.bees360.job.registry.SaveReportResourceJob;
import com.bees360.job.util.PdfCompressPostProcessor;
import com.bees360.job.util.ResetImagesSrcProcessor;
import com.bees360.job.util.RmImageCrossOriginProcessor;
import com.bees360.map.BasicMap;
import com.bees360.map.RedisMap;
import com.bees360.report.Message.ReportProcessStatus;
import com.bees360.report.ReportProcessStatusManager;
import com.bees360.resource.ResourcePool;
import com.bees360.util.SecureTokens;
import com.google.common.util.concurrent.ListenableFuture;

import jakarta.annotation.Nullable;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Optional;

@Log4j2
@Configuration
@ConditionalOnProperty(prefix = "report", value = "enable", havingValue = "true")
@EnableJobAutoRegister
public class ReportJobConfig {
    private static final String DPS_REPORT_TYPE = "37";
    private static final String MPS_REPORT_TYPE = "38";

    @Data
    @ConfigurationProperties(prefix = "report.split")
    @Configuration
    static class SplitPdfProperty {
        private Integer splitStep;
        // 报告切片起压值，压缩后的切片不应该大于该值
        private Long compressThresholdSize;

        // 当只切分成两片时保证两片页数尽可能相等
        private boolean averageOnSliceTwo;
    }

    @Data
    @ConfigurationProperties(prefix = "report.process-status")
    @Configuration
    static class ReportProcessStatusProperties {
        private String systemUserId;
    }

    @Bean
    RedisMap<String, Message.JobListMessage> jobMap(RedissonClient redissonClient) {
        return new RedisMap<>(redissonClient, "chain_job", null);
    }

    static String getReportName(String reportType, @Nullable String spliceIndex) {
        String filename;
        switch (reportType) {
            case DPS_REPORT_TYPE:
                filename = "Drone Photo Sheet";
                break;
            case MPS_REPORT_TYPE:
                filename = "Mobile Photo Sheet";
                break;
            default:
                filename = SecureTokens.generateRandomHexToken(16);
                break;
        }
        return spliceIndex == null ? filename + ".pdf" : filename + " - " + spliceIndex + ".pdf";
    }

    @ConditionalOnProperty(prefix = "report.split", value = "enable", havingValue = "true")
    static class SplitConfig {

        @Bean
        GenericCommandChainJobExecutor pdfSplitToCompressJobExecutor(
                JobScheduler jobScheduler,
                BasicMap<String, Message.JobListMessage> jobMap,
                ResourcePool resourcePool,
                SplitPdfProperty property) {
            var pdfSplitCommandJobExecutor =
                    new CommandJobExecutor(resourcePool, CHAIN_PDF_SPLIT_JOB_NAME);
            return new GenericCommandChainJobExecutor(
                    jobScheduler,
                    pdfSplitCommandJobExecutor,
                    jobMap,
                    job -> List.of(getPdfCompressJob(job, property.getCompressThresholdSize())));
        }
    }

    @Bean
    @ConditionalOnProperty(prefix = "report.generate-dps", value = "enable", havingValue = "true")
    GenericCommandChainJobExecutor generateDpsExecutor(
            JobScheduler jobScheduler,
            BasicMap<String, Message.JobListMessage> jobMap,
            ResourcePool resourcePool,
            RmImageCrossOriginProcessor rmImageCrossOriginProcessor,
            ResetImagesSrcProcessor resetImagesSrcProcessor,
            PdfCompressPostProcessor pdfCompressPostProcessor) {
        var generateDpsExecutor =
                new HtmlToPdfJobExecutor(
                        resourcePool,
                        GENERATE_DPS_JOB_NAME,
                        List.of(rmImageCrossOriginProcessor, resetImagesSrcProcessor),
                        List.of(pdfCompressPostProcessor));
        return new GenericCommandChainJobExecutor(
                jobScheduler,
                generateDpsExecutor,
                jobMap,
                job -> List.of(getReportCreationJob(job)));
    }

    @Bean
    @ConditionalOnProperty(prefix = "report.generate-mps", value = "enable", havingValue = "true")
    GenericCommandChainJobExecutor generateMpsExecutor(
            JobScheduler jobScheduler,
            BasicMap<String, Message.JobListMessage> jobMap,
            ResourcePool resourcePool,
            RmImageCrossOriginProcessor rmImageCrossOriginProcessor,
            ResetImagesSrcProcessor resetImagesSrcProcessor,
            PdfCompressPostProcessor pdfCompressPostProcessor) {
        var generateMpsExecutor =
                new HtmlToPdfJobExecutor(
                        resourcePool,
                        GENERATE_MPS_JOB_NAME,
                        List.of(rmImageCrossOriginProcessor, resetImagesSrcProcessor),
                        List.of(pdfCompressPostProcessor));
        return new GenericCommandChainJobExecutor(
                jobScheduler,
                generateMpsExecutor,
                jobMap,
                job -> List.of(getReportCreationJob(job)));
    }

    @Bean
    @ConditionalOnProperty(prefix = "report.generate-pdf", value = "enable", havingValue = "true")
    GenericCommandChainJobExecutor generatePdfReportExecutor(
            JobScheduler jobScheduler,
            BasicMap<String, Message.JobListMessage> jobMap,
            ResourcePool resourcePool,
            RmImageCrossOriginProcessor rmImageCrossOriginProcessor,
            ResetImagesSrcProcessor resetImagesSrcProcessor,
            PdfCompressPostProcessor pdfCompressPostProcessor) {
        var generateMpsExecutor =
                new HtmlToPdfJobExecutor(
                        resourcePool,
                        GENERATE_PDF_REPORT,
                        List.of(rmImageCrossOriginProcessor, resetImagesSrcProcessor),
                        List.of(pdfCompressPostProcessor));
        return new GenericCommandChainJobExecutor(
                jobScheduler,
                generateMpsExecutor,
                jobMap,
                job -> List.of(getReportCreationJob(job)));
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "report.unarchive-report",
            value = "enable",
            havingValue = "true")
    GenericCommandChainJobExecutor unarchiveReport(
            JobScheduler jobScheduler,
            BasicMap<String, Message.JobListMessage> jobMap,
            ResourcePool resourcePool) {
        var unarchiveFile = new CommandJobExecutor(resourcePool, UNARCHIVE_AND_SAVE_REPORT);
        return new GenericCommandChainJobExecutor(
                jobScheduler, unarchiveFile, jobMap, job -> List.of(getReportCreationJob(job)));
    }

    @Bean
    @ConditionalOnProperty(prefix = "report.merge", value = "enable", havingValue = "true")
    GenericCommandChainJobExecutor mergePdfReportExecutor(
            JobScheduler jobScheduler,
            BasicMap<String, Message.JobListMessage> jobMap,
            ResourcePool resourcePool) {
        var mergePdfExecutor = new CommandJobExecutor(resourcePool, GENERATE_MERGE_PDF_REPORT);
        return new GenericCommandChainJobExecutor(
                jobScheduler, mergePdfExecutor, jobMap, job -> List.of(getReportCreationJob(job)));
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "report.merge-with-status",
            value = "enable",
            havingValue = "true")
    GenericCommandChainJobExecutor mergePdfReportWithStatusExecutor(
            JobScheduler jobScheduler,
            BasicMap<String, Message.JobListMessage> jobMap,
            ResourcePool resourcePool,
            ReportProcessStatusManager grpcReportProcessStatusClient,
            ReportProcessStatusProperties properties) {
        var mergePdfExecutor = new CommandJobExecutor(resourcePool, GENERATE_MERGE_PDF_REPORT);
        return new GenericCommandChainJobExecutor(
                jobScheduler,
                mergePdfExecutor,
                jobMap,
                job -> List.of(getReportCreationJob(job, ReportProcessStatus.Type.MERGE))) {
            @Override
            protected ListenableFuture<List<?>> acceptAndSpawnNextJob(GenericCommandJob job) {
                log.debug("Update report merge status and spawn save report job for {}.", job);
                var metadata = job.getMetadata();
                var groupKey =
                        Optional.ofNullable(metadata.get("groupKey"))
                                .orElse(metadata.get("projectId"));
                var groupType =
                        Optional.ofNullable(metadata.get("groupType")).orElse(GROUP_PROJECT.name());
                var reportType = metadata.get("reportType");
                try {
                    grpcReportProcessStatusClient.updateReportProcessStatus(
                            groupKey,
                            groupType,
                            reportType,
                            ReportProcessStatus.Type.MERGE,
                            ReportProcessStatus.Status.PROCESSING,
                            properties.getSystemUserId(),
                            null);
                } catch (RuntimeException e) {
                    // logging exception but not throwing
                    log.warn(
                            "Fail to update report merge status by groupKey={}, groupType={},"
                                    + " reportType={}.",
                            groupKey,
                            groupType,
                            reportType,
                            e);
                }
                return super.acceptAndSpawnNextJob(job);
            }
        };
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "report.generate.resource",
            value = "enable",
            havingValue = "true",
            matchIfMissing = true)
    GenericCommandChainJobExecutor generatePdfByHtml(
            JobScheduler jobScheduler,
            BasicMap<String, Message.JobListMessage> jobMap,
            ResourcePool resourcePool,
            RmImageCrossOriginProcessor rmImageCrossOriginProcessor,
            ResetImagesSrcProcessor resetImagesSrcProcessor,
            PdfCompressPostProcessor pdfCompressPostProcessor) {
        var htmlToPdfJobExecutor =
                new HtmlToPdfJobExecutor(
                        resourcePool,
                        GENERATE_REPORT_RESOURCE,
                        List.of(rmImageCrossOriginProcessor, resetImagesSrcProcessor),
                        List.of(pdfCompressPostProcessor));
        return new GenericCommandChainJobExecutor(
                jobScheduler,
                htmlToPdfJobExecutor,
                jobMap,
                job -> List.of(getSaveReportResourceJob(job)));
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "report.document.generate",
            value = "enable",
            havingValue = "true",
            matchIfMissing = false)
    GenericCommandChainJobExecutor generateReportDocument(
            JobScheduler jobScheduler,
            BasicMap<String, Message.JobListMessage> jobMap,
            ResourcePool resourcePool) {
        var executor = new CommandJobExecutor(resourcePool, GENERATE_REPORT_DOCUMENT);
        return new GenericCommandChainJobExecutor(
                jobScheduler,
                executor,
                jobMap,
                job -> List.of(new PostGenReportDocProcessJob(job.getMetadata())));
    }

    private static Object getSaveReportResourceJob(GenericCommandJob projectCommandJob) {
        var metadata = projectCommandJob.getMetadata();
        var reportId = metadata.get("reportId");
        var reportType = metadata.get("type");
        var reportKey = metadata.get("reportKey");
        return SaveReportResourceJob.newBuilder()
                .setReportId(reportId)
                .setType(Integer.parseInt(reportType))
                .setResourceKey(reportKey)
                .build();
    }

    private static Object getPdfCompressJob(
            GenericCommandJob projectCommandJob, Long compressThresholdSize) {
        var inputUrl = projectCommandJob.getOutputUrl();
        var compressJob =
                (CommandJob)
                        PdfCompressJob.getInstance(
                                SecureTokens.generateRandomBase64Token(16),
                                compressThresholdSize,
                                inputUrl,
                                inputUrl);
        var metadata = projectCommandJob.getMetadata();
        return GenericCommandJob.getInstance(
                metadata, compressJob.toMessage(), CHAIN_PDF_COMPRESS_JOB_NAME);
    }

    public static Job getReportCreationJob(GenericCommandJob job, ReportProcessStatus.Type type) {
        var metadata = job.getMetadata();
        var groupKey =
                Optional.ofNullable(metadata.get("groupKey")).orElse(metadata.get("projectId"));
        var groupType = Optional.ofNullable(metadata.get("groupType")).orElse(GROUP_PROJECT.name());
        var reportType = metadata.get("reportType");
        var jobId =
                StringUtils.joinWith(
                        "-",
                        type.name(),
                        groupKey,
                        groupType,
                        reportType,
                        SecureTokens.generateRandomHexToken(8));
        var reportCreationJob = getReportCreationJob(job);
        return Job.ofPayload(reportCreationJob, jobId);
    }

    // TODO 这里构建Job的方法可以优化一下
    public static Object getReportCreationJob(GenericCommandJob projectCommandJob) {
        var metadata = projectCommandJob.getMetadata();
        // todo projectId 字段已弃用，请使用 groupKey 和 groupType 字段。
        var projectId = metadata.get("projectId");
        var groupKey = Optional.ofNullable(metadata.get("groupKey")).orElse(projectId);
        var groupType = Optional.ofNullable(metadata.get("groupType")).orElse(GROUP_PROJECT.name());
        var reportType = metadata.get("reportType");
        var reportKey = metadata.get("reportKey");
        var summary = Optional.ofNullable(metadata.get("summary")).orElse(StringUtils.EMPTY);
        var summaryJsonString =
                Optional.ofNullable(metadata.get("summaryJsonString")).orElse(StringUtils.EMPTY);
        var htmlKey = Optional.ofNullable(metadata.get("htmlKey")).orElse(StringUtils.EMPTY);
        var createdBy = metadata.get("createdBy");
        // reportType, reportKey, summary, htmlKey, createdBy
        return SaveReportJob.newBuilder()
                .setCreatedBy(createdBy)
                .setReportKey(reportKey)
                .setSummary(summary)
                .setSummaryJsonString(summaryJsonString)
                .setReportType(reportType)
                .setHtmlKey(htmlKey)
                .setGroupKey(groupKey)
                .setGroupType(groupType)
                .build();
    }
}
