package com.bees360.lc360.config;

import com.bees360.event.AddPauseReasonNoteToInspectionOnProjectPause;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegrationProvider;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.Function;
import java.util.function.Predicate;

@Configuration
@ConditionalOnProperty(
        prefix = "app.lc360.inspection.note",
        name = "add-pause-reason-note-on-project-pause",
        havingValue = "true")
@Import({
    LC360CustomerConfig.class,
})
public class AddPauseReasonNoteToInspectionOnProjectPauseConfig {

    @Bean
    AddPauseReasonNoteToInspectionOnProjectPause addPauseReasonNoteToInspectionOnProjectPause(
            ExternalIntegrationProvider externalIntegrationProvider,
            Function<String, Lc360Api> lc360ApiProvider,
            Predicate<String> enableSyncProjectPauseNote) {
        return new AddPauseReasonNoteToInspectionOnProjectPause(
                externalIntegrationProvider, lc360ApiProvider, enableSyncProjectPauseNote);
    }

    @Bean("enableSyncProjectPauseNote")
    Predicate<String> enableSyncProjectPauseNote(
            LC360CustomerConfig.Lc360CustomerProperties properties) {
        return dataset ->
                properties.getDatasets().stream()
                        .filter(d -> StringUtils.equals(dataset, d.getId()))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableSyncProjectPauseNote)
                        .findFirst()
                        .orElse(true);
    }
}
