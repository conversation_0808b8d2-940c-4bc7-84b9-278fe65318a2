package com.bees360.event;

import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.lc360.CancelInspectionResponse;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;
import com.bees360.project.state.ProjectState;
import com.google.common.collect.Iterables;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.TriConsumer;

import java.io.IOException;
import java.util.function.Function;
import java.util.function.Predicate;

/** 监听项目状态变更事件，在项目取消时调用外部API取消关联的检查任务 */
@Log4j2
public class CancelInspectionOnProjectCancel
        extends AbstractNamedEventListener<ProjectStateChangedEvent> {
    private static final String PROJECT_STATE_CHANGE_REASON_COMPLETED_KEY = "COMPLETED";
    private static final String INTEGRATION_TYPE = "LossControl360";
    private final ExternalIntegrationProvider externalIntegrationProvider;
    private final Function<String, Lc360Api> lc360ApiProvider;
    private final TriConsumer<String, String, CancelInspectionResponse> postCancelInspection;
    private final Predicate<String> enableCancelInspection;

    public CancelInspectionOnProjectCancel(
            @NonNull ExternalIntegrationProvider externalIntegrationProvider,
            @NonNull Function<String, Lc360Api> lc360ApiProvider,
            @NonNull TriConsumer<String, String, CancelInspectionResponse> postCancelInspection,
            Predicate<String> enableCancelInspection) {
        this.externalIntegrationProvider = externalIntegrationProvider;
        this.lc360ApiProvider = lc360ApiProvider;
        this.postCancelInspection = postCancelInspection;
        this.enableCancelInspection = enableCancelInspection;
        log.info(
                "Created"
                    + " {}(externalIntegrationProvider={},lc360ApiProvider={},postCancelInspection={}})",
                this,
                externalIntegrationProvider,
                lc360ApiProvider,
                postCancelInspection);
    }

    @Override
    public void handle(ProjectStateChangedEvent event) throws IOException {
        log.info("Received Event: {}", event);
        var state = event.getCurrentState();
        if (!isCloseSinceCancelation(state)) {
            return;
        }
        var projectId = String.valueOf(event.getProjectId());
        var integration = findIntegration(projectId);
        if (integration == null || !enableCancelInspection.test(integration.getDataset())) {
            return;
        }
        var cancelReason = state.getStateChangeReason().getDisplayText();
        cancelReason = StringUtils.isEmpty(cancelReason) ? "No reason provided." : cancelReason;

        var referNumber = integration.getReferenceNumber();
        var lc360IntegrationApi =
                lc360ApiProvider.apply(integration.getDataset()).getIntegrationApi();
        var response = lc360IntegrationApi.cancelInspection(referNumber, cancelReason, "");
        log.info(
                "Canceling inspection {} of project {} with response {}",
                referNumber,
                projectId,
                response);
        postCancelInspection.accept(projectId, referNumber, response);
    }

    private ExternalIntegration findIntegration(String projectId) {
        var integrations =
                externalIntegrationProvider.findAllByProjectId(String.valueOf(projectId));
        return Iterables.find(
                integrations,
                i -> StringUtils.equals(INTEGRATION_TYPE, i.getIntegrationType()),
                null);
    }

    private boolean isCloseSinceCancelation(ProjectState state) {
        if (!ProjectStateEnum.PROJECT_CLOSE.equals(state.getState())) {
            return false;
        }
        return !StringUtils.equalsIgnoreCase(
                PROJECT_STATE_CHANGE_REASON_COMPLETED_KEY, state.getStateChangeReason().getKey());
    }
}
