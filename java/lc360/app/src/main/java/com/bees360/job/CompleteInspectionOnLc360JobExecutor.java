package com.bees360.job;

import com.bees360.integration.IntegrationSummaryProvider;
import com.bees360.job.registry.CompleteInspectionOnLc360Job;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.lc360.Lc360Api;
import com.bees360.util.DateTimes;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;

@Log4j2
public class CompleteInspectionOnLc360JobExecutor
        extends AbstractJobExecutor<CompleteInspectionOnLc360Job> {

    private final Function<String, Lc360Api> lc360ApiProvider;

    private final IntegrationSummaryProvider summaryProvider;

    private static final ZoneId US_CENTRAL_ZONE = ZoneId.of("US/Central");

    private final Predicate<String> enableMoveForwardTwice;

    public CompleteInspectionOnLc360JobExecutor(
            @NonNull Function<String, Lc360Api> lc360ApiProvider,
            @NonNull IntegrationSummaryProvider summaryProvider,
            @NonNull Predicate<String> enableMoveForwardTwice) {
        this.lc360ApiProvider = lc360ApiProvider;
        this.summaryProvider = summaryProvider;
        this.enableMoveForwardTwice = enableMoveForwardTwice;
        log.info("Created {}.", this);
    }

    @Override
    protected void handle(CompleteInspectionOnLc360Job job) throws IOException {
        log.info("Begin to handle job {} for complete inspection in LossControl360.", job);
        var inspectionId = job.getInspectionId();
        var projectId = job.getProjectId();
        var summary = summaryProvider.collectByProjectId(projectId);

        if (summary == null) {
            return;
        }
        var siteInspectedTime =
                DateTimes.toInstant(summary.getProjectSummary().getSiteInspectionTime());
        var dateVisited =
                Optional.ofNullable(siteInspectedTime)
                        .map(s -> LocalDateTime.ofInstant(s, US_CENTRAL_ZONE))
                        .orElse(null);

        var dataset = job.getDataset();
        var lc360Api = lc360ApiProvider.apply(dataset).getVendorApi();
        lc360Api.moveForwardInspection(inspectionId, null, dateVisited, null);
        if (enableMoveForwardTwice.test(dataset)) {
            lc360Api.moveForwardInspection(inspectionId, null, dateVisited, null);
        }
    }
}
