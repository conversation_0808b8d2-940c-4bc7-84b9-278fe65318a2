package com.bees360.lc360.config;

import com.bees360.event.EventDispatcher;
import com.bees360.event.HandleProjectCloseoutOnProjectStateChanged;
import com.bees360.job.JobScheduler;
import com.bees360.job.TriggerFillOutUTCFormJobExecutor;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.lc360.Lc360Api;
import com.bees360.lc360.util.LC360FormDataUtil;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.IntegrationFormProvider;
import com.bees360.project.ProjectIIManager;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;

@Configuration
@Import({
    LC360FullCustomerProperties.class,
    LC360CustomerApiConfig.class,
    AutoRegisterJobExecutorConfig.class,
    LC360FormUtilConfig.class,
    ProjectCloseoutConfig.class,
})
@ConditionalOnProperty(
        prefix = "lc360.app",
        name = "enable-handle-project-close-out",
        havingValue = "true")
public class HandleProjectCloseoutConfig {

    // All canceled project should be returned to lc360
    @Bean
    public HandleProjectCloseoutOnProjectStateChanged handleProjectCloseoutOnProjectStateChanged(
            EventDispatcher eventDispatcher,
            ExternalIntegrationProvider integrationProvider,
            JobScheduler jobScheduler,
            @Qualifier("lc360ApiProvider") Function<String, Lc360Api> lc360ApiProvider,
            LC360FullCustomerProperties customerProperties,
            ProjectIIManager projectManager,
            @Qualifier("projectCanceledPredicate") Predicate<String> projectCanceledPredicate) {
        var listener =
                new HandleProjectCloseoutOnProjectStateChanged(
                        integrationProvider,
                        jobScheduler,
                        enableHandleProjectCloseout(customerProperties),
                        lc360ApiProvider,
                        closeoutInspectionTypeProvider(customerProperties, projectManager),
                        projectCanceledPredicate);
        eventDispatcher.enlist(listener);
        return listener;
    }

    @Bean
    public TriggerFillOutUTCFormJobExecutor triggerUploadUTCFormJobExecutor(
            IntegrationFormProvider lc360IntegrationFormManager,
            PipelineService pipelineService,
            LC360FormDataUtil formDataUtil) {
        return new TriggerFillOutUTCFormJobExecutor(
                lc360IntegrationFormManager,
                pipelineService,
                dataset -> formDataUtil.getFormTemplateByDataset(dataset, true));
    }

    private Predicate<String> enableHandleProjectCloseout(LC360FullCustomerProperties properties) {
        return dataset ->
                Optional.ofNullable(properties.getDataset(dataset))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableHandleProjectCloseout)
                        .orElse(false);
    }

    private Function<ExternalIntegration, String> closeoutInspectionTypeProvider(
            LC360FullCustomerProperties properties, ProjectIIManager projectManager) {
        return integration -> {
            var projectId = integration.getProjectId();
            var project = projectManager.findById(projectId);
            var customerKey = project.getContract().getInsuredBy().getCompanyKey();
            var dataset = properties.getDataset(integration.getDataset());

            return Optional.ofNullable(dataset)
                    .map(d -> d.getCloseoutLookupIdByInsuredBy(customerKey))
                    .orElseThrow(null);
        };
    }
}
