package com.bees360.job;

import com.bees360.job.registry.SendProjectClosedEmailJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.mail.MailSender;
import com.bees360.mail.util.MailMessageFactory;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.gson.Gson;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.UnaryOperator;

@Log4j2
public class SendProjectClosedEmailJobExecutor
        extends AbstractJobExecutor<SendProjectClosedEmailJob> {

    private final ProjectIIManager projectIIManager;

    private final ContactManager contactManager;

    private final MailMessageFactory mailMessageFactory;

    private final MailSender mailSender;

    private final Function<String, List<String>> recipientProvider;

    private final UnaryOperator<String> emailTemplateProvider;

    private final UnaryOperator<String> projectLinkProvider;

    private final Gson gson = new Gson();

    public SendProjectClosedEmailJobExecutor(
            ProjectIIManager projectIIManager,
            ContactManager contactManager,
            MailMessageFactory mailMessageFactory,
            MailSender mailSender,
            Function<String, List<String>> recipientProvider,
            UnaryOperator<String> closeReasonToEmailTemplateProvider,
            UnaryOperator<String> projectLinkProvider) {
        this.projectIIManager = projectIIManager;
        this.contactManager = contactManager;
        this.mailMessageFactory = mailMessageFactory;
        this.mailSender = mailSender;
        this.recipientProvider = recipientProvider;
        this.emailTemplateProvider = closeReasonToEmailTemplateProvider;
        this.projectLinkProvider = projectLinkProvider;
        log.info("Created {}.", this);
    }

    @Override
    protected void handle(SendProjectClosedEmailJob job) throws IOException {
        log.info("Received job {}.", job);

        var projectId = job.getProjectId();
        var project = projectIIManager.findById(projectId);
        var insuredBy = project.getContract().getInsuredBy().getCompanyKey();
        var recipients = recipientProvider.apply(insuredBy);

        if (CollectionUtils.isEmpty(recipients)) {
            throw new IllegalArgumentException(
                    String.format(
                            "Send project %s closed email fail: no recipients provided.",
                            projectId));
        }

        var emailTemplate = emailTemplateProvider.apply(job.getCloseReason());
        if (emailTemplate == null) {
            throw new IllegalArgumentException(
                    String.format(
                            "Send project %s closed email fail: no email template for close reason"
                                    + " '%s'.",
                            projectId, job.getCloseReason()));
        }

        var variables = toVariablesJson(project);
        var mailMessage =
                mailMessageFactory.create(
                        recipients, emailTemplate, variables, Collections.emptyMap());

        Futures.addCallback(
                mailSender.send(mailMessage),
                new FutureCallback<>() {
                    @Override
                    public void onSuccess(@Nullable Void result) {
                        log.info(
                                "Successfully send project {} closed email with template {}.",
                                projectId,
                                emailTemplate);
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        log.error("Send project {} closed email fail.", projectId, t);
                    }
                },
                MoreExecutors.directExecutor());

        log.info("Finish sending project closed email to recipients {}.", recipients);
    }

    private String toVariablesJson(ProjectII project) {
        var insuredName = getInsured(project.getId());
        var policyNo = project.getPolicy().getPolicyNo();
        var address = project.getPolicy().getAddress().getAddress();
        var projectUrl = projectLinkProvider.apply(project.getId());
        Map<String, Object> map =
                Map.of(
                        "insuredName",
                        insuredName,
                        "policyNumber",
                        policyNo,
                        "address",
                        address,
                        "projectUrl",
                        projectUrl);
        return gson.toJson(map);
    }

    private String getInsured(String projectId) {
        var insured =
                Iterables.toStream(contactManager.findByProjectId(projectId))
                        .filter(c -> ContactRoleEnum.INSURED.getName().equals(c.getRole()))
                        .findFirst();

        return insured.map(Contact::getFullName)
                .orElseThrow(
                        () ->
                                new IllegalArgumentException(
                                        String.format(
                                                "Send project %s closed email fail: insured contact"
                                                        + " information is empty.",
                                                projectId)));
    }
}
