package com.bees360.lc360.config;

import com.bees360.event.EventDispatcher;
import com.bees360.http.HttpClient;
import com.bees360.image.ImageGroupProvider;
import com.bees360.image.ImageTagProvider;
import com.bees360.image.tag.ImageTag;
import com.bees360.integration.IntegrationSummaryProvider;
import com.bees360.job.CollectDataAndUploadLc360FormJobExecutor;
import com.bees360.job.CompleteInspectionOnLc360JobExecutor;
import com.bees360.job.JobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.lc360.Lc360Api;
import com.bees360.lc360.form.LC360IntegrationFormManager;
import com.bees360.lc360.listener.ScheduleUploadLC360FormOnTaskReady;
import com.bees360.lc360.util.FormDataResponse;
import com.bees360.lc360.util.LC360FormDataUtil;
import com.bees360.lc360.util.ProjectSummaryUtil;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.IntegrationFormManager;
import com.bees360.util.Functions;
import com.google.gson.Gson;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Configuration
@ConditionalOnProperty(value = "lc360.form.enabled", havingValue = "true")
@Import({
    AWSLambdaHttpClientConfig.class,
    LC360FullCustomerProperties.class,
    ProjectCloseoutConfig.class,
    LC360FormUtilConfig.class,
    AutoRegisterJobExecutorConfig.class,
})
public class LC360FormConfig {

    public static final Gson gson = new Gson();

    @Bean
    public ScheduleUploadLC360FormOnTaskReady scheduleUploadLC360FormListener(
            EventDispatcher eventDispatcher,
            ExternalIntegrationManager externalIntegrationManager,
            IntegrationFormManager lc360IntegrationFormManager,
            @Qualifier("formTemplateProvider")
                    BiFunction<String, String, Set<String>> formTemplateProvider,
            JobScheduler jobScheduler,
            PipelineService pipelineService,
            LC360FullCustomerProperties properties) {
        var listener =
                new ScheduleUploadLC360FormOnTaskReady(
                        externalIntegrationManager,
                        lc360IntegrationFormManager,
                        formTemplateProvider,
                        jobScheduler,
                        pipelineService,
                        enableMoveForward(properties),
                        enableUpdateForm(properties));
        eventDispatcher.enlist(listener);
        return listener;
    }

    /**
     * Get template of forms for customer that need to be filled out on lc360. If project is closed,
     * it will return forms for close used.
     */
    @Bean("formTemplateProvider")
    public BiFunction<String, String, Set<String>> customerFormsProvider(
            LC360FormDataUtil lc360FormDataUtil,
            @Qualifier("projectCloseoutPredicate") Predicate<String> projectCloseoutPredicate) {
        return (customer, projectId) -> {
            if (projectCloseoutPredicate.test(projectId)) {
                return lc360FormDataUtil.getFormTemplateByDataset(customer, true);
            }

            return lc360FormDataUtil.getFormTemplateByDataset(customer, false);
        };
    }

    @Bean
    public ProjectSummaryUtil lc360ProjectSummaryUtil(
            Function<List<String>, Map<String, String>> imageIdConverter,
            Function<List<String>, Map<String, Iterable<? extends ImageTag>>> imageTagProvider) {
        return new ProjectSummaryUtil(imageIdConverter, imageTagProvider);
    }

    @Bean
    public CollectDataAndUploadLc360FormJobExecutor collectDataAndUploadLc360FormJobExecutor(
            IntegrationSummaryProvider summaryProvider,
            Function<String, FormDataResponse> httpFormDataProcessor,
            Function<String, Lc360Api> lc360ApiProvider,
            IntegrationFormManager lc360IntegrationFormManager,
            ProjectSummaryUtil projectSummaryUtil,
            LC360FormDataUtil lc360FormDataUtil) {
        return new CollectDataAndUploadLc360FormJobExecutor(
                summaryProvider,
                httpFormDataProcessor,
                lc360ApiProvider,
                lc360FormDataUtil::getFormInternalKey,
                lc360IntegrationFormManager,
                projectSummaryUtil);
    }

    @Bean
    public CompleteInspectionOnLc360JobExecutor completeInspectionOnLc360JobExecutor(
            Function<String, Lc360Api> lc360ApiProvider,
            IntegrationSummaryProvider summaryProvider,
            LC360FullCustomerProperties properties) {
        return new CompleteInspectionOnLc360JobExecutor(
                lc360ApiProvider, summaryProvider, enableMoveForwardTwice(properties));
    }

    @Bean
    public LC360IntegrationFormManager lc360IntegrationFormManager(
            Function<String, Lc360Api> lc360ApiProvider,
            ExternalIntegrationProvider integrationProvider,
            IntegrationFormManager integrationFormManager,
            LC360FormDataUtil lc360FormDataUtil) {
        return new LC360IntegrationFormManager(
                integrationFormManager,
                integrationProvider,
                lc360ApiProvider,
                lc360FormDataUtil::getFormDefinition);
    }

    @Bean
    public Function<String, FormDataResponse> httpFormDataProcessor(
            Function<String, URI> awsLambdaUriProvider, HttpClient awsLambdaHttpClient) {
        return json -> {
            var uri = awsLambdaUriProvider.apply("lc360");
            var request = new HttpPost(uri);
            request.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
            var response =
                    awsLambdaHttpClient.execute(
                            request,
                            Functions.combine(
                                    rsp ->
                                            HttpClient.throwApiExceptionNon2xxResponse(
                                                    rsp, HttpClient::convertResponseToString),
                                    HttpClient::convertResponseToString));
            return gson.fromJson(response, FormDataResponse.class);
        };
    }

    @Bean
    public Function<List<String>, Map<String, String>> lc360ImageIdConverter(
            @Value("${lc360.image.group-type:LC360}") String groupType,
            ImageGroupProvider imageGroupProvider) {
        return ids -> {
            var idMap = imageGroupProvider.findGroupIdByTypeAndImageId(groupType, ids);
            return idMap.entrySet().stream()
                    .collect(
                            Collectors.toMap(
                                    Map.Entry::getKey,
                                    e -> {
                                        if (e.getValue() != null
                                                && e.getValue().iterator().hasNext()) {
                                            var imageId = e.getValue().iterator().next();
                                            return String.format(
                                                    "%s-%s-%s-%s-%s",
                                                    imageId.substring(0, 8),
                                                    imageId.substring(8, 12),
                                                    imageId.substring(12, 16),
                                                    imageId.substring(16, 20),
                                                    imageId.substring(20));
                                        }

                                        return e.getKey();
                                    }));
        };
    }

    /** 额外获取image的annotation tag数据 */
    @Bean
    public Function<List<String>, Map<String, Iterable<? extends ImageTag>>> imageTagProvider(
            ImageTagProvider mysqlImageTagProvider) {
        return mysqlImageTagProvider::findByImageIds;
    }

    private Predicate<String> enableMoveForward(LC360FullCustomerProperties properties) {
        return dataset ->
                properties.getDatasets().stream()
                        .filter(d -> StringUtils.equals(dataset, d.getId()))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableMoveForward)
                        .findFirst()
                        .orElse(true);
    }

    private Predicate<String> enableMoveForwardTwice(LC360FullCustomerProperties properties) {
        return dataset ->
                properties.getDatasets().stream()
                        .filter(d -> StringUtils.equals(dataset, d.getId()))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableMoveForwardTwice)
                        .findFirst()
                        .orElse(false);
    }

    private Predicate<String> enableUpdateForm(LC360FullCustomerProperties properties) {
        return dataset ->
                properties.getDatasets().stream()
                        .filter(d -> StringUtils.equals(dataset, d.getId()))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableUpdateForm)
                        .findFirst()
                        .orElse(true);
    }
}
