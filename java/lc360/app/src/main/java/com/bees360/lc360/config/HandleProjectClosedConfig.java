package com.bees360.lc360.config;

import com.bees360.job.JobScheduler;
import com.bees360.job.SendProjectClosedEmailJobExecutor;
import com.bees360.lc360.listener.HandleLc360ProjectClosedOnProjectStateChanged;
import com.bees360.mail.MailSender;
import com.bees360.mail.util.MailMessageFactory;
import com.bees360.project.ContactManager;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.config.GrpcExternalIntegrationManagerConfig;
import com.bees360.project.config.GrpcProjectContactClientConfig;
import com.bees360.project.config.GrpcProjectIIMangerConfig;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

@ConditionalOnProperty(prefix = "app.project.closed", value = "enable-handle", havingValue = "true")
@Configuration
@Import({
    LC360CustomerConfig.class,
    GrpcProjectIIMangerConfig.class,
    GrpcProjectContactClientConfig.class,
    GrpcExternalIntegrationManagerConfig.class,
    EmailSenderConfig.class,
})
public class HandleProjectClosedConfig {

    @ConfigurationProperties(prefix = "app.project.closed")
    @Getter
    @Setter
    @Configuration
    static class ProjectClosedProperties {

        private Map<String, DataSet> customer = new HashMap<>();

        private Map<String, ClosedReasonConfig> closeReason = new HashMap<>();

        private String portalLink;

        private Boolean enableSendEmail = true;

        private Boolean enableSyncNote = true;

        @Getter
        @Setter
        static class DataSet {
            private List<String> recipient;
        }

        @Getter
        @Setter
        static class ClosedReasonConfig {
            private String description;
            private String emailTemplate;
        }
    }

    @Bean
    public SendProjectClosedEmailJobExecutor sendProjectClosedEmailJobExecutor(
            ProjectIIManager projectIIManager,
            ContactManager contactManager,
            MailMessageFactory mailMessageFactory,
            MailSender noReplySender,
            @Qualifier("closeEmailRecipientProvider")
                    Function<String, List<String>> recipientProvider,
            @Qualifier("closeReasonToEmailTemplateProvider")
                    UnaryOperator<String> closeReasonToEmailTemplateProvider,
            @Qualifier("projectPortalLinkProvider") UnaryOperator<String> projectLinkProvider) {
        return new SendProjectClosedEmailJobExecutor(
                projectIIManager,
                contactManager,
                mailMessageFactory,
                noReplySender,
                recipientProvider,
                closeReasonToEmailTemplateProvider,
                projectLinkProvider);
    }

    @Bean
    public HandleLc360ProjectClosedOnProjectStateChanged handleProjectClosedOnProjectStateChanged(
            JobScheduler jobScheduler,
            ExternalIntegrationProvider externalIntegrationProvider,
            @Qualifier("closeReasonToDescriptionMap") Map<String, String> closeOutReasonMap,
            ProjectClosedProperties properties,
            Predicate<String> enableNotifyProjectClosed) {
        return new HandleLc360ProjectClosedOnProjectStateChanged(
                jobScheduler,
                externalIntegrationProvider,
                closeOutReasonMap,
                properties.getEnableSendEmail(),
                properties.getEnableSyncNote(),
                enableNotifyProjectClosed);
    }

    @Bean("closeReasonToEmailTemplateProvider")
    public UnaryOperator<String> closeReasonToEmailTemplateProvider(
            ProjectClosedProperties properties) {
        return reason ->
                Optional.ofNullable(properties.getCloseReason().get(reason))
                        .map(ProjectClosedProperties.ClosedReasonConfig::getEmailTemplate)
                        .orElse(null);
    }

    @Bean("closeEmailRecipientProvider")
    public Function<String, List<String>> closeEmailRecipientProvider(
            ProjectClosedProperties properties) {
        return customerKey ->
                Optional.ofNullable(properties.getCustomer().get(customerKey))
                        .map(ProjectClosedProperties.DataSet::getRecipient)
                        .orElse(null);
    }

    @Bean("closeReasonToDescriptionMap")
    public Map<String, String> closeReasonToDescriptionMap(ProjectClosedProperties properties) {
        return properties.getCloseReason().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getDescription()));
    }

    @Bean("projectPortalLinkProvider")
    public UnaryOperator<String> projectPortalLinkProvider(ProjectClosedProperties properties) {
        return id -> properties.getPortalLink() + id;
    }

    @Bean("enableNotifyProjectClosed")
    Predicate<String> enableNotifyProjectClosed(
            LC360CustomerConfig.Lc360CustomerProperties properties) {
        return dataset ->
                properties.getDatasets().stream()
                        .filter(d -> StringUtils.equals(dataset, d.getId()))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableNotifyProjectClosed)
                        .findFirst()
                        .orElse(true);
    }
}
