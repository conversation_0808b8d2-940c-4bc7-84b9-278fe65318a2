package com.bees360.job;

import com.bees360.job.registry.MarkInspectionProcessedOnLC360Job;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.lc360.Lc360Api;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.function.Function;

@Log4j2
public class MarkInspectionProcessedOnLC360JobExecutor
        extends AbstractJobExecutor<MarkInspectionProcessedOnLC360Job> {

    private final Function<String, Lc360Api> lc360ApiProvider;

    public MarkInspectionProcessedOnLC360JobExecutor(Function<String, Lc360Api> lc360ApiProvider) {
        this.lc360ApiProvider = lc360ApiProvider;
        log.info("Created {}(lc360Api={}).", this, this.lc360ApiProvider);
    }

    @Override
    protected void handle(MarkInspectionProcessedOnLC360Job job) throws IOException {
        var inspectionIds = job.getInspectionIds();
        var lc360Api = lc360ApiProvider.apply(job.getDataset()).getVendorApi();
        lc360Api.processInspections(inspectionIds);
        log.info("Successfully process {} inspections on LossControl360", inspectionIds);
    }
}
