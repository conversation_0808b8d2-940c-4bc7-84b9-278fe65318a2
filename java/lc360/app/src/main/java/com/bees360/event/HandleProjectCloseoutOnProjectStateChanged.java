package com.bees360.event;

import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.TriggerFillOutUTCFormJob;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.function.Function;
import java.util.function.Predicate;

/** 监听项目状态变更事件，处理项目关闭时的外部集成检查和表单任务调度。 */
@Log4j2
public class HandleProjectCloseoutOnProjectStateChanged
        extends AbstractNamedEventListener<ProjectStateChangedEvent> {

    private final ExternalIntegrationProvider integrationProvider;

    private final JobScheduler jobScheduler;

    private final Predicate<String> enableHandleProjectCloseout;

    private final Function<String, Lc360Api> lc360ApiProvider;

    private final Function<ExternalIntegration, String> closeoutInspectionTypeProvider;

    private final Predicate<String> projectCloseoutPredicate;

    public HandleProjectCloseoutOnProjectStateChanged(
            ExternalIntegrationProvider integrationProvider,
            JobScheduler jobScheduler,
            Predicate<String> enableHandleProjectCloseout,
            Function<String, Lc360Api> lc360ApiProvider,
            Function<ExternalIntegration, String> closeoutInspectionTypeProvider,
            Predicate<String> projectCloseoutPredicate) {
        this.integrationProvider = integrationProvider;
        this.jobScheduler = jobScheduler;
        this.enableHandleProjectCloseout = enableHandleProjectCloseout;
        this.lc360ApiProvider = lc360ApiProvider;
        this.closeoutInspectionTypeProvider = closeoutInspectionTypeProvider;
        this.projectCloseoutPredicate = projectCloseoutPredicate;
        log.info("Created {}.", this);
    }

    @Override
    public void handle(ProjectStateChangedEvent event) throws IOException {
        if (!filter(event)) {
            return;
        }

        var projectId = String.valueOf(event.getProjectId());
        var integration = findIntegration(projectId);
        if (integration == null || !enableHandleProjectCloseout.test(integration.getDataset())) {
            return;
        }

        log.info("Start to change inspection type by event {}.", event);
        var lc360Api = lc360ApiProvider.apply(integration.getDataset());
        lc360Api.getVendorApi()
                .changeInspectionType(
                        integration.getReferenceNumber(),
                        closeoutInspectionTypeProvider.apply(integration));
        scheduleFormJob(integration);
    }

    private void scheduleFormJob(ExternalIntegration integration) {
        var payload =
                new TriggerFillOutUTCFormJob(
                        integration.getDataset(), integration.getId(), integration.getProjectId());
        jobScheduler.schedule(
                RetryableJob.of(Job.ofPayload(payload), 3, Duration.ofMinutes(5), 1.0F));
    }

    private boolean filter(ProjectStateChangedEvent event) {
        return event.getCurrentState().getState()
                        == Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE
                && projectCloseoutPredicate.test(String.valueOf(event.getProjectId()));
    }

    private ExternalIntegration findIntegration(String projectId) {
        final String integrationType = "LossControl360";
        return Iterables.toStream(integrationProvider.findAllByProjectId(projectId))
                .filter(i -> StringUtils.equals(integrationType, i.getIntegrationType()))
                .findFirst()
                .orElse(null);
    }
}
