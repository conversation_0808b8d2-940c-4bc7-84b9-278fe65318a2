package com.bees360.job;

import static com.bees360.job.registry.Lc360ActivitySyncJob.INTEGRATION_LC360;

import com.bees360.activity.Activity;
import com.bees360.job.registry.Lc360ActivitySyncJob;
import com.bees360.job.util.AbstractAsyncJobExecutor;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

@Log4j2
public class Lc360ActivitySyncJobExecutor extends AbstractAsyncJobExecutor<Lc360ActivitySyncJob> {
    private final Function<String, Lc360Api> lc360ApiProvider;
    private final ExternalIntegrationProvider externalIntegrationProvider;

    public Lc360ActivitySyncJobExecutor(
            Function<String, Lc360Api> lc360ApiProvider,
            ExternalIntegrationProvider externalIntegrationProvider) {
        this.lc360ApiProvider = lc360ApiProvider;
        this.externalIntegrationProvider = externalIntegrationProvider;
        log.info("Created {}.", this);
    }

    @Override
    protected ListenableFuture<Void> accept(Lc360ActivitySyncJob job) {
        var activity = job.getActivity();
        return Futures.submit(() -> addNoteToLc3360(activity), MoreExecutors.directExecutor());
    }

    private void addNoteToLc3360(Activity activity) {
        var projectId = String.valueOf(activity.getProjectId());
        var comment = activity.getComment().getContent();
        var integration = getIntegrationByProjectId(projectId);

        var inspectionId = integration.getReferenceNumber();
        var lc360VendorApi = lc360ApiProvider.apply(integration.getDataset()).getVendorApi();
        lc360VendorApi.addInspectionNote(inspectionId, comment);
        log.info(
                "Sync activity {} of project {} to lc360 successfully.",
                activity.getId(),
                projectId);
    }

    private ExternalIntegration getIntegrationByProjectId(String projectId) {
        return Iterables.toStream(externalIntegrationProvider.findAllByProjectId(projectId))
                .filter(
                        integration ->
                                StringUtils.equalsIgnoreCase(
                                        INTEGRATION_LC360, integration.getIntegrationType()))
                .findAny()
                .orElse(null);
    }
}
