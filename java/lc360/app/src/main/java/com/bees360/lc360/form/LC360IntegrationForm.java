package com.bees360.lc360.form;

import com.bees360.lc360.Form;
import com.bees360.project.IntegrationForm;

import java.time.Instant;

public class LC360IntegrationForm implements IntegrationForm {

    private final Form form;

    public LC360IntegrationForm(Form form) {
        this.form = form;
    }

    @Override
    public String getIntegrationId() {
        return null;
    }

    @Override
    public String getFormId() {
        return form.getInspectionFormID();
    }

    @Override
    public String getTemplateId() {
        return form.getFormID();
    }

    @Override
    public String getName() {
        return form.getName();
    }

    @Override
    public Instant getUpdatedAt() {
        return null;
    }
}
