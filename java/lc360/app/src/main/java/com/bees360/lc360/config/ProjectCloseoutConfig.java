package com.bees360.lc360.config;

import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.config.GrpcProjectIIMangerConfig;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.Predicate;

@Import({
    GrpcProjectIIMangerConfig.class,
})
@Configuration
public class ProjectCloseoutConfig {

    @Bean("projectCloseoutPredicate")
    public Predicate<String> projectCloseoutPredicate(ProjectIIManager projectManager) {
        return projectId -> {
            var project = projectManager.findById(projectId);
            var projectStatus = project.getLatestStatus();
            return project.isCanceled()
                    && projectStatus == Message.ProjectStatus.RETURNED_TO_CLIENT;
        };
    }

    @Bean("projectCanceledPredicate")
    public Predicate<String> projectCanceledPredicate(ProjectIIManager projectManager) {
        return projectId -> {
            var project = projectManager.findById(projectId);
            return project.isCanceled();
        };
    }
}
