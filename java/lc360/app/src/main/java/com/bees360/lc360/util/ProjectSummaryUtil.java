package com.bees360.lc360.util;

import com.bees360.image.tag.ImageTag;
import com.bees360.integration.IntegrationSummary;
import com.bees360.integration.Message;
import com.bees360.openapi.Message.ReportMessage.Summary.Risk.Credit;
import com.bees360.util.Iterables;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ProjectSummaryUtil {

    private final Function<List<String>, Map<String, String>> imageIdConverter;

    private final Function<List<String>, Map<String, Iterable<? extends ImageTag>>>
            imageTagProvider;

    public ProjectSummaryUtil(
            Function<List<String>, Map<String, String>> imageIdConverter,
            Function<List<String>, Map<String, Iterable<? extends ImageTag>>> imageTagProvider) {
        this.imageIdConverter = imageIdConverter;
        this.imageTagProvider = imageTagProvider;
    }

    public Message.IntegrationSummaryMessage handleSummary(IntegrationSummary summary) {
        var builder = summary.toMessage().toBuilder();
        handleImage(builder, summary.getImageSummary());
        handleReportSummary(builder, summary.getReportSummary());

        return builder.build();
    }

    private void handleImage(
            Message.IntegrationSummaryMessage.Builder builder,
            Message.IntegrationSummaryMessage.ImageSummary imageSummary) {
        if (imageSummary == null
                || Message.IntegrationSummaryMessage.ImageSummary.getDefaultInstance()
                        .equals(imageSummary)) {
            return;
        }

        var imageSummaryBuilder = imageSummary.toBuilder();
        var imageIds =
                imageSummary.getImageList().stream()
                        .map(com.bees360.image.Message.ImageMessage::getId)
                        .collect(Collectors.toList());
        var imageBuilder =
                imageSummary.getImageList().stream()
                        .map(com.bees360.image.Message.ImageMessage::toBuilder)
                        .collect(Collectors.toList());
        var imageIdMap = imageIdConverter.apply(imageIds);
        var imageTagMap = imageTagProvider.apply(imageIds);
        List<com.bees360.image.Message.ImageMessage> finalImageList = new ArrayList<>();

        imageBuilder.forEach(
                b -> {
                    var finalId = imageIdMap.get(b.getId());
                    if (finalId != null) {
                        Optional.ofNullable(imageTagMap.get(b.getId()))
                                .ifPresent(
                                        tags -> {
                                            b.clearTag();
                                            b.addAllTag(
                                                    Iterables.transform(tags, ImageTag::toMessage));
                                        });

                        // 删掉多余的图片信息
                        b.clearMetadata();
                        b.clearResource();
                        b.setId(finalId);
                        finalImageList.add(b.build());
                    }
                });

        imageSummaryBuilder.clearImage();
        imageSummaryBuilder.addAllImage(finalImageList);
        builder.setImageSummary(imageSummaryBuilder);
    }

    /**
     * 预处理report summary的数据
     *
     * @param builder summary builder
     * @param reportSummary report summary message object
     */
    private void handleReportSummary(
            Message.IntegrationSummaryMessage.Builder builder,
            com.bees360.openapi.Message.ReportMessage.Summary reportSummary) {
        if (reportSummary == null
                || reportSummary
                        == com.bees360.openapi.Message.ReportMessage.Summary.getDefaultInstance()) {
            return;
        }

        var reportSummaryBuilder = reportSummary.toBuilder();
        var risk = reportSummary.getRisk();
        handleRisk(reportSummaryBuilder, risk);

        // remove useless additional properties
        reportSummaryBuilder.clearAdditionalProperties();
        builder.setReportSummary(reportSummaryBuilder.build());
    }

    /**
     * 预处理Risk数据
     *
     * @param builder report summary builder
     * @param risk risk message object
     */
    private void handleRisk(
            com.bees360.openapi.Message.ReportMessage.Summary.Builder builder,
            com.bees360.openapi.Message.ReportMessage.Summary.Risk risk) {
        if (risk == null
                || risk
                        == com.bees360.openapi.Message.ReportMessage.Summary.Risk
                                .getDefaultInstance()) {
            return;
        }
        var riskBuilder = risk.toBuilder();
        if (risk.getCreditsCount() != 0) {
            // 替换credit里的image id
            var credits =
                    Iterables.toStream(risk.getCreditsList())
                            .map(this::replaceImages)
                            .collect(Collectors.toList());
            riskBuilder.clearCredits();
            riskBuilder.addAllCredits(credits);
        }

        builder.setRisk(riskBuilder.build());
    }

    private Credit replaceImages(Credit credit) {
        var creditBuilder = credit.toBuilder();
        var imageIds =
                credit.getImagesList().stream()
                        .map(com.bees360.openapi.Message.ImageMessage::getId)
                        .collect(Collectors.toList());
        var imageIdMap = imageIdConverter.apply(imageIds);
        if (!imageIdMap.isEmpty()) {
            creditBuilder.clearImages();
            creditBuilder.addAllImages(
                    imageIdMap.values().stream()
                            .map(
                                    id ->
                                            com.bees360.openapi.Message.ImageMessage.newBuilder()
                                                    .setId(id)
                                                    .build())
                            .collect(Collectors.toList()));
        }

        return creditBuilder.build();
    }
}
