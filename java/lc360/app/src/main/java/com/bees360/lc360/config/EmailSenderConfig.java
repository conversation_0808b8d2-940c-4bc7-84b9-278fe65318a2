package com.bees360.lc360.config;

import com.bees360.mail.MailSender;
import com.bees360.mail.MailSenderProvider;
import com.bees360.mail.config.JobMailSenderConfig;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    JobMailSenderConfig.class,
})
@Configuration
public class EmailSenderConfig {

    @Bean
    MailSender noReplySender(MailSenderProvider mailSenderProvider) {
        return mailSenderProvider.get("no-reply-sender");
    }
}
