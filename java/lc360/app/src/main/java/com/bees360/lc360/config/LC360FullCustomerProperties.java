package com.bees360.lc360.config;

import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.lc360.util.LC360FormData;
import com.bees360.project.ServiceTypeEnum;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@RefreshableConfigurationProperties(prefix = "lc360")
@EnableConfigurationProperties
@Configuration
class LC360FullCustomerProperties {

    private List<LC360Dataset> datasets = new ArrayList<>();

    @Getter
    @Setter
    static class LC360Dataset extends LC360CustomerConfig.Lc360CustomerProperties.Dataset {

        @NotNull private String creator;

        private ContractProperties contract;

        private ServiceTypeMapper serviceTypeMapper;

        private PolicyRenewalMapper policyRenewalMapper;

        // key for division lookup id
        private Map<String, ClientDataset> clients = new HashMap<>();

        private List<LC360FormData> forms = new ArrayList<>();

        @Setter
        @Getter
        static class ServiceTypeMapper {
            @NotNull private ServiceTypeEnum defaultValue;

            private Map<String, ServiceTypeEnum> mapping = new HashMap<>();
        }

        @Setter
        @Getter
        static class PolicyRenewalMapper {
            private String field;

            private Map<String, Boolean> mapping = new HashMap<>();

            private Boolean defaultValue = false;
        }

        @Getter
        @Setter
        static class ClientDataset {
            private String clientName;

            private ContractProperties contract;

            private String closeoutLookupId;
        }

        public String getCloseoutLookupIdByInsuredBy(String customerKey) {
            return clients.values().stream()
                    .filter(c -> StringUtils.equals(c.getContract().getInsuredBy(), customerKey))
                    .findFirst()
                    .map(ClientDataset::getCloseoutLookupId)
                    .orElse("");
        }

        @Getter
        @Setter
        static class ContractProperties {
            @NotEmpty private String insuredBy;

            @NotEmpty private String processedBy;
        }
    }

    public LC360Dataset getDataset(String customer) {
        return datasets.stream()
                .filter(d -> StringUtils.equals(customer, d.getId()))
                .findFirst()
                .orElse(null);
    }
}
