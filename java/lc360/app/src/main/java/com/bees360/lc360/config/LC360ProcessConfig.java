package com.bees360.lc360.config;

import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.job.MarkInspectionProcessedOnLC360JobExecutor;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.lc360.listener.MarkInspectionProcessedOnProjectCreationJobCompleted;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.Function;

@Configuration
@ConditionalOnProperty(value = "lc360.process.enabled", havingValue = "true")
@Import({
    MarkInspectionProcessedOnProjectCreationJobCompleted.class,
    MarkInspectionProcessedOnLC360JobExecutor.class,
    AutoRegisterJobExecutorConfig.class,
    AutoRegisterEventListenerConfig.class,
})
public class LC360ProcessConfig {

    @Bean
    public Function<String, String> creationJobIdDecoder() {
        return jobId -> jobId.substring(13);
    }
}
