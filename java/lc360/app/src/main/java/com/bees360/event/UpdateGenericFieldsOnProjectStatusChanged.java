package com.bees360.event;

import com.bees360.event.registry.ProjectStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.lc360.GenericField;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;

/** 监听项目状态变更事件，更新相关通用字段到外部集成系统。 */
@Log4j2
public class UpdateGenericFieldsOnProjectStatusChanged
        extends AbstractNamedEventListener<ProjectStatusChanged> {

    private final Function<String, Lc360Api> lc360ApiProvider;

    private final ExternalIntegrationProvider integrationProvider;

    private final Predicate<String> enableUpdateProjectStatus;

    public UpdateGenericFieldsOnProjectStatusChanged(
            Function<String, Lc360Api> lc360ApiProvider,
            ExternalIntegrationProvider integrationProvider,
            Predicate<String> enableUpdateProjectStatus) {
        this.lc360ApiProvider = lc360ApiProvider;
        this.integrationProvider = integrationProvider;
        this.enableUpdateProjectStatus = enableUpdateProjectStatus;
        log.info("Created {}.", this);
    }

    @Override
    public void handle(ProjectStatusChanged event) throws IOException {
        var projectId = event.getProjectId();

        var integration = findIntegration(projectId);
        if (integration == null || !enableUpdateProjectStatus.test(integration.getDataset())) {
            return;
        }

        log.info("Start to update inspection generic fields by event {}.", event);
        var inspectionId = integration.getReferenceNumber();
        var lc360Api = lc360ApiProvider.apply(integration.getDataset()).getIntegrationV4Api();

        var fields =
                buildGenericField(projectId, Message.ProjectStatus.forNumber(event.getStatus()));
        lc360Api.updateInspectionGenericFields(inspectionId, fields);
        log.info(
                "Successfully update inspection {} generic fields for project {}.",
                inspectionId,
                projectId);
    }

    private List<GenericField> buildGenericField(String projectId, Message.ProjectStatus status) {
        List<GenericField> fields = new ArrayList<>();

        // If created event happens, sync project number.
        if (Message.ProjectStatus.PROJECT_CREATED == status) {
            var projectNumber = GenericField.number(new BigDecimal(projectId));
            projectNumber.setKey("Bees360 Project Number");
            fields.add(projectNumber);
        }

        var statusText = WordUtils.capitalizeFully(status.name(), '_').replace("_", " ");
        var statusField = GenericField.text(statusText);
        statusField.setKey("Bees360 Status");
        fields.add(statusField);

        return fields;
    }

    private ExternalIntegration findIntegration(String projectId) {
        final String integrationType = "LossControl360";
        return Iterables.toStream(integrationProvider.findAllByProjectId(projectId))
                .filter(i -> StringUtils.equals(integrationType, i.getIntegrationType()))
                .findFirst()
                .orElse(null);
    }
}
