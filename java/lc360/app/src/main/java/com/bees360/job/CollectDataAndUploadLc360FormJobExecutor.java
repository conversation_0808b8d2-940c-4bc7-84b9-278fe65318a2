package com.bees360.job;

import com.bees360.codec.GsonCodec;
import com.bees360.codec.Lc360GsonCodec;
import com.bees360.integration.IntegrationSummaryProvider;
import com.bees360.integration.Message;
import com.bees360.job.registry.CollectDataAndUploadLc360FormJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.lc360.Form;
import com.bees360.lc360.FormDataForUpdate;
import com.bees360.lc360.Lc360Api;
import com.bees360.lc360.util.FormDataResponse;
import com.bees360.lc360.util.ProjectSummaryUtil;
import com.bees360.project.IntegrationForm;
import com.bees360.project.IntegrationFormManager;
import com.bees360.util.Iterables;
import com.google.protobuf.ByteString;
import com.google.protobuf.util.JsonFormat;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
public class CollectDataAndUploadLc360FormJobExecutor
        extends AbstractJobExecutor<CollectDataAndUploadLc360FormJob> {

    private final IntegrationSummaryProvider summaryProvider;

    private final Function<String, FormDataResponse> formDataProcessor;

    private final Function<String, Lc360Api> lc360ApiProvider;

    private final BiFunction<String, String, String> formInternalKeyProvider;

    private static final GsonCodec gson = Lc360GsonCodec.INSTANCE;

    private final IntegrationFormManager integrationFormManager;

    private final ProjectSummaryUtil projectSummaryUtil;

    public CollectDataAndUploadLc360FormJobExecutor(
            @NonNull IntegrationSummaryProvider summaryProvider,
            @NonNull Function<String, FormDataResponse> formDataProcessor,
            @NonNull Function<String, Lc360Api> lc360ApiProvider,
            @NonNull BiFunction<String, String, String> formInternalKeyProvider,
            @NonNull IntegrationFormManager integrationFormManager,
            @NonNull ProjectSummaryUtil projectSummaryUtil) {
        this.summaryProvider = summaryProvider;
        this.formDataProcessor = formDataProcessor;
        this.lc360ApiProvider = lc360ApiProvider;
        this.formInternalKeyProvider = formInternalKeyProvider;
        this.integrationFormManager = integrationFormManager;
        this.projectSummaryUtil = projectSummaryUtil;
        log.info(
                "Created {}(summaryProvider={}, formDataProcessor={},lc360Api={},"
                        + "formInternalKeyProvider={},integrationFormManager={}).",
                this,
                this.summaryProvider,
                this.formDataProcessor,
                this.lc360ApiProvider,
                this.formInternalKeyProvider,
                this.integrationFormManager);
    }

    @Override
    protected void handle(CollectDataAndUploadLc360FormJob job) throws IOException {
        log.info("Begin to handle job {} for upload LossControl360 form.", job);
        var projectId = job.getProjectId();
        var summary = summaryProvider.collectByProjectId(projectId);
        if (Objects.isNull(summary)) {
            throw new IllegalArgumentException(
                    String.format(
                            "Integration summary for update project %s to LossControl360 is"
                                    + " illegal.",
                            projectId));
        }

        Map<String, FormDataForUpdate> formDataMap = new HashMap<>();
        var dataset = job.getDataset();
        var summaryMessage = projectSummaryUtil.handleSummary(summary);
        var initialForm = new Form();
        initialForm.setInspectionFormID(job.getInspectionFormId());
        initialForm.setFormID(job.getFormTemplateId());
        processForm(
                dataset,
                projectId,
                job.getIntegrationId(),
                initialForm,
                summaryMessage,
                formDataMap);
        uploadFormData(dataset, job.getInspectionId(), formDataMap);
    }

    private void processForm(
            String dataset,
            String projectId,
            String integrationId,
            Form form,
            Message.IntegrationSummaryMessage summary,
            Map<String, FormDataForUpdate> formDataMap) {
        var formData = process(dataset, projectId, form.getFormID(), summary);
        var formDataForUpdate =
                gson.decode(ByteString.copyFromUtf8(formData.getData()), FormDataForUpdate.class);

        formDataMap.put(form.getInspectionFormID(), formDataForUpdate);

        var formsToAdd = formData.getForms();
        if (CollectionUtils.isEmpty(formsToAdd)) {
            return;
        }

        var forms = processNewForm(integrationId, formsToAdd);
        forms.forEach(
                (key, value) -> {
                    var newForm = new Form();
                    newForm.setFormID(key);
                    newForm.setInspectionFormID(value);
                    processForm(dataset, projectId, integrationId, newForm, summary, formDataMap);
                });
    }

    private FormDataResponse process(
            String dataset,
            String projectId,
            String formTemplateId,
            Message.IntegrationSummaryMessage summary) {
        var templateKey = formInternalKeyProvider.apply(dataset, formTemplateId);
        if (templateKey == null) {
            throw new IllegalArgumentException(
                    String.format(
                            "No internal template key found for process LossControl360 form"
                                    + " template %s ",
                            formTemplateId));
        }

        Map<String, Object> map = new HashMap<>();
        map.put("formTemplateId", templateKey);
        try {
            var formData = JsonFormat.printer().print(summary);
            map.put("formData", formData);
            var formDataJson = gson.encode(map);
            return formDataProcessor.apply(formDataJson.toStringUtf8());
        } catch (Exception e) {
            throw new IllegalStateException(
                    String.format(
                            "Failed to process integration summary for project %s with template key"
                                    + " %s to LossControl360.",
                            projectId, templateKey),
                    e);
        }
    }

    private Map<String, String> processNewForm(String integrationId, List<String> formsToAdd) {
        Map<String, String> formMap = new HashMap<>();
        var existedForms =
                Iterables.toStream(
                                integrationFormManager.findFormsByIntegrationId(
                                        List.of(integrationId)))
                        .map(IntegrationForm::getTemplateId)
                        .collect(Collectors.toList());

        Iterables.toStream(formsToAdd)
                .filter(f -> !existedForms.contains(f))
                .forEach(
                        f -> {
                            var formToAdd =
                                    IntegrationForm.from(
                                            com.bees360.project.Message.IntegrationFormMessage
                                                    .newBuilder()
                                                    .setTemplateId(f)
                                                    .setIntegrationId(integrationId)
                                                    .build());

                            var formId = integrationFormManager.addIntegrationForm(formToAdd);
                            formMap.put(f, formId);
                        });
        return formMap;
    }

    private void uploadFormData(
            String dataset, String inspectionId, Map<String, FormDataForUpdate> formDataMap) {
        var lc360Api = lc360ApiProvider.apply(dataset).getVendorApi();
        for (Map.Entry<String, FormDataForUpdate> form : formDataMap.entrySet()) {
            lc360Api.saveFormData(inspectionId, form.getKey(), List.of(form.getValue()));
        }
    }
}
