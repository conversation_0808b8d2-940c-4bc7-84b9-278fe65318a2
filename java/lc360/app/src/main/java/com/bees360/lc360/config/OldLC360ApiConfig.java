package com.bees360.lc360.config;

import com.bees360.lc360.Lc360Api;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.Function;

@Configuration
@ConditionalOnProperty(
        prefix = "lc360",
        name = "customer-enabled",
        havingValue = "false",
        matchIfMissing = true)
@Import({
    Lc360HttpClientConfig.class,
})
public class OldLC360ApiConfig {

    @Bean
    public Function<String, Lc360Api> lc360ApiProvider(Lc360Api lc360Api) {
        return customer -> lc360Api;
    }
}
