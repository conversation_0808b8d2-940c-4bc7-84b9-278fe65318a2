package com.bees360.lc360;

import com.bees360.activity.config.GrpcCommentClientConfig;
import com.bees360.apolloconfig.config.ApolloClientConfig;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.config.ActuatorSecurityConfig;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.grpc.config.GrpcClientConfig;
import com.bees360.image.config.GrpcImageGroupClientConfig;
import com.bees360.image.config.GrpcImageTagClientConfig;
import com.bees360.image.config.GrpcImageTagDictClientConfig;
import com.bees360.image.config.MysqlTagProviderConfig;
import com.bees360.integration.config.GrpcIntegrationSummaryClientConfig;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.lc360.config.AddPauseReasonNoteToInspectionOnProjectPauseConfig;
import com.bees360.lc360.config.CancelInspectionOnProjectCancelConfig;
import com.bees360.lc360.config.HandleProjectClosedConfig;
import com.bees360.lc360.config.HandleProjectCloseoutConfig;
import com.bees360.lc360.config.LC360CreationConfig;
import com.bees360.lc360.config.LC360CustomerApiConfig;
import com.bees360.lc360.config.LC360FormConfig;
import com.bees360.lc360.config.LC360ProcessConfig;
import com.bees360.lc360.config.OldLC360ApiConfig;
import com.bees360.lc360.config.SetInspectionScheduledTimeConfig;
import com.bees360.lc360.config.SyncActivityConfig;
import com.bees360.lc360.config.SyncNoteToLc360JobConfig;
import com.bees360.lc360.config.UpdateInspectionGenericFieldsConfig;
import com.bees360.pipeline.config.GrpcPipelineClientConfig;
import com.bees360.project.config.GrpcExternalIntegrationManagerConfig;
import com.bees360.project.config.GrpcIntegrationFormManagerConfig;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.user.config.GrpcUserProviderConfig;
import com.bees360.util.Iterables;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

import java.util.Comparator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import java.util.function.UnaryOperator;

@Log4j2
@Import({
    GrpcExternalIntegrationManagerConfig.class,
    GrpcIntegrationFormManagerConfig.class,
    GrpcIntegrationSummaryClientConfig.class,
    GrpcPipelineClientConfig.class,
    GrpcCommentClientConfig.class,
    GrpcUserProviderConfig.class,
    RabbitApiConfig.class,
    RabbitEventPublisher.class,
    RabbitEventDispatcher.class,
    AutoRegisterEventListenerConfig.class,
    AutoRegisterJobExecutorConfig.class,
    RabbitJobScheduler.class,
    RabbitJobDispatcher.class,
    SyncActivityConfig.class,
    GrpcClientConfig.class,

    // image
    GrpcImageGroupClientConfig.class,
    GrpcImageTagClientConfig.class,
    GrpcImageTagDictClientConfig.class,
    MysqlTagProviderConfig.class,

    // config
    OldLC360ApiConfig.class,
    LC360FormConfig.class,
    LC360ProcessConfig.class,
    CancelInspectionOnProjectCancelConfig.class,
    AddPauseReasonNoteToInspectionOnProjectPauseConfig.class,
    SyncActivityConfig.class,
    SyncNoteToLc360JobConfig.class,
    HandleProjectClosedConfig.class,
    SetInspectionScheduledTimeConfig.class,
    LC360CustomerApiConfig.class,
    LC360CreationConfig.class,
    UpdateInspectionGenericFieldsConfig.class,
    HandleProjectCloseoutConfig.class,
    ActuatorSecurityConfig.class,
    // apollo
    ApolloClientConfig.class,
})
@EnableEncryptableProperties
@EnableConfigurationProperties
@ApplicationAutoConfig
@EnableWebSecurity
public class LC360App {
    public static void main(String[] args) {
        ExitableSpringApplication.run(LC360App.class, args);
    }

    @Configuration
    static class Config {

        @Bean
        Supplier<String> robotUserIdSupplier(
                UserProvider userProvider,
                @Value("${app.lc360.user.robot-email}") String robotUserEmail) {

            UnaryOperator<String> emailUserIdProvider =
                    (email) -> {
                        var id =
                                Iterables.toList(userProvider.findUserByEmail(email)).stream()
                                        .min(Comparator.comparing(User::getId))
                                        .map(User::getId)
                                        .orElse(null);
                        if (id == null) {
                            var message =
                                    String.format(
                                            "Robot User with email %s not found.", robotUserEmail);
                            throw new IllegalStateException(message);
                        }
                        return id;
                    };
            Map<String, String> load = new ConcurrentHashMap<>();
            Supplier<String> robotUserIdSupplier =
                    () -> load.computeIfAbsent(robotUserEmail, emailUserIdProvider);
            log.info(
                    "Created {}(userProvider={},robotUserEmail={})",
                    robotUserIdSupplier,
                    userProvider,
                    robotUserEmail);
            return robotUserIdSupplier;
        }
    }
}
