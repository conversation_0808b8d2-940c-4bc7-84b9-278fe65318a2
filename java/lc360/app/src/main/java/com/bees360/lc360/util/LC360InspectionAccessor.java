package com.bees360.lc360.util;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.address.Address;
import com.bees360.building.Message;
import com.bees360.lc360.Inspection;
import com.bees360.policy.Policy;
import com.bees360.project.Building;
import com.bees360.project.Contact;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.claim.Claim;
import com.bees360.project.underwriting.Underwriting;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

public class LC360InspectionAccessor {

    private final String dataset;

    private final Inspection inspectionDto;

    private final ServiceTypeEnum serviceType;

    private Map<String, Object> customFields;

    private final BiFunction<String, LC360InspectionAccessor, Boolean> policyRenewalMappings;

    public LC360InspectionAccessor(
            String dataset,
            Inspection inspection,
            BiFunction<String, Inspection, ServiceTypeEnum> serviceTypeMappings,
            BiFunction<String, LC360InspectionAccessor, Boolean> policyRenewalMappings) {
        this.dataset = dataset;
        this.inspectionDto = inspection;
        this.policyRenewalMappings = policyRenewalMappings;
        this.serviceType = serviceTypeMappings.apply(dataset, inspectionDto);
    }

    public String getId() {
        return inspectionDto.getInspectionID();
    }

    public com.bees360.project.inspection.Inspection getInspection() {
        var builder = com.bees360.project.inspection.Inspection.InspectionBuilder.newBuilder();
        acceptIfNotNull(
                builder::setInspectionNo, inspectionDto.getInspectionNumber(), String::valueOf);
        acceptIfNotNull(builder::setDueDate, inspectionDto.getDueDate());

        return builder.build();
    }

    public Policy getPolicy() {
        var builder =
                Policy.PolicyBuilder.newBuilder()
                        .setPolicyNo(
                                Optional.ofNullable(inspectionDto.getPolicyNumber()).orElse(""))
                        .setAddress(getAddress())
                        .setBuilding(getBuilding());
        acceptIfNotNull(builder::setPolicyEffectiveDate, inspectionDto.getEffectiveDate());
        acceptIfNotNull(builder::setIsRenewal, getPolicyRenewal());
        return builder.build();
    }

    private Boolean getPolicyRenewal() {
        return policyRenewalMappings.apply(dataset, this);
    }

    // inspection has a list of building and info stored in form
    public Building getBuilding() {
        var builder = Building.BuildingBuilder.newBuilder();
        acceptIfNotNull(
                builder::setYearBuilt,
                getCustomFields("Year Of Construction"),
                y -> ((BigDecimal) y).intValue());
        acceptIfNotNull(builder::setBuildingType, getBuildingType());
        return builder.build();
    }

    public String getOperatingCompany() {
        return Optional.ofNullable((String) getCustomFields("Writing Company")).orElse("");
    }

    // from locationAddress
    public Address getAddress() {
        var address = inspectionDto.getLocationAddress();
        var builder = Address.AddressBuilder.newBuilder();
        acceptIfNotNull(builder::setZip, address.getZipCode());
        acceptIfNotNull(builder::setCountry, address.getCountry());
        acceptIfNotNull(builder::setCity, address.getCity());
        acceptIfNotNull(builder::setState, address.getRegion1());
        acceptIfNotNull(builder::setCounty, address.getRegion2());

        String street;
        if (StringUtils.isEmpty(address.getStreet2())) {
            street = address.getStreet1();
        } else {
            street = String.join(", ", address.getStreet1(), address.getStreet2());
        }
        acceptIfNotNull(builder::setStreetAddress, street);

        return builder.build();
    }

    public Claim getClaim() {
        if (!ProjectTypeEnum.CLAIM.equals(serviceType.getProjectType())) {
            return null;
        }

        var claimNo =
                Optional.ofNullable(inspectionDto.getInspectionNumber())
                        .map(String::valueOf)
                        .orElse("");
        return Claim.ClaimBuilder.newBuilder()
                .setClaimNo(claimNo)
                .setServiceType(serviceType)
                .build();
    }

    public Message.BuildingType getBuildingType() {
        var coverage = inspectionDto.getCoverages();
        if (coverage == null) {
            return null;
        }

        if (CollectionUtils.isNotEmpty(coverage.getCommercial())) {
            return Message.BuildingType.COMMERCIAL;
        }

        return Message.BuildingType.RESIDENTIAL_SINGLE_FAMILY;
    }

    public Underwriting getUnderwriting() {
        if (!ProjectTypeEnum.UNDERWRITING.equals(serviceType.getProjectType())) {
            return null;
        }

        return Underwriting.UnderwritingBuilder.newBuilder().setServiceType(serviceType).build();
    }

    public Collection<Contact> getContact() {
        var additionalContacts = inspectionDto.getAdditionalContacts();
        if (CollectionUtils.isEmpty(additionalContacts)) {
            return List.of();
        }

        return additionalContacts.stream()
                .map(
                        c -> {
                            var builder = Contact.ContactBuilder.newBuilder();
                            builder.setRole(
                                    Optional.ofNullable(c.getContactType()).orElse("Other"));
                            acceptIfNotNull(builder::setFirstName, c.getFirstName());
                            acceptIfNotNull(builder::setLastName, c.getLastName());
                            acceptIfNotNull(
                                    builder::setPrimaryPhone,
                                    Objects.isNull(c.getCellPhone())
                                            ? c.getWorkPhone()
                                            : c.getCellPhone());
                            acceptIfNotNull(builder::setPrimaryEmail, c.getEmail());
                            return builder.build();
                        })
                .collect(Collectors.toList());
    }

    public Contact getAgent() {
        var agency = inspectionDto.getAgent();
        var builder = Contact.ContactBuilder.newBuilder().setRole(ContactRoleEnum.AGENT.getName());
        acceptIfNotNull(builder::setFullName, agency.getAgentName());
        acceptIfNotNull(builder::setPrimaryEmail, agency.getEmail());
        acceptIfNotNull(
                builder::setPrimaryPhone,
                Objects.isNull(agency.getPhoneNumber())
                        ? agency.getFaxNumber()
                        : agency.getPhoneNumber());
        return builder.build();
    }

    public Contact getInsured() {
        var policyHolder = inspectionDto.getPolicyHolder();
        var builder =
                Contact.ContactBuilder.newBuilder().setRole(ContactRoleEnum.INSURED.getName());
        acceptIfNotNull(builder::setFirstName, policyHolder.getFirstName());
        acceptIfNotNull(builder::setLastName, policyHolder.getLastName());
        acceptIfNotNull(builder::setPrimaryEmail, policyHolder.getEmail());

        // Handle phone numbers in priority order
        List<String> otherPhone = new ArrayList<>();
        String[] phones = {
            policyHolder.getCellPhone(), policyHolder.getHomePhone(), policyHolder.getWorkPhone()
        };

        boolean primarySet = false;
        for (String phone : phones) {
            if (!primarySet && phone != null) {
                builder.setPrimaryPhone(phone);
                primarySet = true;
            } else if (phone != null) {
                otherPhone.add(phone);
            }
        }

        builder.setOtherPhone(otherPhone.isEmpty() ? null : otherPhone);
        return builder.build();
    }

    public String getInsuredWorkPhone() {
        var policyHolder = inspectionDto.getPolicyHolder();
        return policyHolder.getWorkPhone();
    }

    public String getInsuredHomePhone() {
        var policyHolder = inspectionDto.getPolicyHolder();
        return policyHolder.getHomePhone();
    }

    public String getNotes() {
        return Optional.ofNullable(inspectionDto.getOrderNotes()).orElse("");
    }

    public Object getCustomFields(String key) {
        if (customFields == null) {
            customFields = new HashMap<>();
            initExtraFields();
        }

        return customFields.get(key);
    }

    private void initExtraFields() {
        if (CollectionUtils.isEmpty(inspectionDto.getExtraInfoes())) {
            return;
        }

        inspectionDto
                .getExtraInfoes()
                .forEach(
                        info -> {
                            switch (info.getGenericFieldValueType()) {
                                case 0:
                                    customFields.put(info.getKey(), info.getText());
                                    break;
                                case 1:
                                    customFields.put(info.getKey(), info.getNumber());
                                    break;
                                case 2:
                                    customFields.put(info.getKey(), info.getDateTime());
                                    break;
                                case 3:
                                    customFields.put(info.getKey(), info.getTrueFalse());
                            }
                        });
    }
}
