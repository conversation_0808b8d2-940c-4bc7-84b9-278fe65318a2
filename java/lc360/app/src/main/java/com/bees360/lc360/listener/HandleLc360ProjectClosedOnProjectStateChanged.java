package com.bees360.lc360.listener;

import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SendProjectClosedEmailJob;
import com.bees360.job.registry.SyncNoteToLc360Job;
import com.bees360.job.util.EventTriggeredJobMultiple;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

/** 处理项目状态变更事件，在项目关闭时触发发送邮件和同步笔记任务。 */
@Log4j2
public class HandleLc360ProjectClosedOnProjectStateChanged
        extends EventTriggeredJobMultiple<ProjectStateChangedEvent> {

    private static final String INTEGRATION_TYPE = "LossControl360";

    private final ExternalIntegrationProvider externalIntegrationProvider;

    private final Map<String, String> closeOutReasonMap;

    private final Boolean enableSendEmail;

    private final Boolean enableSyncNote;

    private final Predicate<String> enableNotifyProjectClosed;

    public HandleLc360ProjectClosedOnProjectStateChanged(
            JobScheduler jobScheduler,
            ExternalIntegrationProvider externalIntegrationProvider,
            Map<String, String> closeOutReasonMap,
            Boolean enableSendEmail,
            Boolean enableSyncNote,
            Predicate<String> enableNotifyProjectClosed) {
        super(jobScheduler);
        this.externalIntegrationProvider = externalIntegrationProvider;
        this.closeOutReasonMap = closeOutReasonMap;
        this.enableSendEmail = enableSendEmail;
        this.enableSyncNote = enableSyncNote;
        this.enableNotifyProjectClosed = enableNotifyProjectClosed;
        log.info("Created {}(closeOutReasonMap={}).", this, this.closeOutReasonMap);
    }

    @Override
    protected Iterable<Job> convert(ProjectStateChangedEvent event) {
        var projectId = String.valueOf(event.getProjectId());
        var changeReason = event.getCurrentState().getStateChangeReason().getKey();

        List<Job> jobs = new ArrayList<>();
        if (enableSendEmail) {
            var emailJob = getSendEmailJob(projectId, changeReason);
            jobs.add(emailJob);
        }

        if (enableSyncNote) {
            var syncNoteJob = getSyncNoteJob(projectId, changeReason);
            jobs.add(syncNoteJob);
        }
        return jobs;
    }

    @Override
    protected boolean filter(ProjectStateChangedEvent event) {
        var state = event.getCurrentState();
        if (!Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE.equals(
                state.getState())) {
            return false;
        }

        var changeReason = state.getStateChangeReason();
        if (!closeOutReasonMap.containsKey(changeReason.getKey())) {
            return false;
        }

        var inspection =
                Iterables.toStream(
                                externalIntegrationProvider.findAllByProjectId(
                                        String.valueOf(event.getProjectId())))
                        .filter(i -> INTEGRATION_TYPE.equals(i.getIntegrationType()))
                        .findFirst();
        return inspection.isPresent()
                && enableNotifyProjectClosed.test(inspection.get().getDataset());
    }

    private Job getSendEmailJob(String projectId, String changeReason) {
        var payload = new SendProjectClosedEmailJob();
        payload.setProjectId(projectId);
        payload.setCloseReason(changeReason);
        return RetryableJob.of(Job.ofPayload(payload), 3, Duration.ofSeconds(3), 1.0F);
    }

    private Job getSyncNoteJob(String projectId, String changeReason) {
        var integration =
                Iterables.toStream(externalIntegrationProvider.findAllByProjectId(projectId))
                        .filter(i -> INTEGRATION_TYPE.equals(i.getIntegrationType()))
                        .findFirst()
                        .orElse(null);

        var payload = new SyncNoteToLc360Job();
        payload.setInspectionId(integration.getReferenceNumber());
        payload.setNote(closeOutReasonMap.get(changeReason));
        payload.setDataset(integration.getDataset());
        return RetryableJob.of(Job.ofPayload(payload), 3, Duration.ofSeconds(3), 1.0F);
    }
}
