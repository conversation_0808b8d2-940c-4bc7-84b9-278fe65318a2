package com.bees360.lc360.config;

import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.event.CancelInspectionOnProjectCancel;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegrationProvider;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;

@Configuration
@ConditionalOnProperty(
        prefix = "app.lc360.inspection",
        name = "cancelation-if-project-canceled",
        havingValue = "true")
@Import({LC360CustomerConfig.class})
public class CancelInspectionOnProjectCancelConfig {

    @Bean
    CancelInspectionOnProjectCancel cancelInspectionOnProjectCancel(
            ExternalIntegrationProvider externalIntegrationProvider,
            Function<String, Lc360Api> lc360ApiProvider,
            CommentManager commentManager,
            @Qualifier("robotUserIdSupplier") Supplier<String> robotUserIdSupplier,
            Predicate<String> enableCancelInspection) {
        return new CancelInspectionOnProjectCancel(
                externalIntegrationProvider,
                lc360ApiProvider,
                (projectId, inspectionId, response) -> {
                    var createdBy = robotUserIdSupplier.get();
                    var content = "";
                    if (response.isSuccessful()) {
                        content =
                                String.format(
                                        "The Lc360 Inspection(%s) related to the project has been"
                                                + " canceled.",
                                        inspectionId);
                    } else {
                        content =
                                String.format(
                                        "Fail to cancel the inspection(%s) related to the project"
                                                + " for reason: %s.",
                                        inspectionId, response.getBaseCancelError());
                    }
                    var comment = Comment.from(Long.parseLong(projectId), createdBy, content);
                    commentManager.addComment(comment);
                },
                enableCancelInspection);
    }

    @Bean("enableCancelInspection")
    Predicate<String> enableCancelInspection(
            LC360CustomerConfig.Lc360CustomerProperties properties) {
        return dataset ->
                properties.getDatasets().stream()
                        .filter(d -> StringUtils.equals(dataset, d.getId()))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableCancelInspection)
                        .findFirst()
                        .orElse(true);
    }
}
