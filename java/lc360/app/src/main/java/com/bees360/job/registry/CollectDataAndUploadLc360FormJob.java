package com.bees360.job.registry;

import lombok.Builder;
import lombok.Data;

@JobPayload
@Data
@Builder(setterPrefix = "set", builderMethodName = "newBuilder")
public class CollectDataAndUploadLc360FormJob {
    private String inspectionId;
    private String inspectionFormId;
    private String projectId;
    private String formTemplateId;
    private String integrationId;
    private String dataset;
}
