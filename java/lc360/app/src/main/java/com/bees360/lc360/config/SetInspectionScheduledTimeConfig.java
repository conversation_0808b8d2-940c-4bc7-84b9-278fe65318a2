package com.bees360.lc360.config;

import com.bees360.event.SetInspectionScheduledTimeToLc360Listener;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.project.config.GrpcExternalIntegrationManagerConfig;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.Predicate;

@Configuration
@ConditionalOnProperty(
        prefix = "lc360.app.set-inspection-scheduled-time",
        value = "enabled",
        havingValue = "true")
@Import({
    LC360CustomerConfig.class,
    GrpcExternalIntegrationManagerConfig.class,
    SetInspectionScheduledTimeToLc360Listener.class,
    AutoRegisterEventListenerConfig.class,
})
public class SetInspectionScheduledTimeConfig {

    @Bean("enableSyncScheduledTime")
    Predicate<String> enableSyncScheduledTime(
            LC360CustomerConfig.Lc360CustomerProperties properties) {
        return dataset ->
                properties.getDatasets().stream()
                        .filter(d -> StringUtils.equals(dataset, d.getId()))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableSyncScheduledTime)
                        .findFirst()
                        .orElse(true);
    }
}
