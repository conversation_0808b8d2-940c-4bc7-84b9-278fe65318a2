package com.bees360.event;

import com.bees360.event.registry.InspectionScheduledTimeChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;

/** 监听检查计划时间变更事件，并将变更同步到LC360系统。 */
@Log4j2
public class SetInspectionScheduledTimeToLc360Listener
        extends AbstractNamedEventListener<InspectionScheduledTimeChanged> {

    private final Function<String, Lc360Api> lc360ApiProvider;

    private final ExternalIntegrationProvider integrationProvider;

    private static final String INTEGRATION_TYPE = "LossControl360";

    private final Predicate<String> enableSyncScheduledTime;

    public SetInspectionScheduledTimeToLc360Listener(
            Function<String, Lc360Api> lc360ApiProvider,
            ExternalIntegrationProvider integrationProvider,
            Predicate<String> enableSyncScheduledTime) {
        this.lc360ApiProvider = lc360ApiProvider;
        this.integrationProvider = integrationProvider;
        this.enableSyncScheduledTime = enableSyncScheduledTime;
        log.info("Created {}.", this);
    }

    @Override
    public void handle(InspectionScheduledTimeChanged event) throws IOException {
        log.info("Received event: {}.", event);

        var projectId = event.getProjectId();
        var inspection =
                Iterables.toStream(integrationProvider.findAllByProjectId(projectId))
                        .filter(e -> StringUtils.equals(e.getIntegrationType(), INTEGRATION_TYPE))
                        .findFirst()
                        .orElse(null);
        if (inspection == null || !enableSyncScheduledTime.test(inspection.getDataset())) {
            // not a LC360 project
            return;
        }

        var inspectionId = inspection.getReferenceNumber();

        var lc360Api = lc360ApiProvider.apply(inspection.getDataset()).getVendorApi();
        lc360Api.saveInspectionSchedule(
                inspectionId,
                Optional.ofNullable(event.getScheduledTime())
                        .map(Instant::ofEpochMilli)
                        .orElse(null),
                Optional.ofNullable(event.getReason())
                        .map(InspectionScheduledTimeChanged.Reason::getReason)
                        .orElse(null));
        log.info(
                "Successfully set inspection {} scheduled time for project {} on LC360.",
                inspectionId,
                projectId);
    }
}
