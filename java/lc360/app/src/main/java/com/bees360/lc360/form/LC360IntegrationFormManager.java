package com.bees360.lc360.form;

import com.bees360.lc360.Form;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.IntegrationForm;
import com.bees360.project.IntegrationFormManager;
import com.bees360.project.util.ForwardingIntegrationFormManager;

import lombok.extern.log4j.Log4j2;

import java.util.function.BiFunction;
import java.util.function.Function;

@Log4j2
public class LC360IntegrationFormManager extends ForwardingIntegrationFormManager {

    private final Function<String, Lc360Api> lc360ApiProvider;

    private final ExternalIntegrationProvider integrationProvider;

    private final BiFunction<String, String, Form> formDefinitionProvider;

    public LC360IntegrationFormManager(
            IntegrationFormManager integrationFormManager,
            ExternalIntegrationProvider integrationProvider,
            Function<String, Lc360Api> lc360ApiProvider,
            BiFunction<String, String, Form> formDefinitionProvider) {
        super(integrationFormManager);
        this.lc360ApiProvider = lc360ApiProvider;
        this.integrationProvider = integrationProvider;
        this.formDefinitionProvider = formDefinitionProvider;
        log.info(
                "Created {}(lc360Api={},integrationProvider={},formDefinitionProvider={}).",
                this,
                this.lc360ApiProvider,
                this.integrationProvider,
                this.formDefinitionProvider);
    }

    @Override
    public String addIntegrationForm(IntegrationForm form) {
        var integrationId = form.getIntegrationId();
        var integration = getInspection(integrationId);
        if (integration == null) {
            throw new IllegalStateException(
                    String.format(
                            "Inspection for integration %s to add form is null.", integrationId));
        }

        var dataset = integration.getDataset();
        var formTemplateId = form.getTemplateId();
        var formData = formDefinitionProvider.apply(dataset, formTemplateId);
        if (formData == null || formData.getFormLookupID() == null) {
            throw new IllegalStateException(
                    String.format(
                            "Form %s to add in LC360 can not match a form lookup id.",
                            formTemplateId));
        }

        var lc360Api = lc360ApiProvider.apply(dataset).getVendorApi();
        var formId =
                lc360Api.addInspectionForm(
                        integration.getReferenceNumber(), formData.getFormLookupID(), null);

        var formToSave =
                IntegrationForm.from(
                        form.toMessage().toBuilder()
                                .setFormId(formId)
                                .setName(formData.getName())
                                .build());

        delegate().addIntegrationForm(formToSave);
        return formId;
    }

    private ExternalIntegration getInspection(String integrationId) {
        return integrationProvider.findById(integrationId);
    }
}
