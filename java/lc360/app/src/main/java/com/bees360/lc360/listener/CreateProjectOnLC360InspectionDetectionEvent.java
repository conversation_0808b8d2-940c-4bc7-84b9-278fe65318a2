package com.bees360.lc360.listener;

import com.bees360.event.registry.LC360InspectionDetectionEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.CreateProjectFromLC360InspectionJob;
import com.bees360.job.registry.MarkInspectionProcessedOnLC360Job;
import com.bees360.lc360.Inspection;
import com.bees360.lc360.InspectionsResponse;
import com.bees360.lc360.Lc360VendorApi;
import com.bees360.lc360.form.LC360IntegrationForm;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.IntegrationForm;
import com.bees360.project.IntegrationFormManager;

import jakarta.annotation.Nullable;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/** 监听LC360检查检测事件，处理检查数据并创建相关项目或标记已处理。 */
@Log4j2
public class CreateProjectOnLC360InspectionDetectionEvent
        extends AbstractNamedEventListener<LC360InspectionDetectionEvent> {

    private final Lc360VendorApi lc360Api;

    private final ExternalIntegrationManager integrationManager;

    private final JobScheduler jobScheduler;

    private final String dataset;

    public static final String INTEGRATION_TYPE = "LossControl360";

    private final List<String> divisionLookupIds;

    private final Function<String, String> creationJobIdProvider;

    private final IntegrationFormManager formManager;

    public CreateProjectOnLC360InspectionDetectionEvent(
            @NonNull Lc360VendorApi lc360Api,
            @NonNull ExternalIntegrationManager integrationManager,
            @NonNull IntegrationFormManager integrationFormManager,
            @NonNull JobScheduler jobScheduler,
            @NonNull Function<String, String> creationJobIdProvider,
            @NonNull String dataset,
            @Nullable List<String> divisionLookupIds) {
        this.lc360Api = lc360Api;
        this.integrationManager = integrationManager;
        this.formManager = integrationFormManager;
        this.jobScheduler = jobScheduler;
        this.creationJobIdProvider = creationJobIdProvider;
        this.dataset = dataset;
        this.divisionLookupIds = divisionLookupIds;
        log.info(
                "Created {}(lc360Api={}, integrationManager={}, jobScheduler={},"
                        + " dataset={}, divisionLookupIds={}).",
                this,
                this.lc360Api,
                this.integrationManager,
                this.jobScheduler,
                this.dataset,
                this.divisionLookupIds);
    }

    /**
     * 拉取LC360的inspection list，将inspection封装为job用于创建case;
     * 若inspection已经被关联上project，则创建job将其标志为processed
     */
    @Override
    public void handle(LC360InspectionDetectionEvent event) throws IOException {
        log.info(
                "Start to fetch inspections from LossControl360 dataset {} for project creation.",
                dataset);
        InspectionsResponse inspections;
        try {
            inspections = lc360Api.getInspections(divisionLookupIds);
        } catch (Exception ex) {
            // catch exceptions in cron tasks
            log.warn(
                    "Fetch lc360 inspections from dataset {} failed: response message: {}.",
                    dataset,
                    ex.getMessage());
            return;
        }

        if (inspections == null || CollectionUtils.isEmpty(inspections.getInspections())) {
            return;
        }

        List<String> processInspectionIds = new ArrayList<>();
        for (Inspection inspection : inspections.getInspections()) {
            var inspectionId = inspection.getInspectionID();
            var integrationId = createIntegration(inspectionId);
            if (Objects.nonNull(integrationId)) {
                var integration = integrationManager.findById(integrationId);
                scheduleCreationJob(integration, inspection);
            } else {
                var integration =
                        integrationManager.findByReference(INTEGRATION_TYPE, inspectionId);
                if (integration != null && integration.getProjectId() != null) {
                    integrationId = integration.getId();
                    processInspectionIds.add(inspectionId);
                }
            }

            updateIntegrationForms(integrationId, inspection);
        }

        scheduleProcessJob(processInspectionIds);
        log.info(
                "Successfully handle {} inspection from LossControl360 dataset {}.",
                inspections.getInspections().size(),
                dataset);
    }

    private void scheduleCreationJob(ExternalIntegration integration, Inspection inspection) {
        var payload = new CreateProjectFromLC360InspectionJob();
        payload.setInspection(inspection);
        payload.setIntegration(integration);
        var job = Job.ofPayload(payload, creationJobIdProvider.apply(inspection.getInspectionID()));
        jobScheduler.schedule(RetryableJob.of(job, 5, Duration.ofSeconds(30), 1.0F));
    }

    private void scheduleProcessJob(List<String> inspectionIds) {
        if (CollectionUtils.isEmpty(inspectionIds)) {
            return;
        }

        var payload = new MarkInspectionProcessedOnLC360Job();
        payload.setInspectionIds(inspectionIds);
        payload.setDataset(dataset);
        var job = Job.ofPayload(payload);
        jobScheduler.schedule(RetryableJob.of(job, 3, Duration.ofSeconds(30), 1.0F));
    }

    private String createIntegration(String inspectionId) {
        return integrationManager.create(dataset, INTEGRATION_TYPE, inspectionId);
    }

    private void updateIntegrationForms(String integrationId, Inspection inspection) {
        if (integrationId == null) {
            return;
        }

        if (CollectionUtils.isEmpty(inspection.getBuildings())) {
            formManager.setIntegrationForm(integrationId, List.of());
            return;
        }

        List<IntegrationForm> forms =
                inspection.getBuildings().stream()
                        .flatMap(b -> b.getForms().stream())
                        .map(LC360IntegrationForm::new)
                        .collect(Collectors.toList());

        formManager.setIntegrationForm(integrationId, forms);
    }

    @Nullable
    @Override
    public String getName() {
        var customerName = dataset.replace(" ", "_").toLowerCase(Locale.ROOT);
        return super.getName() + "_" + customerName;
    }
}
