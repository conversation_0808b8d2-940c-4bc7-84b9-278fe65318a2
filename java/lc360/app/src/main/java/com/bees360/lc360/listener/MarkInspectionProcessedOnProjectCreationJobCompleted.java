package com.bees360.lc360.listener;

import com.bees360.api.ApiStatus;
import com.bees360.event.registry.JobCompleted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

/** 监听项目创建完成事件，处理关联的检查项并标记为已处理 */
@Log4j2
public class MarkInspectionProcessedOnProjectCreationJobCompleted
        extends AbstractNamedEventListener<JobCompleted> {

    private final Function<String, Lc360Api> lc360ApiProvider;

    private final Function<String, String> creationJobIdDecoder;

    private final ExternalIntegrationProvider externalIntegrationProvider;

    private static final String LC360_INTEGRATION_TYPE = "LossControl360";

    public MarkInspectionProcessedOnProjectCreationJobCompleted(
            Function<String, Lc360Api> lc360ApiProvider,
            Function<String, String> creationJobIdDecoder,
            ExternalIntegrationProvider externalIntegrationProvider) {
        this.lc360ApiProvider = lc360ApiProvider;
        this.creationJobIdDecoder = creationJobIdDecoder;
        this.externalIntegrationProvider = externalIntegrationProvider;
        log.info("Created {}(lc360Api={}).", this, this.lc360ApiProvider);
    }

    @Override
    public void handle(JobCompleted event) throws IOException {
        if (ApiStatus.OK.getCode() != event.getStatus().getCode()) {
            return;
        }

        var jobId = event.getId();
        var inspectionId = creationJobIdDecoder.apply(jobId);
        var lc360Api = lc360ApiProvider.apply(getDataset(inspectionId)).getVendorApi();
        lc360Api.processInspections(List.of(inspectionId));
        log.info("Successfully process {} inspections on LossControl360", inspectionId);
    }

    private String getDataset(String inspectionId) {
        return Optional.ofNullable(
                        externalIntegrationProvider.findByReference(
                                LC360_INTEGRATION_TYPE, inspectionId))
                .map(ExternalIntegration::getDataset)
                .orElse(null);
    }

    @Override
    public String getRoutingKey() {
        return super.getRoutingKey() + "." + "create_project_from_lc360_inspection_job";
    }
}
