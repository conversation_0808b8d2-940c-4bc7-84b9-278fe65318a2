package com.bees360.lc360.config;

import com.bees360.contract.Contract;
import com.bees360.contract.ContractManager;
import com.bees360.contract.config.GrpcContractManagerConfig;
import com.bees360.customer.CustomerProvider;
import com.bees360.customer.config.GrpcCustomerClientConfig;
import com.bees360.event.EventDispatcher;
import com.bees360.job.CreateProjectFromLC360IntegrationJobExecutor;
import com.bees360.job.JobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.lc360.Inspection;
import com.bees360.lc360.Lc360Api;
import com.bees360.lc360.listener.CreateProjectOnLC360InspectionDetectionEvent;
import com.bees360.lc360.util.LC360InspectionAccessor;
import com.bees360.lc360.util.LC360InspectionConverter;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.GenericProjectCreator;
import com.bees360.project.IntegrationFormManager;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.config.GrpcGenericProjectCreatorConfig;
import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.UnaryOperator;

@Import({
    LC360FullCustomerProperties.class,
    GrpcGenericProjectCreatorConfig.class,
    GrpcContractManagerConfig.class,
    GrpcCustomerClientConfig.class,
    AutoRegisterJobExecutorConfig.class,
})
@Configuration
@Log4j2
public class LC360CreationConfig {

    @Bean
    public List<CreateProjectOnLC360InspectionDetectionEvent>
            createProjectOnLC360InspectionListenerList(
                    EventDispatcher eventDispatcher,
                    Function<String, Lc360Api> lc360ApiProvider,
                    ExternalIntegrationManager externalIntegrationManager,
                    IntegrationFormManager grpcIntegrationFormManager,
                    JobScheduler rabbitJobScheduler,
                    LC360FullCustomerProperties properties) {
        List<CreateProjectOnLC360InspectionDetectionEvent> listenerList = new ArrayList<>();
        for (LC360CustomerConfig.Lc360CustomerProperties.Dataset dataset :
                properties.getDatasets()) {
            var lc360Api = lc360ApiProvider.apply(dataset.getId());
            if (lc360Api == null) {
                log.info("No lc360 api found for customer {}.", dataset.getId());
                continue;
            }
            var creationJobIdProvider = creationJobIdProvider();
            var listener =
                    new CreateProjectOnLC360InspectionDetectionEvent(
                            lc360Api.getVendorApi(),
                            externalIntegrationManager,
                            grpcIntegrationFormManager,
                            rabbitJobScheduler,
                            creationJobIdProvider,
                            dataset.getId(),
                            dataset.getDivisionLookupIds());
            listenerList.add(listener);
        }
        listenerList.forEach(eventDispatcher::enlist);
        return listenerList;
    }

    private Function<String, String> creationJobIdProvider() {
        return inspectionId -> Instant.now().toEpochMilli() + inspectionId;
    }

    @Configuration
    @ConditionalOnProperty(prefix = "lc360.app", name = "enable-creation", havingValue = "true")
    static class ProjectCreationConfig {
        @Bean
        public CreateProjectFromLC360IntegrationJobExecutor
                createProjectFromLC360IntegrationJobExecutor(
                        GenericProjectCreator projectCreator,
                        @Qualifier("lc360ProjectCreationConverter")
                                BiFunction<ExternalIntegration, Inspection, ProjectCreationRequest>
                                        lc360ProjectCreationConverter) {
            return new CreateProjectFromLC360IntegrationJobExecutor(
                    lc360ProjectCreationConverter, projectCreator);
        }

        @Bean("lc360ProjectCreationConverter")
        public BiFunction<ExternalIntegration, Inspection, ProjectCreationRequest>
                lc360ProjectCreationConverter(
                        @Qualifier("lc360ContractProvider")
                                BiFunction<String, String, Contract> lc360ContractProvider,
                        @Qualifier("lc360ServiceTypeMappings")
                                BiFunction<String, Inspection, ServiceTypeEnum>
                                        lc360ServiceTypeMapper,
                        @Qualifier("lc360PolicyRenewalMappings")
                                BiFunction<String, LC360InspectionAccessor, Boolean>
                                        lc360PolicyRenewalMapper,
                        @Qualifier("lc360CreatorProvider")
                                UnaryOperator<String> lc360CreatorProvider) {
            var converter =
                    new LC360InspectionConverter(
                            lc360ContractProvider,
                            lc360ServiceTypeMapper,
                            lc360PolicyRenewalMapper,
                            lc360CreatorProvider);
            return converter::buildCreationRequest;
        }

        @Bean("lc360ContractProvider")
        public BiFunction<String, String, Contract> lc360ContractProvider(
                @Qualifier("lc360DatasetProvider")
                        Function<String, LC360FullCustomerProperties.LC360Dataset>
                                lc360DatasetProvider,
                CustomerProvider customerProvider,
                ContractManager contractManager) {
            return (customer, divisionLookupId) -> {
                var dataset = lc360DatasetProvider.apply(customer);
                var clients = dataset.getClients();

                LC360FullCustomerProperties.LC360Dataset.ContractProperties contract;
                if (clients.isEmpty()) {
                    contract = dataset.getContract();
                } else {
                    contract =
                            Optional.ofNullable(dataset.getClients().get(divisionLookupId))
                                    .map(
                                            LC360FullCustomerProperties.LC360Dataset.ClientDataset
                                                    ::getContract)
                                    .orElse(null);
                }

                return Optional.ofNullable(contract)
                        .map(
                                c -> {
                                    var insuredBy =
                                            customerProvider.findByKey(contract.getInsuredBy());
                                    var processedBy =
                                            customerProvider.findByKey(contract.getProcessedBy());
                                    Preconditions.checkNotNull(
                                            insuredBy,
                                            "Create project from LC360 fails: insured company {}"
                                                    + " not exists.",
                                            contract.getInsuredBy());
                                    Preconditions.checkNotNull(
                                            processedBy,
                                            "Create project from LC360 fails: processed company {}"
                                                    + " not exists.",
                                            contract.getProcessedBy());
                                    return contractManager.findByCompanyId(
                                            insuredBy.getId(), processedBy.getId());
                                })
                        .orElseThrow(
                                () ->
                                        new IllegalStateException(
                                                String.format(
                                                        "Create project from LC360 fails: no"
                                                                + " contract for division %s of"
                                                                + " customer %s",
                                                        divisionLookupId, customer)));
            };
        }

        @Bean("lc360CreatorProvider")
        public UnaryOperator<String> lc360CreatorProvider(
                @Qualifier("lc360DatasetProvider")
                        Function<String, LC360FullCustomerProperties.LC360Dataset>
                                lc360DatasetProvider) {
            return (customer) -> {
                var dataset = lc360DatasetProvider.apply(customer);
                return dataset.getCreator();
            };
        }

        @Bean("lc360ServiceTypeMappings")
        public BiFunction<String, Inspection, ServiceTypeEnum> lc360ServiceTypeMappings(
                @Qualifier("lc360DatasetProvider")
                        Function<String, LC360FullCustomerProperties.LC360Dataset>
                                lc360DatasetProvider) {
            return (customer, inspection) -> {
                var dataset = lc360DatasetProvider.apply(customer);
                Map<String, ServiceTypeEnum> serviceTypeMapper =
                        dataset.getServiceTypeMapper().getMapping();
                String inspectionTypeLookupID = inspection.getInspectionTypeLookupID();
                ServiceTypeEnum serviceType = serviceTypeMapper.get(inspectionTypeLookupID);
                if (serviceType == null) {
                    log.warn(
                            "inspection type {} for customer {} does not match service type",
                            inspectionTypeLookupID,
                            customer);
                    return dataset.getServiceTypeMapper().getDefaultValue();
                }
                return serviceType;
            };
        }

        @Bean("lc360PolicyRenewalMappings")
        public BiFunction<String, LC360InspectionAccessor, Boolean> lc360PolicyRenewalMappings(
                @Qualifier("lc360DatasetProvider")
                        Function<String, LC360FullCustomerProperties.LC360Dataset>
                                lc360DatasetProvider) {
            return (dataset, accessor) -> {
                var mapper = lc360DatasetProvider.apply(dataset).getPolicyRenewalMapper();
                if (mapper == null) {
                    return false;
                }

                // get value of isPolicyRenewal from custom field.
                String value = (String) accessor.getCustomFields(mapper.getField());
                return mapper.getMapping().getOrDefault(value, mapper.getDefaultValue());
            };
        }

        @Bean("lc360DatasetProvider")
        public Function<String, LC360FullCustomerProperties.LC360Dataset> datasetProvider(
                LC360FullCustomerProperties properties) {
            return dataset ->
                    properties.getDatasets().stream()
                            .filter(d -> StringUtils.equals(d.getId(), dataset))
                            .findFirst()
                            .orElseThrow(
                                    () ->
                                            new IllegalStateException(
                                                    String.format(
                                                            "Create project form lc360 fails:"
                                                                    + " dataset %s not exists.",
                                                            dataset)));
        }
    }
}
