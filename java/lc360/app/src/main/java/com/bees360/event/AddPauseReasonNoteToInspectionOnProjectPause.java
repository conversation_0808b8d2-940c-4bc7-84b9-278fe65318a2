package com.bees360.event;

import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;
import com.google.common.collect.Iterables;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.function.Function;
import java.util.function.Predicate;

/** 监听项目状态变更事件，在项目暂停时将暂停原因作为备注同步到外部检查系统 */
@Log4j2
public class AddPauseReasonNoteToInspectionOnProjectPause
        extends AbstractNamedEventListener<ProjectStateChangedEvent> {
    private static final String INTEGRATION_TYPE = "LossControl360";
    private final ExternalIntegrationProvider externalIntegrationProvider;
    private final Function<String, Lc360Api> lc360ApiProvider;
    private final Predicate<String> enableSyncProjectPauseNote;

    public AddPauseReasonNoteToInspectionOnProjectPause(
            @NonNull ExternalIntegrationProvider externalIntegrationProvider,
            @NonNull Function<String, Lc360Api> lc360ApiProvider,
            Predicate<String> enableSyncProjectPauseNote) {
        this.externalIntegrationProvider = externalIntegrationProvider;
        this.lc360ApiProvider = lc360ApiProvider;
        this.enableSyncProjectPauseNote = enableSyncProjectPauseNote;
        log.info("Created {}.", this);
    }

    @Override
    public void handle(ProjectStateChangedEvent event) throws IOException {
        var curState = event.getCurrentState();
        // 判断目前是否为 pause 状态
        if (!ProjectStateEnum.PROJECT_PAUSE.equals(curState.getState())) {
            return;
        }

        // 查询对应的 integration，并获取 referNumber
        var projectId = String.valueOf(event.getProjectId());
        var integration = findIntegration(projectId);
        if (integration == null || !enableSyncProjectPauseNote.test(integration.getDataset())) {
            return;
        }

        var referNumber = integration.getReferenceNumber();
        var lc360VendorApi = lc360ApiProvider.apply(integration.getDataset()).getVendorApi();

        // 获取 pause 原因，并调用 lc360 api 的 addNote 接口
        var pauseReason = curState.getStateChangeReason().getDisplayText();
        pauseReason = StringUtils.isEmpty(pauseReason) ? "No reason provided" : pauseReason;
        lc360VendorApi.addInspectionNote(
                referNumber,
                String.format("Project %s is paused due to '%s'.", projectId, pauseReason));
        log.info(
                "Add project pause reason ({}) as note to lc360 inspection {} (integrated project"
                        + " {}).",
                pauseReason,
                referNumber,
                projectId);
    }

    private ExternalIntegration findIntegration(String projectId) {
        var integrations = externalIntegrationProvider.findAllByProjectId(projectId);
        return Iterables.find(
                integrations,
                i -> StringUtils.equals(INTEGRATION_TYPE, i.getIntegrationType()),
                null);
    }
}
