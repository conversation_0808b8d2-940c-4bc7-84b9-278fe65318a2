package com.bees360.lc360.listener;

import com.bees360.api.InvalidArgumentException;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.event.util.PipelineTaskChangedListeners;
import com.bees360.job.Job;
import com.bees360.job.JobFuture;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.CollectDataAndUploadLc360FormJob;
import com.bees360.job.registry.CompleteInspectionOnLc360Job;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.IntegrationForm;
import com.bees360.project.IntegrationFormManager;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/** 监听PipelineTaskChanged事件，调度任务上传表单数据到LossControl360并在完成后更新管道任务状态。 */
@Log4j2
public class ScheduleUploadLC360FormOnTaskReady
        extends AbstractNamedEventListener<PipelineTaskChanged> {

    private final ExternalIntegrationManager integrationManager;

    private final IntegrationFormManager integrationFormManager;

    public static final String INTEGRATION_TYPE = "LossControl360";

    private final BiFunction<String, String, Set<String>> formTemplateProvider;

    private final JobScheduler jobScheduler;

    private final PipelineService pipelineService;

    private final Predicate<String> enableMoveForward;

    private final Predicate<String> enableUpdateForm;

    private static final String TASK_KEY = "fill_out_form_on_lc360";

    public ScheduleUploadLC360FormOnTaskReady(
            @NonNull ExternalIntegrationManager integrationManager,
            @NonNull IntegrationFormManager integrationFormManager,
            BiFunction<String, String, Set<String>> formTemplateProvider,
            JobScheduler jobScheduler,
            PipelineService pipelineService,
            Predicate<String> enableMoveForward,
            Predicate<String> enableUpdateForm) {
        this.integrationManager = integrationManager;
        this.integrationFormManager = integrationFormManager;
        this.formTemplateProvider = formTemplateProvider;
        this.jobScheduler = jobScheduler;
        this.pipelineService = pipelineService;
        this.enableMoveForward = enableMoveForward;
        this.enableUpdateForm = enableUpdateForm;
        log.info("Created {}.", this);
    }

    @Override
    public void handle(PipelineTaskChanged event) throws IOException {
        var projectId = event.getPipelineId();
        var integration = findIntegration(projectId);
        if (Objects.isNull(integration) || !enableUpdateForm.test(integration.getDataset())) {
            return;
        }

        var formForUpdate = formTemplateProvider.apply(integration.getDataset(), projectId);
        if (formForUpdate.isEmpty()) {
            log.info(
                    "No form template configured for dataset {}. Skip to upload lc360 form.",
                    integration.getDataset());
            return;
        }

        var forms = integrationFormManager.findFormsByIntegrationId(List.of(integration.getId()));
        if (com.google.common.collect.Iterables.isEmpty(forms)) {
            return;
        }
        log.info(
                "Find {} integration forms for project {}",
                Iterables.toStream(forms)
                        .map(IntegrationForm::toMessage)
                        .collect(Collectors.toList()),
                projectId);

        List<JobFuture> jobFutures = new ArrayList<>();
        formForUpdate.forEach(
                f ->
                        Optional.ofNullable(scheduleFormJob(integration, f, forms))
                                .ifPresent(jobFutures::add));

        setPipelineTaskAnsSpawnCompleteJobWhenDone(
                jobFutures,
                projectId,
                event.getTaskDefKey(),
                integration.getReferenceNumber(),
                integration.getDataset());
        log.info(
                "Successfully schedule job to upload project {} form on LossControl360", projectId);
    }

    private ExternalIntegration findIntegration(String projectId) {
        return Iterables.toStream(integrationManager.findAllByProjectId(projectId))
                .filter(i -> StringUtils.equals(INTEGRATION_TYPE, i.getIntegrationType()))
                .findFirst()
                .orElse(null);
    }

    private JobFuture scheduleFormJob(
            ExternalIntegration integration,
            String templateId,
            Iterable<? extends IntegrationForm> forms) {
        var form =
                Iterables.toStream(forms)
                        .filter(f -> StringUtils.equals(templateId, f.getTemplateId()))
                        .findFirst()
                        .orElse(null);

        if (Objects.isNull(form)) {
            return null;
        }

        var payload =
                CollectDataAndUploadLc360FormJob.newBuilder()
                        .setProjectId(integration.getProjectId())
                        .setInspectionId(integration.getReferenceNumber())
                        .setInspectionFormId(form.getFormId())
                        .setFormTemplateId(form.getTemplateId())
                        .setIntegrationId(integration.getId())
                        .setDataset(integration.getDataset())
                        .build();

        return jobScheduler.schedule(
                RetryableJob.of(Job.ofPayload(payload), 3, Duration.ofMinutes(1), 1.0F));
    }

    private void setPipelineTaskAnsSpawnCompleteJobWhenDone(
            List<JobFuture> jobFutures,
            String projectId,
            String taskKey,
            String inspectionId,
            String dataset) {

        // schedule job to move forward inspection when all form job done.
        if (enableMoveForward.test(dataset)) {
            Futures.whenAllSucceed(jobFutures)
                    .run(
                            () ->
                                    jobFutures.add(
                                            scheduleCompleteJob(projectId, inspectionId, dataset)),
                            MoreExecutors.directExecutor());
        }

        // add callback to set pipeline task status
        Futures.whenAllComplete(jobFutures)
                .run(
                        () -> setPipelineTask(jobFutures, projectId, taskKey),
                        MoreExecutors.directExecutor());
    }

    private JobFuture scheduleCompleteJob(String projectId, String inspectionId, String dataset) {
        var payload =
                CompleteInspectionOnLc360Job.newBuilder()
                        .setProjectId(projectId)
                        .setInspectionId(inspectionId)
                        .setDataset(dataset)
                        .build();
        return jobScheduler.schedule(
                RetryableJob.of(Job.ofPayload(payload), 3, Duration.ofSeconds(10), 1.0F));
    }

    private void setPipelineTask(List<JobFuture> jobFutures, String projectId, String taskKey) {
        try {
            for (JobFuture jobFuture : jobFutures) {
                jobFuture.get();
            }
            setPipelineTaskStatus(projectId, taskKey, Message.PipelineStatus.DONE, null);
        } catch (Throwable e) {
            String errorMsg = e.getMessage();
            if (e.getCause() != null) {
                errorMsg = e.getCause().getMessage();
            }
            setPipelineTaskStatus(projectId, taskKey, Message.PipelineStatus.ERROR, errorMsg);
        }
    }

    private void setPipelineTaskStatus(
            String pipelineId, String taskKey, Message.PipelineStatus status, String comment) {
        try {
            pipelineService.setTaskStatus(pipelineId, taskKey, status, comment);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.error(
                    "Failed set pipeline '{}' key '{}' to '{}' due to {}",
                    pipelineId,
                    taskKey,
                    status,
                    e);
        }
    }

    @Override
    public String getRoutingKey() {
        return PipelineTaskChangedListeners.getEventName(TASK_KEY, Message.PipelineStatus.READY);
    }
}
