package com.bees360.job.registry;

import com.bees360.codec.ProtoGsonDecoder;
import com.bees360.lc360.Inspection;
import com.bees360.project.ExternalIntegration;
import com.google.gson.annotations.JsonAdapter;

import lombok.Data;
import lombok.ToString;

@Data
@JobPayload("create_project_from_lc360_inspection_job")
@ToString
public class CreateProjectFromLC360InspectionJob {

    @JsonAdapter(ProtoGsonDecoder.class)
    private ExternalIntegration integration;

    private Inspection inspection;
}
