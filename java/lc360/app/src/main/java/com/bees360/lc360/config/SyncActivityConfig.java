package com.bees360.lc360.config;

import com.bees360.event.TriggerSyncActivityJobOnChanged;
import com.bees360.job.JobScheduler;
import com.bees360.job.Lc360ActivitySyncJobExecutor;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegrationProvider;

import lombok.Data;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Duration;
import java.util.function.Function;
import java.util.function.Predicate;

@Configuration
@ConditionalOnProperty(prefix = "lc360.note.sync-activity", name = "enabled", havingValue = "true")
@EnableConfigurationProperties
@Import({
    LC360CustomerConfig.class,
})
public class SyncActivityConfig {

    @Data
    public static class RetryableJobProperties {
        private Integer retryCount = 3;
        private Duration retryDelay = Duration.ofSeconds(30);
        private Float retryDelayIncreaseFactor = 1.0F;
    }

    @Bean
    @ConfigurationProperties(prefix = "lc360.note.sync-activity")
    public RetryableJobProperties syncActivityProperties() {
        return new RetryableJobProperties();
    }

    @Bean
    public TriggerSyncActivityJobOnChanged lc360SyncCommentOnActivityChanged(
            JobScheduler jobScheduler,
            ExternalIntegrationProvider externalIntegrationProvider,
            @Qualifier("syncActivityProperties") RetryableJobProperties properties,
            Predicate<String> enableSyncActivity) {
        return new TriggerSyncActivityJobOnChanged(
                jobScheduler,
                externalIntegrationProvider,
                properties.getRetryCount(),
                properties.getRetryDelay(),
                properties.getRetryDelayIncreaseFactor(),
                enableSyncActivity);
    }

    @Bean
    public Lc360ActivitySyncJobExecutor lc360SyncActivityJobExecutor(
            Function<String, Lc360Api> lc360ApiProvider,
            ExternalIntegrationProvider externalIntegrationProvider) {
        return new Lc360ActivitySyncJobExecutor(lc360ApiProvider, externalIntegrationProvider);
    }

    @Bean("enableSyncActivity")
    Predicate<String> enableSyncActivity(LC360CustomerConfig.Lc360CustomerProperties properties) {
        return dataset ->
                properties.getDatasets().stream()
                        .filter(d -> StringUtils.equals(dataset, d.getId()))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableSyncActivity)
                        .findFirst()
                        .orElse(true);
    }
}
