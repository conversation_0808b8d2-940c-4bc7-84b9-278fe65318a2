package com.bees360.job;

import com.bees360.api.InternalException;
import com.bees360.api.InvalidArgumentException;
import com.bees360.job.registry.TriggerFillOutUTCFormJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.IntegrationFormProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

@Log4j2
public class TriggerFillOutUTCFormJobExecutor
        extends AbstractJobExecutor<TriggerFillOutUTCFormJob> {

    private final IntegrationFormProvider integrationFormProvider;

    private final PipelineService pipelineService;

    private final Function<String, Set<String>> closeoutFormProvider;

    public TriggerFillOutUTCFormJobExecutor(
            IntegrationFormProvider integrationFormProvider,
            PipelineService pipelineService,
            Function<String, Set<String>> closeoutFormProvider) {
        this.integrationFormProvider = integrationFormProvider;
        this.pipelineService = pipelineService;
        this.closeoutFormProvider = closeoutFormProvider;
        log.info("Created {}.", this);
    }

    @Override
    protected void handle(TriggerFillOutUTCFormJob job) throws IOException {
        log.info("Received job {}.", job);

        var projectId = job.getProjectId();
        var forms =
                integrationFormProvider.findFormsByIntegrationId(List.of(job.getIntegrationId()));
        var formTemplateList = closeoutFormProvider.apply(job.getDataset());
        var formForUpdate =
                Iterables.toStream(forms)
                        .filter(f -> formTemplateList.contains(f.getTemplateId()))
                        .findFirst()
                        .orElse(null);

        if (formForUpdate == null) {
            // throw exception for retry
            throw new InternalException(
                    String.format(
                            "Fill out UTC form on LC360 fails: UTC form of project %s does not"
                                    + " exist.",
                            projectId));
        }

        // trigger pipeline task ready for fill out utc form.
        setPipelineTaskStatus(projectId);
        log.info(
                "Successfully set pipeline task ready for fill out UTC form of project {}.",
                projectId);
    }

    private void setPipelineTaskStatus(String pipelineId) {
        var taskKey = "fill_out_form_on_lc360";
        try {
            pipelineService.setTaskStatus(
                    pipelineId,
                    taskKey,
                    com.bees360.pipeline.Message.PipelineStatus.READY,
                    "Fill out closeout form needed.");
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.error(
                    "Fill out UTC form on LC360 fails: failed set pipeline '{}' key '{}' to '{}'"
                            + " due to {}",
                    pipelineId,
                    taskKey,
                    com.bees360.pipeline.Message.PipelineStatus.ERROR,
                    e);
        }
    }
}
