package com.bees360.lc360.config;

import com.bees360.event.EventDispatcher;
import com.bees360.event.UpdateGenericFieldsOnProjectStatusChanged;
import com.bees360.lc360.Lc360Api;
import com.bees360.project.ExternalIntegrationProvider;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.Function;
import java.util.function.Predicate;

@Configuration
@Import({LC360CustomerApiConfig.class})
@ConditionalOnProperty(
        prefix = "lc360.app",
        name = "enable-update-generic-fields",
        havingValue = "true")
public class UpdateInspectionGenericFieldsConfig {

    @Bean
    public UpdateGenericFieldsOnProjectStatusChanged updateGenericFieldsOnProjectStatusChanged(
            EventDispatcher eventDispatcher,
            @Qualifier("lc360ApiProvider") Function<String, Lc360Api> lc360ApiProvider,
            ExternalIntegrationProvider integrationProvider,
            LC360CustomerConfig.Lc360CustomerProperties properties) {
        var predicate = enableUpdateProjectStatus(properties);
        var listener =
                new UpdateGenericFieldsOnProjectStatusChanged(
                        lc360ApiProvider, integrationProvider, predicate);
        eventDispatcher.enlist(listener);
        return listener;
    }

    private Predicate<String> enableUpdateProjectStatus(
            LC360CustomerConfig.Lc360CustomerProperties properties) {
        return dataset ->
                properties.getDatasets().stream()
                        .filter(d -> StringUtils.equals(dataset, d.getId()))
                        .map(
                                LC360CustomerConfig.Lc360CustomerProperties.Dataset
                                        ::getEnableUpdateProjectStatus)
                        .findFirst()
                        .orElse(false);
    }
}
