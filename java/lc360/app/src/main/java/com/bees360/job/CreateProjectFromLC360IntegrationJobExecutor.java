package com.bees360.job;

import com.bees360.job.registry.CreateProjectFromLC360InspectionJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.lc360.Inspection;
import com.bees360.project.CreationChannelType;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.GenericProjectCreator;
import com.bees360.project.ProjectCreationRequest;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.function.BiFunction;

@Log4j2
public class CreateProjectFromLC360IntegrationJobExecutor
        extends AbstractJobExecutor<CreateProjectFromLC360InspectionJob> {

    private final BiFunction<ExternalIntegration, Inspection, ProjectCreationRequest>
            creationRequestBuilder;

    private final GenericProjectCreator projectCreator;

    public CreateProjectFromLC360IntegrationJobExecutor(
            BiFunction<ExternalIntegration, Inspection, ProjectCreationRequest>
                    creationRequestBuilder,
            GenericProjectCreator projectCreator) {
        this.creationRequestBuilder = creationRequestBuilder;
        this.projectCreator = projectCreator;
        log.info("Created {}.", this);
    }

    @Override
    protected void handle(CreateProjectFromLC360InspectionJob job) throws IOException {
        log.info("Received job {}.", job);

        var integration = job.getIntegration();
        var inspection = job.getInspection();
        var project = creationRequestBuilder.apply(integration, inspection);
        var result =
                projectCreator.create(project, true, CreationChannelType.LOSS_CONTROL_360.name());
        log.info(
                "Successfully create project {} from lc360 inspection {}.",
                result.getId(),
                inspection.getInspectionID());
    }
}
