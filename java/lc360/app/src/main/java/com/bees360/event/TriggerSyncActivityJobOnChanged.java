package com.bees360.event;

import static com.bees360.job.registry.Lc360ActivitySyncJob.INTEGRATION_LC360;

import com.bees360.activity.Activity;
import com.bees360.activity.Comment;
import com.bees360.activity.Message.ActivityMessage.ActionType;
import com.bees360.activity.Message.ActivityMessage.EntityType;
import com.bees360.event.registry.ActivityChangedEvent;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.Lc360ActivitySyncJob;
import com.bees360.job.util.EventTriggeredJob;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.util.Optional;
import java.util.function.Predicate;

/** 该类的主要功能是在活动变更事件触发时，检查活动是否符合同步条件，并创建可重试的LC360活动同步任务。 */
@Log4j2
public class TriggerSyncActivityJobOnChanged extends EventTriggeredJob<ActivityChangedEvent> {
    private static final String WEB_SOURCE = "WEB";
    private final ExternalIntegrationProvider externalIntegrationProvider;
    private final Integer retryCount;
    private final Duration retryDelay;
    private final Float retryDelayIncreaseFactor;
    private final Predicate<String> enableSyncActivity;

    public TriggerSyncActivityJobOnChanged(
            JobScheduler jobScheduler,
            ExternalIntegrationProvider externalIntegrationProvider,
            Integer retryCount,
            Duration retryDelay,
            Float retryDelayIncreaseFactor,
            Predicate<String> enableSyncActivity) {
        super(jobScheduler);
        this.externalIntegrationProvider = externalIntegrationProvider;
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.retryDelayIncreaseFactor = retryDelayIncreaseFactor;
        this.enableSyncActivity = enableSyncActivity;
        log.info(
                "Created {}(externalIntegrationProvider={}, retryCount={}, retryDelay={},"
                        + " retryIncreaseFactor={}).",
                this,
                this.externalIntegrationProvider,
                this.retryCount,
                this.retryDelay,
                this.retryDelayIncreaseFactor);
    }

    @Override
    protected boolean filter(ActivityChangedEvent activityChangedEvent) {
        return checkIfSupport(activityChangedEvent.getSource());
    }

    @Override
    protected Job convert(ActivityChangedEvent activityChangedEvent) {
        var activity = activityChangedEvent.getSource();
        var job = Job.ofPayload(new Lc360ActivitySyncJob(activity));
        return RetryableJob.of(job, retryCount, retryDelay, retryDelayIncreaseFactor);
    }

    private boolean checkIfSupport(Activity activity) {
        log.debug("Check whether activity support to sync to lc360. {}", activity::toMessage);
        var projectId = String.valueOf(activity.getProjectId());
        return checkIsProjectNeedSync(projectId) && checkIfActivityNeedSync(activity);
    }

    private boolean checkIfActivityNeedSync(Activity activity) {
        var action = activity.getAction();
        var entityType = activity.getEntityType();
        var source = activity.getSource();
        var visibility = activity.getVisibility();
        // 1.action为Contact
        // 2.entity为Comment并且source不为Web但visibility包含web
        // 以上两种情况有一种满足,且comment.content不为空时满足条件
        return (ActionType.CONTACT.name().equals(action)
                        || (EntityType.COMMENT.name().equals(entityType)
                                && !WEB_SOURCE.equals(source)
                                && visibility.contains(WEB_SOURCE)))
                && Optional.ofNullable(activity.getComment())
                        .map(Comment::getContent)
                        .map(StringUtils::isNotBlank)
                        .orElse(false);
    }

    private boolean checkIsProjectNeedSync(String projectId) {
        var inspection =
                Iterables.toStream(externalIntegrationProvider.findAllByProjectId(projectId))
                        .filter(
                                integration ->
                                        StringUtils.equalsIgnoreCase(
                                                INTEGRATION_LC360,
                                                integration.getIntegrationType()))
                        .findFirst();
        return inspection
                .map(i -> i.getReferenceNumber() != null && enableSyncActivity.test(i.getDataset()))
                .orElse(false);
    }
}
