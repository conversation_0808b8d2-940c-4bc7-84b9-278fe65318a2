package com.bees360.lc360.util;

import com.bees360.lc360.Form;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/** This is an util class to get lc360 form data from customer dataset in configuration. */
public class LC360FormDataUtil {

    // TODO remove later when customer dataset used.
    private final List<LC360FormData> oldFormData;

    private final Map<String, List<LC360FormData>> customerFormDataMap;

    public LC360FormDataUtil(
            Map<String, List<LC360FormData>> customerFormDataMap, List<LC360FormData> oldFormData) {
        this.oldFormData = oldFormData;
        this.customerFormDataMap = customerFormDataMap;
    }

    private List<LC360FormData> getFormsByDataset(String dataset) {
        if (customerFormDataMap.isEmpty()) {
            return oldFormData;
        }

        return customerFormDataMap.getOrDefault(dataset, List.of());
    }

    /**
     * Get form template by customer dataset.
     *
     * @param dataset customer dataset id
     * @param forClosed if form is for closed
     * @return set of form template.
     */
    public Set<String> getFormTemplateByDataset(String dataset, Boolean forClosed) {
        return getFormsByDataset(dataset).stream()
                .filter(f -> f.getForClosed() == forClosed)
                .map(LC360FormData::getTemplateId)
                .collect(Collectors.toSet());
    }

    /**
     * Get form internal key for lambda function by customer dataset and form template id.
     *
     * @param dataset customer dataset id
     * @param formTemplateId form template id
     * @return form internal key
     */
    public String getFormInternalKey(String dataset, String formTemplateId) {
        return getFormsByDataset(dataset).stream()
                .filter(f -> StringUtils.equals(f.getTemplateId(), formTemplateId))
                .findFirst()
                .map(LC360FormData::getInternalTemplateKey)
                .orElse(null);
    }

    /**
     * Get form definition by customer dataset and form template id.
     *
     * @param dataset customer dataset id
     * @param formTemplateId form template id
     * @return form dto with name and lookup id
     */
    public Form getFormDefinition(String dataset, String formTemplateId) {
        return getFormsByDataset(dataset).stream()
                .filter(f -> StringUtils.equals(f.getTemplateId(), formTemplateId))
                .findFirst()
                .map(
                        f -> {
                            var form = new Form();
                            form.setFormLookupID(f.getLookupId());
                            form.setName(f.getName());
                            return form;
                        })
                .orElse(null);
    }
}
