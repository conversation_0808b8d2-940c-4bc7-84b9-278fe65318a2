package com.bees360.lc360.config;

import com.bees360.lc360.util.LC360FormData;
import com.bees360.lc360.util.LC360FormDataUtil;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
@EnableConfigurationProperties
public class LC360FormUtilConfig {

    @Data
    @ConfigurationProperties(prefix = "lc360.form")
    @Configuration
    public static class FormProperties {
        private List<LC360FormData> dataset = new ArrayList<>();

        private Boolean enableComplete;

        private Boolean enableMoveForwardTwice = false;
    }

    @Bean
    public LC360FormDataUtil lc360FormDataUtil(
            FormProperties properties, LC360FullCustomerProperties customerProperties) {
        var customerFormMap =
                customerProperties.getDatasets().stream()
                        .collect(
                                Collectors.toMap(
                                        LC360CustomerConfig.Lc360CustomerProperties.Dataset::getId,
                                        LC360FullCustomerProperties.LC360Dataset::getForms));

        return new LC360FormDataUtil(customerFormMap, properties.getDataset());
    }
}
