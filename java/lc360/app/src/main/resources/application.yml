spring:
  profiles:
    active: ${ENV}
    include: actuator
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver
  mysql:
    jdbc:
      url: ************************************************************************************************************************************************************************
      username: root
      password: 123456

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}

lc360:
  process:
    enabled: false
  form:
    enabled: false
    dataset:
      - name: "Exterior"
        template-id: "d8ce7e39-6925-45f4-9d19-d9e5656cec80"
        internal-template-key: ""
      - name: "Interior"
        template-id: "f5413960-9193-4137-9a76-4d6046dd1e20"
        internal-template-key-key: ""
      - name: "Loss Prevention / Liability Concerns"
        template-id: "5db15bf6-ef3d-4a52-ae75-30c2faba9618"
        internal-template-key: ""
  datasets:
    - id: "Vault Insurance"
  image:
    group-type: LC360

http:
  aws-lambda:
    enabled: false

grpc:
  client:
    projectIntegration:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    integrationFormManager:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineService:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    integrationSummaryClient:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    imageManager:
      address: static://bees360-image-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    commentManager:
      address: static://bees360-activity-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    userProvider:
      address: static://bees360-bifrost-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    imageTagManager:
      address: static://bees360-image-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIIManager:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectContactManager:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    genericProjectCreator:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    contractManager:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    customerManager:
      address: static://bees360-project-app-grpc:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
app:
  id: LC360_APP
  lc360:
    user:
      robot-email: <EMAIL>
