package com.bees360.lc360;

import static com.bees360.integration.TestIntegrationSummaryUtil.randomInstance;
import static com.bees360.lc360.LC360TestUtil.generateInspection;
import static com.bees360.lc360.LC360TestUtil.generateIntegration;
import static com.bees360.lc360.LC360TestUtil.generateIntegrationForm;
import static com.bees360.util.Functions.acceptIfNotNull;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.Events;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.image.ImageGroupProvider;
import com.bees360.image.ImageTagProvider;
import com.bees360.integration.IntegrationSummaryProvider;
import com.bees360.job.CollectDataAndUploadLc360FormJobExecutor;
import com.bees360.job.CompleteInspectionOnLc360JobExecutor;
import com.bees360.job.JobScheduler;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.lc360.config.LC360FormConfig;
import com.bees360.lc360.form.LC360IntegrationFormManager;
import com.bees360.lc360.listener.ScheduleUploadLC360FormOnTaskReady;
import com.bees360.lc360.util.FormDataResponse;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.IntegrationForm;
import com.bees360.project.JooqExternalIntegrationManager;
import com.bees360.project.JooqIntegrationFormManager;
import com.bees360.project.Message;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.state.ProjectState;
import com.bees360.project.state.ProjectStateChangeReason;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.SneakyThrows;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@SpringBootTest(classes = LC360FormITest.Config.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class LC360FormITest {

    @Configuration
    @Import({
        JooqConfig.class,
        JooqExternalIntegrationManager.class,
        JooqIntegrationFormManager.class,
        LC360FormConfig.class,
    })
    static class Config {

        @Bean
        InMemoryJobScheduler jobScheduler() {
            return new InMemoryJobScheduler(MoreExecutors.directExecutor());
        }

        @Bean
        InMemoryEventPublisher eventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }

        @MockBean public IntegrationSummaryProvider summaryProvider;

        @MockBean public Lc360VendorApi lc360VendorApi;

        @MockBean public PipelineService pipelineService;

        @MockBean public ImageGroupProvider imageGroupProvider;

        @MockBean public ImageTagProvider imageTagProvider;

        @Bean
        Function<String, Lc360Api> lc360ApiProvider(Lc360VendorApi lc360VendorApi) {
            var lc360Api = Mockito.mock(Lc360Api.class);
            when(lc360Api.getVendorApi()).thenAnswer(e -> lc360VendorApi);
            return s -> lc360Api;
        }
    }

    @MockBean(name = "httpFormDataProcessor")
    private Function<String, FormDataResponse> dataFormProcessor;

    @MockBean(name = "grpcProjectIIManager")
    private ProjectIIManager projectManager;

    @Autowired private EventPublisher eventPublisher;

    @Autowired private CollectDataAndUploadLc360FormJobExecutor executor;

    @Autowired private IntegrationSummaryProvider summaryProvider;

    @Autowired private PipelineService pipelineService;

    @Autowired private ExternalIntegrationManager integrationManager;

    @Autowired private JooqIntegrationFormManager integrationFormManager;

    @Autowired private Lc360VendorApi lc360Api;

    @Autowired private ImageGroupProvider imageGroupProvider;

    @Autowired private CompleteInspectionOnLc360JobExecutor completeInspectionOnLc360JobExecutor;

    @SpyBean public LC360IntegrationFormManager lc360IntegrationFormManager;

    @SpyBean public JobScheduler jobScheduler;

    @Autowired private ScheduleUploadLC360FormOnTaskReady listener;

    private static final String taskKey = "fill_out_form_on_lc360";

    /**
     * Test for scheduling upload lc360 form job and executor executing upload lc360 form. {@link
     * ScheduleUploadLC360FormOnTaskReady} and {@link CompleteInspectionOnLc360JobExecutor}
     */
    @Test
    void testCollectAndUploadFormDataJob() throws IOException {
        var inspection = generateInspection();
        var integration = generateIntegration(inspection.getInspectionID());
        var form = generateIntegrationForm();
        var newFormId = "5db15bf6-ef3d-4a52-ae75-30c2faba9618";
        mockData(integration, inspection, form, newFormId);
        mockProject(integration.getProjectId(), false);

        publishEvent(integration.getProjectId());

        // assert if form is updated
        Mockito.verify(lc360Api, Mockito.timeout(3000).times(1))
                .saveFormData(
                        eq(inspection.getInspectionID()), eq(form.getFormId()), Mockito.any());

        // assert if new form is updated
        Mockito.verify(lc360Api, Mockito.timeout(3000).times(1))
                .saveFormData(eq(inspection.getInspectionID()), eq(newFormId), Mockito.any());

        // assert if move forward inspection is executed
        Mockito.verify(lc360Api, Mockito.timeout(3000).times(1))
                .moveForwardInspection(eq(inspection.getInspectionID()), eq(null), any(), eq(null));
    }

    @SneakyThrows
    @Test
    void ignoreWhenNoLC360Integration() {
        var inspection = generateInspection();
        var integration = generateIntegration(inspection.getInspectionID());
        mockProject(integration.getProjectId(), false);

        var event = new PipelineTaskChanged();
        event.setPipelineId(integration.getProjectId());

        listener.handle(event);

        // assert if projectCloseoutPredicate is not called.
        verify(projectManager, never()).findById(any());
    }

    @SneakyThrows
    @Test
    void ignoreWhenDatasetNotEnableUpdateForm() {
        var inspection = generateInspection();
        var integration = generateIntegration(inspection.getInspectionID(), "TEST");
        var form = generateIntegrationForm();
        var newFormId = "5db15bf6-ef3d-4a52-ae75-30c2faba9618";
        mockData(integration, inspection, form, newFormId);
        mockProject(integration.getProjectId(), false);

        var event = new PipelineTaskChanged();
        event.setPipelineId(integration.getProjectId());

        listener.handle(event);

        // assert if checking project closeout is not called.
        verify(projectManager, never()).findById(any());
    }

    @SneakyThrows
    @Test
    void ignoreWhenNoFormsConfiguredForDataset() {
        var inspection = generateInspection();
        var integration =
                generateIntegration(inspection.getInspectionID(), "NO FORM TEMPLATE COMPANY");
        mockData(integration, inspection, null, null);
        mockProject(integration.getProjectId(), false);

        publishEvent(integration.getProjectId());

        verify(projectManager, timeout(3000).times(1)).findById(eq(integration.getProjectId()));

        // assert if finding forms for integration is not called.
        verify(lc360IntegrationFormManager, never()).findFormsByIntegrationId(any());
    }

    @SneakyThrows
    @Test
    void ignoreWhenNoFormsForProjectToUpdate() {
        var inspection = generateInspection();
        var integration =
                generateIntegration(inspection.getInspectionID(), "NO FORM TEMPLATE COMPANY");
        var form = generateIntegrationForm();
        var newFormId = "5db15bf6-ef3d-4a52-ae75-30c2faba9618";
        mockData(integration, inspection, form, newFormId);
        mockProject(integration.getProjectId(), false);

        publishEvent(integration.getProjectId());

        verify(projectManager, timeout(3000).times(1)).findById(eq(integration.getProjectId()));

        // assert if scheduling job is not called.
        verify(jobScheduler, never()).schedule(any());
    }

    @SneakyThrows
    @Test
    void ignoreScheduleCompleteJobWhenDatasetNotEnable() {
        var inspection = generateInspection();
        var integration =
                generateIntegration(inspection.getInspectionID(), "NO MOVE FORWARD COMPANY");
        mockData(integration, inspection, generateIntegrationForm(), null);
        mockProject(integration.getProjectId(), false);

        publishEvent(integration.getProjectId());

        verify(projectManager, timeout(3000).times(1)).findById(eq(integration.getProjectId()));

        // assert only form job is scheduled
        verify(jobScheduler, times(1)).schedule(any());

        // assert form task is done
        verify(pipelineService, times(1))
                .setTaskStatus(
                        eq(integration.getProjectId()),
                        eq(taskKey),
                        eq(com.bees360.pipeline.Message.PipelineStatus.DONE),
                        eq(null));
    }

    private void publishEvent(String projectId) {
        var event = new PipelineTaskChanged();
        event.setPipelineId(projectId);
        event.setTaskDefKey(taskKey);
        eventPublisher.publish(
                "pipeline_task_changed.status_changed." + taskKey + ".ready", Events.encode(event));
    }

    private void mockData(
            ExternalIntegration integration,
            Inspection inspection,
            IntegrationForm form,
            String newFormId) {
        var builder = integration.toMessage().toBuilder().clearId();
        var id = integrationManager.save(ExternalIntegration.from(builder.build()));

        if (form != null) {
            integrationFormManager.setIntegrationForm(id, List.of(form));
        }

        var summary = randomInstance();
        when(summaryProvider.collectByProjectId(eq(integration.getProjectId())))
                .thenAnswer(e -> summary);
        when(imageGroupProvider.findGroupIdByTypeAndImageId(Mockito.anyString(), Mockito.anyList()))
                .thenAnswer(e -> Map.of());

        when(lc360Api.addInspectionForm(
                        eq(inspection.getInspectionID()), Mockito.any(), Mockito.any()))
                .thenAnswer(e -> newFormId);

        when(dataFormProcessor.apply(any()))
                .thenAnswer(
                        e -> {
                            var response = new FormDataResponse();
                            response.setData(e.getArgument(0));
                            acceptIfNotNull(response::setForms, newFormId, List::of);
                            return response;
                        });
    }

    private void mockProject(String projectId, Boolean isCloseout) {
        var project = Mockito.mock(ProjectII.class);
        var state = Mockito.mock(ProjectState.class);
        var changeReason = Mockito.mock(ProjectStateChangeReason.class);
        var closeReason = isCloseout ? "INSURED_DENIED" : "COMPLETED";
        when(projectManager.findById(eq(projectId))).thenAnswer(e -> project);
        when(project.getCurrentState()).thenAnswer(e -> state);
        when(state.getState())
                .thenAnswer(
                        e -> Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE);
        when(state.getStateChangeReason()).thenAnswer(e -> changeReason);
        when(changeReason.getKey()).thenAnswer(e -> closeReason);
    }
}
