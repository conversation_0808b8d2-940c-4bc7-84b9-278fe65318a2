package com.bees360.lc360.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.job.JobScheduler;
import com.bees360.lc360.config.HandleProjectClosedConfig;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message;
import com.bees360.project.state.AbstractProjectState;
import com.bees360.project.state.ProjectStateChangeReason;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;

@SpringJUnitConfig
@SpringBootTest(
        classes = HandleLc360ProjectClosedListenerTest.Config.class,
        properties = "GRPC_SERVER_PORT=9127")
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class HandleLc360ProjectClosedListenerTest {

    @Configuration
    @ApplicationAutoConfig
    @Import({HandleProjectClosedConfig.class})
    static class Config {}

    @MockBean private ExternalIntegrationProvider integrationProvider;

    @MockBean private JobScheduler jobScheduler;

    @Autowired private HandleLc360ProjectClosedOnProjectStateChanged listener;

    @Test
    void testHandleProjectClosed() {
        var projectId = randomId();
        var closeReason = "DENIED";

        var event = new ProjectStateChangedEvent();
        var projectState =
                randomState(
                        Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE,
                        closeReason);
        event.setProjectId(Long.parseLong(projectId));
        event.setCurrentState(projectState);

        var integration =
                ExternalIntegration.from(
                        Message.IntegrationMessage.newBuilder()
                                .setIntegrationType("LossControl360")
                                .setReferenceNumber(RandomStringUtils.randomAlphabetic(8))
                                .build());
        Mockito.when(integrationProvider.findAllByProjectId(eq(projectId)))
                .thenAnswer(e -> List.of(integration));

        listener.handle(event);

        Mockito.verify(jobScheduler, Mockito.times(2)).schedule(any());
    }

    @Test
    void testIgnoreWhenChangeReasonNoContained() {
        var projectId = randomId();
        var closeReason = "TEST DENIED";

        var event = new ProjectStateChangedEvent();
        var projectState =
                randomState(
                        Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE,
                        closeReason);
        event.setProjectId(Long.parseLong(projectId));
        event.setCurrentState(projectState);

        var integration =
                ExternalIntegration.from(
                        Message.IntegrationMessage.newBuilder()
                                .setIntegrationType("LossControl360")
                                .setReferenceNumber(RandomStringUtils.randomAlphabetic(8))
                                .build());
        Mockito.when(integrationProvider.findAllByProjectId(eq(projectId)))
                .thenAnswer(e -> List.of(integration));

        listener.handle(event);

        Mockito.verify(jobScheduler, Mockito.never()).schedule(any());
    }

    @Test
    void testIgnoreWhenProjectNotRelatedToLC360() {
        var projectId = randomId();
        var closeReason = "DENIED";

        var event = new ProjectStateChangedEvent();
        var projectState =
                randomState(
                        Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE,
                        closeReason);
        event.setProjectId(Long.parseLong(projectId));
        event.setCurrentState(projectState);

        var integration =
                ExternalIntegration.from(
                        Message.IntegrationMessage.newBuilder()
                                .setIntegrationType("XA")
                                .setReferenceNumber(RandomStringUtils.randomAlphabetic(8))
                                .build());
        Mockito.when(integrationProvider.findAllByProjectId(eq(projectId)))
                .thenAnswer(e -> List.of(integration));

        listener.handle(event);

        Mockito.verify(jobScheduler, Mockito.never()).schedule(any());
    }

    @Test
    void testIgnoreWhenStateNotClose() {
        var projectId = randomId();
        var changeReason = "CLIENT REQUESTED";

        var event = new ProjectStateChangedEvent();
        var projectState =
                randomState(
                        Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN,
                        changeReason);
        event.setProjectId(Long.parseLong(projectId));
        event.setCurrentState(projectState);

        var integration =
                ExternalIntegration.from(
                        Message.IntegrationMessage.newBuilder()
                                .setIntegrationType("LossControl360")
                                .setReferenceNumber(RandomStringUtils.randomAlphabetic(8))
                                .build());
        Mockito.when(integrationProvider.findAllByProjectId(eq(projectId)))
                .thenAnswer(e -> List.of(integration));

        listener.handle(event);

        Mockito.verify(jobScheduler, Mockito.never()).schedule(any());
    }

    private String randomId() {
        return "1" + RandomStringUtils.randomNumeric(6);
    }

    private AbstractProjectState randomState(
            Message.ProjectMessage.ProjectState.ProjectStateEnum state, String changeReason) {
        var projectState = Mockito.mock(AbstractProjectState.class);
        var stateReason = Mockito.mock(ProjectStateChangeReason.class);

        Mockito.when(projectState.getState()).thenAnswer(e -> state);
        Mockito.when(projectState.getStateChangeReason()).thenAnswer(e -> stateReason);
        Mockito.when(stateReason.getKey()).thenAnswer(e -> changeReason);

        return projectState;
    }
}
