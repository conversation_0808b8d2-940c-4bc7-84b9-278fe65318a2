package com.bees360.lc360;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.contract.Contract;
import com.bees360.contract.ContractManager;
import com.bees360.customer.Customer;
import com.bees360.customer.CustomerProvider;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.job.CreateProjectFromLC360IntegrationJobExecutor;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.CreateProjectFromLC360InspectionJob;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.lc360.config.LC360CreationConfig;
import com.bees360.lc360.util.LC360InspectionTestUtil;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.GenericProjectCreator;
import com.bees360.project.IntegrationFormManager;
import com.bees360.project.Message;
import com.bees360.project.ProjectII;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.concurrent.Executor;
import java.util.function.Function;

@SpringBootTest(classes = CreateProjectFromLC360JobExecutorITest.Config.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class CreateProjectFromLC360JobExecutorITest {

    @Configuration
    @Import({
        LC360CreationConfig.class,
        InMemoryJobScheduler.class,
        InMemoryEventPublisher.class,
    })
    static class Config {
        @Bean
        public Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @MockBean public Lc360VendorApi lc360VendorApi;

        @MockBean public ExternalIntegrationManager externalIntegrationManager;

        @MockBean public IntegrationFormManager integrationFormManager;

        @Bean
        Function<String, Lc360Api> lc360ApiProvider(Lc360VendorApi lc360VendorApi) {
            var lc360Api = Mockito.mock(Lc360Api.class);
            Mockito.when(lc360Api.getVendorApi()).thenAnswer(e -> lc360VendorApi);
            return s -> lc360Api;
        }
    }

    @Autowired private JobScheduler jobScheduler;

    @Autowired private CreateProjectFromLC360IntegrationJobExecutor executor;

    @MockBean GenericProjectCreator projectCreator;

    @MockBean ContractManager contractManager;

    @MockBean CustomerProvider customerProvider;

    @Test
    void createProject() {
        var dataset = "Davies";
        var divisionLookupId = "FFI_RES";
        var inspection = LC360InspectionTestUtil.getInspection(divisionLookupId);
        var integration = getIntegration(dataset, inspection.getInspectionID());
        var insuredBy = getCustomer("Florida Family");
        var processedBy = getCustomer("IRS");
        Mockito.when(customerProvider.findByKey(insuredBy.getCompanyKey()))
                .thenAnswer(e -> insuredBy);
        Mockito.when(customerProvider.findByKey(processedBy.getCompanyKey()))
                .thenAnswer(e -> processedBy);
        Mockito.when(
                        contractManager.findByCompanyId(
                                eq(insuredBy.getId()), eq(processedBy.getId())))
                .thenAnswer(e -> Mockito.mock(Contract.class));
        Mockito.when(projectCreator.create(any(), eq(true), any()))
                .thenAnswer(e -> Mockito.mock(ProjectII.class));

        var job = new CreateProjectFromLC360InspectionJob();
        job.setIntegration(integration);
        job.setInspection(inspection);
        jobScheduler.schedule(Job.ofPayload(job));

        Mockito.verify(projectCreator, Mockito.times(1)).create(any(), eq(true), any());
    }

    @Test
    void testThrowExpWhenContractNotConfigured() {
        var dataset = "Davies";
        var divisionLookupId = "TWFG_RES";
        var inspection = LC360InspectionTestUtil.getInspection(divisionLookupId);
        var integration = getIntegration(dataset, inspection.getInspectionID());
        var insuredBy = getCustomer("Florida Family");
        var processedBy = getCustomer("IRS");
        Mockito.when(customerProvider.findByKey(insuredBy.getCompanyKey()))
                .thenAnswer(e -> insuredBy);
        Mockito.when(customerProvider.findByKey(processedBy.getCompanyKey()))
                .thenAnswer(e -> processedBy);

        var job = new CreateProjectFromLC360InspectionJob();
        job.setIntegration(integration);
        job.setInspection(inspection);
        jobScheduler.schedule(Job.ofPayload(job));

        Assertions.assertThrows(
                IllegalStateException.class, () -> executor.execute(Job.ofPayload(job)));
    }

    private Customer getCustomer(String customerKey) {
        return Customer.of(
                com.bees360.customer.Message.CustomerMessage.newBuilder()
                        .setKey(customerKey)
                        .setId(RandomStringUtils.randomNumeric(4))
                        .build());
    }

    private ExternalIntegration getIntegration(String dataset, String inspectionId) {
        return ExternalIntegration.from(
                Message.IntegrationMessage.newBuilder()
                        .setIntegrationType("LossControl360")
                        .setReferenceNumber(inspectionId)
                        .setDataset(dataset)
                        .build());
    }
}
