package com.bees360.lc360;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.bees360.api.ApiStatus;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.Events;
import com.bees360.event.registry.JobCompleted;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.MarkInspectionProcessedOnLC360Job;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.lc360.config.LC360ProcessConfig;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.function.Function;

@SpringBootTest(classes = LC360ProcessInspectionITest.Config.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class LC360ProcessInspectionITest {

    @Import({
        LC360ProcessConfig.class,
        InMemoryEventPublisher.class,
        InMemoryJobScheduler.class,
    })
    @Configuration
    static class Config {

        @Bean
        public Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @Bean
        Function<String, Lc360Api> lc360ApiProvider(Lc360VendorApi lc360VendorApi) {
            var lc360Api = Mockito.mock(Lc360Api.class);
            Mockito.when(lc360Api.getVendorApi()).thenAnswer(e -> lc360VendorApi);
            return s -> lc360Api;
        }
    }

    @MockBean private ExternalIntegrationProvider externalIntegrationProvider;

    @MockBean private Lc360VendorApi lc360VendorApi;

    @Autowired private EventPublisher eventPublisher;

    @Autowired private JobScheduler jobScheduler;

    @Autowired Function<String, String> creationJobIdDecoder;

    private static final String LC360_INTEGRATION_TYPE = "LossControl360";

    @Test
    void testProcessInspectionByJobCompleted() {
        var inspectionId = RandomStringUtils.randomAlphabetic(8);
        Mockito.when(
                        externalIntegrationProvider.findByReference(
                                eq(LC360_INTEGRATION_TYPE), eq(inspectionId)))
                .thenAnswer(e -> Mockito.mock(ExternalIntegration.class));

        var event =
                new JobCompleted(
                        "create_project_from_lc360_inspection_job",
                        Instant.now().toEpochMilli() + inspectionId,
                        ApiStatus.OK);
        eventPublisher.publish("job_completed" + "." + event.getName(), Events.encode(event));

        Assertions.assertEquals(inspectionId, creationJobIdDecoder.apply(event.getId()));
        Mockito.verify(lc360VendorApi, Mockito.times(1)).processInspections(any());
    }

    @Test
    void testProcessInspectionByMarkProcessedInspectionJob() {
        var inspectionId = RandomStringUtils.randomAlphabetic(8);
        var job = new MarkInspectionProcessedOnLC360Job();
        job.setDataset("Test");
        job.setInspectionIds(List.of(inspectionId));
        jobScheduler.schedule(Job.ofPayload(job));

        Mockito.verify(lc360VendorApi, Mockito.times(1)).processInspections(any());
    }
}
