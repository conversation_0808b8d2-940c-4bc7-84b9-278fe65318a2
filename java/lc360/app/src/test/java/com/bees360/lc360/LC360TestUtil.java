package com.bees360.lc360;

import com.bees360.project.ExternalIntegration;
import com.bees360.project.IntegrationForm;
import com.bees360.project.Message;

import org.apache.commons.lang3.RandomStringUtils;

import java.util.List;

public class LC360TestUtil {

    public static void setForms(Inspection inspection) {
        var building = new Building();
        var formA = generateForm();
        var formB = generateForm();
        building.setForms(List.of(formA, formB));
        inspection.setBuildings(List.of(building));
    }

    public static Form generateForm() {
        var form = new Form();
        form.setInspectionFormID(RandomStringUtils.randomAlphabetic(12));
        form.setFormID("d8ce7e39-6925-45f4-9d19-d9e5656cec80");
        form.setName("Exterior");
        return form;
    }

    public static Inspection generateInspection() {
        var inspection = new Inspection();
        inspection.setInspectionID(RandomStringUtils.randomAlphabetic(12));
        return inspection;
    }

    public static ExternalIntegration generateIntegration(String inspectionId) {
        return generateIntegration(inspectionId, "Davies");
    }

    public static ExternalIntegration generateIntegrationWithId(
            String inspectionId, String dataset) {
        return ExternalIntegration.from(
                Message.IntegrationMessage.newBuilder()
                        .setId(RandomStringUtils.randomNumeric(8))
                        .setProjectId(1 + RandomStringUtils.randomNumeric(8))
                        .setIntegrationType("LossControl360")
                        .setReferenceNumber(inspectionId)
                        .setDataset(dataset)
                        .build());
    }

    public static ExternalIntegration generateIntegration(String inspectionId, String dataset) {
        return ExternalIntegration.from(
                Message.IntegrationMessage.newBuilder()
                        .setProjectId(1 + RandomStringUtils.randomNumeric(8))
                        .setIntegrationType("LossControl360")
                        .setReferenceNumber(inspectionId)
                        .setDataset(dataset)
                        .build());
    }

    public static IntegrationForm generateIntegrationForm() {
        return IntegrationForm.from(
                Message.IntegrationFormMessage.newBuilder()
                        .setFormId(RandomStringUtils.randomAlphabetic(12))
                        .setTemplateId("d8ce7e39-6925-45f4-9d19-d9e5656cec80")
                        .setName("Exterior")
                        .build());
    }
}
