package com.bees360.lc360;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = LC360CustomerApiITest.Config.class, properties = "GRPC_SERVER_PORT=5265")
@ActiveProfiles("customerApiTest")
public class LC360CustomerApiITest {

    @Import({
        LC360App.class,
    })
    @Configuration
    static class Config {}

    @Test
    void testLoadContext() {}
}
