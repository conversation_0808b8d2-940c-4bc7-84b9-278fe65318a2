package com.bees360.lc360;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.CancelInspectionOnProjectCancel;
import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.lc360.config.CancelInspectionOnProjectCancelConfig;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message;
import com.bees360.project.Message.IntegrationMessage;
import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;
import com.bees360.project.state.AbstractProjectState;
import com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage;

import jakarta.annotation.PostConstruct;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

@DirtiesContext
@SpringBootTest(
        classes = CancelInspectionOnProjectCancelITest.Config.class,
        properties = "app.lc360.inspection.cancelation-if-project-canceled=true")
@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
class CancelInspectionOnProjectCancelITest {

    @Configuration
    @Import({
        JooqConfig.class,
        CancelInspectionOnProjectCancelConfig.class,
    })
    static class Config {
        @MockBean Lc360IntegrationApi lc360IntegrationApi;

        @Bean
        Function<String, Lc360Api> lc360ApiProvider(Lc360Api lc360Api) {
            return s -> lc360Api;
        }

        @Bean
        Lc360Api lc360Api() {
            var lc360Api = Mockito.mock(Lc360Api.class);
            Mockito.when(lc360Api.getIntegrationApi()).thenReturn(lc360IntegrationApi);
            return lc360Api;
        }

        @MockBean CommentManager commentManager;

        @Bean
        ExternalIntegrationProvider externalIntegrationProvider() {
            var externalIntegrationProvider = Mockito.mock(ExternalIntegrationProvider.class);
            Mockito.when(externalIntegrationProvider.findAllByProjectId(anyString()))
                    .thenAnswer(
                            arg -> {
                                String projectId = arg.getArgument(0);
                                var integrationType = "LossControl360";
                                if (Long.parseLong(projectId) < 10000) {
                                    integrationType = "INTEGRATION_XACTANALYSIS";
                                }
                                var integration =
                                        ExternalIntegration.from(
                                                IntegrationMessage.newBuilder()
                                                        .setIntegrationType(integrationType)
                                                        .setProjectId(projectId)
                                                        .setReferenceNumber(projectId)
                                                        .build());
                                return List.of(integration);
                            });
            return externalIntegrationProvider;
        }

        @Bean
        Supplier<String> robotUserIdSupplier() {
            return () -> "B3:10000";
        }

        @PostConstruct
        void assemble() {
            Mockito.when(commentManager.addComment(any(Comment.class))).thenReturn("1");
        }
    }

    private final Lc360IntegrationApi lc360IntegrationApi;

    private final CancelInspectionOnProjectCancel cancelInspectionOnProjectCancel;

    private final CommentManager commentManager;

    CancelInspectionOnProjectCancelITest(
            @Autowired Lc360IntegrationApi lc360IntegrationApi,
            @Autowired CancelInspectionOnProjectCancel cancelInspectionOnProjectCancel,
            @Autowired CommentManager commentManager) {
        this.lc360IntegrationApi = lc360IntegrationApi;
        this.cancelInspectionOnProjectCancel = cancelInspectionOnProjectCancel;
        this.commentManager = commentManager;
    }

    @SneakyThrows
    @Test
    void testReceivedNotClose() {
        var projectId = RandomUtils.nextLong(100000, 1000000);
        var stateNotClose = event(projectId, ProjectStateEnum.PROJECT_OPEN, "", "");
        cancelInspectionOnProjectCancel.handle(stateNotClose);

        Mockito.verify(commentManager, Mockito.times(0)).addComment(any(Comment.class));
    }

    @SneakyThrows
    @Test
    void testReceivedCloseCompleted() {
        var projectId = RandomUtils.nextLong(100000, 1000000);
        var changeReason = "Already Completed";
        var changeReasonKey = "COMPLETED";
        var closeCompleted =
                event(projectId, ProjectStateEnum.PROJECT_CLOSE, changeReasonKey, changeReason);
        cancelInspectionOnProjectCancel.handle(closeCompleted);

        Mockito.verify(commentManager, Mockito.times(0)).addComment(any(Comment.class));
    }

    @SneakyThrows
    @Test
    void testReceivedCloseByCancellation() {
        var projectId = RandomUtils.nextLong(100000, 1000000);
        var changeReason = "any but not completed";
        var closeCompleted =
                event(
                        projectId,
                        ProjectStateEnum.PROJECT_CLOSE,
                        changeReason.toUpperCase(),
                        changeReason);

        Mockito.when(lc360IntegrationApi.cancelInspection(anyString(), anyString(), anyString()))
                .thenReturn(new CancelInspectionResponse().setSuccessful(true));
        cancelInspectionOnProjectCancel.handle(closeCompleted);

        Mockito.verify(commentManager, Mockito.times(1)).addComment(any(Comment.class));
    }

    @SneakyThrows
    @Test
    void testCloseByCancellationWithoutIntegration() {
        var projectId = RandomUtils.nextLong(1, 10000);
        var changeReason = "any but not completed";
        var closeCompleted =
                event(
                        projectId,
                        ProjectStateEnum.PROJECT_CLOSE,
                        changeReason.toUpperCase(),
                        changeReason);

        Mockito.clearInvocations(commentManager);
        cancelInspectionOnProjectCancel.handle(closeCompleted);

        Mockito.verify(commentManager, Mockito.times(0)).addComment(any(Comment.class));
    }

    private ProjectStateChangedEvent event(
            long projectId,
            ProjectStateEnum projectState,
            String changedReasonKey,
            String changedReason) {
        var stateReason =
                ProjectStateChangeReasonMessage.newBuilder()
                        .setKey(changedReasonKey)
                        .setDisplayText(changedReason)
                        .build();
        var currentState =
                AbstractProjectState.from(
                        Message.ProjectMessage.ProjectState.newBuilder()
                                .setStateChangeReason(stateReason)
                                .setState(projectState)
                                .build());
        var event = new ProjectStateChangedEvent();
        event.setProjectId(projectId);
        event.setCurrentState(currentState);
        return event;
    }
}
