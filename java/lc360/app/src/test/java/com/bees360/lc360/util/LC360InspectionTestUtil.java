package com.bees360.lc360.util;

import com.bees360.lc360.Address;
import com.bees360.lc360.Agent;
import com.bees360.lc360.CommercialCoverage;
import com.bees360.lc360.Contact;
import com.bees360.lc360.Coverages;
import com.bees360.lc360.GenericField;
import com.bees360.lc360.Inspection;

import org.apache.commons.lang3.RandomStringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Random;

public class LC360InspectionTestUtil {

    public static Inspection getInspection(String divisionLookupID) {
        var inspection = new Inspection();

        var random = new Random();
        inspection.setInspectionID(RandomStringUtils.randomAlphabetic(8));
        inspection.setInspectionTypeLookupID("Exterior");
        inspection.setDivisionLookupID(divisionLookupID);
        inspection.setInspectionNumber(random.nextInt(10));
        inspection.setDueDate(LocalDate.now());
        inspection.setEffectiveDate(LocalDate.now().plusDays(1));
        inspection.setPolicyNumber(RandomStringUtils.randomAlphabetic(12));
        inspection.setOrderNotes("test notes");

        var address = new Address();
        address.setCity("city");
        address.setCountry("US");
        address.setRegion1("state");
        address.setRegion2("county");
        address.setZipCode("4399");
        address.setStreet1("department");
        address.setStreet2("43th Centre Street");
        inspection.setLocationAddress(address);

        var agent = new Agent();
        agent.setAgentName("agent");
        agent.setEmail("<EMAIL>");
        agent.setPhoneNumber("***********");
        agent.setFaxNumber("123 1231421");
        inspection.setAgent(agent);

        var insured = new Contact();
        insured.setFirstName("policy");
        insured.setLastName("holder");
        insured.setEmail("<EMAIL>");
        insured.setCellPhone("************");
        insured.setWorkPhone("************");
        insured.setHomePhone("************");
        inspection.setPolicyHolder(insured);

        var coverage = new Coverages();
        var commercial = new CommercialCoverage();
        commercial.setEffectiveDate(LocalDate.now());
        coverage.setCommercial(List.of(commercial));
        inspection.setCoverages(coverage);

        var extraField = new GenericField();
        extraField.setKey("Year Of Construction");
        extraField.setGenericFieldValueType(1);
        extraField.setNumber(new BigDecimal(2000));
        var transactionType = new GenericField();
        transactionType.setGenericFieldValueType(0);
        transactionType.setKey("Transaction Type");
        transactionType.setText("New Business");

        inspection.setExtraInfoes(List.of(extraField, transactionType));
        return inspection;
    }

    public static Inspection getInspection() {
        return getInspection(RandomStringUtils.randomAlphabetic(12));
    }
}
