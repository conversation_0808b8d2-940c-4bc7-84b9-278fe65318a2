package com.bees360.lc360.job;

import static org.mockito.ArgumentMatchers.eq;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.job.Job;
import com.bees360.job.SyncNoteToLc360JobExecutor;
import com.bees360.job.registry.SyncNoteToLc360Job;
import com.bees360.lc360.Lc360Api;
import com.bees360.lc360.Lc360VendorApi;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.function.Function;

@SpringJUnitConfig
@SpringBootTest(classes = SyncNoteToLc360JobExecutorTest.Config.class)
public class SyncNoteToLc360JobExecutorTest {

    @Import({
        SyncNoteToLc360JobExecutor.class,
    })
    @Configuration
    @ApplicationAutoConfig
    static class Config {

        @Bean
        Function<String, Lc360Api> lc360ApiProvider(Lc360VendorApi lc360VendorApi) {
            var lc360Api = Mockito.mock(Lc360Api.class);
            Mockito.when(lc360Api.getVendorApi()).thenAnswer(e -> lc360VendorApi);
            return s -> lc360Api;
        }
    }

    @MockBean private Lc360VendorApi lc360VendorApi;

    @Autowired private SyncNoteToLc360JobExecutor executor;

    @SneakyThrows
    @Test
    void testSyncNote() {
        var note = RandomStringUtils.randomAlphabetic(8);
        var inspectionId = RandomStringUtils.randomAlphabetic(8);
        var job = new SyncNoteToLc360Job();
        job.setInspectionId(inspectionId);
        job.setNote(note);
        executor.execute(Job.ofPayload(job));

        Mockito.verify(lc360VendorApi, Mockito.times(1))
                .addInspectionNote(eq(inspectionId), eq(note));
    }
}
