package com.bees360.lc360;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.AddPauseReasonNoteToInspectionOnProjectPause;
import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.lc360.config.AddPauseReasonNoteToInspectionOnProjectPauseConfig;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message;
import com.bees360.project.Message.ProjectMessage.ProjectState;
import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;
import com.bees360.project.state.AbstractProjectState;
import com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.function.Function;

@DirtiesContext
@SpringBootTest(
        classes = AddPauseReasonNoteToInspectionOnProjectPauseITest.Config.class,
        properties = "app.lc360.inspection.note.add-pause-reason-note-on-project-pause=true")
@ApplicationAutoConfig
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class AddPauseReasonNoteToInspectionOnProjectPauseITest {
    @Configuration
    @Import({
        AddPauseReasonNoteToInspectionOnProjectPauseConfig.class,
    })
    static class Config {
        @Bean
        Function<String, Lc360Api> lc360ApiProvider(Lc360VendorApi lc360VendorApi) {
            var lc360Api = Mockito.mock(Lc360Api.class);
            Mockito.when(lc360Api.getVendorApi()).thenAnswer(e -> lc360VendorApi);
            return s -> lc360Api;
        }

        @MockBean Lc360VendorApi lc360VendorApi;

        @Bean
        ExternalIntegrationProvider externalIntegrationProvider() {
            var externalIntegrationProvider = Mockito.mock(ExternalIntegrationProvider.class);
            when(externalIntegrationProvider.findAllByProjectId(anyString()))
                    .thenAnswer(
                            arg -> {
                                String projectId = arg.getArgument(0);
                                var integrationType = "LossControl360";
                                if (Long.parseLong(projectId) < 10000) {
                                    integrationType = "INTEGRATION_XACTANALYSIS";
                                }
                                var integration =
                                        ExternalIntegration.from(
                                                Message.IntegrationMessage.newBuilder()
                                                        .setIntegrationType(integrationType)
                                                        .setProjectId(projectId)
                                                        .setReferenceNumber(projectId)
                                                        .build());
                                return List.of(integration);
                            });
            return externalIntegrationProvider;
        }
    }

    private final AddPauseReasonNoteToInspectionOnProjectPause addPauseReasonNoteToInspection;

    private final Lc360VendorApi lc360VendorApi;

    public AddPauseReasonNoteToInspectionOnProjectPauseITest(
            @Autowired AddPauseReasonNoteToInspectionOnProjectPause addPauseReasonNoteToInspection,
            @Autowired Lc360VendorApi lc360VendorApi) {
        this.addPauseReasonNoteToInspection = addPauseReasonNoteToInspection;
        this.lc360VendorApi = lc360VendorApi;
    }

    @SneakyThrows
    @Test
    void testReceivedPauseChangedState() {
        var projectId = RandomUtils.nextLong(10000, 100000);
        var event = createEvent(projectId, ProjectStateEnum.PROJECT_PAUSE, "any pause reason");

        addPauseReasonNoteToInspection.handle(event);
        verify(lc360VendorApi, times(1)).addInspectionNote(anyString(), anyString());
    }

    @SneakyThrows
    @Test
    void testReceivedNotPauseChangedState() {
        var projectId = RandomUtils.nextLong(10000, 100000);
        var event = createEvent(projectId, ProjectStateEnum.PROJECT_CLOSE, "completed");

        addPauseReasonNoteToInspection.handle(event);
        verify(lc360VendorApi, times(0)).addInspectionNote(anyString(), anyString());
    }

    @SneakyThrows
    @Test
    void testReceivedNotLC360Integration() {
        var projectId = RandomUtils.nextLong(1, 10000);
        var event = createEvent(projectId, ProjectStateEnum.PROJECT_PAUSE, "any pause reason");

        addPauseReasonNoteToInspection.handle(event);
        verify(lc360VendorApi, times(0)).addInspectionNote(anyString(), anyString());
    }

    private ProjectStateChangedEvent createEvent(
            long projectId, ProjectStateEnum projectState, String changedReason) {
        var stateChangedReason =
                ProjectStateChangeReasonMessage.newBuilder().setDisplayText(changedReason).build();
        var curState =
                AbstractProjectState.from(
                        ProjectState.newBuilder()
                                .setState(projectState)
                                .setStateChangeReason(stateChangedReason)
                                .build());

        var event = new ProjectStateChangedEvent();
        event.setProjectId(projectId);
        event.setCurrentState(curState);
        return event;
    }
}
