package com.bees360.lc360;

import static com.bees360.lc360.LC360TestUtil.generateInspection;
import static com.bees360.lc360.LC360TestUtil.generateIntegration;
import static com.bees360.lc360.LC360TestUtil.setForms;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import com.bees360.api.InvalidArgumentException;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.LC360InspectionDetectionEvent;
import com.bees360.job.Job;
import com.bees360.job.JobDispatcher;
import com.bees360.job.JobScheduler;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.lc360.config.LC360CreationConfig;
import com.bees360.lc360.listener.CreateProjectOnLC360InspectionDetectionEvent;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.IntegrationFormManager;
import com.bees360.project.JooqExternalIntegrationManager;
import com.bees360.project.JooqIntegrationFormManager;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.function.Function;

@DirtiesContext
@SpringBootTest
@ApplicationAutoConfig
@TestPropertySource(
        properties = {
            "spring.config.location = classpath:application-test.yml",
            "lc360.app.enable-creation=false"
        })
public class CreateProjectOnLC360InspectionITest {

    @Configuration
    @Import({
        JooqConfig.class,
        JooqExternalIntegrationManager.class,
        JooqIntegrationFormManager.class,
        LC360CreationConfig.class,
    })
    static class Config {

        @Bean
        Function<String, Lc360Api> lc360ApiProvider(Lc360VendorApi lc360VendorApi) {
            var lc360Api = Mockito.mock(Lc360Api.class);
            Mockito.when(lc360Api.getVendorApi()).thenAnswer(e -> lc360VendorApi);
            return s -> {
                if ("TEST".equals(s)) {
                    return lc360Api;
                }

                return null;
            };
        }

        @Bean
        public InMemoryEventPublisher eventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }

        @MockBean public Lc360VendorApi lc360VendorApi;

        @MockBean public JobScheduler jobScheduler;

        @MockBean public JobDispatcher jobDispatcher;
    }

    private final ExternalIntegrationManager externalIntegrationManager;

    private final Lc360VendorApi lc360VendorApi;

    private final JobScheduler jobScheduler;

    private final IntegrationFormManager integrationFormManager;

    @Autowired private EventPublisher eventPublisher;

    @Autowired
    private List<CreateProjectOnLC360InspectionDetectionEvent>
            createProjectOnLC360InspectionListenerList;

    public CreateProjectOnLC360InspectionITest(
            @Autowired ExternalIntegrationManager externalIntegrationManager,
            @Autowired Lc360VendorApi lc360VendorApi,
            @Autowired JobScheduler jobScheduler,
            @Autowired IntegrationFormManager integrationFormManager) {
        this.externalIntegrationManager = externalIntegrationManager;
        this.lc360VendorApi = lc360VendorApi;
        this.jobScheduler = jobScheduler;
        this.integrationFormManager = integrationFormManager;
    }

    @Test
    void testCreateExternalIntegration() throws IOException {
        var inspection = generateInspection();
        var inspectionAndType = new InspectionsResponse();
        inspectionAndType.setInspections(List.of(inspection));
        Mockito.when(lc360VendorApi.getInspections(Mockito.any()))
                .thenAnswer(e -> inspectionAndType);

        var event = new LC360InspectionDetectionEvent();
        event.setTriggerTime(Instant.now());
        eventPublisher.publish(event);

        var integration =
                externalIntegrationManager.findByReference(
                        CreateProjectOnLC360InspectionDetectionEvent.INTEGRATION_TYPE,
                        inspection.getInspectionID());
        Assertions.assertNotNull(integration);
        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(Mockito.any(Job.class));
    }

    @Test
    void testProcessExternalIntegration() throws IOException {
        var inspection = generateInspection();
        var inspectionB = generateInspection();
        var inspectionAndType = new InspectionsResponse();
        inspectionAndType.setInspections(List.of(inspection, inspectionB));
        Mockito.when(lc360VendorApi.getInspections(Mockito.any()))
                .thenAnswer(e -> inspectionAndType);

        var integration = generateIntegration(inspection.getInspectionID());
        externalIntegrationManager.save(integration);

        var event = new LC360InspectionDetectionEvent();
        event.setTriggerTime(Instant.now());
        eventPublisher.publish(event);

        Mockito.verify(jobScheduler, Mockito.times(2)).schedule(Mockito.any(Job.class));
    }

    @Test
    void testSetIntegrationForms() throws IOException {
        var inspection = generateInspection();
        setForms(inspection);
        var inspectionAndType = new InspectionsResponse();
        inspectionAndType.setInspections(List.of(inspection));
        Mockito.when(lc360VendorApi.getInspections(Mockito.any()))
                .thenAnswer(e -> inspectionAndType);

        var event = new LC360InspectionDetectionEvent();
        event.setTriggerTime(Instant.now());
        eventPublisher.publish(event);

        var integration =
                externalIntegrationManager.findByReference(
                        "LossControl360", inspection.getInspectionID());
        Assertions.assertNotNull(integration);

        var forms = integrationFormManager.findFormsByIntegrationId(List.of(integration.getId()));
        Assertions.assertNotNull(forms);
    }

    @Test
    void testUpdateIntegrationForms() throws IOException {
        var inspection = generateInspection();
        setForms(inspection);
        var inspectionAndType = new InspectionsResponse();
        inspectionAndType.setInspections(List.of(inspection));
        Mockito.when(lc360VendorApi.getInspections(Mockito.any()))
                .thenAnswer(e -> inspectionAndType);

        var event = new LC360InspectionDetectionEvent();
        event.setTriggerTime(Instant.now());
        eventPublisher.publish(event);

        var integration =
                externalIntegrationManager.findByReference(
                        "LossControl360", inspection.getInspectionID());
        Assertions.assertNotNull(integration);

        var forms = integrationFormManager.findFormsByIntegrationId(List.of(integration.getId()));
        Assertions.assertNotNull(forms);

        inspection.setBuildings(List.of());
        externalIntegrationManager.setProjectId(
                integration.getId(), RandomStringUtils.randomNumeric(8));
        eventPublisher.publish(event);

        forms = integrationFormManager.findFormsByIntegrationId(List.of(integration.getId()));
        Assertions.assertEquals(0, Iterables.toList(forms).size());
    }

    @Test
    void fetchInspectionAndIgnoreExceptionTest() {
        var event = new LC360InspectionDetectionEvent();
        event.setTriggerTime(Instant.now());
        Mockito.doThrow(new InvalidArgumentException("mock invalid argument."))
                .when(lc360VendorApi)
                .getInspections(Mockito.anyList());
        for (var listener : createProjectOnLC360InspectionListenerList) {
            assertDoesNotThrow(() -> listener.handle(event));
        }
    }
}
