package com.bees360.lc360;

import static com.bees360.lc360.LC360TestUtil.generateInspection;
import static com.bees360.lc360.LC360TestUtil.generateIntegration;
import static com.bees360.lc360.LC360TestUtil.generateIntegrationWithId;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.api.InternalException;
import com.bees360.event.EventPublisher;
import com.bees360.event.HandleProjectCloseoutOnProjectStateChanged;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.job.Job;
import com.bees360.job.TriggerFillOutUTCFormJobExecutor;
import com.bees360.job.registry.TriggerFillOutUTCFormJob;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.lc360.config.HandleProjectCloseoutConfig;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.IntegrationForm;
import com.bees360.project.IntegrationFormProvider;
import com.bees360.project.Message;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.RandomProjectUtil;
import com.bees360.project.state.AbstractProjectState;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.SneakyThrows;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.function.Function;

@SpringBootTest(classes = HandleProjectCloseoutITest.Config.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class HandleProjectCloseoutITest {

    @Configuration
    @Import({
        HandleProjectCloseoutConfig.class,
        InMemoryEventPublisher.class,
        InMemoryJobScheduler.class,
    })
    static class Config {

        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @MockBean private Lc360VendorApi lc360VendorApi;

        @Bean
        Function<String, Lc360Api> lc360ApiProvider() {
            var lc360Api = Mockito.mock(Lc360Api.class);
            Mockito.when(lc360Api.getVendorApi()).thenAnswer(e -> lc360VendorApi);
            return dataset -> lc360Api;
        }
    }

    @MockBean private ExternalIntegrationProvider integrationProvider;

    @Autowired private Lc360VendorApi lc360VendorApi;

    @MockBean private ProjectIIManager projectManager;

    @MockBean private PipelineService pipelineService;

    @MockBean private IntegrationFormProvider integrationFormProvider;

    @Autowired private EventPublisher eventPublisher;

    @Autowired public HandleProjectCloseoutOnProjectStateChanged listener;

    @SpyBean public TriggerFillOutUTCFormJobExecutor executor;

    String formId = "closeout-form-template-key";

    /**
     * testing for {@link HandleProjectCloseoutOnProjectStateChanged} and {@link
     * TriggerFillOutUTCFormJobExecutor}
     */
    @Test
    void testChangeInspectionTypeAndScheduleFormTaskReady() {
        var inspection = generateInspection();
        var integration = generateIntegration(inspection.getInspectionID());
        mockData(integration, formId, true);

        var event = new ProjectStateChangedEvent();
        event.setProjectId(Long.parseLong(integration.getProjectId()));
        event.setCurrentState(
                AbstractProjectState.from(
                        Message.ProjectMessage.ProjectState.newBuilder()
                                .setState(
                                        Message.ProjectMessage.ProjectState.ProjectStateEnum
                                                .PROJECT_CLOSE)
                                .build()));
        eventPublisher.publish(event);

        // assert if change inspection type executed
        verify(lc360VendorApi, timeout(3000).times(1))
                .changeInspectionType(
                        eq(inspection.getInspectionID()), eq("FFI_RES_DRONE_CLOSEOUT"));

        // assert if set task ready for fill out form
        verify(pipelineService, timeout(3000).times(1))
                .setTaskStatus(
                        eq(integration.getProjectId()),
                        eq("fill_out_form_on_lc360"),
                        eq(com.bees360.pipeline.Message.PipelineStatus.READY),
                        eq("Fill out closeout form needed."));
    }

    @SneakyThrows
    @Test
    void testIgnoreWhenProjectNotLC360Integration() {
        var inspection = generateInspection();
        var integration =
                ExternalIntegration.from(
                        Message.IntegrationMessage.newBuilder()
                                .setProjectId("1" + RandomStringUtils.randomNumeric(4))
                                .setReferenceNumber(inspection.getInspectionID())
                                .setIntegrationType("RiskControl")
                                .setDataset("Davies")
                                .build());
        mockData(integration, formId, true);

        var event = new ProjectStateChangedEvent();
        event.setProjectId(Long.parseLong(integration.getProjectId()));
        event.setCurrentState(
                AbstractProjectState.from(
                        Message.ProjectMessage.ProjectState.newBuilder()
                                .setState(
                                        Message.ProjectMessage.ProjectState.ProjectStateEnum
                                                .PROJECT_CLOSE)
                                .build()));
        listener.handle(event);

        verify(lc360VendorApi, Mockito.never()).changeInspectionType(any(), any());
    }

    @SneakyThrows
    @Test
    void testIgnoreWhenCustomerNotEnableHandleProjectCloseout() {
        var inspection = generateInspection();
        var integration = generateIntegration(inspection.getInspectionID(), "TEST");
        mockData(integration, formId, true);

        var event = new ProjectStateChangedEvent();
        event.setProjectId(Long.parseLong(integration.getProjectId()));
        event.setCurrentState(
                AbstractProjectState.from(
                        Message.ProjectMessage.ProjectState.newBuilder()
                                .setState(
                                        Message.ProjectMessage.ProjectState.ProjectStateEnum
                                                .PROJECT_CLOSE)
                                .build()));
        listener.handle(event);

        verify(lc360VendorApi, never()).changeInspectionType(any(), any());
    }

    @SneakyThrows
    @Test
    void testRetryWhenUTCFormNotReady() {
        var inspection = generateInspection();
        var integration = generateIntegrationWithId(inspection.getInspectionID(), "Davies");
        mockData(integration, "RandomForm", true);

        var job =
                new TriggerFillOutUTCFormJob(
                        integration.getDataset(), integration.getId(), integration.getProjectId());
        Assertions.assertThrows(
                InternalException.class, () -> executor.execute(Job.ofPayload(job)));

        verify(integrationFormProvider).findFormsByIntegrationId(any());
        verify(pipelineService, never()).setTaskStatus(any(), any(), any(), any());
    }

    private void mockData(ExternalIntegration integration, String formId, boolean isCanceled) {
        when(integrationProvider.findAllByProjectId(eq(integration.getProjectId())))
                .thenAnswer(e -> List.of(integration));
        var form =
                IntegrationForm.from(
                        Message.IntegrationFormMessage.newBuilder().setTemplateId(formId).build());
        when(integrationFormProvider.findFormsByIntegrationId(any()))
                .thenAnswer(e -> List.of(form));
        var project = Mockito.mock(ProjectII.class);
        when(projectManager.findById(eq(integration.getProjectId()))).thenAnswer(e -> project);
        var contract = RandomProjectUtil.randomContract(null, "Florida Family");
        when(project.getContract()).thenAnswer(e -> contract);
        when(project.isCanceled()).thenAnswer(e -> isCanceled);
        when(project.getLatestStatus()).thenAnswer(e -> Message.ProjectStatus.RETURNED_TO_CLIENT);
    }
}
