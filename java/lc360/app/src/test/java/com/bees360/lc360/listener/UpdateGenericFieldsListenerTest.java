package com.bees360.lc360.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.ProjectStatusChanged;
import com.bees360.lc360.Lc360Api;
import com.bees360.lc360.Lc360IntegrationV4Api;
import com.bees360.lc360.config.UpdateInspectionGenericFieldsConfig;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message;
import com.google.common.util.concurrent.MoreExecutors;

import jakarta.annotation.PostConstruct;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Function;

@TestPropertySource(
        properties = {
            "spring.config.location = classpath:application-test.yml",
            "lc360.customer-enabled=true"
        })
@SpringBootTest(classes = UpdateGenericFieldsListenerTest.Config.class)
public class UpdateGenericFieldsListenerTest {

    @Configuration
    @Import({UpdateInspectionGenericFieldsConfig.class})
    static class Config {

        @Bean
        public InMemoryEventPublisher eventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }
    }

    @MockBean private Function<String, Lc360Api> lc360ApiProvider;

    @MockBean private ExternalIntegrationProvider integrationProvider;

    @MockBean private Lc360IntegrationV4Api integrationV4Api;

    @Autowired private EventPublisher eventPublisher;

    @PostConstruct
    void mockApi() {
        var lc360Api = Mockito.mock(Lc360Api.class);
        when(lc360ApiProvider.apply(any())).thenAnswer(e -> lc360Api);
        when(lc360Api.getIntegrationV4Api()).thenAnswer(e -> integrationV4Api);
    }

    @Test
    void testUpdateGenericFieldsOnProjectCreated() {
        var projectId = "1" + RandomStringUtils.randomNumeric(4);
        var inspectionId = mockIntegration(projectId, "Davies");

        var event = new ProjectStatusChanged();
        event.setStatus(Message.ProjectStatus.PROJECT_CREATED_VALUE);
        event.setProjectId(projectId);

        eventPublisher.publish(event);

        verify(integrationV4Api, Mockito.times(1))
                .updateInspectionGenericFields(
                        eq(inspectionId),
                        argThat(
                                fields ->
                                        fields.size() == 2
                                                && fields.get(0)
                                                        .getKey()
                                                        .equals("Bees360 Project Number")
                                                && fields.get(0)
                                                        .getNumber()
                                                        .equals(new BigDecimal(projectId))
                                                && fields.get(1).getKey().equals("Bees360 Status")
                                                && fields.get(1)
                                                        .getText()
                                                        .equals("Project Created")));
    }

    @Test
    void testUpdateGenericFieldsOnOtherStatusChanged() {
        var projectId = "1" + RandomStringUtils.randomNumeric(4);
        var inspectionId = mockIntegration(projectId, "Davies");

        var event = new ProjectStatusChanged();
        event.setStatus(Message.ProjectStatus.CUSTOMER_CONTACTED_VALUE);
        event.setProjectId(projectId);

        eventPublisher.publish(event);

        verify(integrationV4Api, Mockito.times(1))
                .updateInspectionGenericFields(
                        eq(inspectionId),
                        argThat(
                                fields ->
                                        fields.size() == 1
                                                && fields.get(0).getKey().equals("Bees360 Status")
                                                && fields.get(0)
                                                        .getText()
                                                        .equals("Customer Contacted")));
    }

    @Test
    void testIgnoreWhenCustomerDisable() {
        var projectId = "1" + RandomStringUtils.randomNumeric(4);
        mockIntegration(projectId, "TEST");

        var event = new ProjectStatusChanged();
        event.setStatus(Message.ProjectStatus.CUSTOMER_CONTACTED_VALUE);
        event.setProjectId(projectId);

        eventPublisher.publish(event);

        verify(integrationV4Api, Mockito.never()).updateInspectionGenericFields(any(), any());
    }

    private String mockIntegration(String projectId, String dataset) {
        var integration =
                ExternalIntegration.from(
                        Message.IntegrationMessage.newBuilder()
                                .setDataset(dataset)
                                .setIntegrationType("LossControl360")
                                .setProjectId(projectId)
                                .setReferenceNumber(RandomStringUtils.randomAlphabetic(12))
                                .build());
        when(integrationProvider.findAllByProjectId(eq(projectId)))
                .thenAnswer(e -> List.of(integration));
        return integration.getReferenceNumber();
    }
}
