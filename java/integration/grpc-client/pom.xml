<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bees360</groupId>
    <artifactId>bees360-integration</artifactId>
    <version>${revision}${changelist}</version>
  </parent>

  <artifactId>bees360-integration-grpc-client</artifactId>
  <packaging>jar</packaging>

  <name>bees360-integration-grpc-client</name>

  <dependencies>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-grpc-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-integration-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-integration-api</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-report-api</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-integration-jooq</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-integration-grpc</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
