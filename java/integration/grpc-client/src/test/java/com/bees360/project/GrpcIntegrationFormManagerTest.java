package com.bees360.project;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.project.config.GrpcExternalIntegrationManagerConfig;
import com.bees360.project.config.GrpcIntegrationFormManagerConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@SpringBootTest(properties = "GRPC_SERVER_PORT=9236")
public class GrpcIntegrationFormManagerTest extends TestIntegrationFormManager {

    @ApplicationAutoConfig
    @Import({
        GrpcExternalIntegrationManagerConfig.class,
        GrpcIntegrationFormManagerConfig.class,
        GrpcExternalIntegrationManagerService.class,
        JooqConfig.class,
        JooqIntegrationFormManager.class,
    })
    @Configuration
    static class Config {

        @Bean
        IntegrationFormManager grpcServiceIntegrationFormManager(
                JooqIntegrationFormManager jooqIntegrationFormManager) {
            return jooqIntegrationFormManager;
        }
    }

    public GrpcIntegrationFormManagerTest(
            @Autowired IntegrationFormManager grpcIntegrationFormManager) {
        super(grpcIntegrationFormManager);
    }

    @Test
    void testSetFormsAndFind() {
        super.setFormsAndFind();
    }

    @Test
    void addFormAndGet() {
        super.addFormAndGet();
    }
}
