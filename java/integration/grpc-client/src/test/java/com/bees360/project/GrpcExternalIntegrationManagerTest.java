package com.bees360.project;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.project.config.GrpcExternalIntegrationManagerConfig;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@SpringBootTest(properties = "GRPC_SERVER_PORT=9891")
public class GrpcExternalIntegrationManagerTest extends TestExternalIntegrationManager {

    @ApplicationAutoConfig
    @Import({
        GrpcExternalIntegrationManagerService.class,
        GrpcExternalIntegrationManagerConfig.class,
        JooqConfig.class,
        JooqExternalIntegrationManager.class,
        JooqIntegrationFormManager.class,
    })
    @Configuration
    static class Config {

        @Bean
        ExternalIntegrationManager gprcServiceExternalIntegrationManager(
                JooqExternalIntegrationManager jooqExternalIntegrationManager) {
            return jooqExternalIntegrationManager;
        }
    }

    public GrpcExternalIntegrationManagerTest(
            @Autowired GrpcExternalIntegrationManager grpcExternalIntegrationManager) {
        super(grpcExternalIntegrationManager);
    }

    @Test
    void testNotFound() {
        super.notFound();
    }

    @Test
    void testCreateThenGet() {
        super.createThenGet();
    }

    @Test
    void testSaveThenGet() {
        super.saveThenGet();
    }

    @Test
    void testUpdateProjectId() {
        super.updateProjectId();
    }

    @Test
    void testSaveThenGetAndDeleteThenAssertNotFound() {
        super.saveThenGetAndDeleteThenAssertNotFound();
    }

    @Test
    void testSaveAndDeleteThenUpdateAndDeleteNotExistsIntegration() {
        super.saveAndDeleteThenUpdateAndDeleteNotExistsIntegration();
    }

    @Test
    void testSaveThenGetWithSubReferenceNumber() {
        super.saveThenGetBySubReferenceNumber();
    }

    @Test
    void testGetBySubReferenceNumberUsingIllegalArguments() {
        super.getBySubReferenceNumberUsingIllegalArguments();
    }

    @Test
    void testGetBySubReferenceNumberNotFound() {
        super.getBySubReferenceNumberNotFound();
    }
}
