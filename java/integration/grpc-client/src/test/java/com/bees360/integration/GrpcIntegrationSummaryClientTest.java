package com.bees360.integration;

import static com.bees360.integration.TestIntegrationSummaryUtil.assertEquals;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.integration.config.GrpcIntegrationSummaryClientConfig;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@SpringBootTest(properties = "GRPC_SERVER_PORT=9522")
public class GrpcIntegrationSummaryClientTest {

    @ApplicationAutoConfig
    @Configuration
    @Import({
        GrpcIntegrationService.class,
        GrpcIntegrationSummaryClientConfig.class,
    })
    static class Config {
        @MockBean(name = "grpcIntegrationSummaryProvider")
        public IntegrationSummaryProvider integrationSummaryProvider;
    }

    @Autowired private IntegrationSummaryProvider grpcIntegrationSummaryProvider;

    @Autowired private GrpcIntegrationSummaryClient integrationSummaryClient;

    @Test
    void testCollectForm() {
        var projectId = RandomStringUtils.randomNumeric(8);
        var expected = TestIntegrationSummaryUtil.randomInstance();
        Mockito.when(grpcIntegrationSummaryProvider.collectByProjectId(Mockito.any()))
                .thenAnswer(e -> expected);

        var actual = integrationSummaryClient.collectByProjectId(projectId);
        assertEquals(expected, actual);
    }
}
