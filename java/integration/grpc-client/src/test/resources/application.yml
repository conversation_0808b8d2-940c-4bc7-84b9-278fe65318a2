spring:
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver

grpc:
  server:
    port: ${GRPC_SERVER_PORT:9898}
  client:
    projectIntegration:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    integrationFormManager:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    integrationSummaryClient:
      address: static://127.0.0.1:${GRPC_SERVER_PORT:9898}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
