package com.bees360.project.config;

import com.bees360.grpc.GrpcApi;
import com.bees360.project.GrpcIntegrationFormManager;
import com.bees360.project.ProjectIntegrationServiceGrpc;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcIntegrationFormManagerConfig {

    @GrpcClient("integrationFormManager")
    private ProjectIntegrationServiceGrpc.ProjectIntegrationServiceBlockingStub blockingStub;

    @GrpcClient("integrationFormManager")
    private ProjectIntegrationServiceGrpc.ProjectIntegrationServiceStub stub;

    @Bean
    public GrpcIntegrationFormManager grpcIntegrationFormManager() {
        return new GrpcIntegrationFormManager(GrpcApi.of(blockingStub), stub);
    }
}
