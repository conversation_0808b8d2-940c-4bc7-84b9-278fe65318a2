package com.bees360.project.config;

import com.bees360.grpc.GrpcApi;
import com.bees360.project.GrpcExternalIntegrationManager;
import com.bees360.project.ProjectIntegrationServiceGrpc.ProjectIntegrationServiceBlockingStub;
import com.bees360.project.ProjectIntegrationServiceGrpc.ProjectIntegrationServiceStub;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcExternalIntegrationManagerConfig {

    @GrpcClient("projectIntegration")
    private ProjectIntegrationServiceBlockingStub blockingStub;

    @GrpcClient("projectIntegration")
    private ProjectIntegrationServiceStub stub;

    @Bean
    public GrpcExternalIntegrationManager grpcExternalIntegrationManager() {
        return new GrpcExternalIntegrationManager(GrpcApi.of(blockingStub), stub);
    }
}
