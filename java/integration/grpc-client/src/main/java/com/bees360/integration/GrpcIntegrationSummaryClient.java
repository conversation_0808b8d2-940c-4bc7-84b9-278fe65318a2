package com.bees360.integration;

import com.bees360.grpc.GrpcApi;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class GrpcIntegrationSummaryClient implements IntegrationSummaryProvider {

    private final GrpcApi<IntegrationServiceGrpc.IntegrationServiceBlockingStub> api;

    public GrpcIntegrationSummaryClient(
            GrpcApi<IntegrationServiceGrpc.IntegrationServiceBlockingStub> api) {
        this.api = api;
        log.info("Created {}(api={}).", this, this.api);
    }

    @Override
    public IntegrationSummary collectByProjectId(String projectId) {
        var result = api.apply(e -> e.collectSummaryByProjectId(StringValue.of(projectId)));
        return IntegrationSummary.from(result);
    }
}
