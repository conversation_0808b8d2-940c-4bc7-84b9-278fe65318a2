package com.bees360.project;

import com.bees360.grpc.GrpcApi;
import com.bees360.grpc.ResponseListFutureStreamObserver;
import com.bees360.util.ListenableFutures;
import com.google.common.base.Preconditions;
import com.google.common.collect.Iterables;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

import java.util.List;

@Log4j2
public class GrpcIntegrationFormManager implements IntegrationFormManager {

    private final GrpcApi<ProjectIntegrationServiceGrpc.ProjectIntegrationServiceBlockingStub> api;
    private final ProjectIntegrationServiceGrpc.ProjectIntegrationServiceStub asyncApi;

    public GrpcIntegrationFormManager(
            GrpcApi<ProjectIntegrationServiceGrpc.ProjectIntegrationServiceBlockingStub> api,
            ProjectIntegrationServiceGrpc.ProjectIntegrationServiceStub asyncApi) {
        this.api = api;
        this.asyncApi = asyncApi;
        log.info("Created {}(api={},asyncApi={})", this, this.api, this.asyncApi);
    }

    @Override
    public void setIntegrationForm(String id, Iterable<? extends IntegrationForm> forms) {
        var request =
                Message.ProjectIntegrationFormRequest.newBuilder()
                        .setIntegrationId(id)
                        .addAllForm(Iterables.transform(forms, IntegrationForm::toMessage))
                        .build();
        api.apply(api -> api.setIntegrationForm(request));
    }

    @Override
    public String addIntegrationForm(IntegrationForm form) {
        Preconditions.checkNotNull(form, "Integration form to add can not be null.");
        return api.apply(api -> api.addIntegrationForm(form.toMessage())).getValue();
    }

    @Override
    public Iterable<? extends IntegrationForm> findFormsByIntegrationId(Iterable<String> ids) {
        if (com.google.common.collect.Iterables.isEmpty(ids)) {
            return List.of();
        }
        var response = new ResponseListFutureStreamObserver<>(IntegrationForm::from);
        var request = asyncApi.findFormsByIntegrationId(response);

        ids.forEach(id -> request.onNext(StringValue.of(id)));
        request.onCompleted();
        return ListenableFutures.getUnchecked(response);
    }
}
