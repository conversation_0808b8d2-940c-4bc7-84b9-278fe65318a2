package com.bees360.integration.config;

import com.bees360.grpc.GrpcApi;
import com.bees360.integration.GrpcIntegrationSummaryClient;
import com.bees360.integration.IntegrationServiceGrpc;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcIntegrationSummaryClientConfig {

    @GrpcClient("integrationSummaryClient")
    private IntegrationServiceGrpc.IntegrationServiceBlockingStub blockingStub;

    @Bean
    public GrpcIntegrationSummaryClient grpcIntegrationSummaryClient() {
        return new GrpcIntegrationSummaryClient(GrpcApi.of(blockingStub));
    }
}
