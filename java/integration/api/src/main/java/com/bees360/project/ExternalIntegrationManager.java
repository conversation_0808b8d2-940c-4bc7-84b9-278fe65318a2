package com.bees360.project;

import jakarta.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2021/3/24
 */
public interface ExternalIntegrationManager extends ExternalIntegrationProvider {

    String save(ExternalIntegration integration);

    /**
     * @return the id of the integration, null if no new integration created or integration exists
     */
    @Nullable
    default String create(String dataset, String integrationType, String referenceNumber) {
        return create(dataset, integrationType, referenceNumber, "");
    }

    @Nullable
    String create(
            String dataset,
            String integrationType,
            String referenceNumber,
            String subReferenceNumber);

    void setProjectId(String id, String projectId);

    /**
     * 根据projectId和type删除integration,删除操作执行成功为true,否则为false <br>
     * e.g. 删除不存在的integration返回false,因为操作了不存在的数据 <br>
     *
     * @param projectId projectId
     * @param integrationType 对应数据库中project_integration_type枚举类型 e.g. RiskControl
     * @return boolean true/false说明是否为有效操作
     */
    boolean deleteByProjectIdAndType(String projectId, String integrationType);
}
