package com.bees360.todo.config;

import com.bees360.jooq.config.JooqConfig;
import com.bees360.jooq.persistent.todo.tables.TodoNotification;
import com.bees360.todo.JooqNotificationTodoRepository;
import com.bees360.todo.JooqPipelineTodoRepository;
import com.bees360.todo.JooqTodoManager;
import com.bees360.todo.JooqTodoRepository;
import com.bees360.todo.Message.Notification;
import com.bees360.todo.Message.PipelineTask;
import com.bees360.todo.Message.TodoMessage;
import com.bees360.todo.Message.TodoQueryMessage.Type;

import org.jooq.DSLContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;

@Configuration
@Import({JooqConfig.class})
public class JooqTodoRepositoryConfig {
    @Bean
    JooqTodoRepository commonPipelineTodoRepository(DSLContext dsl) {
        return new JooqPipelineTodoRepository(
                dsl, PipelineTask.getDefaultInstance(), Type.PIPELINE);
    }

    @Bean
    JooqNotificationTodoRepository notificationTodoRepository(DSLContext dsl) {
        return new JooqNotificationTodoRepository(
                dsl,
                TodoNotification.TODO_NOTIFICATION,
                TodoMessage.getDescriptor().findFieldByName("notification"),
                Notification.getDefaultInstance(),
                Type.NOTIFICATION);
    }

    @Bean
    JooqTodoManager jooqTodoManager(DSLContext dsl, List<JooqTodoRepository> todoRepositoryList) {
        return new JooqTodoManager(dsl, todoRepositoryList);
    }
}
