package com.bees360.todo;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.pipeline.todo.PipelineTodoRepository;
import com.bees360.util.DateTimes;
import com.google.common.base.Preconditions;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.context.annotation.Import;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@GrpcService
@Import({ExceptionTranslateInterceptor.class})
@Log4j2
public class GrpcTodoService extends TodoServiceGrpc.TodoServiceImplBase {
    private final TodoManager todoManager;
    public final PipelineTodoRepository pipelineTodoRepository;

    public GrpcTodoService(
            TodoManager grpcTodoManager, PipelineTodoRepository pipelineTodoRepository) {
        this.todoManager = grpcTodoManager;
        this.pipelineTodoRepository = pipelineTodoRepository;
        log.info("Created {}(PipelineTaskProvider {})", this, todoManager);
    }

    @Override
    public void findTodoByOwnerId(
            StringValue request, StreamObserver<Message.TodoMessage> responseObserver) {
        var response = todoManager.findTodoByUserId(request.getValue());
        response.forEach(task -> responseObserver.onNext(task.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void findTodoByQuery(
            Message.TodoQueryMessage request,
            StreamObserver<Message.TodoMessage> responseObserver) {
        var response = todoManager.findTodoByQuery(TodoQuery.from(request));
        response.forEach(task -> responseObserver.onNext(task.toMessage()));
        responseObserver.onCompleted();
    }

    @Override
    public void markTodoCompleted(
            Message.MarkTodoRequest request, StreamObserver<Empty> responseObserver) {
        todoManager.markTodoCompleted(request.getTodoIdList(), request.getUpdatedBy());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public StreamObserver<StringValue> markTodoRead(StreamObserver<Empty> responseObserver) {
        return new StreamObserver<>() {
            final List<String> ids = new ArrayList<>();

            @Override
            public void onNext(StringValue id) {
                Preconditions.checkNotNull(id);
                ids.add(id.getValue());
            }

            @Override
            public void onError(Throwable t) {
                log.warn("A error occurs when mark todo read.", t);
            }

            @Override
            public void onCompleted() {
                todoManager.markTodoRead(ids.toArray(String[]::new));
                responseObserver.onNext(Empty.getDefaultInstance());
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public StreamObserver<StringValue> markTodoPinned(StreamObserver<Empty> responseObserver) {
        return new StreamObserver<>() {
            final List<String> ids = new ArrayList<>();

            @Override
            public void onNext(StringValue id) {
                Preconditions.checkNotNull(id);
                ids.add(id.getValue());
            }

            @Override
            public void onError(Throwable t) {}

            @Override
            public void onCompleted() {
                todoManager.markTodoPinned(ids.toArray(String[]::new));
                responseObserver.onNext(Empty.getDefaultInstance());
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public StreamObserver<StringValue> markTodoUnpinned(StreamObserver<Empty> responseObserver) {
        return new StreamObserver<>() {
            final List<String> ids = new ArrayList<>();

            @Override
            public void onNext(StringValue id) {
                Preconditions.checkNotNull(id);
                ids.add(id.getValue());
            }

            @Override
            public void onError(Throwable t) {}

            @Override
            public void onCompleted() {
                todoManager.markTodoUnpinned(ids.toArray(String[]::new));
                responseObserver.onNext(Empty.getDefaultInstance());
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public StreamObserver<Message.ChangePipelineTodoOwnerRequest> batchChangePipelineTodoOwner(
            StreamObserver<Empty> responseObserver) {
        return new StreamObserver<>() {
            final Map<String, String> map = new HashMap<>();

            @Override
            public void onNext(Message.ChangePipelineTodoOwnerRequest value) {
                map.put(value.getTodoId(), value.getOwnerId());
            }

            @Override
            public void onError(Throwable t) {
                log.error("A error occurs when batch update pipeline todo owner.", t);
            }

            @Override
            public void onCompleted() {
                pipelineTodoRepository.batchChangeTodoOwner(map);
                responseObserver.onNext(Empty.getDefaultInstance());
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public StreamObserver<Message.SetTodoFollowupRequest> markTodoFollowup(
            StreamObserver<Empty> responseObserver) {
        return new StreamObserver<>() {
            final Map<String, Instant> map = new HashMap<>();

            @Override
            public void onNext(Message.SetTodoFollowupRequest value) {
                map.put(value.getTodoId(), DateTimes.toInstant(value.getFollowupDate()));
            }

            @Override
            public void onError(Throwable throwable) {
                log.warn("A error occurs when mark todo followup.", throwable);
            }

            @Override
            public void onCompleted() {
                todoManager.markTodoFollowup(map);
                responseObserver.onNext(Empty.getDefaultInstance());
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public StreamObserver<StringValue> cancelTodoFollowup(StreamObserver<Empty> responseObserver) {
        return new StreamObserver<>() {
            final List<String> ids = new ArrayList<>();

            @Override
            public void onNext(StringValue value) {
                ids.add(value.getValue());
            }

            @Override
            public void onError(Throwable throwable) {
                log.warn("A error occurs when cancel todo followup.", throwable);
            }

            @Override
            public void onCompleted() {
                todoManager.cancelTodoFollowup(ids);
                responseObserver.onNext(Empty.getDefaultInstance());
                responseObserver.onCompleted();
            }
        };
    }
}
