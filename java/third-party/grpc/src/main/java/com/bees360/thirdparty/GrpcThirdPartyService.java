package com.bees360.thirdparty;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Optional;

@Log4j2
@GrpcService
public class GrpcThirdPartyService extends ThirdPartyServiceGrpc.ThirdPartyServiceImplBase {

    private final ThirdPartyManager thirdPartyManager;

    public GrpcThirdPartyService(
            @Qualifier("thirdPartyManagerInvoker") ThirdPartyManager thirdPartyManagerInvoker) {
        this.thirdPartyManager = thirdPartyManagerInvoker;
        log.info("created '{}(ThirdPartyManager={})'.", this, this.thirdPartyManager);
    }

    @Override
    public void send(
            Message.ThirdPartyRequest request,
            StreamObserver<Message.ThirdPartyResponse> responseObserver) {
        log.info("GrpcThirdPartyService key='{}', type='{}'", request.getKey(), request.getType());
        ThirdPartyQuery thirdPartyQuery = ThirdPartyQuery.from(request);
        var response = thirdPartyManager.send(thirdPartyQuery);
        responseObserver.onNext(
                Optional.ofNullable(response)
                        .map(ThirdPartyStatus::toMessage)
                        .orElse(Message.ThirdPartyResponse.getDefaultInstance()));
        responseObserver.onCompleted();
        log.info("GrpcThirdPartyService Successfully. response='{}'", response);
    }
}
