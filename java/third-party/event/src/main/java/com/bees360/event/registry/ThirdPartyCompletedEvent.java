package com.bees360.event.registry;

import com.bees360.thirdparty.ThirdPartyDataTypeEnum;
import com.bees360.thirdparty.ThirdPartyTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/** 该类作为 thirdParty 模块调用第三方接口后生成的资源数据 Event 类， 可以在各个模块监听该事件进行后续的业务处理。 */
@Event
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ThirdPartyCompletedEvent {
    private String key;
    private ThirdPartyTypeEnum type;
    private CompletedStatus status;
    private List<ThirdPartyResource> resources;

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThirdPartyResource {
        private String resourceKey;
        private ThirdPartyDataTypeEnum resourceType;
    }

    public enum CompletedStatus {
        SUCCESS,
        FAILED
    }
}
