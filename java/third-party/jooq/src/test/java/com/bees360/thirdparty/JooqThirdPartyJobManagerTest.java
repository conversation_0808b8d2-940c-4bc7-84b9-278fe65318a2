package com.bees360.thirdparty;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.persistent.thirdparty.enums.ThirdPartyJobStatusEnum;
import com.bees360.jooq.persistent.thirdparty.enums.ThirdPartyJobTypeEnum;
import com.bees360.thirdparty.config.JooqThirdPartyConfig;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

import java.util.Arrays;
import java.util.stream.Collectors;

@SpringBootTest
@DirtiesContext
@ApplicationAutoConfig
public class JooqThirdPartyJobManagerTest extends AbstractThirdPartyJobStatusManagerTest {
    public JooqThirdPartyJobManagerTest(
            @Autowired ThirdPartyJobStatusManager thirdPartyJobStatusManager) {
        super(thirdPartyJobStatusManager, thirdPartyJobStatusManager);
    }

    @Import({JooqThirdPartyConfig.class})
    @Configuration
    static class Config {}

    @Test
    public void saveAndFindJobStatusTest() {
        super.saveAndFindJobStatusTest();
    }

    @Test
    public void saveAndFindJobStatusByRelatedIdTest() {
        super.saveAndFindJobStatusByRelatedIdTest();
    }

    @Test
    public void saveAndUpdateJobStatusTest() {
        super.saveAndUpdateJobStatusTest();
    }

    @Test
    public void saveUnknownStatusTest() {
        super.saveUnknownStatusTest();
    }

    @Test
    public void saveUnknownTypeTest() {
        super.saveUnknownTypeTest();
    }

    protected String getRandomType() {
        return ThirdPartyJobTypeEnum.values()[
                RandomUtils.nextInt(1, ThirdPartyJobTypeEnum.values().length)]
                .getLiteral();
    }

    @Override
    protected Iterable<String> getTypes() {
        var list =
                Arrays.stream(ThirdPartyJobTypeEnum.values())
                        .map(ThirdPartyJobTypeEnum::getLiteral)
                        .collect(Collectors.toList());
        list.remove("UNKNOWN");
        return list;
    }

    protected String getRandomStatus() {
        return ThirdPartyJobStatusEnum.values()[
                RandomUtils.nextInt(1, ThirdPartyJobStatusEnum.values().length)]
                .getLiteral();
    }
}
