package com.bees360.thirdparty;

import static com.bees360.jooq.persistent.thirdparty.Tables.THIRD_PARTY_JOB_STATUS;

import org.jooq.Record;

public class RecordThirdPartyJobStatus implements ThirdPartyJobStatus {

    private final Record record;

    public RecordThirdPartyJobStatus(Record thirdPartyJobStatusRecord) {
        this.record = thirdPartyJobStatusRecord;
    }

    @Override
    public String getRelatedId() {
        return record.get(THIRD_PARTY_JOB_STATUS.RELATED_ID);
    }

    @Override
    public String getType() {
        return record.get(THIRD_PARTY_JOB_STATUS.TYPE).getLiteral();
    }

    @Override
    public String getStatus() {
        return record.get(THIRD_PARTY_JOB_STATUS.STATUS).getLiteral();
    }

    @Override
    public String getUpdatedBy() {
        return record.get(THIRD_PARTY_JOB_STATUS.UPDATED_BY);
    }

    @Override
    public Long getVersion() {
        return record.get(THIRD_PARTY_JOB_STATUS.VERSION);
    }

    @Override
    public String getComment() {
        return record.get(THIRD_PARTY_JOB_STATUS.COMMENT);
    }
}
