package com.bees360.job;

import com.bees360.job.registry.SendInferaImageAnnotationJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.thirdparty.ThirdPartyDataTypeEnum;
import com.bees360.thirdparty.ThirdPartyManager;
import com.bees360.thirdparty.ThirdPartyQuery;
import com.bees360.thirdparty.ThirdPartyTypeEnum;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.List;

@Log4j2
public class SendInferaImageAnnotationJobExecutor
        extends AbstractJobExecutor<SendInferaImageAnnotationJob> {
    public final String INFERA_JOBS_REQUEST_BODY = "infera_body";
    public final String INFERA_JOBS_REQUEST_JOB_KEY = "job_key";

    private final ThirdPartyManager thirdPartyManager;

    public SendInferaImageAnnotationJobExecutor(ThirdPartyManager thirdPartyManager) {
        this.thirdPartyManager = thirdPartyManager;
        log.info("Created '{}(thirdPartyManager={})'", this, this.thirdPartyManager);
    }

    @Override
    protected void handle(SendInferaImageAnnotationJob job) throws IOException {
        log.info("Received job {}.", job);
        var projectId = job.getProjectId();
        var serviceKey = job.getServiceKey();
        var entityJsonStr = job.getInferaBodyStr();
        var data1 =
                ThirdPartyQuery.ThirdPartyData.of(
                        INFERA_JOBS_REQUEST_JOB_KEY, ThirdPartyDataTypeEnum.STRING, serviceKey);
        var data2 =
                ThirdPartyQuery.ThirdPartyData.of(
                        INFERA_JOBS_REQUEST_BODY, ThirdPartyDataTypeEnum.JSON, entityJsonStr);
        var thirdPartyQuery =
                ThirdPartyQuery.of(projectId, ThirdPartyTypeEnum.INFERA, List.of(data1, data2));
        thirdPartyManager.send(thirdPartyQuery);
    }
}
