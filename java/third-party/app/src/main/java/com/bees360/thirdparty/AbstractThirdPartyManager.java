package com.bees360.thirdparty;

import com.google.common.base.Preconditions;

public abstract class AbstractThirdPartyManager implements ThirdPartyManager {

    public abstract ThirdPartyTypeEnum getThirdPartyType();

    public ThirdPartyStatus send(ThirdPartyQuery query) {
        check(query);
        return sendThirdParty(query);
    }

    protected void check(ThirdPartyQuery query) {
        Preconditions.checkArgument(
                query.getType().equals(getThirdPartyType()),
                "this.getThirdPartyType() is not equal to query type ('%s' != '%s').",
                getThirdPartyType(),
                query.getType());
    }

    protected abstract ThirdPartyStatus sendThirdParty(ThirdPartyQuery query);
}
