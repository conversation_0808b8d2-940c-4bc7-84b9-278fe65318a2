package com.bees360.image.util;

import com.bees360.image.Message.AttributeMessage;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;

import jakarta.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/** image tag/annotation attribute 字段适配器 */
public class AttributeMessageAdapter {

    public static final String DEFAULT_ATTRIBUTE_JSON = "{}";

    private static final JsonFormat.Printer jsonPrinter = JsonFormat.printer();
    private static final JsonFormat.Parser jsonParser = JsonFormat.parser();

    private AttributeMessageAdapter() {}

    /**
     * transform json string to attributeMessage
     *
     * @param jsonString json string
     * @return attributeMessage
     * @throws IllegalArgumentException jsonString无法被转化为 AttributeMessage
     */
    public static AttributeMessage jsonToAttribute(String jsonString) {
        if (StringUtils.isEmpty(jsonString) || Objects.equals(DEFAULT_ATTRIBUTE_JSON, jsonString)) {
            return AttributeMessage.getDefaultInstance();
        }

        var builder = AttributeMessage.newBuilder();
        try {
            // 使用 JsonFormat 将 JSON 字符串转换为 Protobuf 对象
            jsonParser.merge(jsonString, builder);
        } catch (Exception e) {
            throw new IllegalArgumentException(
                    String.format("Json %s cannot be convert to AttributeMessage.", jsonString), e);
        }
        return builder.build();
    }

    /**
     * transform attributeMessage to json string
     *
     * @param attribute attributeMessage
     * @return json string
     * @throws IllegalArgumentException AttributeMessage对象无法被转化为json
     */
    @Nullable
    public static String attributeToJson(AttributeMessage attribute) {
        if (Objects.equals(AttributeMessage.getDefaultInstance(), attribute)) {
            return null;
        }

        try {
            return jsonPrinter.print(attribute);
        } catch (InvalidProtocolBufferException e) {
            throw new IllegalArgumentException(
                    String.format("AttributeMessage %s cannot be converted to json", attribute), e);
        }
    }
}
