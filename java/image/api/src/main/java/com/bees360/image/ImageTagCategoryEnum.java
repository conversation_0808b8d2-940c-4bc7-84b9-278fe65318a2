package com.bees360.image;

import java.util.Arrays;

public enum ImageTagCategoryEnum {
    OBJECT(1, "Object"),

    SCOPE(2, "Scope"),

    DIRECTION(3, "Direction"),

    LOCATION(4, "Location"),

    COMPONENT(6, "Component"),

    DAMAGE(7, "Damage"),

    HAZARD(8, "Hazard"),

    FEED<PERSON><PERSON><PERSON>(9, "Feedback"),

    CATEGORY(10, "Category"),

    CREDIT(11, "Credit"),

    REPORT(16, "Report"),

    ORIENTATION(17, "Orientation"),

    FLOOR_LEVEL(18, "Floor Level"),

    NUMBER(19, "Number"),
    ;

    private final int code;
    private final String display;

    ImageTagCategoryEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public static ImageTagCategoryEnum valueOf(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values()).filter(e -> e.code == code).findFirst().orElse(null);
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }
}
