package com.bees360.image.tag;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Entity;
import com.bees360.api.Proto;
import com.bees360.image.ImageAnnotation;
import com.bees360.image.Message;
import com.bees360.image.util.AttributeMessageAdapter;
import com.bees360.tag.Message.ImageTagMessage;
import com.bees360.util.DateTimes;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.Instant;

// 将Proto中的泛型由ImageTagMessage修改为Tag,Tag消息中有字段包含了ImageMessage的信息
public interface ImageTag extends Entity, Proto<Message.ImageMessage.Tag> {
    String NAMESPACE = "image/tag";

    @Override
    default String getNamespace() {
        return NAMESPACE;
    }

    @Nullable
    String getImageId();

    @Nonnull
    String getTitle();

    @Nonnull
    String getCategory();

    @Nullable
    Iterable<? extends ImageAnnotation> getAnnotation();

    @Nullable
    String getDescription();

    @Nullable
    String getColor();

    @Nullable
    String getIcon();

    boolean isSupportUpdate();

    @Nullable
    Instant getCreatedAt();

    @Nullable
    TagCreator getCreatedBy();

    @Nullable
    String getAbbreviation();

    /** 获取 image tag 的 relationId */
    @Nullable
    default String getImageTagId() {
        return null;
    }

    /** 获取 image tag 的attribute */
    @Nullable
    default String getAttribute() {
        return null;
    }

    @Override
    default Message.ImageMessage.Tag toMessage() {
        var builder = Message.ImageMessage.Tag.newBuilder();
        var oldTagBuilder = ImageTagMessage.newBuilder();
        oldTagBuilder.setTitle(getTitle());
        oldTagBuilder.setCategory(getCategory());
        acceptIfNotNull(oldTagBuilder::setAbbreviation, getAbbreviation());
        acceptIfNotNull(oldTagBuilder::setId, getId());
        acceptIfNotNull(oldTagBuilder::setDescription, getDescription());
        acceptIfNotNull(oldTagBuilder::setColor, getColor());
        acceptIfNotNull(oldTagBuilder::setIcon, getIcon());
        acceptIfNotNull(oldTagBuilder::setSupportUpdate, isSupportUpdate());
        acceptIfNotNull(builder::setTag, oldTagBuilder);
        Message.ImageMessage.Tag.User.Builder userBuilder =
                Message.ImageMessage.Tag.User.newBuilder();
        if (getCreatedBy() != null) {
            acceptIfNotNull(userBuilder::setId, getCreatedBy().getUserId());
            acceptIfNotNull(userBuilder::setName, getCreatedBy().getUserName());
        }
        Message.ImageMessage.Tag.User user = userBuilder.build();
        acceptIfNotNull(builder::setCreatedBy, user);
        acceptIfNotNull(builder::setCreatedAt, getCreatedAt(), DateTimes::toTimestamp);
        acceptIfNotNull(builder::setImageId, getImageId());
        acceptIfNotNull(builder::addAnnotation, getAnnotation(), ImageAnnotation::toMessage);
        acceptIfNotNull(builder::setImageTagId, getImageTagId());
        acceptIfNotNull(
                builder::setAttribute, getAttribute(), AttributeMessageAdapter::jsonToAttribute);
        return builder.build();
    }
}
