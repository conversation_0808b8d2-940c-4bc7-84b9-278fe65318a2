package com.bees360.image.tag;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.Proto;
import com.bees360.image.Message.ImageApiMessage;

/**
 * imageApi tag dict 的基本信息接口类
 *
 * <AUTHOR>
 * @since 2024/1/24
 */
public interface ImageApiTag extends Proto<ImageApiMessage.Tag> {

    /** 获取 tag dict 的id */
    String getId();

    /** 获取 tag dict 的title */
    String getTitle();

    /** 获取 tag 的分类类别 */
    String getCategory();

    static ImageApiTag from(ImageApiMessage.Tag message) {
        return new ImageApiTag() {
            @Override
            public String getId() {
                return message.getId();
            }

            @Override
            public String getTitle() {
                return message.getTitle();
            }

            @Override
            public String getCategory() {
                return message.getCategory();
            }

            @Override
            public ImageApiMessage.Tag toMessage() {
                return message;
            }
        };
    }

    @Override
    default ImageApiMessage.Tag toMessage() {
        var builder = ImageApiMessage.Tag.newBuilder();
        acceptIfNotNull(builder::setId, getId());
        acceptIfNotNull(builder::setTitle, getTitle());
        acceptIfNotNull(builder::setCategory, getCategory());
        return builder.build();
    }
}
