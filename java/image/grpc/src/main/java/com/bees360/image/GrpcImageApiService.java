package com.bees360.image;

import static com.bees360.util.ProtoStructAdapter.structToJson;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.image.ImageApiServiceGrpc.ImageApiServiceImplBase;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @since 2024/1/29
 */
@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
public class GrpcImageApiService extends ImageApiServiceImplBase {

    private final ImageApiManager grpcImageApiServiceManager;

    public GrpcImageApiService(ImageApiManager grpcImageApiServiceManager) {
        this.grpcImageApiServiceManager = grpcImageApiServiceManager;
        log.info(
                "Created {}(grpcImageApiServiceManager={}).",
                this,
                this.grpcImageApiServiceManager);
    }

    @Override
    public void findImageByQuery(
            Message.ImageApiQueryRequest request,
            StreamObserver<Message.ImageApiQueryResponse> responseObserver) {
        var query = structToJson(request.getQuery());
        var limit = request.getLimit();
        var offset = request.getOffset();
        var imageApiQuery = ImageApiQuery.of(query, limit, offset);
        var imageByQuery = grpcImageApiServiceManager.findImageByQuery(imageApiQuery);
        responseObserver.onNext(imageByQuery.toMessage());
        responseObserver.onCompleted();
    }
}
