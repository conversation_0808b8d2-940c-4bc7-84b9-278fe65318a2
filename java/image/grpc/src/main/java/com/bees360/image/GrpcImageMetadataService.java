package com.bees360.image;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.image.ImageMetadataServiceGrpc.ImageMetadataServiceImplBase;
import com.bees360.image.Message.ImageMessage.Metadata;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/1/24
 */
@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
@RequiredArgsConstructor
public class GrpcImageMetadataService extends ImageMetadataServiceImplBase {

    private final ImageMetadataProvider grpcImageMetadataProvider;

    @Override
    public StreamObserver<StringValue> get(StreamObserver<Metadata> responseObserver) {
        return new StreamObserver<>() {
            final List<String> imageIds = new ArrayList<>();

            @Override
            public void onNext(StringValue id) {
                if (StringUtils.isBlank(id.getValue())) {
                    throw new IllegalArgumentException("The image id should not be empty");
                }
                imageIds.add(id.getValue());
            }

            @Override
            public void onError(Throwable throwable) {}

            @Override
            public void onCompleted() {
                var imageMetadataMap = grpcImageMetadataProvider.findByImageId(imageIds);
                imageIds.forEach(
                        id ->
                                responseObserver.onNext(
                                        Optional.ofNullable(imageMetadataMap.get(id))
                                                .map(ImageMetadata::toMessage)
                                                .orElse(null)));
                responseObserver.onCompleted();
            }
        };
    }
}
