package com.bees360.image;

import com.bees360.image.ImageNoteServiceGrpc.ImageNoteServiceImplBase;
import com.bees360.image.Message.DeleteImageNoteRequest;
import com.bees360.image.Message.DeleteNoteByImageIdRequest;
import com.bees360.image.Message.ImageNoteList;
import com.bees360.image.Message.ImageNoteSearchRequest;
import com.bees360.image.grpc.ProtobufImageNote;
import com.bees360.util.Iterables;
import com.google.protobuf.Empty;

import io.grpc.stub.StreamObserver;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
@Log4j2
@GrpcService
@RequiredArgsConstructor
public class GrpcImageNoteService extends ImageNoteServiceImplBase {

    private final ImageNoteManager grpcImageNoteServiceManager;

    public void save(ImageNoteList request, StreamObserver<ImageNoteList> responseObserver) {
        var imageNoteList =
                request.getNoteList().stream()
                        .map(ProtobufImageNote::new)
                        .collect(Collectors.toList());
        var imageNotes = grpcImageNoteServiceManager.saveAll(imageNoteList);
        var noteList =
                ImageNoteList.newBuilder()
                        .addAllNote(Iterables.transform(imageNotes, ImageNote::toMessage))
                        .build();
        responseObserver.onNext(noteList);
        responseObserver.onCompleted();
    }

    public void findByImageId(
            ImageNoteSearchRequest request, StreamObserver<ImageNoteList> responseObserver) {
        var imageIds = request.getImageIdList();
        Map<String, Iterable<? extends ImageNote>> imageNotes;
        if (request.getHistory()) {
            imageNotes = grpcImageNoteServiceManager.findHistoryByImageIds(imageIds);
        } else {
            imageNotes = grpcImageNoteServiceManager.findByImageIds(imageIds);
        }
        var noteListBuilder = ImageNoteList.newBuilder();
        for (var entry : imageNotes.entrySet()) {
            noteListBuilder.addAllNote(Iterables.transform(entry.getValue(), ImageNote::toMessage));
        }
        responseObserver.onNext(noteListBuilder.build());
        responseObserver.onCompleted();
    }

    public void delete(DeleteImageNoteRequest request, StreamObserver<Empty> responseObserver) {
        grpcImageNoteServiceManager.deleteByIds(request.getIdList(), request.getDeletedBy());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    public void deleteByImageId(
            DeleteNoteByImageIdRequest request, StreamObserver<Empty> responseObserver) {
        grpcImageNoteServiceManager.deleteByImageIds(
                request.getImageIdList(), request.getDeletedBy());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
