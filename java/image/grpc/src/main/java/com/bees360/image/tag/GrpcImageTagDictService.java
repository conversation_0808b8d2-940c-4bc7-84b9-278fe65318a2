package com.bees360.image.tag;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.image.Message.ImageMessage.Tag.User;
import com.bees360.image.grpc.ProtobufImageTagDict;
import com.bees360.tag.ImageTagManagerGrpc.ImageTagManagerImplBase;
import com.bees360.tag.Message.DeleteTagRequest;
import com.bees360.tag.Message.FindTagRequest;
import com.bees360.tag.Message.ImageTagMessage;
import com.bees360.tag.Message.SaveTagRequest;
import com.bees360.tag.Message.SetTagPositionRequest;
import com.bees360.user.Message;
import com.google.protobuf.Empty;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.List;

@Log4j2
@GrpcService
@Import({ExceptionTranslateInterceptor.class})
@RequiredArgsConstructor
public class GrpcImageTagDictService extends ImageTagManagerImplBase {

    private final ImageTagDictManager imageTagDictManager;

    @Override
    public void getAll(Empty request, StreamObserver<ImageTagMessage> responseObserver) {
        var response = imageTagDictManager.getAll();
        response.forEach(imageTag -> responseObserver.onNext(imageTag.toMessage().getTag()));
        responseObserver.onCompleted();
    }

    @Override
    public StreamObserver<StringValue> findById(StreamObserver<ImageTagMessage> responseObserver) {
        return new StreamObserver<>() {
            final List<String> tagIds = new ArrayList<>();

            @Override
            public void onNext(StringValue id) {
                if (StringUtils.isBlank(id.getValue())) {
                    throw new IllegalArgumentException("The image tag id should not be empty");
                }
                tagIds.add(id.getValue());
            }

            @Override
            public void onError(Throwable t) {}

            @Override
            public void onCompleted() {
                imageTagDictManager
                        .findById(tagIds)
                        .forEach(
                                imageTag -> responseObserver.onNext(imageTag.toMessage().getTag()));
                responseObserver.onCompleted();
            }
        };
    }

    @Override
    public void findByGroup(
            FindTagRequest request, StreamObserver<ImageTagMessage> responseObserver) {
        var response =
                imageTagDictManager.findByGroup(request.getGroupKey(), request.getGroupType());
        response.forEach(imageTag -> responseObserver.onNext(imageTag.toMessage().getTag()));
        responseObserver.onCompleted();
    }

    @Override
    public void setPosition(SetTagPositionRequest request, StreamObserver<Empty> responseObserver) {
        imageTagDictManager.setPosition(
                request.getTagId(), request.getPositionTagId(), request.getUpdatedBy().getId());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void save(SaveTagRequest request, StreamObserver<StringValue> responseObserver) {
        User savedBy = null;
        if (!request.getSavedBy().equals(Message.UserMessage.getDefaultInstance())) {
            savedBy =
                    User.newBuilder()
                            .setId(request.getSavedBy().getId())
                            .setName(request.getSavedBy().getName())
                            .build();
        }
        var tagId = imageTagDictManager.save(new ProtobufImageTagDict(request.getTag(), savedBy));
        responseObserver.onNext(StringValue.of(tagId));
        responseObserver.onCompleted();
    }

    @Override
    public void deleteById(DeleteTagRequest request, StreamObserver<Empty> responseObserver) {
        imageTagDictManager.deleteById(request.getTagId(), request.getDeletedBy().getId());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
