package com.bees360.image;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.image.config.GrpcImageMetadataClientConfig;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@SpringJUnitConfig
@DirtiesContext
@ApplicationAutoConfig
public class GrpcImageMetadataManagerTest extends TestImageMetadataManager {

    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class
    })
    @Import({GrpcImageMetadataService.class, GrpcImageMetadataClientConfig.class})
    @Configuration
    static class Config {

        @Bean
        public ImageMetadataManager grpcImageMetadataProvider() {
            return new InMemoryImageMetadataManager();
        }
    }

    public GrpcImageMetadataManagerTest(
            @Autowired GrpcImageMetadataClient grpcImageMetadataClient,
            @Autowired ImageMetadataManager grpcImageMetadataProvider) {
        super(grpcImageMetadataClient, grpcImageMetadataProvider);
    }

    @Test
    void testSaveThenExistsThenFind() {
        saveThenExistsThenFind();
    }

    @Test
    void testClientFind() {
        clientFind();
    }

    @Test
    void testFindMetadataThrowIAEWhenIdIsBlank() {
        findMetadataThrowIAEWhenIdIsBlank();
    }
}
