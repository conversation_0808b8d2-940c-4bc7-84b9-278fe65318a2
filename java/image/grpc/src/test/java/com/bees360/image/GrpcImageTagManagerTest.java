package com.bees360.image;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.image.config.GrpcImageAnnotationClientConfig;
import com.bees360.image.config.GrpcImageTagClientConfig;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/**
 * <AUTHOR>
 * @since 2022/8/1 下午7:38
 */
@SpringBootTest()
@SpringJUnitConfig
@DirtiesContext
@ApplicationAutoConfig
public class GrpcImageTagManagerTest extends AbstractImageTagTest {

    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class
    })
    @Import({
        GrpcImageTagService.class,
        GrpcImageTagClientConfig.class,
        GrpcImageAnnotationClientConfig.class,
    })
    @Configuration
    static class Config {
        @Bean
        ImageTagManager grpcImageTagServiceManager() {
            return new InMemoryImageTagManager();
        }

        @Bean
        ImageAnnotationManager grpcImageAnnotationServiceManager(
                InMemoryImageTagManager grpcImageTagServiceManager) {
            return grpcImageTagServiceManager;
        }
    }

    public GrpcImageTagManagerTest(
            @Autowired GrpcImageTagClient grpcImageTagClient,
            @Autowired GrpcImageAnnotationClient grpcImageAnnotationClient) {
        super(grpcImageTagClient, grpcImageAnnotationClient);
    }

    @Override
    @Test
    void testBatchAddThenFindByImageIds() {
        super.testBatchAddThenFindByImageIds();
    }

    @Override
    @Test
    void testAddAllThenDeleteAllAndFindInHistoryImageTag() {
        super.testAddAllThenDeleteAllAndFindInHistoryImageTag();
    }

    @Override
    @Test
    void testAddThenDeleteThenFindInHistoryImageTag() {
        super.testAddThenDeleteThenFindInHistoryImageTag();
    }

    @Override
    @Test
    void testFindByImageIdAndCategories() {
        super.testFindByImageIdAndCategories();
    }

    @Override
    @Test
    void testAddThenDeleteImageTag() {
        super.testAddThenDeleteImageTag();
    }

    @Override
    @Test
    void testAddAllWithAttributeThenDeleteAllImageTag() {
        super.testAddAllWithAttributeThenDeleteAllImageTag();
    }

    @Override
    @Test
    void testAddAllThenUpdateImageTag() {
        super.testAddAllThenUpdateImageTag();
    }

    @Override
    @Test
    void testFindByImageId() {
        super.testFindByImageId();
    }

    @Override
    @Test
    void testAddThenDeleteImageAnnotation() {
        super.testAddThenDeleteImageAnnotation();
    }

    @Override
    @Test
    void testAddThenUpdateImageAnnotationSort() {
        super.testAddThenUpdateImageAnnotationSort();
    }

    @Override
    @Test
    void testUpdateOriginAnnotationId() {
        super.testUpdateOriginAnnotationId();
    }

    @Override
    @Test
    void testUpdateAnnotationPolygonAndDescription() {
        super.testUpdateAnnotationPolygonAndDescription();
    }

    @Override
    @Test
    void testUpdateAnnotationAttribute() {
        super.testUpdateAnnotationAttribute();
    }

    @Override
    @Test
    void testUpdateAnnotationAttributeAndOriginId() {
        super.testUpdateAnnotationAttributeAndOriginId();
    }

    @Override
    @Test
    void testUpdateAnnotationAttributeAndOriginIdWithDuplicateAnnotationId() {
        super.testUpdateAnnotationAttributeAndOriginIdWithDuplicateAnnotationId();
    }
}
