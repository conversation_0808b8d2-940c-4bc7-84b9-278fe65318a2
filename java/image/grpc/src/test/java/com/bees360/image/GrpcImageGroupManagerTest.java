package com.bees360.image;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.image.config.GrpcImageClientConfig;
import com.bees360.image.config.GrpcImageGroupClientConfig;
import com.bees360.image.config.GrpcImageTagClientConfig;
import com.bees360.image.util.ImageGroupFillImageManager;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@SpringJUnitConfig
@DirtiesContext
@ApplicationAutoConfig
public class GrpcImageGroupManagerTest extends TestImageGroupManager {

    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class
    })
    @Import({
        GrpcImageService.class,
        GrpcImageClientConfig.class,
        GrpcImageGroupService.class,
        GrpcImageGroupClientConfig.class,
        GrpcImageTagClientConfig.class,
        GrpcImageTagService.class
    })
    @Configuration
    static class Config {
        @Bean
        ImageManager grpcImageServiceManager() {
            return new InMemoryImageManager();
        }

        @Bean
        ImageGroupManager grpcImageGroupServiceManager(
                ImageTagManager grpcImageTagServiceManager, ImageManager grpcImageServiceManager) {
            return new ImageGroupFillImageManager(
                    new InMemoryImageGroupManager(
                            grpcImageTagServiceManager, grpcImageServiceManager),
                    grpcImageServiceManager);
        }

        @Primary
        @Bean
        ImageTagManager grpcImageTagServiceManager() {
            return new InMemoryImageTagManager();
        }
    }

    public GrpcImageGroupManagerTest(
            @Autowired GrpcImageGroupClient grpcImageGroupClient,
            @Autowired GrpcImageClient grpcImageClient,
            @Autowired GrpcImageTagClient grpcImageTagClient) {
        super(grpcImageGroupClient, grpcImageClient, grpcImageTagClient);
    }

    @Test
    void createGroupImageThenFind() {
        createGroupImageThenFindTest();
    }

    @Test
    void createThenFindThenToGroup() {
        createThenFindThenToGroupTest();
    }

    @Test
    void createThenDeleteThenRecoverThenRemove() {
        createThenDeleteThenRecoverThenRemoveTest();
    }

    @Test
    void createThenBatchDeleteThenRecoverThenRemove() {
        createThenBatchDeleteThenRecoverThenRemoveTest();
    }

    @Test
    void createThenSort() {
        createThenSortTest();
    }

    @Test
    void addNotExistsImageToGroupTest() {
        addNotExistsImageToGroup();
    }

    @Test
    void createThenFindByTag() {
        createThenFindByQueryTest();
    }

    @Test
    void findByTagSort() {
        findByTagSortTest();
    }

    @Test
    void findByTagSortId() {
        findByTagIdSortTest();
    }

    @Test
    void findGroupIdByTypeAndImageId() {
        findGroupIdByTypeAndImageIdTest();
    }

    @Test
    void findGroupIdByEmptyImageId() {
        findGroupIdByEmptyImageIdTest();
    }

    @Test
    void findGroupIdByNotExistGroupType() {
        findGroupIdByNotExistGroupTypeTest();
    }

    @Test
    void testFindImageByGroupIds() {
        super.testFindImageByGroupIds();
    }

    @Test
    void testCreateGroupImageWithoutShootingTimeShouldSucceed() {
        super.testCreateGroupImageWithoutShootingTimeShouldSucceed();
    }

    @Test
    void testCreateGroupImageFutureShootingTimeShouldThrow() {
        super.testCreateGroupImageFutureShootingTimeShouldThrow();
    }
}
