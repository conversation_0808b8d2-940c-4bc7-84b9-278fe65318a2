package com.bees360.image;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.image.config.GrpcImageApiClientConfig;
import com.bees360.image.config.JooqImageApiManagerConfig;
import com.bees360.image.config.JooqImageManagerConfig;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.config.S3ResourceRepositoryFactoryConfig;
import com.bees360.resource.factory.S3ResourceRepositoryFactory;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.net.URI;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@SpringJUnitConfig
@DirtiesContext
@ApplicationAutoConfig
public class GrpcImageApiManagerTest extends TestImageApiManager {

    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class,
    })
    @Import({
        GrpcImageApiClientConfig.class,
        GrpcImageApiService.class,
        JooqImageManagerConfig.class,
        JooqImageApiManagerConfig.class,
        JooqImageAnnotationManager.class,
        S3ResourceRepositoryFactoryConfig.class,
    })
    @Configuration
    static class Config {

        @Bean
        public ResourcePool imageResourceUrlProvider(
                S3ResourceRepositoryFactory s3ResourceRepositoryFactory,
                @Value("${image.resource.uri}") String imageResourceURI) {
            return s3ResourceRepositoryFactory.get(URI.create(imageResourceURI));
        }

        @Bean
        ImageApiManager grpcImageApiServiceManager(@Autowired ImageApiManager jooqImageApiManager) {
            return jooqImageApiManager;
        }
    }

    public GrpcImageApiManagerTest(
            @Autowired GrpcImageApiClient grpcImageApiClient,
            @Autowired ImageManager jooqImageManager,
            @Autowired ImageGroupManager jooqImageGroupManager,
            @Autowired ImageTagManager jooqImageTagManager,
            @Autowired ImageAnnotationManager imageAnnotationManager,
            @Autowired ImageNoteManager imageNoteManager) {
        super(
                grpcImageApiClient,
                jooqImageGroupManager,
                jooqImageManager,
                jooqImageTagManager,
                imageAnnotationManager,
                imageNoteManager);
    }

    @Test
    void testConditionQueryWithLimitCount() {
        super.testConditionQueryWithLimitCount();
    }

    @Test
    void testIncAndExcConditionQueryWithTagAndCategory() {
        super.testIncAndExcConditionQueryWithTagAndCategory();
    }

    @Test
    void testInAndNinConditionQueryWithTagAndCategory() {
        super.testInAndNinConditionQueryWithTagAndCategory();
    }

    @Test
    void testConditionQueryWithCategory() {
        super.testConditionQueryWithCategory();
    }

    @Test
    void testConditionQueryWithTag() {
        super.testConditionQueryWithTag();
    }

    @Test
    void testConditionQueryByGroupIdOrImageId() {
        super.testConditionQueryByGroupIdOrImageId();
    }

    @Test
    void testConditionQueryByImageIdAfterUpdateImageNotes() {
        super.testConditionQueryByImageIdAfterUpdateImageNotes();
    }

    @Test
    void testConditionQueryWithoutProjectIdAndImageIdShouldThrow() {
        super.testConditionQueryWithoutProjectIdAndImageIdShouldThrow();
    }

    @Test
    void testConditionQueryWithIllegalQueryShouldThrow() {
        super.testConditionQueryWithIllegalQueryShouldThrow();
    }
}
