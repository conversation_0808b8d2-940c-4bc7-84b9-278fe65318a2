package com.bees360.image;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.image.config.GrpcImageClientConfig;
import com.bees360.image.config.GrpcImageNoteClientConfig;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@SpringJUnitConfig
@DirtiesContext
@ApplicationAutoConfig
public class GrpcImageNoteManagerTest extends TestImageNoteManager {

    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class
    })
    @Import({
        GrpcImageService.class,
        GrpcImageClientConfig.class,
        GrpcImageNoteService.class,
        GrpcImageNoteClientConfig.class,
    })
    @Configuration
    static class Config {
        @Bean
        ImageNoteManager grpcImageNoteServiceManager() {
            return new InMemoryImageNoteManager();
        }

        @Bean
        ImageManager grpcImageServiceManager() {
            return new InMemoryImageManager();
        }
    }

    public GrpcImageNoteManagerTest(
            @Autowired GrpcImageClient grpcImageClient,
            @Autowired GrpcImageNoteClient grpcImageNoteClient) {
        super(grpcImageClient, grpcImageNoteClient);
    }

    @Test
    void testSaveThenFindImageNoteAndFindByImageId() {
        saveThenFindImageNoteAndFindByImageId();
    }

    @Test
    void testSaveThenDeleteByIdsThenFindHistory() {
        saveThenDeleteByIdsThenFindHistory();
    }

    @Test
    void testSaveThenDeleteByImageIdsThenFindHistory() {
        saveThenDeleteByImageIdsThenFindHistory();
    }
}
