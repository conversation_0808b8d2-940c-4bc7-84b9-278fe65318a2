package com.bees360.image;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.image.Message.ImageMessage.Resource.Type;
import com.bees360.image.config.GrpcImageClientConfig;
import com.bees360.image.util.ImageFillResourceManager;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.function.BiFunction;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@SpringJUnitConfig
@DirtiesContext
@ApplicationAutoConfig
public class GrpcImageManagerTest extends TestImageManager {

    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class,
        ExceptionTranslateInterceptor.class
    })
    @Import({GrpcImageService.class, GrpcImageClientConfig.class})
    @Configuration
    static class Config {
        @Bean
        ImageResourceManager imageResourceManager() {
            return new InMemoryImageResourceManager();
        }

        @Bean
        ImageManager grpcImageServiceManager(ImageResourceProvider imageResourceProvider) {
            return new ImageFillResourceManager(new InMemoryImageManager(), imageResourceProvider);
        }

        @Bean
        public BiFunction<String, Type, String> urlProvider() {
            return (imageId, type) -> "image/" + imageId + "/" + type.getNumber();
        }
    }

    public GrpcImageManagerTest(
            @Autowired GrpcImageClient grpcImageClient,
            @Autowired ImageResourceManager imageResourceManager,
            @Autowired BiFunction<String, Type, String> urlProvider) {
        super(grpcImageClient, imageResourceManager, urlProvider);
    }

    @Test
    void testUpdateImageTiffOrientation() {
        updateImageTiffOrientation();
    }

    @Test
    void testGetImagesThrowIAEWhenIdIsBlank() {
        getImagesThrowIAEWhenIdIsBlank();
    }

    @Test
    void testSaveResourceThenFindFullImage() {
        saveResourceThenFindFullImage(null);
    }

    @Test
    void testMultiSliceResource() {
        saveThenGetMultiSliceResource();
    }

    @Test
    void testCreateImageWithoutShootingTimeShouldSucceed() {
        super.testCreateImageWithoutShootingTimeShouldSucceed();
    }

    @Test
    void testSaveFutureShootingTimeShouldThrow() {
        super.testSaveFutureShootingTimeShouldThrow();
    }

    @Test
    void testPastShootingTimeWillBeSaved() {
        super.testPastShootingTimeWillBeSaved();
    }

    @Test
    void testCreateImageWithIdShouldSucceed() {
        super.testCreateImageWithIdShouldSucceed();
    }

    @Test
    void testCreateImageWithIllegalIdShouldThrow() {
        super.testCreateImageWithIllegalIdShouldThrow();
    }

    @Test
    void testCreateImageWithExistedIdShouldThrow() {
        super.testCreateImageWithExistedIdShouldThrow();
    }
}
