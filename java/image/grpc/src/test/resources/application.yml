grpc:
  server:
    port: 9896
  client:
    imageManager:
      address: 'static://127.0.0.1:9896'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    resourcePool:
      address: static://127.0.0.1:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

spring:
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres

s3:
  clients:
    - endpoint: http://s3-primary
      key: local-identity
      secret: local-credential
      pathStyleAccess: true
      checksumValidationEnabled: false
    - endpoint: http://s3-secondary
      key: local-identity
      secret: local-credential
      pathStyleAccess: true
      checksumValidationEnabled: false

resource:
  s3:
    - client: s3-primary
      bucket: test-bucket
      url-expiration: PT1h
      url-stable-duration: PT15m
image:
  resource:
    uri: s3://s3-primary/test-bucket/
