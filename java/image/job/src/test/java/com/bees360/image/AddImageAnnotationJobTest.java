package com.bees360.image;

import static com.bees360.image.RandomImageApiUtil.randomAttribute;
import static com.bees360.image.RandomImageUtil.getPointMessages;
import static com.bees360.image.RandomImageUtil.randomImageId;
import static com.bees360.image.RandomImageUtil.randomUserId;
import static com.bees360.image.util.AttributeMessageAdapter.attributeToJson;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.bees360.job.Job;
import com.bees360.job.JobDispatcher;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.BatchAddImageAnnotationJob;
import com.bees360.job.util.AbstractAsyncJobExecutor;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.util.concurrent.SettableListenableFuture;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

@SpringBootTest
@SpringJUnitConfig
@Log4j2
@DirtiesContext
public class AddImageAnnotationJobTest {

    @Configuration
    @Import(value = {InMemoryJobScheduler.class})
    static class Config {

        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired private JobDispatcher jobDispatcher;
    @Autowired private JobScheduler jobScheduler;

    @Test
    @SneakyThrows
    void testMapInferaAnnotationJob() {
        final SettableListenableFuture<BatchAddImageAnnotationJob> jobResult =
                new SettableListenableFuture<>();
        var jobExecutor =
                new AbstractAsyncJobExecutor<BatchAddImageAnnotationJob>() {
                    @Override
                    protected ListenableFuture<Void> accept(BatchAddImageAnnotationJob job) {
                        return Futures.submit(() -> setResult(job), MoreExecutors.directExecutor());
                    }

                    private void setResult(BatchAddImageAnnotationJob job) {
                        jobResult.set(job);
                    }
                };
        jobDispatcher.enlist(jobExecutor);
        var imageId = randomImageId();
        var userId = randomUserId();
        var tagId = String.valueOf(ImageTagEnum.OUTDOOR_KITCHEN.getCode());
        var attribute = attributeToJson(randomAttribute());
        var points = getPointMessages();
        var addImageAnnotation =
                new BatchAddImageAnnotationJob.AddImageAnnotation(
                        imageId,
                        List.of(
                                new BatchAddImageAnnotationJob.Annotation(
                                        tagId, attribute, points)));
        jobScheduler.schedule(
                Job.ofPayload(new BatchAddImageAnnotationJob(List.of(addImageAnnotation), userId)));

        var batchAddimageAnnotationJob = jobResult.get(20, TimeUnit.SECONDS);
        assertEquals(1, batchAddimageAnnotationJob.getImageAnnotations().size());
        assertEquals(userId, batchAddimageAnnotationJob.getUserId());
        var imageAnnotationJob = batchAddimageAnnotationJob.getImageAnnotations().get(0);
        assertEquals(imageId, imageAnnotationJob.getImageId());
        assertEquals(1, imageAnnotationJob.getAnnotations().size());

        var imageAnnotation = imageAnnotationJob.getAnnotations().get(0);
        assertEquals(tagId, imageAnnotation.getTagId());
        assertEquals(attribute, imageAnnotation.getAttribute());
        assertEquals(points.size(), Iterables.toList(imageAnnotation.getPolygon()).size());
    }
}
