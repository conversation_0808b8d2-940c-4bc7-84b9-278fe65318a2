package com.bees360.image;

import static com.bees360.image.ImageJooqQueryUtil.buildNewQueryJsonString;
import static com.bees360.image.RandomImageUtil.randomImageGroupId;
import static com.bees360.image.RandomImageUtil.randomImageSources;
import static com.bees360.image.RandomImageUtil.randomUserId;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_TAG;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_TAG_RELATION;
import static com.bees360.jooq.persistent.image.tables.ImageGroup.IMAGE_GROUP;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.jooq.persistent.image.enums.ImageTagCategory;
import com.bees360.util.Iterables;

import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest
@ApplicationAutoConfig
@SpringJUnitConfig
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
class ImageJooqQueryUtilTest {

    @Configuration
    @Import({JooqConfig.class})
    static class Config {
        @Bean
        ImageManager imageManager(DSLContext dsl) {
            return new JooqImageManager(dsl);
        }

        @Bean
        ImageGroupManager imageGroupManager(DSLContext dsl, ImageManager imageManager) {
            return new JooqImageGroupManager(dsl, imageManager);
        }

        @Bean
        ImageTagManager imageTagManager(DSLContext dsl) {
            return new JooqImageTagManager(
                    dsl, List.of("Annotation"), 100, List.of(ImageTagCategory.Annotation));
        }
    }

    @Autowired DSLContext dsl;

    @Autowired ImageManager imageManager;

    @Autowired ImageGroupManager imageGroupManager;

    @Autowired ImageTagManager imageTagManager;

    private final String GROUP_PROJECT_TYPE = "GROUP_PROJECT";

    private final List<String> nonAggFields = List.of("id", "projectId");
    private final Map<String, String> fieldQueryMap =
            Map.of(
                    "id",
                    "\"public\".\"image_group\".\"image_id\"",
                    "projectId",
                    "\"public\".\"image_group\".\"group_type\" = '"
                            + GROUP_PROJECT_TYPE
                            + "' and \"public\".\"image_group\".\"group_id\"",
                    "tag",
                    "cast(array_agg(\"public\".\"image_tag\".\"title\") as varchar[])",
                    "category",
                    "cast(array_agg(case\n"
                            + "  when cast(\"public\".\"image_tag\".\"category\" as varchar) <>"
                            + " 'Annotation' then cast(\"public\".\"image_tag\".\"category\" as"
                            + " varchar)\n"
                            + "  else \"public\".\"image_tag\".\"description\"\n"
                            + "end) as varchar[])");

    @Test
    void testEqQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test EQ
        var query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\": %s}}]}",
                        spliceJsonStringByList(groupId));
        var imageIdsByQueryCondition = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(imageIds.size(), imageIdsByQueryCondition.size());
        Assertions.assertTrue(imageIds.containsAll(imageIdsByQueryCondition));

        query =
                String.format(
                        "{\"AND\":[{\"id\":{\"EQ\": %s}}]}",
                        spliceJsonStringByList(imageIds.get(0)));
        imageIdsByQueryCondition = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(1, imageIdsByQueryCondition.size());
        Assertions.assertEquals(imageIds.get(0), imageIdsByQueryCondition.get(0));
    }

    @Test
    void testNeQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test NE
        var query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\": %s}},{\"id\":{\"NE\": %s}}]}",
                        spliceJsonStringByList(groupId), spliceJsonStringByList(imageIds.get(0)));
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(imageIds.get(1), result.get(0));
    }

    @Test
    void testGtQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test GT
        var query = String.format("{\"AND\":[{\"projectId\":{\"GT\": %s}}]}", groupId);
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertTrue(
                !result.contains(imageIds.get(0)) && !result.contains(imageIds.get(1)));
    }

    @Test
    void testGteQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test GTE
        var query = String.format("{\"AND\":[{\"projectId\":{\"GTE\": %s}}]}", groupId);
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertTrue(result.size() >= imageIds.size());
    }

    @Test
    void testLtQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test LT
        var query = String.format("{\"AND\":[{\"projectId\":{\"LT\": %s}}]}", groupId);
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertTrue(
                !result.contains(imageIds.get(0)) && !result.contains(imageIds.get(1)));
    }

    @Test
    void testLteQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test LTE
        var query = String.format("{\"AND\":[{\"projectId\":{\"LTE\": %s}}]}", groupId);
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertTrue(result.size() >= imageIds.size());
    }

    @Test
    void testInQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test IN
        var query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\": %s}},{\"id\":{\"IN\": [%s]}}]}",
                        spliceJsonStringByList(groupId), spliceJsonStringByList(imageIds.get(0)));
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(imageIds.get(0), result.get(0));

        query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"IN\": [%s]}},{\"id\":{\"IN\": [%s]}}]}",
                        spliceJsonStringByList(groupId), spliceJsonStringByList(imageIds.get(0)));
        result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(imageIds.get(0), result.get(0));
    }

    @Test
    void testNinQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test NIN
        var query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\": %s}},{\"id\":{\"NIN\": [%s]}}]}",
                        spliceJsonStringByList(groupId), spliceJsonStringByList(imageIds.get(0)));
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(imageIds.get(1), result.get(0));

        query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"IN\": [%s]}},{\"id\":{\"NIN\": [%s]}}]}",
                        spliceJsonStringByList(groupId), spliceJsonStringByList(imageIds.get(0)));
        result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(imageIds.get(1), result.get(0));
    }

    @Test
    void testLikeQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test LIKE
        var query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"LIKE\": %s}}]}",
                        spliceJsonStringByList(groupId));
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(2, result.size());
        Assertions.assertTrue(imageIds.containsAll(result));

        query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\": %s}},{\"id\":{\"LIKE\": %s}}]}",
                        spliceJsonStringByList(groupId), spliceJsonStringByList(imageIds.get(0)));
        result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(imageIds.get(0), result.get(0));
    }

    @Test
    void testExistsQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test EXIST
        var query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\": %s}},{\"id\":{\"EXISTS\": false}}]}",
                        spliceJsonStringByList(groupId));
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(2, result.size());
        Assertions.assertTrue(imageIds.containsAll(result));

        query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\": %s}},{\"id\":{\"EXISTS\": true}}]}",
                        spliceJsonStringByList(groupId));
        result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(0, result.size());
    }

    @Test
    void testIsNullQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test IS_NULL
        var query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\": %s}},{\"id\":{\"IS_NULL\": false}}]}",
                        spliceJsonStringByList(groupId));
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(2, result.size());
        Assertions.assertTrue(imageIds.containsAll(result));

        query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\": %s}},{\"id\":{\"IS_NULL\": true}}]}",
                        spliceJsonStringByList(groupId));
        result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(0, result.size());
    }

    @Test
    void testMultiConditionJoinQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // test AND,OR,EQ,INC,EXC
        var query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\":"
                            + " %s}},{\"AND\":[{\"tag\":{\"INC\":[%s]}},{\"category\":{\"INC\":[%s]}}]}]}",
                        spliceJsonStringByList(groupId),
                        spliceJsonStringByList(ImageTagEnum.FOUNDATION.getDisplay()),
                        spliceJsonStringByList(ImageTagCategoryEnum.SCOPE.getDisplay()));
        var result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(0, result.size());

        query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\":"
                            + " %s}},{\"OR\":[{\"tag\":{\"INC\":[%s]}},{\"category\":{\"INC\":[%s]}}]}]}",
                        spliceJsonStringByList(groupId),
                        spliceJsonStringByList(ImageTagEnum.FOUNDATION.getDisplay()),
                        spliceJsonStringByList(ImageTagCategoryEnum.SCOPE.getDisplay()));
        result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(2, result.size());
        Assertions.assertTrue(imageIds.containsAll(result));

        query =
                String.format(
                        "{\"AND\":[{\"projectId\":{\"EQ\":"
                            + " %s}},{\"category\":{\"INC\":[%s]}},{\"category\":{\"EXC\":[%s]}},{\"tag\":{\"INC\":[%s]}}]}",
                        spliceJsonStringByList(groupId),
                        spliceJsonStringByList(ImageTagCategoryEnum.CATEGORY.getDisplay()),
                        spliceJsonStringByList(ImageTagCategoryEnum.COMPONENT.getDisplay()),
                        spliceJsonStringByList(ImageTagEnum.OVERVIEW.getDisplay()));
        result = getImageIdsByQueryCondition(query);
        Assertions.assertEquals(1, result.size());
        Assertions.assertTrue(imageIds.contains(result.get(0)));
    }

    @Test
    void testIllegalArgsQueryCondition() {
        var groupId = randomImageGroupId();
        var imageIds = loadTestImage(groupId);
        // Default id or projectId field.
        var e = assertThrows(IllegalArgumentException.class, () -> getImageIdsByQueryCondition(""));
        assertEquals(e.getMessage(), "Either the imageId or the projectId field must be provided.");

        assertThrows(NullPointerException.class, () -> getImageIdsByQueryCondition(null));

        e = assertThrows(IllegalArgumentException.class, () -> getImageIdsByQueryCondition("{}"));
        assertEquals(e.getMessage(), "Either the imageId or the projectId field must be provided.");

        // error test：query 查询字段值一级查询运算符必须是 AND
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"OR\":[{\"tag\":{\"EXC\":[\"Damage\"]}}]}"));
        assertEquals(e.getMessage(), "The condition field must start with the AND operator.");

        // error test：query 查询字段值一级查询运算符 AND 值必须包含 id 或者 projectId 等非聚合字段的查询。
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":[{\"tag\":{\"EXC\":[\"Damage\"]}}]}"));
        assertEquals(e.getMessage(), "Either the imageId or the projectId field must be provided.");

        // error test: 非法操作符
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":[{\"projectId\":{\"throw\": \"123\"}}]}"));
        assertEquals(e.getMessage(), "projectId invalid operator key: throw");

        // error test: 非法的json字符串
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":{\"tag\":{\"EXC\":[\"Damage\""));
        assertEquals(e.getMessage(), "Invalid json string: {\"AND\":{\"tag\":{\"EXC\":[\"Damage\"");

        // error test: AND values should be json arrays
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":{\"tag\":{\"EXC\":[\"Damage\"]}}}"));
        assertEquals(
                e.getMessage(), "The values for the logical operators AND should be json arrays.");

        // error test: OR values should be json arrays
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":[{\"projectId\":{\"EQ\":"
                                            + " %s}},{\"OR\":{\"tag\":{\"EXC\":[\"Damage\"]}}}]}"));
        assertEquals(
                e.getMessage(), "The values for the logical operators OR should be json arrays.");

        // error test: 操作符的值不合规
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":[{\"projectId\":{\"EXISTS\": \"true\"}}]}"));
        assertEquals(
                e.getMessage(), "Invalid operator value for operator 'EXISTS/IS_NULL': \"true\"");
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":[{\"projectId\":{\"IS_NULL\": \"true\"}}]}"));
        assertEquals(
                e.getMessage(), "Invalid operator value for operator 'EXISTS/IS_NULL': \"true\"");
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":[{\"projectId\":{\"IN\": \"1\"}}]}"));
        assertEquals(e.getMessage(), "Invalid operator value for operator 'IN': \"1\"");
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":[{\"projectId\":{\"IN\": []}}]}"));
        assertEquals(e.getMessage(), "Invalid operator value for operator 'IN': []");
        e =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                getImageIdsByQueryCondition(
                                        "{\"AND\":[{\"projectId\":{\"LIKE\": {\"str\":1}}}]}"));
        assertEquals(e.getMessage(), "Invalid operator value for operator 'LIKE': {\"str\":1}");
    }

    private List<String> getImageIdsByQueryCondition(String queryJsonString) {
        var whereCondition =
                ImageJooqQueryUtil.buildWhereCondition(
                        DSL.noCondition(), queryJsonString, nonAggFields, fieldQueryMap::get);
        var newQueryJsonString = buildNewQueryJsonString(queryJsonString);
        return dsl.select(IMAGE_GROUP.IMAGE_ID)
                .from(IMAGE_GROUP)
                .leftJoin(IMAGE_TAG_RELATION)
                .on(IMAGE_GROUP.IMAGE_ID.eq(IMAGE_TAG_RELATION.IMAGE_ID))
                .leftJoin(IMAGE_TAG)
                .on(IMAGE_TAG.ID.eq(IMAGE_TAG_RELATION.TAG_ID))
                .where(whereCondition)
                .groupBy(
                        IMAGE_GROUP.IMAGE_ID,
                        IMAGE_GROUP.GROUP_ID,
                        IMAGE_GROUP.GROUP_TYPE,
                        IMAGE_GROUP.SEQ_NO)
                .having(
                        ImageJooqQueryUtil.joinQueryCondition(
                                DSL.noCondition(),
                                newQueryJsonString,
                                nonAggFields,
                                fieldQueryMap::get))
                .orderBy(IMAGE_GROUP.SEQ_NO.asc())
                .fetch(IMAGE_GROUP.IMAGE_ID);
    }

    private List<String> loadTestImage(String groupId) {
        var groupType = GROUP_PROJECT_TYPE;
        var imageSources = randomImageSources();
        var userId = randomUserId();

        var images = Iterables.toList(imageManager.createImages(imageSources, userId));

        var imageIds = images.stream().map(Image::getId).collect(Collectors.toList());
        imageGroupManager.addImagesToGroup(groupId, groupType, imageIds, userId);
        var imagesFind = imageGroupManager.getAllImageInGroup(groupId, groupType);
        Assertions.assertNotNull(imagesFind);
        Assertions.assertEquals(imageIds.size(), Iterables.toList(imagesFind).size());

        imageIds.forEach(
                imageId ->
                        imageTagManager.addImageTag(
                                imageId,
                                List.of(String.valueOf(ImageTagEnum.ELEVATION.getCode())),
                                userId));
        imageTagManager.addImageTag(
                imageIds.get(0), List.of(String.valueOf(ImageTagEnum.OVERVIEW.getCode())), userId);
        imageTagManager.addImageTag(
                imageIds.get(1),
                List.of(
                        String.valueOf(ImageTagEnum.HAIL_DAMAGE.getCode()),
                        String.valueOf(ImageTagEnum.FOUNDATION.getCode())),
                userId);
        return imageIds;
    }

    private String spliceJsonStringByList(String... list) {
        return Arrays.stream(list)
                .map(x -> String.format("\"%s\"", x))
                .collect(Collectors.joining(","));
    }
}
