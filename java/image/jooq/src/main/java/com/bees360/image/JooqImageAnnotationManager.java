package com.bees360.image;

import static com.bees360.image.JooqImageApiManager.IMAGE_ANNOTATION_NOT_DELETED_CONDITION;
import static com.bees360.image.JooqImageApiManager.IMAGE_ANNOTATION_SELECT_STEP_NAME;
import static com.bees360.image.JooqImageApiManager.IMAGE_TAG_NOT_DELETED_CONDITION;
import static com.bees360.image.JooqImageApiManager.getAnnotationSourceTypeProvider;
import static com.bees360.image.RecordImageApi.ANNOTATIONS_FIELD_ALIAS;
import static com.bees360.image.RecordImageApi.ANNOTATION_TAGS_FIELD_ALIAS;
import static com.bees360.image.RecordImageApi.IMAGE_ANNOTATION_FIELDS;
import static com.bees360.image.RecordImageApi.IMAGE_TAG_DICT_FIELDS;
import static com.bees360.image.RecordImageApi.getAnnotationsByAnnotationRecords;
import static com.bees360.image.util.AttributeMessageAdapter.DEFAULT_ATTRIBUTE_JSON;
import static com.bees360.image.util.AttributeMessageAdapter.jsonToAttribute;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_ANNOTATION;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_TAG;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_TAG_RELATION;

import static org.jooq.JSONB.jsonb;
import static org.jooq.impl.DSL.arrayAgg;
import static org.jooq.impl.DSL.coalesce;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;
import static org.jooq.impl.DSL.row;
import static org.jooq.impl.DSL.select;
import static org.jooq.impl.DSL.table;
import static org.jooq.impl.DSL.val;

import com.bees360.api.Proto;
import com.bees360.api.common.Point;
import com.bees360.image.tag.ImageTag;
import com.bees360.jooq.persistent.image.tables.records.ImageAnnotationRecord;
import com.bees360.jooq.util.DSLUtils;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.CaseConditionStep;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record;
import org.jooq.Record2;
import org.jooq.Record7;
import org.jooq.Select;
import org.jooq.SelectSelectStep;
import org.jooq.WithStep;
import org.jooq.impl.DSL;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/20
 */
@Log4j2
public class JooqImageAnnotationManager implements ImageAnnotationManager {
    public static final String ORIGIN_ATTRIBUTE = "origin_attribute";
    public static final String ORIGIN_ANNOTATION_ALIAS = "origin";
    public static final Field<JSONB> ORIGIN_ANNOTATION_ATTRIBUTE_FIELD =
            coalesce(
                    field(
                            name(ORIGIN_ANNOTATION_ALIAS, IMAGE_ANNOTATION.ATTRIBUTE.getName()),
                            IMAGE_ANNOTATION.ATTRIBUTE.getDataType()),
                    IMAGE_ANNOTATION.ATTRIBUTE);
    public static final String ANNOTATION_ATTRIBUTE = "annotations_attribute";

    private final DSLContext dsl;

    private final JooqImageTagManager jooqImageTagManager;

    public JooqImageAnnotationManager(DSLContext dsl, JooqImageTagManager jooqImageTagManager) {
        this.dsl = dsl;
        this.jooqImageTagManager = jooqImageTagManager;
    }

    @Transactional
    @Override
    public Iterable<? extends ImageTag> saveAll(
            Iterable<? extends ImageAnnotation> imageAnnotations, String createdBy) {
        log.info(
                "Save annotation with parameters: imageAnnotations = {}, createdBy = {}",
                Iterables.transform(imageAnnotations, Proto::toMessage),
                createdBy);
        var annotationList = Iterables.toList(imageAnnotations);
        Select<Record7<String, String, Point[], String, String, Integer, JSONB>> select = null;
        for (var annotation : annotationList) {
            var polygon = Iterables.toList(annotation.getPolygon());
            if (polygon.size() < 2) {
                throw new IllegalArgumentException("Polygon data format error.");
            }
            if (select == null) {
                select = getBaseInsertSelect(annotation);
            } else {
                select = select.unionAll(getBaseInsertSelect(annotation));
            }
            annotation.getImageId();
        }
        Preconditions.checkArgument(select != null, "Annotations cannot be empty.");

        var imageIdAnnotations =
                annotationList.stream().collect(Collectors.groupingBy(ImageAnnotation::getImageId));
        var imageIdTags =
                Maps.transformValues(
                        imageIdAnnotations,
                        annotations ->
                                annotations.stream()
                                        .map(
                                                a ->
                                                        Message.ImageTagRequest.AddTag.newBuilder()
                                                                .setTagId(a.getTagId())
                                                                .setAttribute(
                                                                        a.toMessage()
                                                                                .getAttribute())
                                                                .build())
                                        .collect(Collectors.toSet()));
        var imageTagRequests =
                imageIdTags.entrySet().stream()
                        .map(
                                entry ->
                                        Message.ImageTagRequest.newBuilder()
                                                .setImageId(entry.getKey())
                                                .addAllTag(entry.getValue())
                                                .build())
                        .map(ImageTagRequest::from)
                        .collect(Collectors.toList());
        jooqImageTagManager.addAllImageTag(imageTagRequests, createdBy);

        var insertSelect =
                dsl.select(
                                IMAGE_TAG_RELATION.ID.as("imageTagId"),
                                select.field("imageId", IMAGE_ANNOTATION.IMAGE_ID.getDataType()),
                                select.field("tagId", IMAGE_ANNOTATION.TAG_ID.getDataType()),
                                DSL.field("polygon", IMAGE_ANNOTATION.POLYGON.getDataType()),
                                DSL.field(
                                        "description", IMAGE_ANNOTATION.DESCRIPTION.getDataType()),
                                select.field(
                                        "originAnnotationId",
                                        IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID.getDataType()),
                                select.field(
                                        "sourceType", IMAGE_ANNOTATION.SOURCE_TYPE.getDataType()),
                                select.field(
                                        ORIGIN_ATTRIBUTE, IMAGE_ANNOTATION.ATTRIBUTE.getDataType()),
                                val(createdBy),
                                val(createdBy))
                        .from(select)
                        .leftJoin(IMAGE_TAG_RELATION)
                        .on(
                                IMAGE_TAG_RELATION.IMAGE_ID.eq(
                                        select.field("imageId", String.class)),
                                IMAGE_TAG_RELATION.TAG_ID.eq(select.field("tagId", String.class)),
                                IMAGE_TAG_RELATION.IS_DELETED.eq(0L));

        var annotationIds =
                dsl.insertInto(
                                IMAGE_ANNOTATION,
                                IMAGE_ANNOTATION.IMAGE_TAG_ID,
                                IMAGE_ANNOTATION.IMAGE_ID,
                                IMAGE_ANNOTATION.TAG_ID,
                                IMAGE_ANNOTATION.POLYGON,
                                IMAGE_ANNOTATION.DESCRIPTION,
                                IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID,
                                IMAGE_ANNOTATION.SOURCE_TYPE,
                                IMAGE_ANNOTATION.ATTRIBUTE,
                                IMAGE_ANNOTATION.CREATED_BY,
                                IMAGE_ANNOTATION.UPDATED_BY)
                        .select(insertSelect)
                        .returningResult(IMAGE_ANNOTATION.ID)
                        .fetch(IMAGE_ANNOTATION.ID);

        var imageIds =
                annotationList.stream()
                        .map(ImageAnnotation::getImageId)
                        .collect(Collectors.toList());
        var tagIds =
                annotationList.stream().map(ImageAnnotation::getTagId).collect(Collectors.toList());

        return findTags(
                annotationIds,
                IMAGE_TAG_RELATION.IMAGE_ID.in(imageIds),
                IMAGE_TAG_RELATION.TAG_ID.in(tagIds),
                IMAGE_ANNOTATION.ID.in(annotationIds),
                IMAGE_TAG_RELATION.IS_DELETED.eq(0L));
    }

    @Override
    public void deleteAll(Iterable<String> ids, String deletedBy) {
        log.info("Delete annotation with parameters: ids = {}, deletedBy = {}", ids, deletedBy);
        dsl.update(IMAGE_ANNOTATION)
                .set(IMAGE_ANNOTATION.IS_DELETED, Boolean.TRUE)
                .set(IMAGE_ANNOTATION.UPDATED_BY, deletedBy)
                .where(IMAGE_ANNOTATION.ID.in(Iterables.toCollection(ids)))
                .execute();
    }

    @Transactional
    @Override
    public void updateAnnotationSort(Map<String, Integer> annotationSorts, String updatedBy) {
        log.info(
                "Update annotation sort with parameters: annotationSorts = {}, updatedBy = {}",
                annotationSorts,
                updatedBy);
        // update imageId annotations origin report sort.
        var records =
                annotationSorts.entrySet().stream()
                        .map(
                                entry -> {
                                    var id = entry.getKey();
                                    var seqNo = entry.getValue();
                                    var record = new ImageAnnotationRecord();
                                    record.setId(id);
                                    record.setSeqNo(seqNo);
                                    record.setUpdatedBy(updatedBy);
                                    return record;
                                })
                        .collect(Collectors.toList());
        dsl.batchUpdate(records).execute();
    }

    @Override
    public void update(UpdateAnnotationRequest updateAnnotationRequest) {
        log.info(
                "Update annotation with parameters: updateAnnotationRequest = {}",
                updateAnnotationRequest.toMessage());
        var annotations = updateAnnotationRequest.getAnnotation();
        var updatedBy = updateAnnotationRequest.getUpdatedBy();

        var originAnnotationIds =
                Iterables.toStream(annotations)
                        .map(UpdateAnnotation::getOriginAnnotationId)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(originAnnotationIds)) {
            var tempIds =
                    dsl.select(IMAGE_ANNOTATION.ID)
                            .from(IMAGE_ANNOTATION)
                            .where(IMAGE_ANNOTATION.ID.in(originAnnotationIds))
                            .and(IMAGE_ANNOTATION.IS_DELETED.eq(false))
                            .fetch(IMAGE_ANNOTATION.ID);
            var tempNewOriginIds = new ArrayList<>(originAnnotationIds);
            tempNewOriginIds.removeAll(tempIds);
            if (CollectionUtils.isNotEmpty(tempNewOriginIds)) {
                throw new IllegalArgumentException(
                        String.format(
                                "Cannot update originAnnotationId: New origin annotation ids [%s]"
                                        + " not found.",
                                tempNewOriginIds));
            }
        }

        // update image annotations.
        var ids = new HashSet<String>();
        var processedAttributeIds = new HashSet<String>();
        CaseConditionStep<Point[]> updateAnnotationPolygonCase = null;
        CaseConditionStep<String> updateAnnotationDescriptionCase = null;
        CaseConditionStep<String> updateAnnotationOriginAnnotationIdCase = null;
        CaseConditionStep<JSONB> updateAnnotationAttributeCase = null;
        for (UpdateAnnotation annotation : annotations) {
            var id = annotation.getId();
            var polygon = annotation.getPolygon();
            var description = annotation.getDescription();
            var originAnnotationId = annotation.getOriginAnnotationId();
            var attribute = annotation.getAttribute();

            if (Iterables.isNotEmpty(polygon)) {
                updateAnnotationPolygonCase =
                        resetUpdateAnnotationPolygonCase(updateAnnotationPolygonCase, id, polygon);
            }
            if (Objects.nonNull(description)) {
                var descriptionField = val(description, IMAGE_ANNOTATION.DESCRIPTION.getDataType());
                updateAnnotationDescriptionCase =
                        resetUpdateAnnotationCase(
                                updateAnnotationDescriptionCase, id, descriptionField);
            }
            if (Objects.nonNull(originAnnotationId)) {
                var originAnnotationIdField =
                        val(
                                originAnnotationId,
                                IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID.getDataType());
                updateAnnotationOriginAnnotationIdCase =
                        resetUpdateAnnotationCase(
                                updateAnnotationOriginAnnotationIdCase,
                                id,
                                originAnnotationIdField);
            }
            if (Objects.nonNull(attribute)
                    && (Objects.isNull(originAnnotationId)
                            || Objects.equals(id, originAnnotationId))) {
                updateAnnotationAttributeCase =
                        resetUpdateAnnotationAttributeCase(
                                updateAnnotationAttributeCase, id, attribute, originAnnotationId);
                processedAttributeIds.add(id);
            }
            ids.add(id);
        }
        // 处理 originAnnotationId non null 的 attribute
        var originIdWithAttribute = getOriginIdWithAttribute(annotations);

        if (originIdWithAttribute != null && !originIdWithAttribute.isEmpty()) {
            for (Map.Entry<String, String> entry : originIdWithAttribute.entrySet()) {
                var originId = entry.getKey();
                var attribute = entry.getValue();
                if (processedAttributeIds.contains(originId)) {
                    continue;
                }

                updateAnnotationAttributeCase =
                        resetUpdateAnnotationAttributeCase(
                                updateAnnotationAttributeCase, originId, attribute);
                ids.add(originId);
            }
        }

        if (!ids.isEmpty()) {
            var update = dsl.update(IMAGE_ANNOTATION);
            if (updateAnnotationDescriptionCase != null) {
                update.set(
                        IMAGE_ANNOTATION.DESCRIPTION,
                        updateAnnotationDescriptionCase.otherwise(IMAGE_ANNOTATION.DESCRIPTION));
            }
            if (updateAnnotationOriginAnnotationIdCase != null) {
                update.set(
                        IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID,
                        updateAnnotationOriginAnnotationIdCase.otherwise(
                                IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID));
            }
            if (updateAnnotationPolygonCase != null) {
                update.set(
                        IMAGE_ANNOTATION.POLYGON,
                        updateAnnotationPolygonCase.otherwise(IMAGE_ANNOTATION.POLYGON));
            }
            if (updateAnnotationAttributeCase != null) {
                update.set(
                        IMAGE_ANNOTATION.ATTRIBUTE,
                        updateAnnotationAttributeCase.otherwise(IMAGE_ANNOTATION.ATTRIBUTE));
            }

            update.set(IMAGE_ANNOTATION.UPDATED_BY, updatedBy)
                    .where(IMAGE_ANNOTATION.ID.in(ids))
                    .execute();
        }
    }

    private Map<String, String> getOriginIdWithAttribute(
            Iterable<? extends UpdateAnnotation> annotations) {
        if (Iterables.isEmpty(annotations)) {
            return Map.of();
        }
        // get idWithAttribute by annotations
        var idWithAttribute =
                Iterables.toStream(annotations)
                        .filter(a -> Objects.nonNull(a.getAttribute()))
                        .collect(
                                Collectors.toMap(
                                        UpdateAnnotation::getId,
                                        UpdateAnnotation::getAttribute,
                                        (o1, o2) -> o1));
        Iterables.toStream(annotations)
                .filter(
                        a ->
                                Objects.nonNull(a.getOriginAnnotationId())
                                        && !Objects.equals(a.getId(), a.getOriginAnnotationId()))
                .filter(a -> Objects.nonNull(a.getAttribute()))
                .forEach(
                        a -> {
                            var id = a.getId();
                            var originAnnotationId = a.getOriginAnnotationId();
                            var attribute = a.getAttribute();
                            if (idWithAttribute.containsKey(id)) {
                                idWithAttribute.remove(id);
                            }
                            if (!idWithAttribute.containsKey(originAnnotationId)) {
                                idWithAttribute.put(originAnnotationId, attribute);
                            }
                        });
        if (MapUtils.isEmpty(idWithAttribute)) {
            return Map.of();
        }

        var allIds = idWithAttribute.keySet();
        var result =
                dsl.select(IMAGE_ANNOTATION.ID, IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID)
                        .from(IMAGE_ANNOTATION)
                        .where(IMAGE_ANNOTATION.ID.in(allIds))
                        .and(IMAGE_ANNOTATION.IS_DELETED.eq(false))
                        .fetch();
        var originIdWithAttribute = new HashMap<>(idWithAttribute);
        for (Record2<String, String> record2 : result) {
            var id = record2.get(IMAGE_ANNOTATION.ID);
            var originAnnotationId = record2.get(IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID);
            if (Objects.nonNull(originAnnotationId)
                    && !Objects.equals(id, originAnnotationId)
                    && idWithAttribute.containsKey(id)) {
                originIdWithAttribute.put(originAnnotationId, idWithAttribute.get(id));
            }
        }
        log.info(
                "idWithAttribute:{}, originIdWithAttribute:{}",
                idWithAttribute,
                originIdWithAttribute);
        return originIdWithAttribute;
    }

    @Override
    public List<ImageApiAnnotation> findByIds(Iterable<String> ids) {
        var annotationAttributeFields =
                new ArrayList<>(List.of(IMAGE_ANNOTATION.ID, ORIGIN_ANNOTATION_ATTRIBUTE_FIELD));

        var sourceTypeProvider = getAnnotationSourceTypeProvider(dsl);
        var imageAnnotationSelect =
                select(
                                arrayAgg(field(row(IMAGE_ANNOTATION_FIELDS)))
                                        .as(ANNOTATIONS_FIELD_ALIAS),
                                arrayAgg(field(row(IMAGE_TAG_DICT_FIELDS)))
                                        .as(ANNOTATION_TAGS_FIELD_ALIAS),
                                arrayAgg(field(row(annotationAttributeFields)))
                                        .as(ANNOTATION_ATTRIBUTE))
                        .from(IMAGE_ANNOTATION)
                        .leftJoin(IMAGE_TAG)
                        .on(IMAGE_TAG.ID.eq(IMAGE_ANNOTATION.TAG_ID))
                        // Self join to get the attribute from the origin_annotation_id
                        .leftJoin(IMAGE_ANNOTATION.as(ORIGIN_ANNOTATION_ALIAS))
                        .on(
                                IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID.eq(
                                        field(
                                                name(ORIGIN_ANNOTATION_ALIAS, "id"),
                                                IMAGE_ANNOTATION.ID.getDataType())))
                        .where(
                                IMAGE_ANNOTATION
                                        .ID
                                        .in(Iterables.toList(ids))
                                        .and(IMAGE_ANNOTATION_NOT_DELETED_CONDITION)
                                        .and(IMAGE_TAG_NOT_DELETED_CONDITION));

        WithStep withStep = dsl.with(IMAGE_ANNOTATION_SELECT_STEP_NAME).as(imageAnnotationSelect);
        var table = table(IMAGE_ANNOTATION_SELECT_STEP_NAME);
        return withStep.select(
                        table.field(ANNOTATIONS_FIELD_ALIAS, Record[].class),
                        table.field(ANNOTATION_TAGS_FIELD_ALIAS, Record[].class),
                        table.field(ANNOTATION_ATTRIBUTE, Record[].class))
                .from(table)
                .fetchStream()
                .map(
                        r -> {
                            var annotationRecords =
                                    getAnnotationsByAnnotationRecords(
                                            (Record[]) r.get(0),
                                            (Record[]) r.get(1),
                                            sourceTypeProvider);
                            var annotationAttributeRecords = (Record[]) r.get(2);
                            var idWithAttribute = new HashMap<String, String>();
                            if (annotationAttributeRecords != null) {
                                Arrays.stream(annotationAttributeRecords)
                                        .forEach(
                                                a -> {
                                                    var id = (String) a.get(0);
                                                    var attribute = (String) a.get(1);
                                                    idWithAttribute.put(id, attribute);
                                                });
                            }
                            return resetAttributeField(annotationRecords, idWithAttribute);
                        })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private List<ImageApiAnnotation> resetAttributeField(
            List<ImageApiAnnotation> annotations, Map<String, String> idWithAttribute) {
        if (Iterables.isEmpty(annotations)) {
            return annotations;
        }
        return annotations.stream()
                .map(
                        a -> {
                            var originId = a.getOriginAnnotationId();
                            if (StringUtils.isBlank(originId)
                                    || !idWithAttribute.containsKey(originId)) {
                                return a;
                            }
                            var attribute = idWithAttribute.get(originId);
                            var newAnnotationMessage =
                                    a.toMessage().toBuilder()
                                            .setAttribute(jsonToAttribute(attribute))
                                            .build();
                            return ImageApiAnnotation.from(newAnnotationMessage);
                        })
                .collect(Collectors.toList());
    }

    private CaseConditionStep<JSONB> resetUpdateAnnotationAttributeCase(
            CaseConditionStep<JSONB> updateAnnotationAttributeCase, String id, String attribute) {
        return resetUpdateAnnotationAttributeCase(
                updateAnnotationAttributeCase, id, attribute, null);
    }

    private CaseConditionStep<JSONB> resetUpdateAnnotationAttributeCase(
            CaseConditionStep<JSONB> updateAnnotationAttributeCase,
            String id,
            String attribute,
            String originAnnotationId) {
        var condition = IMAGE_ANNOTATION.ID.eq(id);
        if (Objects.isNull(originAnnotationId)) {
            condition =
                    condition.and(
                            IMAGE_ANNOTATION
                                    .ORIGIN_ANNOTATION_ID
                                    .isNull()
                                    .or(
                                            IMAGE_ANNOTATION.ID.eq(
                                                    IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID)));
        }
        return resetUpdateAnnotationAttributeCase(
                updateAnnotationAttributeCase, condition, attribute);
    }

    private CaseConditionStep<JSONB> resetUpdateAnnotationAttributeCase(
            CaseConditionStep<JSONB> updateAnnotationAttributeCase,
            Condition condition,
            String attribute) {
        var attributeJsonb = jsonb(attribute);
        if (updateAnnotationAttributeCase == null) {
            updateAnnotationAttributeCase = DSL.when(condition, attributeJsonb);
        } else {
            updateAnnotationAttributeCase.when(condition, attributeJsonb);
        }
        return updateAnnotationAttributeCase;
    }

    private CaseConditionStep<Point[]> resetUpdateAnnotationPolygonCase(
            CaseConditionStep<Point[]> updateAnnotationCase,
            String id,
            Iterable<? extends Point> pointList) {
        var polygonField =
                val(
                        Iterables.toArray(pointList, Point[]::new),
                        IMAGE_ANNOTATION.POLYGON.getDataType());
        if (updateAnnotationCase == null) {
            updateAnnotationCase = DSL.when(IMAGE_ANNOTATION.ID.eq(id), polygonField);
        } else {
            updateAnnotationCase.when(IMAGE_ANNOTATION.ID.eq(id), polygonField);
        }
        return updateAnnotationCase;
    }

    private CaseConditionStep<String> resetUpdateAnnotationCase(
            CaseConditionStep<String> updateAnnotationCase, String id, Field<String> objectField) {
        if (updateAnnotationCase == null) {
            updateAnnotationCase = DSL.when(IMAGE_ANNOTATION.ID.eq(id), objectField);
        } else {
            updateAnnotationCase.when(IMAGE_ANNOTATION.ID.eq(id), objectField);
        }
        return updateAnnotationCase;
    }

    private SelectSelectStep<Record7<String, String, Point[], String, String, Integer, JSONB>>
            getBaseInsertSelect(ImageAnnotation annotation) {
        var originAnnotationId = annotation.getOriginAnnotationId();
        var attributeJsonb =
                Optional.ofNullable(annotation.getAttribute())
                        .map(JSONB::jsonb)
                        .orElse(jsonb(DEFAULT_ATTRIBUTE_JSON));
        return dsl.select(
                val(annotation.getImageId()).as("imageId"),
                val(annotation.getTagId(), IMAGE_TAG_RELATION.TAG_ID.getDataType()).as("tagId"),
                val(
                                Iterables.toArray(annotation.getPolygon(), Point[]::new),
                                IMAGE_ANNOTATION.POLYGON.getDataType())
                        .as("polygon"),
                val(annotation.getDescription()).as("description"),
                val(originAnnotationId, IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID.getDataType())
                        .as("originAnnotationId"),
                val(annotation.getSourceType(), IMAGE_ANNOTATION.SOURCE_TYPE.getDataType())
                        .as("sourceType"),
                val(attributeJsonb, IMAGE_ANNOTATION.ATTRIBUTE.getDataType()).as(ORIGIN_ATTRIBUTE));
    }

    private Collection<RecordImageTag> findTags(
            List<String> annotationIds, Condition... condition) {
        var fields = new ArrayList<>(List.of(IMAGE_ANNOTATION.fields()));
        fields.addAll(List.of(IMAGE_TAG.fields()));
        fields.addAll(List.of(IMAGE_TAG_RELATION.fields()));
        fields.add(ORIGIN_ANNOTATION_ATTRIBUTE_FIELD.as(ORIGIN_ATTRIBUTE));

        var annotationTagSelect =
                DSLUtils.commonSelect(
                        dsl.select(fields.toArray(Field[]::new))
                                .from(IMAGE_ANNOTATION)
                                .leftJoin(IMAGE_TAG)
                                .on(IMAGE_ANNOTATION.TAG_ID.eq(IMAGE_TAG.ID))
                                .innerJoin(IMAGE_TAG_RELATION)
                                .on(IMAGE_TAG_RELATION.ID.eq(IMAGE_ANNOTATION.IMAGE_TAG_ID))
                                // Self join to get the attribute from the origin_annotation_id
                                .leftJoin(IMAGE_ANNOTATION.as(ORIGIN_ANNOTATION_ALIAS))
                                .on(
                                        IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID.eq(
                                                field(
                                                        name(ORIGIN_ANNOTATION_ALIAS, "id"),
                                                        IMAGE_ANNOTATION.ID.getDataType()))),
                        condition);
        return DSLUtils.mapReduce(
                        annotationTagSelect
                                .orderBy(
                                        IMAGE_ANNOTATION.ID.sortAsc(
                                                Iterables.toCollection(annotationIds)))
                                .stream(),
                        RecordImageTag::new,
                        record -> record.get(IMAGE_TAG_RELATION.ID),
                        (recordAnnotation, record) -> {
                            if (StringUtils.isNotBlank(record.get(IMAGE_ANNOTATION.ID))) {
                                var attribute =
                                        Optional.ofNullable(record.get(ORIGIN_ATTRIBUTE))
                                                .map(Object::toString)
                                                .orElse(null);
                                recordAnnotation.addAnnotation(
                                        new RecordImageAnnotation(record, attribute));
                            }
                            return recordAnnotation;
                        })
                .values();
    }
}
