package com.bees360.image;

import static com.bees360.jooq.persistent.image.Tables.IMAGE_ANNOTATION;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_NOTE;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_RESOURCE;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_TAG;
import static com.bees360.jooq.persistent.image.Tables.IMAGE_TAG_RELATION;
import static com.bees360.jooq.persistent.image.tables.Image.IMAGE;
import static com.bees360.jooq.persistent.image.tables.ImageGroup.IMAGE_GROUP;
import static com.bees360.util.Defaults.nullIfEmpty;

import com.bees360.api.common.BoundingBox;
import com.bees360.api.common.Point;
import com.bees360.image.tag.ImageApiTag;
import com.bees360.jooq.PolygonConverter;
import com.bees360.jooq.persistent.image.enums.ImageTagCategory;
import com.bees360.jooq.persistent.image.tables.records.ImageAnnotationRecord;
import com.bees360.jooq.persistent.image.tables.records.ImageGroupRecord;
import com.bees360.jooq.persistent.image.tables.records.ImageNoteRecord;
import com.bees360.jooq.persistent.image.tables.records.ImageResourceRecord;
import com.bees360.jooq.persistent.image.tables.records.ImageTagRecord;
import com.bees360.jooq.persistent.image.tables.records.ImageTagRelationRecord;
import com.bees360.util.ETagArray;
import com.google.gson.Gson;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import lombok.NonNull;

import org.jooq.Field;
import org.jooq.Record;
import org.jooq.Record12;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/1/24
 */
class RecordImageApi implements ImageApi {
    static final Field[] IMAGE_GROUP_FIELDS =
            List.of(IMAGE_GROUP.GROUP_ID, IMAGE_GROUP.GROUP_TYPE).toArray(Field[]::new);
    static final Field[] IMAGE_RESOURCE_FIELDS = IMAGE_RESOURCE.fields();
    static final Field[] IMAGE_TAG_RELATION_FIELDS = IMAGE_TAG_RELATION.fields();
    static final Field[] IMAGE_ANNOTATION_FIELDS = IMAGE_ANNOTATION.fields();
    static final Field[] IMAGE_TAG_DICT_FIELDS = IMAGE_TAG.fields();
    static final Field[] IMAGE_NOTE_FIELDS = IMAGE_NOTE.fields();

    static final String GROUPS_FIELD_ALIAS = "groups";
    static final String NOTES_FIELD_ALIAS = "notes";
    static final String RESOURCES_FIELD_ALIAS = "resources";
    static final String RELATIONS_FIELD_ALIAS = "relations";
    static final String ANNOTATIONS_FIELD_ALIAS = "annotations";
    static final String RELATION_TAGS_FIELD_ALIAS = "relation_tags";
    static final String ANNOTATION_TAGS_FIELD_ALIAS = "annotation_tags";
    static final String IMAGE_TOTAL_COUNT_ALIAS = "total_count";

    private static final Gson GSON = new Gson();

    private final Record imageRecord;

    private final String attribute;

    private final List<ImageApiResource> resources;

    private final List<ImageApiAnnotation> annotations;

    private final List<ImageNote> notes;

    RecordImageApi(
            Record imageRecord,
            String attribute,
            List<ImageApiResource> resources,
            List<ImageApiAnnotation> annotations,
            List<ImageNote> notes) {
        this.imageRecord = imageRecord;
        this.attribute = attribute;
        this.resources = resources;
        this.annotations = annotations;
        this.notes = notes;
    }

    RecordImageApi(
            Record12<
                            String,
                            Integer,
                            Instant,
                            Instant,
                            Record[],
                            Record[],
                            Record[],
                            Record[],
                            Record[],
                            Record[],
                            Record[],
                            Integer>
                    imageApiRecord,
            AtomicInteger totalCountAtomicInteger,
            Function<Integer, String> sourceTypeProvider) {
        Record[] groupsRecords = (Record[]) imageApiRecord.get(GROUPS_FIELD_ALIAS);
        Record[] resourceRecords = (Record[]) imageApiRecord.get(RESOURCES_FIELD_ALIAS);
        Record[] relationRecords = (Record[]) imageApiRecord.get(RELATIONS_FIELD_ALIAS);
        Record[] relationTagRecords = (Record[]) imageApiRecord.get(RELATION_TAGS_FIELD_ALIAS);
        Record[] annotationRecords = (Record[]) imageApiRecord.get(ANNOTATIONS_FIELD_ALIAS);
        Record[] annotationTagRecords = (Record[]) imageApiRecord.get(ANNOTATION_TAGS_FIELD_ALIAS);
        Record[] noteRecords = (Record[]) imageApiRecord.get(NOTES_FIELD_ALIAS);
        Integer totalCount = (Integer) imageApiRecord.get(IMAGE_TOTAL_COUNT_ALIAS);
        totalCountAtomicInteger.set(Objects.requireNonNull(totalCount).intValue());

        String attribute = getAttributeByRecords(groupsRecords);
        var resources = getResourcesByRecords(resourceRecords);

        var relations =
                getAnnotationsByRelationRecords(
                        relationRecords, relationTagRecords, sourceTypeProvider);
        var commonAnnotations =
                getAnnotationsByAnnotationRecords(
                        annotationRecords, annotationTagRecords, sourceTypeProvider);
        var annotations =
                new ArrayList<ImageApiAnnotation>(relations.size() + commonAnnotations.size());
        annotations.addAll(relations);
        annotations.addAll(commonAnnotations);

        var notes = getNotesByRecords(noteRecords);

        this.imageRecord = imageApiRecord;
        this.attribute = attribute;
        this.resources = resources;
        this.annotations = annotations;
        this.notes = notes;
    }

    @Override
    public String getId() {
        return imageRecord.get(IMAGE.ID);
    }

    @Override
    public String getAttribute() {
        return attribute;
    }

    @Override
    public int getTiffOrientation() {
        return imageRecord.get(IMAGE.TIFF_ORIENTATION);
    }

    @Override
    public Instant getShootingTime() {
        return imageRecord.get(IMAGE.SHOOTING_TIME);
    }

    @Override
    public Instant getCreatedAt() {
        return imageRecord.get(IMAGE.CREATED_AT);
    }

    @Override
    public Iterable<? extends ImageApiResource> getResources() {
        return resources;
    }

    @Override
    public Iterable<? extends ImageApiAnnotation> getAnnotations() {
        return annotations;
    }

    @Override
    public Iterable<? extends ImageNote> getNotes() {
        return notes;
    }

    private List<ImageApiAnnotation> getAnnotationsByRelationRecords(
            Record[] annotationRecords,
            Record[] annotationTagRecords,
            Function<Integer, String> sourceTypeProvider) {
        List<ImageApiAnnotation> annotations = new ArrayList<>();
        if (annotationRecords != null) {
            for (int i = 0; i < annotationRecords.length; i++) {
                var relationRecord = annotationRecords[i];
                var targetRelationRecord =
                        resetValueToTargetRecord(
                                relationRecord,
                                IMAGE_TAG_RELATION_FIELDS,
                                new ImageTagRelationRecord());

                var relationTagRecord = annotationTagRecords[i];
                var targetTagDictRecord =
                        resetValueToTargetRecord(
                                relationTagRecord, IMAGE_TAG_DICT_FIELDS, new ImageTagRecord());

                var annotation =
                        new RecordImageApiAnnotation(
                                targetRelationRecord, targetTagDictRecord, sourceTypeProvider);
                annotations.add(annotation);
            }
        }
        return annotations;
    }

    static List<ImageApiAnnotation> getAnnotationsByAnnotationRecords(
            Record[] annotationRecords,
            Record[] annotationTagRecords,
            Function<Integer, String> sourceTypeProvider) {
        List<ImageApiAnnotation> annotations = new ArrayList<>();
        if (annotationRecords != null) {
            for (int i = 0; i < annotationRecords.length; i++) {
                var annotationRecord = annotationRecords[i];
                var targetAnnotationRecord =
                        resetValueToTargetRecord(
                                annotationRecord,
                                IMAGE_ANNOTATION_FIELDS,
                                new ImageAnnotationRecord());

                var tagRecord = annotationTagRecords[i];
                var targetTagDictRecord =
                        resetValueToTargetRecord(
                                tagRecord, IMAGE_TAG_DICT_FIELDS, new ImageTagRecord());

                var annotation =
                        new RecordImageApiAnnotation(
                                targetAnnotationRecord, targetTagDictRecord, sourceTypeProvider);
                annotations.add(annotation);
            }
        }
        return annotations;
    }

    private List<ImageApiResource> getResourcesByRecords(Record[] resourceRecords) {
        List<ImageApiResource> resources = new ArrayList<>();
        if (resourceRecords != null) {
            for (Record resourceRecord : resourceRecords) {
                var targetResourceRecord =
                        resetValueToTargetRecord(
                                resourceRecord, IMAGE_RESOURCE_FIELDS, new ImageResourceRecord());
                var resource = new RecordImageApiResource(targetResourceRecord);
                resources.add(resource);
            }
        }
        return resources;
    }

    private List<ImageNote> getNotesByRecords(Record[] noteRecords) {
        var resources = new ArrayList<ImageNote>();
        if (noteRecords != null) {
            for (Record record : noteRecords) {
                var targetResourceRecord =
                        resetValueToTargetRecord(record, IMAGE_NOTE_FIELDS, new ImageNoteRecord());
                var resource = new RecordImageNote(targetResourceRecord);
                resources.add(resource);
            }
        }
        return resources;
    }

    private String getAttributeByRecords(Record[] groupsRecords) {
        String attribute = null;
        if (groupsRecords != null) {
            Map<String, List<String>> groups =
                    Arrays.stream(groupsRecords)
                            .map(
                                    r ->
                                            resetValueToTargetRecord(
                                                    r, IMAGE_GROUP_FIELDS, new ImageGroupRecord()))
                            .collect(
                                    Collectors.groupingBy(
                                            x -> x.get(IMAGE_GROUP.GROUP_TYPE),
                                            Collectors.mapping(
                                                    x -> x.get(IMAGE_GROUP.GROUP_ID),
                                                    Collectors.toList())));
            attribute = GSON.toJson(groups);
        }
        return attribute;
    }

    private static Record resetValueToTargetRecord(
            Record record, Field[] fields, Record targetRecord) {
        for (int i = 0; i < record.size(); i++) {
            var value = record.get(i);
            var targetField = fields[i];
            // 处理 Long[] 类型的特殊逻辑
            if (targetField.getType() == Long[].class && value instanceof String) {
                Long[] convertedValue = convertStringToArray((String) value);
                targetRecord.set(targetField, convertedValue);
            } else if (targetField == IMAGE_ANNOTATION.POLYGON) {
                Point[] points = new PolygonConverter().converter().from(value);
                targetRecord.set(targetField, points);
            } else {
                // 使用 convert 方法进行显式转换
                Object convertedValue = targetField.getDataType().convert(value);
                targetRecord.set(targetField, convertedValue);
            }
        }
        return targetRecord;
    }

    /** 特殊处理 Long[] 类型的转换 */
    private static Long[] convertStringToArray(String stringValue) {
        // 去除大括号，并根据逗号分隔字符串
        String[] elements = stringValue.replaceAll("[{}]", "").split(",");

        // 将字符串数组转换为 Long 数组
        Long[] result = Arrays.stream(elements).map(Long::parseLong).toArray(Long[]::new);

        return result;
    }
}

class RecordImageApiAnnotation implements ImageApiAnnotation {

    private final Record record;

    private final RecordImageApiTag tag;

    private final Iterable<? extends Point> polygon;

    private final BoundingBox boundingBox;

    private final Function<Integer, String> sourceTypeProvider;

    RecordImageApiAnnotation(
            Record record, Record tagDictRecord, Function<Integer, String> sourceTypeProvider) {
        this.record = record;
        this.tag = new RecordImageApiTag(tagDictRecord);
        this.sourceTypeProvider = sourceTypeProvider;
        if (record instanceof ImageAnnotationRecord) {
            this.polygon = Arrays.asList(record.get(IMAGE_ANNOTATION.POLYGON));
            this.boundingBox = BoundingBox.from(polygon);
        } else {
            // 默认是全图
            this.polygon =
                    Arrays.asList(Point.of(0, 0), Point.of(0, 1), Point.of(1, 1), Point.of(1, 0));
            this.boundingBox = BoundingBox.of(0.5, 0.5, 1, 1);
        }
    }

    @Override
    public String getId() {
        return record instanceof ImageAnnotationRecord
                ? record.get(IMAGE_ANNOTATION.ID)
                : record.get(IMAGE_TAG_RELATION.ID);
    }

    @Override
    public String getCreatedBy() {
        return record instanceof ImageAnnotationRecord
                ? record.get(IMAGE_ANNOTATION.CREATED_BY)
                : record.get(IMAGE_TAG_RELATION.CREATED_BY);
    }

    @Override
    public Instant getCreatedAt() {
        return record instanceof ImageAnnotationRecord
                ? record.get(IMAGE_ANNOTATION.CREATED_AT).toInstant()
                : record.get(IMAGE_TAG_RELATION.CREATED_AT);
    }

    @Override
    public ImageApiTag getTag() {
        return tag;
    }

    @Override
    public Iterable<? extends Point> getPolygon() {
        return polygon;
    }

    @Override
    public BoundingBox getBoundingBox() {
        return boundingBox;
    }

    @Nullable
    @Override
    public String getOriginAnnotationId() {
        return record instanceof ImageAnnotationRecord
                ? nullIfEmpty(record.get(IMAGE_ANNOTATION.ORIGIN_ANNOTATION_ID))
                : null;
    }

    @Nullable
    @Override
    public String getSourceType() {
        return record instanceof ImageAnnotationRecord
                ? sourceTypeProvider.apply(record.get(IMAGE_ANNOTATION.SOURCE_TYPE))
                : null;
    }

    @Nullable
    @Override
    public String getAttribute() {
        var attribute =
                record instanceof ImageAnnotationRecord
                        ? record.get(IMAGE_ANNOTATION.ATTRIBUTE)
                        : record.get(IMAGE_TAG_RELATION.ATTRIBUTE);
        return Optional.ofNullable(attribute).map(Object::toString).orElse(null);
    }
}

class RecordImageApiResource implements ImageApiResource {

    private final Record resourceRecord;

    private final String eTag;

    RecordImageApiResource(Record resourceRecord) {
        this.resourceRecord = resourceRecord;
        this.eTag =
                ETagArray.toETag(
                        resourceRecord.get(IMAGE_RESOURCE.MD5),
                        resourceRecord.get(IMAGE_RESOURCE.SLICE_COUNT));
    }

    @NonNull
    @Override
    public Message.ImageMessage.Resource.Type getType() {
        return Message.ImageMessage.Resource.Type.forNumber(
                resourceRecord.get(IMAGE_RESOURCE.TYPE));
    }

    @Nullable
    @Override
    public String getETag() {
        return eTag;
    }

    @Nullable
    @Override
    public Long getContentLength() {
        return resourceRecord.get(IMAGE_RESOURCE.CONTENT_LENGTH);
    }

    @Nullable
    @Override
    public Integer getWidth() {
        return resourceRecord.get(IMAGE_RESOURCE.WIDTH);
    }

    @Nullable
    @Override
    public Integer getHeight() {
        return resourceRecord.get(IMAGE_RESOURCE.HEIGHT);
    }
}

class RecordImageApiTag implements ImageApiTag {
    private final Record imageTagRecord;

    public RecordImageApiTag(Record imageTag) {
        this.imageTagRecord = imageTag;
    }

    @Nullable
    @Override
    public String getId() {
        return imageTagRecord.get(IMAGE_TAG.ID);
    }

    @Nonnull
    @Override
    public String getTitle() {
        return imageTagRecord.get(IMAGE_TAG.TITLE);
    }

    @Nonnull
    @Override
    public String getCategory() {
        var category = imageTagRecord.get(IMAGE_TAG.CATEGORY);
        if (category != ImageTagCategory.Annotation) {
            return category.getLiteral();
        }
        return imageTagRecord.get(IMAGE_TAG.DESCRIPTION);
    }
}
