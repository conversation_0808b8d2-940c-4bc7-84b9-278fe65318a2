package com.bees360.image.config;

import com.bees360.grpc.ExceptionTranslatedGrpcApi;
import com.bees360.image.GrpcImageClient;
import com.bees360.image.ImageServiceGrpc.ImageServiceBlockingStub;
import com.bees360.image.ImageServiceGrpc.ImageServiceStub;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/2/17
 */
@Configuration
public class GrpcImageClientConfig {

    @GrpcClient("imageManager")
    private ImageServiceBlockingStub imageServiceBlockingStub;

    @GrpcClient("imageManager")
    private ImageServiceStub imageServiceStub;

    @Bean
    public GrpcImageClient grpcImageClient() {
        return new GrpcImageClient(
                ExceptionTranslatedGrpcApi.of(imageServiceBlockingStub), imageServiceStub);
    }
}
