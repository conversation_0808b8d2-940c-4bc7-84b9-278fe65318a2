Configuration:
  status: WARN

  Properties: # 定义全局变量
    Property:
      - name: APP_NAME #服务名称
        value: image
      - name: PATTERN
        value: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS'Z'}{UTC},[%thread],%5p,%c,%L,%ex{short}{separator(|)},%replace{%m}{\n}{|},%ex{separator(|)}%n"
      - name: LOG_PATH
        value: /var/bees360/www/logs
      - name: LOG_FILE_NAME
        value: ${LOG_PATH}/${APP_NAME}.log
      - name: LOG_FILE_PATTERN
        value: ${LOG_PATH}/${APP_NAME}-%d{yyyy-MM-dd}-%i.log

  Appenders:
    Console:  #输出到控制台
      name: console
      target: SYSTEM_OUT
      PatternLayout:
        pattern: ${PATTERN}

    RollingFile: # 输出到文件，超过256MB归档
      - name: RollingFile
        ignoreExceptions: false
        fileName: ${LOG_FILE_NAME}
        filePattern: ${LOG_FILE_PATTERN}
        PatternLayout:
          pattern: ${PATTERN}
        Policies:
          SizeBasedTriggeringPolicy:
            size: "256 MB"
        DefaultRolloverStrategy:
          max: 1000

    Async:
      - name: asyncFileAppender
        AppenderRef:
          - ref: RollingFile

  Loggers:
    Root:
      level: INFO
      AppenderRef:
        - ref: console
        - ref: asyncFileAppender
