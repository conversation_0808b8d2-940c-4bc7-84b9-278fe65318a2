package com.bees360.image;

import com.bees360.image.tag.ImageTag;
import com.bees360.image.util.ForwardingImageTagManager;

import lombok.extern.log4j.Log4j2;

import java.util.Map;

@Log4j2
public class FullImageTagManager extends ForwardingImageTagManager {

    private final ImageTagManager jooqImageTagManager;

    private final ImageTagProvider mysqlImageTagProvider;

    public FullImageTagManager(
            ImageTagManager jooqImageTagManager, ImageTagProvider mysqlImageTagProvider) {
        this.jooqImageTagManager = jooqImageTagManager;
        this.mysqlImageTagProvider = mysqlImageTagProvider;
        log.info(
                "Created '{}(jooqImageTagManager='{}', mysqlImageTagProvider='{}')'",
                this,
                this.jooqImageTagManager,
                this.mysqlImageTagProvider);
    }

    @Override
    protected ImageTagManager delegate() {
        return jooqImageTagManager;
    }

    @Override
    public Map<String, Iterable<? extends ImageTag>> findByImageIds(Iterable<String> imageIds) {
        return mysqlImageTagProvider.findByImageIds(imageIds);
    }

    @Override
    public Map<String, Iterable<? extends ImageTag>> findInHistory(Iterable<String> imageIds) {
        return mysqlImageTagProvider.findInHistory(imageIds);
    }
}
