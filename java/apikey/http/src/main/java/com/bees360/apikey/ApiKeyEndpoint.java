package com.bees360.apikey;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

import com.bees360.api.NotFoundException;
import com.bees360.auth.CustomTokenReader;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.util.Iterables;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.springframework.context.annotation.Import;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@RestController
@RequestMapping("${http.apikey.endpoint:api-key}")
@Import({
    ProtoHttpMessageConverterConfig.class,
    ApiExceptionHandler.class,
})
public class ApiKeyEndpoint {
    private static final String TOKEN_COMPANY_ID = "company_id";
    private final ApiKeyManager apiKeyManager;
    private final CustomTokenReader customTokenReader;

    public ApiKeyEndpoint(ApiKeyManager apiKeyManager, CustomTokenReader customTokenReader) {
        this.apiKeyManager = apiKeyManager;
        this.customTokenReader = customTokenReader;
        log.info(
                "Created {}(apiKeyManager={}, customTokenReader={}).",
                this,
                this.apiKeyManager,
                this.customTokenReader);
    }

    /**
     * Create an api key and returns the created api key
     *
     * @param userId the id of operating user
     * @param message api key message
     * @return the api key created
     */
    @PostMapping
    public ApiKeyResponse createApiKey(
            @AuthenticationPrincipal(expression = "id") String userId,
            @RequestBody Message.ApiKeyMessage message) {
        var apiKeyBuilder = message.toBuilder();
        // when companyId has no value, use the company id of operating user
        if (Message.ApiKeyMessage.getDefaultInstance()
                .getCompanyId()
                .equals(message.getCompanyId())) {
            apiKeyBuilder.setCompanyId(customTokenReader.getByKey(TOKEN_COMPANY_ID));
        }
        // when createdBy has no value, use the id of operating user
        if (Message.ApiKeyMessage.getDefaultInstance()
                .getCreatedBy()
                .equals(message.getCreatedBy())) {
            apiKeyBuilder.setCreatedBy(userId);
        }
        message = apiKeyBuilder.build();
        var apiKeyResponse = new ApiKeyResponse();
        apiKeyResponse.setApiKey(apiKeyManager.saveApiKey(ApiKey.from(message)));
        return apiKeyResponse;
    }

    /**
     * Update api key by id
     *
     * @param userId the id of operating user
     * @param apiKeyId the id of api key
     * @param message api key message
     */
    @PutMapping("/{apiKeyId:\\d+}")
    public void updateApiKey(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String apiKeyId,
            @RequestBody Message.ApiKeyMessage message) {
        apiKeyManager.updateApiKey(
                apiKeyId, message.getDescription(), message.getScopeList(), userId);
    }

    /**
     * Disable api key by id
     *
     * @param userId the id of operating user
     * @param apiKeyId the id of api key
     */
    @DeleteMapping("/{apiKeyId:\\d+}")
    public void disableApiKey(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String apiKeyId) {
        var successful = apiKeyManager.disableApiKey(apiKeyId, userId);
        if (!successful) {
            throw new NotFoundException(
                    String.format(
                            "Cannot disable api key with id %s: No such api key exists.",
                            apiKeyId));
        }
    }

    /**
     * Find api keys visible to the current user
     *
     * @param apiKeyId the id of api key
     * @param companyId the id of company
     * @return the list of all visible api key
     */
    @GetMapping
    public ApiKeyResponse findApiKeys(
            @RequestParam(required = false) String apiKeyId,
            @RequestParam(required = false) String companyId) {
        // when companyId has no value, use the company id of operating user
        if (companyId == null) {
            companyId = customTokenReader.getByKey(TOKEN_COMPANY_ID);
        }
        var apiKeys = apiKeyManager.findApiKeysByCompanyId(companyId);
        if (apiKeyId != null) {
            apiKeys = Iterables.filter(apiKeys, apiKey -> apiKey.getId().equals(apiKeyId));
        }
        var apiKeyResponse = new ApiKeyResponse();
        apiKeyResponse.setApiKeys(Iterables.toStream(apiKeys).collect(Collectors.toList()));
        return apiKeyResponse;
    }

    /**
     * Generate an unique api key name
     *
     * @return generated api key name
     */
    @GetMapping("/key-name")
    public ApiKeyResponse generateKeyName() {
        var apiKeyResponse = new ApiKeyResponse();
        apiKeyResponse.setKeyName(apiKeyManager.generateApiKeyName());
        return apiKeyResponse;
    }

    /**
     * Get supported scope resources
     *
     * @return the list of supported scope resources
     */
    @GetMapping("/scope-resource")
    public ApiKeyResponse getScopeResources() {
        var scopeResources = apiKeyManager.getScopeResources();
        var apiKeyResponse = new ApiKeyResponse();
        apiKeyResponse.setScopeResources(
                Iterables.toStream(scopeResources).collect(Collectors.toList()));
        return apiKeyResponse;
    }

    @Data
    @JsonInclude(NON_NULL)
    public static class ApiKeyResponse {
        private String keyName;
        private ApiKey apiKey;
        private List<ApiKey> apiKeys;
        private List<ScopeResource> scopeResources;
    }
}
