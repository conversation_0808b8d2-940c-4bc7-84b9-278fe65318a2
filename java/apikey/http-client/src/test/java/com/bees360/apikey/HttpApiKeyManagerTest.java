package com.bees360.apikey;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.bees360.api.UnimplementedException;
import com.bees360.auth.CustomTokenReader;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.HttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.google.common.collect.Iterables;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

import java.util.List;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {HttpApiKeyManagerTest.Config.class})
@DirtiesContext
@EnableAutoConfiguration(
        exclude = {
            SecurityAutoConfiguration.class,
        })
public class HttpApiKeyManagerTest extends AbstractApiKeyManagerTest {

    public static final String COMPANY_ID = "company_id";
    public static final String TEST_COMPANY_ID = "testCompanyId";

    public static final String TEST_USER_ID = "testUserId";

    @Import({
        ApacheHttpClientConfig.class,
        ApiExceptionHandler.class,
        CustomerPrincipalResolverConfig.class,
        ApiKeyEndpoint.class,
        InMemoryApiKeyManager.class,
    })
    @Configuration
    static class Config {

        @Bean
        CustomTokenReader customTokenReader() {
            var mockTokenReader = mock(CustomTokenReader.class);
            doReturn(TEST_COMPANY_ID).when(mockTokenReader).getByKey(COMPANY_ID);
            return mockTokenReader;
        }
    }

    public HttpApiKeyManagerTest(@LocalServerPort int port, @Autowired HttpClient httpClient) {
        super(new HttpApiKeyManager("http://localhost:" + port, "api-key", httpClient));
    }

    @Test
    void testSaveThenFind() {
        // save then find
        var apiKey = randomApiKey(null, List.of("PROJECT.WRITE"));
        apiKey = updateApiKeyMessage(apiKey, null, null, "", null, null, null, "");
        var keyName = apiKey.getKeyName();
        var savedApiKey = delegate().saveApiKey(apiKey);
        assertNotNull(savedApiKey);
        var actualKey =
                Iterables.find(
                        findApiKeysByCompanyId(TEST_COMPANY_ID),
                        key -> keyName.equals(key.getKeyName()));
        apiKey =
                updateApiKeyMessage(
                        apiKey, null, null, TEST_COMPANY_ID, null, null, null, TEST_USER_ID);
        assertApiKeyEquals(apiKey, actualKey);
    }

    @Test
    void testSaveThenUpdate() {
        var userId = "testUserId";
        // save then find
        var apiKey = randomApiKey(null, List.of("PROJECT.WRITE"));
        apiKey = updateApiKeyMessage(apiKey, null, null, null, null, null, null, userId);
        var companyId = apiKey.getCompanyId();
        var keyName = apiKey.getKeyName();
        var savedApiKey = delegate().saveApiKey(apiKey);
        assertNotNull(savedApiKey);
        var expectedApiKey =
                Iterables.find(
                        findApiKeysByCompanyId(companyId), key -> keyName.equals(key.getKeyName()));
        // update then assert
        expectedApiKey =
                updateApiKeyMessage(
                        expectedApiKey,
                        null,
                        null,
                        null,
                        "",
                        List.of("PROJECT.READ", "PROJECT.WRITE"),
                        null,
                        null);
        delegate()
                .updateApiKey(
                        expectedApiKey.getId(),
                        expectedApiKey.getDescription(),
                        expectedApiKey.getScopes(),
                        userId);
        var actualKey =
                Iterables.find(
                        findApiKeysByCompanyId(companyId), key -> keyName.equals(key.getKeyName()));
        assertApiKeyEquals(expectedApiKey, actualKey);
    }

    @Test
    void testSaveThenDisable() {
        var userId = "testUserId";
        // save then find
        var apiKey = randomApiKey(null, List.of("PROJECT.WRITE"));
        apiKey = updateApiKeyMessage(apiKey, null, null, null, null, null, null, userId);
        var keyName = apiKey.getKeyName();
        var companyId = apiKey.getCompanyId();
        var savedApiKey = delegate().saveApiKey(apiKey);
        assertNotNull(savedApiKey);
        var expectedApiKey =
                Iterables.find(
                        findApiKeysByCompanyId(companyId), key -> keyName.equals(key.getKeyName()));
        expectedApiKey =
                updateApiKeyMessage(expectedApiKey, null, null, null, null, null, true, null);
        // disable then find disabled apikey
        assertTrue(delegate().disableApiKey(expectedApiKey.getId(), userId));
        var actualKey =
                Iterables.find(
                        findApiKeysByCompanyId(companyId), key -> keyName.equals(key.getKeyName()));
        assertApiKeyEquals(expectedApiKey, actualKey);
        // disable again will return false
        assertFalse(delegate().disableApiKey(expectedApiKey.getId(), userId));
    }

    @Test
    void testGetScopeResourcesThenAssert() {
        super.getScopeResourcesThenAssert();
    }

    @Test
    void testGenerateApiKeyNameThenCheck() {
        super.generateApiKeyNameThenCheck();
    }

    @Test
    void testUnImplementedFindApiKeyByKeyName() {
        var keyName = RandomStringUtils.randomAlphanumeric(16);
        assertThrows(UnimplementedException.class, () -> delegate().findApiKeyByKeyName(keyName));
    }
}
