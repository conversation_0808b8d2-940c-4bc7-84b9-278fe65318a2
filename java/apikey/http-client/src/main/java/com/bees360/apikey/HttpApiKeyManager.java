package com.bees360.apikey;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.ApiHttpClient;
import com.bees360.api.UnimplementedException;
import com.bees360.codec.IterableProtoDecoder;
import com.bees360.codec.ProtoGsonDecoder;
import com.bees360.http.HttpClient;
import com.bees360.http.util.URIs;
import com.bees360.util.Functions;
import com.google.gson.Gson;
import com.google.gson.annotations.JsonAdapter;

import jakarta.annotation.Nullable;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;

import org.apache.http.HttpHeaders;
import org.apache.http.HttpMessage;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;

import java.util.List;

@Log4j2
public class HttpApiKeyManager implements ApiKeyManager {

    private final String host;
    private final String endpoint;
    private final HttpClient httpClient;
    private final Gson gson = new Gson();

    public HttpApiKeyManager(String host, String endpoint, HttpClient httpClient) {
        this.host = host;
        this.endpoint = endpoint;
        this.httpClient = httpClient;
        log.info(
                "Created {}(host={}, endpoint={}, httpClient={}).",
                this,
                this.host,
                this.endpoint,
                this.httpClient);
    }

    @Override
    public ApiKey saveApiKey(ApiKey apiKey) {
        var uri = URIs.build(host, uriBuilder -> uriBuilder.setPath(endpoint));
        var request = new HttpPost(uri);
        request.setEntity(ApiHttpClient.protobufEntity(apiKey.toMessage()));
        var responseString = httpClient.execute(request, HttpClient::convertResponseToString);
        var apiKeyResponse = gson.fromJson(responseString, ApiKeyResponse.class);
        return apiKeyResponse.getApiKey();
    }

    @Override
    public void updateApiKey(
            String id, String description, Iterable<String> scopes, String updatedBy) {
        var builder = Message.ApiKeyMessage.newBuilder();
        acceptIfNotNull(builder::setDescription, description);
        acceptIfNotNull(builder::addAllScope, scopes);
        var uri = URIs.build(host, uriBuilder -> uriBuilder.setPath(endpoint + "/" + id));
        var request = new HttpPut(uri);
        setHeader(request);
        request.setEntity(ApiHttpClient.protobufEntity(builder.build()));
        httpClient.execute(
                request,
                Functions.combine(
                        HttpClient::throwNon2xxResponse, HttpClient::convertResponseToString));
    }

    @Override
    public Boolean disableApiKey(String id, String disabledBy) {
        var uri = URIs.build(host, uriBuilder -> uriBuilder.setPath(endpoint + "/" + id));
        var request = new HttpDelete(uri);
        setHeader(request);
        try {
            httpClient.execute(
                    request,
                    Functions.combine(
                            HttpClient::throwNon2xxResponse, HttpClient::convertResponseToString));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Iterable<? extends ApiKey> findApiKeysByCompanyId(String companyId) {
        var uri =
                URIs.build(
                        host,
                        uriBuilder ->
                                uriBuilder.setPath(endpoint).setParameter("companyId", companyId));
        var request = new HttpGet(uri);
        setHeader(request);
        var responseString =
                httpClient.execute(
                        request,
                        Functions.combine(
                                HttpClient::throwNon2xxResponse,
                                HttpClient::convertResponseToString));
        var apiKeyResponse = gson.fromJson(responseString, ApiKeyResponse.class);
        return apiKeyResponse.getApiKeys();
    }

    @Nullable
    @Override
    public ApiKey findApiKeyByKeyName(String keyName) {
        throw new UnimplementedException();
    }

    @Override
    public String generateApiKeyName() {
        var uri = URIs.build(host, uriBuilder -> uriBuilder.setPath(endpoint + "/key-name"));
        var request = new HttpGet(uri);
        setHeader(request);
        var responseString = httpClient.execute(request, HttpClient::convertResponseToString);
        var apiKeyResponse = gson.fromJson(responseString, ApiKeyResponse.class);
        return apiKeyResponse.getKeyName();
    }

    @Override
    public Iterable<? extends ScopeResource> getScopeResources() {
        var uri = URIs.build(host, uriBuilder -> uriBuilder.setPath(endpoint + "/scope-resource"));
        var request = new HttpGet(uri);
        setHeader(request);
        var responseString = httpClient.execute(request, HttpClient::convertResponseToString);
        var apiKeyResponse = gson.fromJson(responseString, ApiKeyResponse.class);
        return apiKeyResponse.getScopeResources();
    }

    private void setHeader(HttpMessage message) {
        message.setHeader(HttpHeaders.CONTENT_TYPE, "application/x-protobuf");
        message.setHeader(HttpHeaders.ACCEPT, "application/json");
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class ApiKeyResponse {
        private String keyName;

        @JsonAdapter(ProtoGsonDecoder.class)
        private ApiKey apiKey;

        @JsonAdapter(IterableProtoDecoder.class)
        private List<ApiKey> apiKeys;

        @JsonAdapter(IterableProtoDecoder.class)
        private List<ScopeResource> scopeResources;
    }
}
