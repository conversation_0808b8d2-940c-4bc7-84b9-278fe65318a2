package com.bees360.attachment;

import com.bees360.resource.ResourceGetUrlProvider;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.util.ContentTypes;
import com.bees360.util.Iterables;

import java.util.List;
import java.util.stream.Collectors;

public class FillResourceAttachmentManager extends ForwardingAttachmentManager {

    private final ResourcePool resourcePool;

    private final ResourceGetUrlProvider resourceGetUrlProvider;

    public FillResourceAttachmentManager(
            AttachmentManager attachmentManager,
            ResourcePool resourcePool,
            ResourceGetUrlProvider resourceGetUrlProvider) {
        super(attachmentManager);
        this.resourcePool = resourcePool;
        this.resourceGetUrlProvider = resourceGetUrlProvider;
    }

    @Override
    public Iterable<? extends Attachment> uploadAttachment(
            Iterable<? extends Attachment> attachments) {
        var fullAttachments =
                Iterables.toStream(attachments)
                        .map(this::fillResourceMetaData)
                        .collect(Collectors.toList());
        return delegate().uploadAttachment(fullAttachments);
    }

    @Override
    public List<? extends Attachment> findAllById(Iterable<String> ids) {
        return Iterables.toStream(delegate().findAllById(ids))
                .map(this::fillResourceUrl)
                .collect(Collectors.toList());
    }

    private Attachment fillResourceMetaData(Attachment attachment) {
        if (attachment.getMetadata() != null) {
            return attachment;
        }

        var builder = attachment.toMessage().toBuilder();
        var metadata = resourcePool.head(attachment.getUrl());

        if (metadata != null) {
            builder.setMetadata(metadata.toMessage());
        } else {
            throw new IllegalArgumentException(
                    String.format("Resource %s does not exists.", attachment.getFilename()));
        }

        return Attachment.from(builder.build());
    }

    private Attachment fillResourceUrl(Attachment attachment) {
        var builder = attachment.toMessage().toBuilder();

        var metadata = attachment.getMetadata();
        var key = metadata.getETag() + ContentTypes.getFileExtension(metadata.getContentType());
        var url = resourceGetUrlProvider.getGetUrl(key);

        if (url == null) {
            throw new IllegalArgumentException(
                    String.format("Resource %s does not exists.", attachment.getFilename()));
        }

        builder.setUrl(url.toString());
        return Attachment.from(builder.build());
    }
}
