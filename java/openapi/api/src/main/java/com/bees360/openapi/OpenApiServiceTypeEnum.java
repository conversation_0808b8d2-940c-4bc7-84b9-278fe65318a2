package com.bees360.openapi;

import com.bees360.project.ServiceTypeEnum;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum OpenApiServiceTypeEnum {
    ROOF_ONLY(ServiceTypeEnum.ROOF_ONLY, "Roof Only"),
    EXTERIOR(ServiceTypeEnum.EXTERIOR, "Exterior"),
    FOUR_POINT(ServiceTypeEnum.FOUR_POINT, "4-Point"),
    FOUR_POINT_SELF(ServiceTypeEnum.FOUR_POINT_SELF, "4-Point Self"),
    PREMIUM_FOUR_POINT(ServiceTypeEnum.PREMIUM_FOUR_POINT, "Premium 4-Point"),
    EXPRESS_UNDERWRITING(ServiceTypeEnum.EXPRESS_UNDERWRITING, "Express Underwriting"),
    WHITE_GLOVE(ServiceTypeEnum.WHITE_GLOVE, "High Value Premium 4-point"),
    ;

    private final ServiceTypeEnum serviceType;

    private final String name;

    public static OpenApiServiceTypeEnum valueOfServiceType(ServiceTypeEnum serviceType) {
        if (serviceType == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(e -> Objects.equals(e.serviceType, serviceType))
                .findFirst()
                .orElse(null);
    }

    public static OpenApiServiceTypeEnum valueOfName(String name) {
        if (name == null) {
            return null;
        }
        // todo : temporary for white glove service name change, should be removed after complete
        // change
        if (Objects.equals(name, "White Glove Service")) {
            return WHITE_GLOVE;
        }
        return Arrays.stream(values())
                .filter(e -> Objects.equals(e.name, name))
                .findFirst()
                .orElse(null);
    }

    OpenApiServiceTypeEnum(ServiceTypeEnum serviceType, String name) {
        this.serviceType = serviceType;
        this.name = name;
    }
}
