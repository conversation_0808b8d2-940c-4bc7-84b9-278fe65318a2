package com.bees360.openapi;

import com.bees360.api.Proto;
import com.bees360.openapi.Message.ReportMessage.Summary.Interior.Plumbing;

public interface SummaryInteriorPlumbing extends Proto<Plumbing> {
    /** If there are any plumbing fixtures without shut off valves Optional */
    Boolean getNoShutoffValve();
    /** If the water heater is older than 15 years Optional */
    Boolean getHasOldWaterHeater();
    /**
     * If the water heater is rusting or in poor condition, has exposed wires or no TPR Valve
     * Optional
     */
    Boolean getHasPoorWaterHeaterCondition();

    /**
     * Indicates if there is evidence that property has galvanized steel pipes based on visual
     * inspection. Galvanized steel pipes may be prone to flow restrictions due to internal
     * corrosion build up over time. example: true
     */
    Boolean getHasGalvanizedPipes();
    /** Whether the plumbing was updated */
    Boolean getIsUpdated();

    /** The year in which the plumbing was updated. */
    Integer getYearUpdated();

    /** Indicates the age and update status of the heating and cooling system in the property. */
    String getSystemUpdateStatus();

    Boolean getHasIneligiblePlumbing();
}
