package com.bees360.openapi;

import com.bees360.api.Proto;
import com.bees360.openapi.Message.ReportMessage.Summary.Exterior;

import java.util.Map;

public interface SummaryExterior extends Proto<Exterior> {
    /** Whether the overall condition of exterior is satisfactory. */
    String getOverallCondition();
    /**
     * Siding material and its corresponding percentage. The object's properties are the name of the
     * siding material, and the values represents its percentage. Possible values for siding
     * materials include ["Asbestos", "Vingl", "Metal", "Stone", "Brick Veneer", "Hardiplank",
     * "Stucco", "Concrete Block", "Wood Shake", "Aluminum", "Log", "Stone Veneer"].
     */
    Map<String, Integer> getSiding();
    /** Whether any shutters discovered. */
    Boolean getHasShutters();
    /** Whether property has porch. */
    Boolean getHasPorch();
    /** If stairs are present without hand rails Optional */
    Boolean getHasStairsWithoutHandRails();
    /** If yard has excessive yard debris/trash Optional */
    Boolean getHasYardDebris();
    /** If yard has unregistered vehicles on premises Optional */
    Boolean getHasDiscardedVehicles();
    /** If there are trees or tree limbs touching or overhanging home Optional */
    Boolean getHasTreeLimbs();
    /** If pool present does it have a 4 ft fence with self locking gate? Optional */
    Boolean getHasPoolWithoutFence();
    /** # of dogs over 40lbs Optional */
    Integer getNumDogPresent();
    /** If there is a dog or beware of dog sign Optional */
    Boolean getHasDogPresent();
    /** List of text comments to exterior. */
    Iterable<String> getComments();

    Iterable<? extends SummaryComparison> getComparison();
    /** If siding damage discovered */
    Boolean getHasSidingDamage();
    /** If wall cracks discovered */
    Boolean getHasWallCracks();
    /** If pealing paint on siding discovered */
    Boolean getHasPealingPaint();
    /** If window screen damage discovered */
    Boolean getHasWindowDamage();
    /** If water damage on exterior discovered */
    Boolean getHasWaterDamage();
    /** If chimney damage discovered */
    Boolean getHasChimneyDamage();
    /** If algae/moss discovered on roof */
    Boolean getHasMildewOrMoss();
    /** If swimming pool fence/cage discovered */
    Boolean getHasPoolCage();
    /** If swimming pool diving board discovered If swimming pool slide discovered */
    Boolean getHasDivingBoardOrSlide();
    /** Pet type with breed, such as Dog - Labrador Retriever, Cat, etc. */
    Iterable<String> getPetType();
    /** Provides the count of pet dog, pet cat, etc. */
    Map<String, Integer> getPet();
    /** If dog sign presented on the property */
    Boolean getHasDogSign();
    /** If pest activity discovered */
    Boolean getHasPestActivity();
    /** If trampoline discovered */
    Boolean getHasTrampoline();
    /** If watercraft discovered */
    Boolean getHasWatercraft();
    /** If there is a play/swing set in the exterior area. */
    Boolean getHasPlaySwingSet();
    /** If there is a tree house in the exterior area. */
    Boolean getHasTreeHouse();
    /** If there is a basketball hoop in the exterior area. */
    Boolean getHasBasketballHoop();
    /** If there is an ATV (all-terrain vehicle) in the exterior area. */
    Boolean getHasATV();
    /** If there is a skateboard or bike ramp in the exterior area. */
    Boolean getHasSkateboardOrBikeRamp();
    /** If there is a dirt bike in the exterior area. */
    Boolean getHasDirtBike();
    /** If there is a propane or fuel tank in the exterior area. */
    Boolean getHasPropaneOrFuelTank();
    /** If there is a swimming pool in the exterior area. */
    Boolean getHasSwimmingPool();
    /** If there is an awning in the exterior area. */
    Boolean getHasAwning();

    Boolean getIsEIFS();

    Boolean getHasPoolSelfLatchingGate();
}
