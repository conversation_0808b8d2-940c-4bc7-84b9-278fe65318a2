package com.bees360.openapi;

import com.bees360.address.Address;
import com.bees360.contract.Contract;
import com.bees360.customer.Customer;
import com.bees360.policy.Policy;
import com.bees360.project.Building;
import com.bees360.project.Contact;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.ProjectII;
import com.bees360.project.RandomContactUtil;
import com.bees360.project.RandomProjectUtil;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.underwriting.Underwriting;
import com.google.common.collect.ForwardingObject;

import lombok.Builder;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

abstract class OpenApiProjectCreatorTest extends ForwardingObject {

    @Override
    protected abstract OpenApiProjectCreator delegate();

    @Test
    void testCreateProjectShouldSucceed() {
        var builder = MockProjectCreationRequest.builder();
        ProjectII projectII = RandomProjectUtil.randomUwProject(selectServiceType());
        projectII = setMockAddress(projectII);
        String createdBy = projectII.getCreateBy().getId();
        Contract contract = projectII.getContract();
        Customer insuredBy = contract.getInsuredBy();
        Customer processedBy = contract.getProcessedBy();
        Contact randomInsured =
                RandomContactUtil.randomContact(ContactRoleEnum.INSURED.getName()).build();
        Contact randomAgent =
                RandomContactUtil.randomContact(ContactRoleEnum.AGENT.getName()).build();
        builder.createdBy(createdBy);
        builder.processedBy(processedBy);
        builder.insuredBy(insuredBy);
        builder.policy(projectII.getPolicy());
        builder.contacts(List.of(randomAgent, randomInsured));
        builder.underwriting(randomUnderwriting(projectII.getServiceType()));
        builder.inspection(
                Inspection.InspectionBuilder.newBuilder()
                        .setInspectionNo(projectII.getInspectionNo())
                        .build());
        MockProjectCreationRequest request = builder.build();

        Message.ProjectMessage project = delegate().createProject(request);
        asserts(project, projectII, randomInsured, randomAgent);
    }

    private ServiceTypeEnum selectServiceType() {
        var values = OpenApiServiceTypeEnum.values();
        return values[RandomUtils.nextInt(0, values.length)].getServiceType();
    }

    private static Underwriting randomUnderwriting(ServiceTypeEnum serviceTypeEnum) {
        return Underwriting.UnderwritingBuilder.newBuilder()
                .setServiceType(serviceTypeEnum)
                .build();
    }

    private static ProjectII setMockAddress(ProjectII projectII) {
        var address =
                Address.AddressBuilder.newBuilder()
                        .setLat(32.9416154)
                        .setLng(-97.3977666)
                        .setAddress("11820 Toppell Trl, Haslet, TX 76052, USA")
                        .setCity("Haslet")
                        .setCounty("Tarrant County")
                        .setState("TX")
                        .setCountry("US")
                        .setZip("76052")
                        .build();
        var projectMsg = projectII.toMessage();
        var addressMsg =
                projectMsg.getAddress().toBuilder()
                        .setCountry(address.getCountry())
                        .setCity(address.getCity())
                        .setState(address.getState())
                        .setZipCode(address.getZip())
                        .setAddressLine1(address.getAddress())
                        .build();
        return ProjectII.from(projectMsg.toBuilder().setAddress(addressMsg).build());
    }

    private static void asserts(
            Message.ProjectMessage result,
            ProjectII request,
            Contact expectedInsured,
            Contact expectedAgent) {
        ServiceTypeEnum serviceType = request.getServiceType();
        OpenApiServiceTypeEnum expectedServiceType =
                OpenApiServiceTypeEnum.valueOfServiceType(serviceType);

        com.bees360.project.Message.ProjectMessage projectMessage = request.toMessage();
        Assertions.assertEquals(projectMessage.getPolicy().getPolicyNo(), result.getPolicyNumber());
        Assertions.assertEquals(expectedServiceType.getName(), result.getServiceName());
        Assertions.assertEquals(projectMessage.getInspectionNumber(), result.getInspectionNumber());
        Assertions.assertEquals(
                projectMessage.getContract().getInsuredBy().getId(), result.getInsuredBy());

        Assertions.assertEquals(expectedInsured.getFullName(), result.getInsuredName());
        Assertions.assertEquals(expectedInsured.getPrimaryEmail(), result.getInsuredEmail());
        Assertions.assertEquals(expectedInsured.getPrimaryPhone(), result.getInsuredPhone());
        Assertions.assertEquals(expectedAgent.getFullName(), result.getAgentName());
        Assertions.assertEquals(expectedAgent.getPrimaryEmail(), result.getAgentEmail());
        Assertions.assertEquals(expectedAgent.getPrimaryPhone(), result.getAgentPhone());

        Policy expectedPolicy = request.getPolicy();
        Building building = expectedPolicy.getBuilding();
        Assertions.assertEquals(
                expectedPolicy.getPolicyEffectiveDate().toString(),
                result.getPolicyEffectiveDate());
        Assertions.assertEquals(building.getYearBuilt(), result.getYearBuilt());
        OpenApiBuildingTypeEnum openApiBuildingTypeEnum =
                OpenApiBuildingTypeEnum.valueOfBuildingType(building.getType());
        Assertions.assertEquals(openApiBuildingTypeEnum.getName(), result.getHouseType());

        Address address = expectedPolicy.getAddress();
        Assertions.assertEquals(address.getCountry(), result.getCountry());
        Assertions.assertEquals(address.getState(), result.getState());
        Assertions.assertEquals(address.getCity(), result.getCity());
        Assertions.assertEquals(address.getZip(), result.getZipcode());
        Assertions.assertEquals(address.getStreetAddress(), result.getStreetAddress());
    }

    @Builder
    static class MockProjectCreationRequest
            implements OpenApiProjectCreator.ProjectCreationRequest {

        private Customer insuredBy;

        private Customer processedBy;

        private String createdBy;

        private Policy policy;

        private List<Contact> contacts;

        private Inspection inspection;

        private Underwriting underwriting;

        @Override
        public Optional<Customer> getInsuredBy() {
            return Optional.ofNullable(insuredBy);
        }

        @Override
        public Optional<Customer> getProcessedBy() {
            return Optional.ofNullable(processedBy);
        }

        @Override
        public String getCreatedBy() {
            return createdBy;
        }

        @Override
        public Collection<Contact> getContacts() {
            return contacts;
        }

        @Override
        public Policy getPolicy() {
            return policy;
        }

        @Override
        public Optional<Inspection> getInspection() {
            return Optional.ofNullable(inspection);
        }

        @Override
        public Underwriting getUnderwriting() {
            return underwriting;
        }
    }
}
