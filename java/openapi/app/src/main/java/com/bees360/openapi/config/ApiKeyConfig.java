package com.bees360.openapi.config;

import co.realms9.bifrost.UserManager;
import co.realms9.bifrost.config.GrpcBifrostUserManagerConfig;

import com.bees360.apikey.ApiKeyManager;
import com.bees360.apikey.GrpcApiKeyService;
import com.bees360.apikey.OpenApiKeyManager;
import com.bees360.apikey.config.JooqApiKeyManagerConfig;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.UUID;
import java.util.function.Function;

@Import({
    GrpcBifrostUserManagerConfig.class,
    GrpcApiKeyService.class,
    JooqApiKeyManagerConfig.class,
})
@Configuration
public class ApiKeyConfig {

    @Bean("secretProvider")
    @ConditionalOnProperty(
            prefix = "apikey",
            name = "secret-provider",
            havingValue = "in-memory-md5",
            matchIfMissing = true)
    Function<String, Pair<String, String>> uuidSecretProvider() {
        return keyName -> {
            var secretString = UUID.randomUUID().toString();
            // encode secret as secretName
            var encodedSecret = DigestUtils.md5Hex(secretString).toUpperCase();
            return Pair.of(encodedSecret, secretString);
        };
    }

    @Bean({"apiKeyManager", "openApiKeyManager"})
    ApiKeyManager openApiKeyManager(
            @Qualifier("jooqApiKeyManager") ApiKeyManager jooqApiKeyManager,
            @Qualifier("secretProvider") Function<String, Pair<String, String>> secretProvider,
            UserManager userManager) {
        return new OpenApiKeyManager(jooqApiKeyManager, secretProvider, userManager);
    }
}
