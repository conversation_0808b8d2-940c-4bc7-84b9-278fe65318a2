{"yearBuilt": 2004, "livingArea": 2300.0, "lotSize": 0.13, "risk": {"overallCondition": "Average", "areaEconomy": "Stable", "neighborhood": "Suburban", "gatedCommunity": false, "locatedOnPavedRoad": true, "isolatedDwelling": false, "seasonalDwelling": false, "businessOperation": "None observed", "vacant": false, "rental": false}, "bldg": {"overallCondition": null, "dwellingType": "Single Family Detached", "construction": "<PERSON>ame", "constructionOverWater": null, "garage": "Built-In", "hvac": "Central", "numStories": 2, "windProtections": [], "hurricaneStraps": null, "designatedHistoricHome": false, "foundation": "Concrete S<PERSON>b", "manufacturedOrMobileHome": null, "exteriorDamage": null}, "roof": {"overallCondition": "Average", "estAge": "15 ~ 20 years", "estLife": null, "geometry": {"HipValley": 100, "Flat": 0, "Gable": 0, "Hip": 0, "Dormer": 0, "Mansard": 0, "Saltbox": 0, "Combination": 0, "Lean": 0, "DutchGable": 0, "Jerkinhead": 0, "Gambrel": 0, "Arched": 0, "SkillionLean": 0, "Pyramid": 0, "Conical": 0}, "coveringMaterial": ["<PERSON><PERSON><PERSON>"], "hasSolarPanel": null, "hasCurlingShingles": false, "hasGranularLoss": false, "hasMissingDamagedShingles": true, "hasPatchedAreas": false, "hasTarp": false, "material": {"CompositeShingles": 100, "BuildupRoofNoGravel": 0, "ClayConcreteTiles": 0, "VinylMembrane": 0, "SlateRoof": 0, "SinglePlyMembrane": 0, "LightMetalPanels": 0, "MetalRoof": 0, "StandingSeamMetalRoof": 0, "WoodenShingles": 0, "ModifiedBitumen": 0, "SinglePlyMembraneBallasted": 0}, "comments": ["Front Slope: Loose, damaged, or missing shingles were noted.", "Right Slope: Loose, damaged, or missing shingles were noted.", "Rear Slope: Loose, damaged, or missing shingles were noted.", "Left Slope: Loose, damaged, or missing shingles were noted."]}, "exterior": {"overallCondition": "Average", "siding": {"BrickVeneer": 50, "Wood": 0, "HardiePlank": 50, "Stucco": 0, "ConcreteBlock": 0, "WoodShake": 0, "Aluminum": 0, "Vinyl": 0, "Metal": 0, "Log": 0, "Asbestos": 0, "StoneVeneer": 0, "VinylShake": 0, "HardiePanel": 0, "HardieShingle": 0}, "hasShutters": null, "hasPorch": null, "hasStairsWithoutHandRails": null, "hasYardDebris": null, "hasDiscardedVehicles": null, "hasTreeLimbs": null, "hasPoolWithoutFence": null, "numDogPresent": 0, "hasDogPresent": false, "comments": ["Front Elevation: Damage was noted to the garage door.", "Front Elevation: <PERSON><PERSON><PERSON> was noted on the siding.", "Rear Elevation: <PERSON><PERSON><PERSON> was noted on the siding.", "Left Elevation: Peeling paint was noted on the siding.", "Left Elevation: Damage to the window frame(s) was noted.", "Left Elevation: Damage to the window screen was noted.", "Right Elevation: <PERSON><PERSON><PERSON> was noted on the siding.", "Right Elevation: HVAC unit(s) was noted in Average condition."]}, "interior": null, "addlStructures": [], "hazards": [], "recommendations": [{"text": "Damage to garage(s) was noted at the time of inspection. It is recommended to contact garage service contractor to repair the garage as soon as possible.", "image": [{"id": "ptLzAw10iJQ32sKiTQoWqdyorjipD6ug"}, {"id": "JgrVM7Aydm1khHTvrrXUSV0QexzNslGD"}]}, {"text": "Chimney through roof on the Unknown slope was noted. It is recommended to conduct interior chimney sweep.", "image": [{"id": "yjAw8WF0pmTyjIEiBjnCrhrATPPZEOqn"}, {"id": "XyvkUJ2ebPaerBmXO1cM2oafsMEuv8Qz"}]}, {"text": "A trampoline  was noted on the property. It is extreme hazard, and recommended to be removed.", "image": [{"id": "fhDTA7jVT-dTmUntpnKdSWyykqsdDj0y"}]}, {"text": "Damaged fence pickets were noted. It is recommended to contact fence service contractor to repair the fence as soon as possible.", "image": [{"id": "aFRtaR37DLgKRsx8F5W5sWWNjNRP_4N8"}, {"id": "iJ8-7hwt0oCey-IgFqU4pBXJBqLXtnZq"}]}, {"text": "Chimney through roof on the Rear slope was noted. It is recommended to conduct interior chimney sweep.", "image": [{"id": "s9-7YK19k3Li483zelfapeKhPM95ZPBm"}]}, {"text": "A swimming pool was noted at the Unknown of the dwelling. It is recommended that self-closing door to access swimming pool should be installed.", "image": [{"id": "fWDaaK69WOsKN9TivQjGRj0hl4oUsJz2"}, {"id": "VhmoJ5sBq1S11VTCbest-5VkObmpw_9w"}]}, {"text": "Pool water appears to be cloudy and in Average condition. It is recommended to consult with professional pool service as soon as possible.", "image": [{"id": "fWDaaK69WOsKN9TivQjGRj0hl4oUsJz2"}, {"id": "VhmoJ5sBq1S11VTCbest-5VkObmpw_9w"}]}, {"text": "Chimney through roof on the Right slope was noted. It is recommended to conduct interior chimney sweep.", "image": [{"id": "sQBIvs_JCL5-9jX5wVuPNh4UyG81jU_v"}]}], "factors": [{"text": "Damage was noted to the garage door.", "name": "COMPONENT DAMAGE", "direction": "Front", "image": [{"id": "uAnaarC7g7hH3KU-3VRkNriSYHl1hl8n"}, {"id": "JgrVM7Aydm1khHTvrrXUSV0QexzNslGD"}]}, {"text": "Milde<PERSON> was noted on the siding.", "name": "MILDEW/MOSS", "direction": "Front", "image": [{"id": "uAnaarC7g7hH3KU-3VRkNriSYHl1hl8n"}, {"id": "JgrVM7Aydm1khHTvrrXUSV0QexzNslGD"}]}, {"text": "Loose, damaged, or missing shingles were noted.", "name": "MISSING/DAMAGE SHINGLES", "direction": "Front", "image": [{"id": "jSkBsCqimVjxiDjdqVXmCFTJuCQcXXFc"}]}, {"text": "Loose, damaged, or missing shingles were noted.", "name": "MISSING/DAMAGE SHINGLES", "direction": "Right", "image": [{"id": "XyvkUJ2ebPaerBmXO1cM2oafsMEuv8Qz"}, {"id": "EQyb_B3Sq-ZTbIFUV3N4iGrst4xu3nqD"}, {"id": "AcE_Aqb760YckTgVun8cTABEBZx_j022"}]}, {"text": "Loose, damaged, or missing shingles were noted.", "name": "MISSING/DAMAGE SHINGLES", "direction": "Rear", "image": [{"id": "mA2NPxJWZsAx4aVw9H_VJt3nfXn5dEka"}]}, {"text": "Loose, damaged, or missing shingles were noted.", "name": "MISSING/DAMAGE SHINGLES", "direction": "Left", "image": [{"id": "sQBIvs_JCL5-9jX5wVuPNh4UyG81jU_v"}]}, {"text": "Damage to garage(s) was noted at the time of inspection. It is recommended to contact garage service contractor to repair the garage as soon as possible.", "name": "GARAGE DAMAGE", "direction": null, "image": [{"id": "ptLzAw10iJQ32sKiTQoWqdyorjipD6ug"}, {"id": "JgrVM7Aydm1khHTvrrXUSV0QexzNslGD"}]}, {"text": "Chimney through roof on the Unknown slope was noted. It is recommended to conduct interior chimney sweep.", "name": "CHIMNEY THROUGH ROOF", "direction": null, "image": [{"id": "yjAw8WF0pmTyjIEiBjnCrhrATPPZEOqn"}, {"id": "XyvkUJ2ebPaerBmXO1cM2oafsMEuv8Qz"}]}, {"text": "A trampoline  was noted on the property. It is extreme hazard, and recommended to be removed.", "name": "PLAY/SWING SET", "direction": null, "image": [{"id": "fhDTA7jVT-dTmUntpnKdSWyykqsdDj0y"}]}, {"text": "Milde<PERSON> was noted on the siding.", "name": "MILDEW/MOSS", "direction": "Rear", "image": [{"id": "fR5ikPAjQ-AOIQxeC8ho2qSgl_0i6CXP"}]}, {"text": "Peeling paint was noted on the siding.", "name": "PEELING PAINT - SIDING", "direction": "Left", "image": [{"id": "MBiH6AAzZCyL94u5OWmldFv6hbNvkSHH"}, {"id": "6p-ZfSC2_L4mjyj4J0FDSySNFxI8xAMI"}]}, {"text": "Damage to the window frame(s) was noted.", "name": "WINDOW FRAME DAMAGE", "direction": "Left", "image": [{"id": "MBiH6AAzZCyL94u5OWmldFv6hbNvkSHH"}, {"id": "6p-ZfSC2_L4mjyj4J0FDSySNFxI8xAMI"}]}, {"text": "Damage to the window screen was noted.", "name": "WINDOW SCREEN DAMAGE", "direction": "Left", "image": [{"id": "MBiH6AAzZCyL94u5OWmldFv6hbNvkSHH"}, {"id": "6p-ZfSC2_L4mjyj4J0FDSySNFxI8xAMI"}]}, {"text": "Milde<PERSON> was noted on the siding.", "name": "MILDEW/MOSS", "direction": "Right", "image": [{"id": "ZvJJNEweiP4Y-y-pW80yfT1XuLtXgkWN"}]}, {"text": "HVAC unit(s) was noted in Average condition.", "name": "HVAC", "direction": "Right", "image": [{"id": "fR5ikPAjQ-AOIQxeC8ho2qSgl_0i6CXP"}]}, {"text": "Damaged fence pickets were noted. It is recommended to contact fence service contractor to repair the fence as soon as possible.", "name": "FENCE DAMAGE", "direction": null, "image": [{"id": "aFRtaR37DLgKRsx8F5W5sWWNjNRP_4N8"}, {"id": "iJ8-7hwt0oCey-IgFqU4pBXJBqLXtnZq"}]}, {"text": "Chimney through roof on the Rear slope was noted. It is recommended to conduct interior chimney sweep.", "name": "CHIMNEY THROUGH ROOF", "direction": "Rear", "image": [{"id": "s9-7YK19k3Li483zelfapeKhPM95ZPBm"}]}, {"text": "A swimming pool was noted at the Unknown of the dwelling. It is recommended that self-closing door to access swimming pool should be installed.", "name": "SWIMMING POOL", "direction": null, "image": [{"id": "fWDaaK69WOsKN9TivQjGRj0hl4oUsJz2"}, {"id": "VhmoJ5sBq1S11VTCbest-5VkObmpw_9w"}]}, {"text": "Pool water appears to be cloudy and in Average condition. It is recommended to consult with professional pool service as soon as possible.", "name": "POOL WATER CONDITION", "direction": null, "image": [{"id": "fWDaaK69WOsKN9TivQjGRj0hl4oUsJz2"}, {"id": "VhmoJ5sBq1S11VTCbest-5VkObmpw_9w"}]}, {"text": "Chimney through roof on the Right slope was noted. It is recommended to conduct interior chimney sweep.", "name": "CHIMNEY THROUGH ROOF", "direction": "Right", "image": [{"id": "sQBIvs_JCL5-9jX5wVuPNh4UyG81jU_v"}]}]}