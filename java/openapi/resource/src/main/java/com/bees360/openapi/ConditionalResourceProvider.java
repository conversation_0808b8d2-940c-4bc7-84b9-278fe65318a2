package com.bees360.openapi;

import com.bees360.resource.Resource;

import lombok.NonNull;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;

public class ConditionalResourceProvider implements Function<String, Resource> {

    private final Map<Predicate<String>, Function<String, Resource>> predicateFunctionMap;

    public ConditionalResourceProvider(
            @NonNull
                    Map<Predicate<String>, Function<String, Resource>>
                            conditionalResourceProviderMap) {
        this.predicateFunctionMap = conditionalResourceProviderMap;
    }

    public ConditionalResourceProvider() {
        this.predicateFunctionMap = new LinkedHashMap<>();
    }

    public void add(
            @NonNull Predicate<String> conditional,
            @NonNull Function<String, Resource> resourceProvider) {
        this.predicateFunctionMap.put(conditional, resourceProvider);
    }

    @Override
    public Resource apply(String key) {
        for (var predicateFunction : predicateFunctionMap.entrySet()) {
            if (predicateFunction.getKey().test(key)) {
                return predicateFunction.getValue().apply(key);
            }
        }
        return null;
    }
}
