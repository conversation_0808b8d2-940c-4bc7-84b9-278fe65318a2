package com.bees360.openapi.securityfirst;

import com.bees360.openapi.ConditionalResourceProvider;
import com.bees360.resource.Resource;
import com.bees360.util.Iterables;

import lombok.NonNull;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

public class SecurityFirstResourceProvider implements Function<String, Resource> {

    private final ConditionalResourceProvider resourceProvider = new ConditionalResourceProvider();
    private final Function<String, Iterable<String>> exportableReportProvider;
    private Function<String, String> projectIdParser =
            (key) -> StringUtils.substringAfterLast(StringUtils.substringBeforeLast(key, "."), "_");
    ;

    /**
     * @param exportableReportProvider get report ids with project id.
     * @param summaryResourceProvider get summary resource with report id.
     * @param reportResourceProvider get report resource with report id.
     */
    public SecurityFirstResourceProvider(
            @NonNull Function<String, Iterable<String>> exportableReportProvider,
            @NonNull Function<String, Resource> summaryResourceProvider,
            @NonNull Function<String, Resource> reportResourceProvider) {
        this.exportableReportProvider = exportableReportProvider;

        resourceProvider.add(
                key -> StringUtils.endsWith(key, ".json"),
                key -> findReportResource(summaryResourceProvider, key));
        resourceProvider.add(
                key -> StringUtils.endsWith(key, ".pdf"),
                key -> findReportResource(reportResourceProvider, key));
    }

    @Override
    public Resource apply(String projectKey) {
        return resourceProvider.apply(projectKey);
    }

    private Resource findReportResource(
            Function<String, Resource> resourceProvider, String projectIdKey) {
        var reportId = findUniqueReportId(projectIdKey);
        return reportId == null ? null : resourceProvider.apply(reportId);
    }

    /**
     * @return the id of the report, null if report not found
     */
    private String findUniqueReportId(String projectIdKey) {
        var projectId = parseProjectId(projectIdKey);
        var reports = Iterables.toList(exportableReportProvider.apply(projectId));
        if (reports.isEmpty()) {
            return null;
        }
        if (reports.size() > 1) {
            throw new IllegalStateException(
                    "There are more than one reports found for project " + projectId);
        }
        return reports.get(0);
    }

    private String parseProjectId(String projectIdKey) {
        return projectIdParser.apply(projectIdKey);
    }

    public void setProjectIdParser(@NonNull Function<String, String> projectIdParser) {
        this.projectIdParser = projectIdParser;
    }
}
