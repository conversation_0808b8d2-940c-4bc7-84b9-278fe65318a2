package com.bees360.openapi.config;

import com.bees360.event.DetectResourceDeletionOnResourceUploaded;
import com.bees360.event.EventPublisher;
import com.bees360.job.JobScheduler;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.ResourceDeletionDetectedJobExecutor;
import com.bees360.resource.EventfulResourcePool;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceTransferJobExecutor;
import com.bees360.resource.ResourceTransferJobService;
import com.bees360.resource.SftpResourceRepository;
import com.bees360.resource.config.SftpResourceRepositoryConfig;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Duration;
import java.util.function.Function;

@Log4j2
@Import(SftpResourceRepositoryConfig.class)
@Configuration
@ConditionalOnProperty(prefix = "resource.app.transfer.resource-pool-security-first", name = "name")
public class SecurityFirstResourceExporterConfig {

    public static final String SECURITY_FIRST_RESOURCE_POOL_EVENT_PREFIX = "security_first";

    private String securityFirstTransferName;

    public SecurityFirstResourceExporterConfig(
            @Value("${resource.app.transfer.resource-pool-security-first.name}")
                    String securityFirstTransferName) {
        this.securityFirstTransferName = securityFirstTransferName;
        log.info("Created '{}(securityFirstTransferName={})'", this, securityFirstTransferName);
    }

    @Bean
    ResourceTransferJobService securityFirstResourceTransferService(JobScheduler jobScheduler) {
        return new ResourceTransferJobService(
                jobScheduler, securityFirstTransferName, 6, Duration.ofMinutes(2), 2.0F);
    }

    @Bean
    @ConditionalOnBean(RabbitJobDispatcher.class)
    ResourceTransferJobExecutor resourceTransferJobExecutor(
            Function<String, Resource> securityFirstResourceProvider,
            ResourcePool securityFirstResourcePool,
            RabbitJobDispatcher rabbitJobDispatcher,
            @Value(
                            "${resource.app.transfer.resource-pool-security-first.resource-transfer.max-concurrency:3}")
                    int maxConcurrency) {
        var resourceTransferJobExecutor =
                new ResourceTransferJobExecutor(
                        securityFirstTransferName,
                        securityFirstResourceProvider,
                        securityFirstResourcePool::put);
        rabbitJobDispatcher.enlist(resourceTransferJobExecutor, maxConcurrency);
        return resourceTransferJobExecutor;
    }

    @Bean
    ResourcePool securityFirstResourcePool(
            SftpResourceRepository sftpResourceRepository, EventPublisher eventPublisher) {
        return new EventfulResourcePool(
                SECURITY_FIRST_RESOURCE_POOL_EVENT_PREFIX, sftpResourceRepository, eventPublisher);
    }

    @Bean
    DetectResourceDeletionOnResourceUploaded triggerResourceDeletionDetectionOnResourceUploaded(
            JobScheduler jobScheduler) {
        // retry for 7 days.
        return new DetectResourceDeletionOnResourceUploaded(
                SECURITY_FIRST_RESOURCE_POOL_EVENT_PREFIX,
                false,
                jobScheduler,
                12,
                Duration.ofMinutes(10),
                2F);
    }

    /** TODO 旧Executor,需要继续监听直到所有堆积Job执行完毕后方可关闭开关/删除代码 */
    @Bean
    @ConditionalOnProperty(
            prefix =
                    "resource.app.transfer.resource-pool-security-first.resource-deletion-detect.unnamespaced-detect-executor",
            name = "enabled",
            havingValue = "true",
            matchIfMissing = true)
    @ConditionalOnBean(RabbitJobDispatcher.class)
    ResourceDeletionDetectedJobExecutor resourceDeletionDetectJobExecutor(
            ResourcePool securityFirstResourcePool,
            EventPublisher eventPublisher,
            RabbitJobDispatcher rabbitJobDispatcher,
            @Value(
                            "${resource.app.transfer.resource-pool-security-first.resource-deletion-detect.max-concurrency:5}")
                    int maxConcurrency) {
        var resourceDeletionDetectExecutor =
                new ResourceDeletionDetectedJobExecutor(
                        SECURITY_FIRST_RESOURCE_POOL_EVENT_PREFIX,
                        securityFirstResourcePool,
                        eventPublisher,
                        Duration.ofMinutes(5),
                        false);
        rabbitJobDispatcher.enlist(resourceDeletionDetectExecutor, maxConcurrency);
        return resourceDeletionDetectExecutor;
    }

    /** 新Executor,需要保证SecurityFirst的DetectJob能够正确传递到该Executor */
    @Bean
    @ConditionalOnProperty(
            prefix =
                    "resource.app.transfer.resource-pool-security-first.resource-deletion-detect.namespaced-detect-executor",
            name = "enabled",
            havingValue = "true",
            matchIfMissing = true)
    @ConditionalOnBean(RabbitJobDispatcher.class)
    ResourceDeletionDetectedJobExecutor securityFirstResourceDeletionDetectJobExecutor(
            ResourcePool securityFirstResourcePool,
            EventPublisher eventPublisher,
            RabbitJobDispatcher rabbitJobDispatcher,
            @Value(
                            "${resource.app.transfer.resource-pool-security-first.resource-deletion-detect.max-concurrency:5}")
                    int maxConcurrency) {
        var resourceDeletionDetectExecutor =
                new ResourceDeletionDetectedJobExecutor(
                        SECURITY_FIRST_RESOURCE_POOL_EVENT_PREFIX,
                        securityFirstResourcePool,
                        eventPublisher,
                        Duration.ofMinutes(5));
        rabbitJobDispatcher.enlist(resourceDeletionDetectExecutor, maxConcurrency);
        return resourceDeletionDetectExecutor;
    }
}
