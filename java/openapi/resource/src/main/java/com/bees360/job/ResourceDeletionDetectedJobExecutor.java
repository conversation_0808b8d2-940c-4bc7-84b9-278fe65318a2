package com.bees360.job;

import com.bees360.event.EventPublisher;
import com.bees360.event.registry.Events;
import com.bees360.event.registry.ResourceDeleted;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.ResourceDeletionDetectionJob;
import com.bees360.job.util.AbstractAsyncJobExecutor;
import com.bees360.resource.ResourcePool;
import com.bees360.util.ListenableFutures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;

import jakarta.annotation.Nullable;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.time.Duration;
import java.util.concurrent.TimeoutException;

/**
 * Detect the resource pool to check the resource has been deleted or not. If deleted, publish
 * {@link ResourceDeleted}.
 */
@Log4j2
public class ResourceDeletionDetectedJobExecutor
        extends AbstractAsyncJobExecutor<ResourceDeletionDetectionJob> {
    private static final String EVENT_NAME_SEPARATOR = ".";
    private static final String QUEUE_NAME_SEPARATOR = "_";

    private final String namespace;
    private final ResourcePool resourcePool;
    private final EventPublisher eventPublisher;
    private final Duration detectionDelay;
    /** 是否使用namespace重命名队列,目的是区分不同namespace的DetectJob避免混乱 */
    private final Boolean namespacedExecutor;

    public ResourceDeletionDetectedJobExecutor(
            @Nullable String namespace,
            @NonNull ResourcePool resourcePool,
            @NonNull EventPublisher eventPublisher,
            @NonNull Duration detectionDelay) {
        this.namespace = namespace;
        this.resourcePool = resourcePool;
        this.eventPublisher = eventPublisher;
        this.detectionDelay = detectionDelay;
        this.namespacedExecutor = true;
        log.info(
                "Created '{}(namespace={}, resourcePool={}, eventPublisher={}, detectionDelay={},"
                        + " namespacedExecutor={})'",
                this,
                namespace,
                resourcePool,
                eventPublisher,
                detectionDelay,
                namespacedExecutor);
    }

    public ResourceDeletionDetectedJobExecutor(
            @Nullable String namespace,
            @NonNull ResourcePool resourcePool,
            @NonNull EventPublisher eventPublisher,
            @NonNull Duration detectionDelay,
            @NonNull Boolean namespacedExecutor) {
        this.namespace = namespace;
        this.resourcePool = resourcePool;
        this.eventPublisher = eventPublisher;
        this.detectionDelay = detectionDelay;
        this.namespacedExecutor = namespacedExecutor;
        log.info(
                "Created '{}(namespace={}, resourcePool={}, eventPublisher={}, detectionDelay={},"
                        + " namespacedExecutor={})'",
                this,
                namespace,
                resourcePool,
                eventPublisher,
                detectionDelay,
                namespacedExecutor);
    }

    @Override
    protected ListenableFuture<Void> accept(ResourceDeletionDetectionJob job) {
        log.info(
                "Resource Deletion Detect for {} in {} will start after {}.",
                job.getKey(),
                resourcePool.getContextURI(),
                detectionDelay);
        return ListenableFutures.callDelayed(
                () -> {
                    if (resourcePool.containsKey(job.getKey())) {
                        var message =
                                String.format(
                                        "The resource %s in pool %s haven't been deleted in %s.",
                                        job.getKey(), resourcePool.getContextURI(), detectionDelay);
                        throw new TimeoutException(message);
                    }
                    eventPublisher.publish(
                            getEventName(), Events.encode(new ResourceDeleted(job.getKey())));
                    return null;
                },
                detectionDelay,
                MoreExecutors.directExecutor());
    }

    private String getEventName() {
        var eventName = Events.getEventName(ResourceDeleted.class);
        return namespace == null ? eventName : namespace + EVENT_NAME_SEPARATOR + eventName;
    }

    @Override
    public String getName() {
        var jobName = JobPayloads.getJobName(ResourceDeletionDetectionJob.class);
        return namespace == null || !namespacedExecutor
                ? jobName
                : namespace + QUEUE_NAME_SEPARATOR + jobName;
    }
}
