package com.bees360.event;

import com.bees360.event.registry.ResourceUploaded;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.ResourceDeletionDetectionJob;
import com.bees360.job.util.EventTriggeredJob;

import jakarta.annotation.Nullable;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.time.Duration;

/** create a job to detect the resource deletion after a resource uploaded. */
@Log4j2
public class DetectResourceDeletionOnResourceUploaded extends EventTriggeredJob<ResourceUploaded> {

    private static final String EVENT_NAME_SEPARATOR = ".";
    private static final String QUEUE_NAME_SEPARATOR = "_";

    private final String namespace;
    private final Integer retryCount;
    private final Duration retryDelay;
    private final Float retryDelayIncreaseFactor;
    /**
     * 是否使用namespace重命名队列,目的是创建不同的队列来分发不同namespace的DetectJob避免混乱,需要保证全局unnamespacedListener的namespace唯一
     */
    private final Boolean namespacedListener;

    public DetectResourceDeletionOnResourceUploaded(
            @NonNull String namespace,
            @NonNull JobScheduler jobScheduler,
            @Nullable Integer retryCount,
            @Nullable Duration retryDelay,
            @Nullable Float retryDelayIncreaseFactor) {
        super(jobScheduler);
        this.namespace = namespace;
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.retryDelayIncreaseFactor = retryDelayIncreaseFactor;
        this.namespacedListener = true;
        log.info(
                "Created {}(namespace={}, namespacedListener={}).",
                this,
                namespace,
                namespacedListener);
    }

    public DetectResourceDeletionOnResourceUploaded(
            @NonNull String namespace,
            @NonNull Boolean namespacedListener,
            @NonNull JobScheduler jobScheduler,
            @Nullable Integer retryCount,
            @Nullable Duration retryDelay,
            @Nullable Float retryDelayIncreaseFactor) {
        super(jobScheduler);
        this.namespace = namespace;
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.retryDelayIncreaseFactor = retryDelayIncreaseFactor;
        this.namespacedListener = namespacedListener;
        log.info(
                "Created {}(namespace={}, namespacedListener={}).",
                this,
                namespace,
                namespacedListener);
    }

    @Override
    protected Job convert(ResourceUploaded event) {
        var job = new ResourceDeletionDetectionJob(event.getKey());
        var jobPayload = Job.ofPayload(job);
        return RetryableJob.of(
                getJobName(),
                jobPayload.getId(),
                retryCount,
                retryDelay,
                retryDelayIncreaseFactor,
                jobPayload.getPayload());
    }

    private String getJobName() {
        var jobName = JobPayloads.getJobName(ResourceDeletionDetectionJob.class);
        return namespace == null ? jobName : namespace + QUEUE_NAME_SEPARATOR + jobName;
    }

    @Override
    public String getEventName() {
        var eventName = super.getEventName();
        return namespace == null ? eventName : namespace + EVENT_NAME_SEPARATOR + eventName;
    }

    @Nullable
    @Override
    public String getName() {
        var listenerName = super.getName();
        return namespace == null || !namespacedListener
                ? listenerName
                : namespace + QUEUE_NAME_SEPARATOR + listenerName;
    }
}
