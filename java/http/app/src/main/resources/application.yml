activity:
  config:
    default-source: WEB

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  profiles:
    active: ${ENV}
    include: actuator
  jooq:
    sql-dialect: POSTGRES
  datasource:
    url: ****************************************
    username: db_username
    password: db_password
    platform: postgres
    driver-class-name: org.postgresql.Driver

server:
  port: 8080
  servlet:
    context-path: /apiv2

springdoc:
  default-produces-media-type: application/json
  api-docs:
    enabled: true # 是否开启springdoc openapi端点 默认true
    path: /openapi/v3/api-docs # springdoc openapi端点路径 默认/v3/api-docs
  swagger-ui:
    enabled: true # 是否开启 swagger ui 默认true
    path: /openapi/swagger-ui/index.html # 自定义 swagger访问路径 默认swagger-ui.html
rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}
    property:
      prefix: ENC(
      suffix: )

es:
  host: elasticsearch
  port: 9200

http:
  image-api:
    endpoint: /v2/image
  user:
    endpoint: /user
  group:
    endpoint: /group
  address:
    endpoint: /address
  project:
    endpoint: /project
  todo:
    endpoint: /todo
  resource:
    endpoint: /resource
  kpi:
    operations-manager:
      endpoint: "/kpi/operations-manager"
  operationsManager:
    endpoint: /operations-manager
  pipeline-def:
    endpoint: /pipeline-def
  customer:
    endpoint: /customer
  projectInvoice:
    endpoint: /project/invoice
  contract:
    endpoint: /contract
  sso-config:
    endpoint: "/sso/config"

  client:
    apache:
      maxConnPerRoute: 16
      maxConnTotal: 64
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT30S
        socketTimeout: PT15S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: true
  cors:
    '[/**]':
      allowed_origins: "*"
      allowed_headers: "*"
      allowed_methods: GET,HEAD,PUT,OPTIONS,POST,DELETE,PATCH
      allow_credentials: true
      exposed_headers: Authorization,Access-Control-Allow-Origin,Access-Control-Allow-Credentials,X-Protobuf-Message
      max_age: 86400
  request:
    matchers:
      - method: DELETE
        path: "/project/{projectId:\\d+}/report/{reportId}"
        accessRule: "hasAnyRole('ROLE_ADMIN') or @PRS.isDeletable(#projectId,#reportId)"
      - method: PUT
        path: "/template/**"
        accessRule: "hasAnyRole('ROLE_ADMIN')"
      - method: PUT
        path: "/project/{projectId:\\d+}/state"
        accessRule: "hasAnyRole('ROLE_ADMIN')"
      - method: PUT
        path: "/project/{projectId:\\d+}/integration"
        accessRule: "hasAnyRole('ROLE_ADMIN')"
      - method: DELETE
        path: "/project/{projectId:\\d+}/integration/**"
        accessRule: "hasAnyRole('ROLE_ADMIN')"
      - method: GET
        path: "/project/{projectId:\\d+}/**"
        accessRule: "authenticated and @PAS.isProjectAccessible(#projectId)"
      - method: PUT,POST,DELETE,PATCH
        path: "/project/{projectId:\\d+}/**"
        accessRule: "authenticated and @PAS.isProjectAccessible(#projectId) and @PAS.isProjectOperable(#projectId)"
      - method: "*"
        path: "/project/statistic/**"
        accessRule: "hasAnyRole('ROLE_ADMIN','ROLE_DU','ROLE_OPERATIONS_MANAGER')"
      - method: PUT,POST,DELETE,PATCH
        path: "/frontend/config/**"
        accessRule: "hasAnyRole('ROLE_ADMIN')"
      - method: GET,PUT,POST,DELETE
        path: "/schedule/**"
        accessRule: "hasAnyRole('ROLE_ADMIN')"
      - method: GET,PUT,POST,DELETE
        path: "/apikey/**"
        accessRule: "hasAnyRole('ROLE_ADMIN','ROLE_COMPANY_DEVELOPER')"
      - method: GET
        path: "/bundle/{bundleId:\\d+}/**"
        accessRule: "authenticated and @BAS.isBundleAccessible(#bundleId)"
      - method: PUT,POST,DELETE
        path: "/bundle/{bundleId:\\d+}/**"
        accessRule: "authenticated and @BAS.isBundleAccessible(#bundleId) and @BAS.isBundleOperable(#bundleId)"
    access-security:
      companyStrategy:
        realms9: 2770
        forbiddenCompanyList: 1742
grpc:
  client-config:
    deadline: PT30S
    wait-for-ready-enable: true
  client:

    activityManager:
      address: static://bees360-activity-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    commentManager:
      address: static://bees360-activity-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectManager:
      address: static://bees360web-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStatisticService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIntegration:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectCancellationManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    userProvider:
      address: static://bees360-bifrost-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    groupManager:
      address: static://bees360-user-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    addressManager:
      address: static://bees360-address-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectTagManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    resourcePool:
      address: static://bees360-resource-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    kPIService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    operationsManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    pipelineService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineDefService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectPipelineConfigService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectContactManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    todoManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    imageManager:
      address: static://bees360-image-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectCatastropheManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    customerManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    customerPolicyTypeManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectInvoiceProvider:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    assignRuleManager:
      address: 'static://bees360-project-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    assigneeManager:
      address: 'static://bees360-project-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    pilotFeedbackManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    contractManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    contractServiceManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    ssoRegistrationManager:
      address: 'static://bees360-customer-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectHoverManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectIIManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectPolicyManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    contactRecordManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    primaryContactRecordManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectStateManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStateChangeReasonManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    stateChangeReasonGroupManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectDaysOldProvider:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    taskAssignRecordService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    userKeyProvider:
      address: static://bees360-user-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    attachmentManager:
      address: 'static://bees360-project-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectMemberManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    similarProjectProvider:
      address: 'static://bees360-project-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectOptionsClient:
      address: 'static://bees360-project-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    scheduleJobManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    reportManager:
      address: static://bees360-report-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    thirdPartyJobStatusProvider:
      address: 'static://bees360-thirdparty-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    genericProjectCreator:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectGroupManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    lambdaExecutionManager:
      address: static://bees360-estintel-lambda-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    nonBlockingLambdaExecutionManager:
      address: static://bees360-estintel-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

    projectImageProcessManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    scheduledLambdaManager:
      address: static://bees360-estintel-airflow-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    estintelManager:
      address: static://bees360-estintel-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    bufBuildService:
      address: static://bees360-buf-build-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    apiKeyManager:
      address: static://bees360-openapi-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectInvoiceGenerator:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    divisionManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    bundleManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    bundleTagManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    reportProcessStatusClient:
      address: static://bees360-report-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

auth:
  jwt:
    verifierKey: |
      -----BEGIN CERTIFICATE-----
      MIIC/DCCAeSgAwIBAgIIOcybo4ESpmMwDQYJKoZIhvcNAQEFBQAwIDEeMBwGA1UE
      AxMVMTAzNzQxMjAwMzE3OTIwODg2ODQ1MCAXDTIxMDcyNzAzNDgxM1oYDzk5OTkx
      MjMxMjM1OTU5WjAgMR4wHAYDVQQDExUxMDM3NDEyMDAzMTc5MjA4ODY4NDUwggEi
      MA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDnpfrDqFClLoVqAg9GcM0sEmIR
      i7BP+gC8KautIhPyrScuujFHZf8MHP1AezjVd2gcWCi1pGTTNUbeZH2xet5qqP/X
      Gc2OsLFh9vAGOXQz1WtJWENv4ncazZu8uBeurVyJrNIaY0G3v3JYAUHKi/Wu0GCy
      t0bSwVYVlzH8IOwlZezuUmsoWMX56eI0CFrfA2WuGwvMJQzB7daN6XB68hGt+WFE
      18xXeJCKmyjhaFoY/KpWO1Z0g8/gLYLkPrC/oa6tNigENaK7aQmaPCJ3phwiDz2a
      WPNJ8evXMGVFIE5qUHG3aNEpA2Rru4pFA9+g3ZfjIu8VnQh1kLmL5dtw0x4pAgMB
      AAGjODA2MAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMBYGA1UdJQEB/wQM
      MAoGCCsGAQUFBwMCMA0GCSqGSIb3DQEBBQUAA4IBAQCTlQLQOUEr5PHvG1Y9z7Hc
      +6MXFb7WxiRM7IcSEPA0BwkZM94BF5fojlHZ2ox335669wrGqGpkNLBqIMLuxw1b
      4N5wxeeS2M8fZeUjM7kaHT8ZE+27FzOY1NNbFn+dyUqonGM/1GlGL3hgOV4hkyzk
      3vderqQakqR7bYyUmjCgBcAvQahOpkJxX4/XgqzaDQBDGmhG1VOvyTJEyZtBX1GN
      nmsz9CzqClBgydf6bnjgm4Gh4CHI4YKL/7yGXAkk50JqYbhkAmlNcIbILryilTRu
      MZGX7oskns0vBqMtLmIPG5eH/KL3KqUS1KTxW33Ucca+WnmKQjNys2QKBVLR1Y+F
      -----END CERTIFICATE-----

  ##### SAML ACS-endpoint of Auth-App #####
  saml:
    acs-endpoint: "http://localhost/saml2/sso"
    service-provider-id-base: "http://localhost/saml2/service-provider"
  ##### OAuth Authorization code flow redirect endpoint of Auth-App #####
  oauth:
    redirect-endpoint: "http://localhost/oauth2/callback"
