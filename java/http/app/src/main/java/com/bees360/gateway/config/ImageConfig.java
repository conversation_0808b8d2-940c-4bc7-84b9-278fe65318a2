package com.bees360.gateway.config;

import com.bees360.image.ImageApiEndpoint;
import com.bees360.image.ImageApiProvider;
import com.bees360.image.ImageEndpoint;
import com.bees360.image.config.GrpcImageApiClientConfig;
import com.bees360.image.config.GrpcImageClientConfig;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    ImageEndpoint.class,
    GrpcImageClientConfig.class,
    GrpcImageApiClientConfig.class,
})
@Configuration
public class ImageConfig {
    @Bean
    public ImageApiEndpoint imageApiEndpoint(
            @Value("${http.image.page_size_max_limit:1000}") int maxLimit,
            @Value("${http.image.default_page_size_limit:100}") int defaultLimit,
            @Value("${http.image.default_page_size_offset:0}") int defaultOffset,
            ImageApiProvider endpointImageApiProvider) {
        return new ImageApiEndpoint(
                endpointImageApiProvider, maxLimit, defaultLimit, defaultOffset);
    }
}
