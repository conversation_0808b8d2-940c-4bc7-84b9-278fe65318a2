package com.bees360.gateway.config;

import com.bees360.bundle.BundleEndpoint;
import com.bees360.bundle.BundleReportEndpoint;
import com.bees360.bundle.BundleTagEndpoint;
import com.bees360.bundle.config.BundleHttpSecurityConfig;
import com.bees360.bundle.config.GrpcBundleManagerConfig;
import com.bees360.bundle.config.GrpcBundleTagManagerConfig;
import com.bees360.bundle.report.BundleReportManager;
import com.bees360.bundle.report.BundleReportProcessStatusProvider;
import com.bees360.bundle.report.BundleReportProcessor;
import com.bees360.bundle.report.DefaultBundleReportManager;
import com.bees360.bundle.report.DefaultBundleReportProcessStatusProvider;
import com.bees360.bundle.report.DefaultBundleReportProcessor;
import com.bees360.report.ReportGroupManager;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportProcessStatusProvider;
import com.bees360.report.ReportProcessor;
import com.bees360.report.config.GrpcReportProcessStatusClientConfig;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    BundleHttpSecurityConfig.class,
    BundleEndpoint.class,
    BundleTagEndpoint.class,
    GrpcBundleManagerConfig.class,
    GrpcBundleTagManagerConfig.class,
    BundleReportEndpoint.class,
    GrpcReportProcessStatusClientConfig.class,
})
@Configuration
public class BundleConfig {
    @Bean
    BundleReportManager bundleReportManager(ReportGroupManager reportGroupManager) {
        return new DefaultBundleReportManager(reportGroupManager);
    }

    @Bean
    BundleReportProcessor bundleReportProcessor(
            ReportProcessor reportProcessor, ReportManager reportManager) {
        return new DefaultBundleReportProcessor(reportProcessor, reportManager);
    }

    @Bean
    BundleReportProcessStatusProvider bundleReportProcessStatusProvider(
            ReportProcessStatusProvider reportProcessStatusProvider) {
        return new DefaultBundleReportProcessStatusProvider(reportProcessStatusProvider);
    }
}
