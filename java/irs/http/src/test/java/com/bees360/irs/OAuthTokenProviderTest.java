package com.bees360.irs;

import com.bees360.oauth.OAuthGrant;
import com.bees360.oauth.OAuthPasswordGrant;
import com.bees360.oauth.OAuthRefreshTokenGrant;
import com.bees360.oauth.OAuthRefreshableToken;
import com.bees360.oauth.OAuthToken;
import com.bees360.oauth.OAuthTokenGranter;
import com.bees360.util.Credential;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.net.URI;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import java.util.UUID;

public class OAuthTokenProviderTest {

    static class MockOAuthTokenGranter implements OAuthTokenGranter {

        @Override
        public OAuthToken grant(OAuthGrant grant) {
            var expectedGrantType =
                    grant instanceof OAuthPasswordGrant || grant instanceof OAuthRefreshTokenGrant;
            if (!expectedGrantType) {
                throw new IllegalArgumentException(
                        String.format("Unsupported grant type: %s", grant.getGrantType()));
            }
            String accessToken = UUID.randomUUID().toString();
            String refreshToken = UUID.randomUUID().toString();

            OAuthToken token =
                    OAuthToken.of(
                            accessToken, LocalDateTime.now().plus(2, ChronoUnit.SECONDS), "test");

            return OAuthRefreshableToken.of(token, refreshToken);
        }
    }

    @Test
    void test() throws InterruptedException {
        MockOAuthTokenGranter mockOAuthTokenGranter = new MockOAuthTokenGranter();
        Credential credential = Credential.of("local-id", "local-secret");
        Set<String> scopes = Set.of("local");
        Credentials irsCredential = new Credentials();
        irsCredential.setPassword("123456");
        irsCredential.setUserName("test");

        OAuthTokenProvider provider =
                new OAuthTokenProvider(
                        URI.create("localhost"), "POST", mockOAuthTokenGranter, credential, scopes);
        OAuthToken token = provider.getToken(irsCredential);
        LocalDateTime expiresAt = token.getExpiresAt();
        Thread.sleep(1000);
        OAuthToken newToken = provider.getToken(irsCredential);
        LocalDateTime newExpiresAt = newToken.getExpiresAt();
        Assertions.assertNotEquals(expiresAt, newExpiresAt);
    }
}
