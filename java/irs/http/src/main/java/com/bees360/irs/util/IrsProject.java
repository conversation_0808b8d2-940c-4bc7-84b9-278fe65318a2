package com.bees360.irs.util;

import static com.bees360.util.Functions.acceptIfNotNull;
import static com.google.common.base.Strings.nullToEmpty;

import static java.util.Optional.ofNullable;

import com.bees360.irs.Address;
import com.bees360.irs.Agent;
import com.bees360.irs.Attributes;
import com.bees360.irs.Inspection;
import com.bees360.irs.PolicyHolder;
import com.bees360.openapi.Message.ProjectMessage;

import jakarta.annotation.Nullable;

import lombok.NonNull;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class IrsProject {

    private final Inspection inspection;
    private String streetAddress;
    private Function<String, String> serviceTypeMapper;

    private static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd";
    private static final DateTimeFormatter DEFAULT_DATE_FORMATTER =
            DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN);

    private IrsProject(
            @NonNull Inspection inspection, @Nullable Function<String, String> serviceTypeMapper) {
        this.inspection = inspection;
        this.serviceTypeMapper = ofNullable(serviceTypeMapper).orElse(Function.identity());
    }

    public static IrsProject from(
            Inspection inspection, Function<String, String> serviceTypeMapper) {
        return new IrsProject(inspection, serviceTypeMapper);
    }

    public String getStreetAddress() {
        if (streetAddress != null) {
            return streetAddress;
        }
        var location = inspection.getLocation();
        var street1 = ofNullable(location).map(Address::getStreet1).orElse(null);
        var street2 = ofNullable(location).map(Address::getStreet2).orElse(null);
        streetAddress =
                Stream.of(street1, street2)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(", "));
        return streetAddress;
    }

    public String getCity() {
        return ofNullable(inspection.getLocation()).map(Address::getCity).orElse("");
    }

    public String getState() {
        return ofNullable(inspection.getLocation()).map(Address::getStateOrProvince).orElse("");
    }

    public String getZipcode() {
        return ofNullable(inspection.getLocation()).map(Address::getZipCode).orElse("");
    }

    public String getServiceName() {
        return nullToEmpty(serviceTypeMapper.apply(inspection.getInspectionType()));
    }

    public String getPolicyNumber() {
        return nullToEmpty(inspection.getPolicyNumber());
    }

    public String getInsuredBy() {
        return null;
    }

    public String getInsuredName() {
        return ofNullable(inspection.getPolicyHolder())
                .map(PolicyHolder::getPolicyHolderName)
                .orElse("");
    }

    public String getInsuredPhone() {
        var holder = inspection.getPolicyHolder();
        if (holder == null) {
            return "";
        }
        var phone = holder.getCellPhone();
        phone = StringUtils.isBlank(phone) ? holder.getHomePhone() : phone;
        phone = StringUtils.isBlank(phone) ? holder.getWorkPhone() : phone;

        return phone;
    }

    public String getInsuredEmail() {
        return ofNullable(inspection.getPolicyHolder())
                .map(PolicyHolder::getEmailAddress)
                .orElse("");
    }

    public String getAgentName() {
        return ofNullable(inspection.getAgent()).map(Agent::getAgencyName).orElse("");
    }

    public String getAgentPhone() {
        return ofNullable(inspection.getAgent()).map(Agent::getPhone).orElse("");
    }

    public String getAgentEmail() {
        return ofNullable(inspection.getAgent()).map(Agent::getContactEmail).orElse("");
    }

    public String getPolicyEffectiveDate() {
        return format(inspection.getEffectiveDate());
    }

    public Integer getYearBuilt() {
        return ofNullable(inspection.getAttributes()).map(Attributes::getYearBuilt).orElse(null);
    }

    public static String format(LocalDateTime localDateTime) {
        return ofNullable(localDateTime)
                .map(dt -> DEFAULT_DATE_FORMATTER.format(localDateTime))
                .orElse("");
    }

    public ProjectMessage toMessage() {
        var builder = ProjectMessage.newBuilder();
        acceptIfNotNull(builder::setStreetAddress, this.getStreetAddress());
        acceptIfNotNull(builder::setCity, this.getCity());
        acceptIfNotNull(builder::setState, this.getState());
        acceptIfNotNull(builder::setZipcode, this.getZipcode());
        acceptIfNotNull(builder::setServiceName, this.getServiceName());
        acceptIfNotNull(builder::setPolicyNumber, this.getPolicyNumber());
        acceptIfNotNull(builder::setInsuredBy, this.getInsuredBy());
        acceptIfNotNull(builder::setInsuredName, this.getInsuredName());
        acceptIfNotNull(builder::setInsuredPhone, this.getInsuredPhone());
        acceptIfNotNull(builder::setInsuredEmail, this.getInsuredEmail());
        acceptIfNotNull(builder::setAgentName, this.getAgentName());
        acceptIfNotNull(builder::setAgentPhone, this.getAgentPhone());
        acceptIfNotNull(builder::setAgentEmail, this.getAgentEmail());
        acceptIfNotNull(builder::setPolicyEffectiveDate, this.getPolicyEffectiveDate());
        acceptIfNotNull(builder::setYearBuilt, this.getYearBuilt());
        return builder.build();
    }
}
