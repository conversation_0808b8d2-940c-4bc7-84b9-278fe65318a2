package com.bees360.irs.util;

import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.regex.Pattern;

public class JsonLocalDateTimeMillisZoneOffsetFormatDateOnlyDeserializer
        extends JsonDeserializer<LocalDateTime> {

    private final String DATE_REGEX = "/Date\\(\\d+-\\d{4}\\)/";
    private final Pattern DATE_PATTERN = Pattern.compile(DATE_REGEX);

    @Override
    public LocalDateTime deserialize(
            com.fasterxml.jackson.core.JsonParser jsonParser,
            DeserializationContext deserializationContext)
            throws IOException {
        var dateStr = jsonParser.readValueAs(String.class);
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        if (!DATE_PATTERN.matcher(dateStr).matches()) {
            throw new IllegalArgumentException(
                    String.format("The date time should match regex %s.", DATE_REGEX));
        }
        dateStr = StringUtils.removeStart(dateStr, "/Date(");
        dateStr = StringUtils.removeEnd(dateStr, ")/");
        String[] parts = dateStr.split("-");
        var millis = Long.valueOf(parts[0]);
        var zoneOffSet = ZoneOffset.of("-" + parts[1]);
        // remain date only
        return Instant.ofEpochMilli(millis).atOffset(zoneOffSet).toLocalDate().atStartOfDay();
    }
}
