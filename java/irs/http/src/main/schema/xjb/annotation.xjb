<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<jaxb:bindings
    xmlns:jaxb="https://jakarta.ee/xml/ns/jaxb" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:annox="urn:jaxb.jvnet.org:annox"
    xsi:schemaLocation="https://jakarta.ee/xml/ns/jaxb https://jakarta.ee/xml/ns/jaxb/bindingschema_3_0.xsd"
    jaxb:extensionBindingPrefixes="xjc annox"
    version="3.0">

    <jaxb:bindings schemaLocation="../xsd/irs.xsd" node="/xs:schema">
        <jaxb:bindings node="xs:complexType[@name='Address']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Address")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Agent']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Agent")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfError']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfError")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfFormMeta']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfFormMeta")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfFormMetaField']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfFormMetaField")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfHazard']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfHazard")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfImportResult']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfImportResult")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfInspection']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfInspection")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfInspectionExport']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfInspectionExport")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfMessage']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfMessage")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfPhoto']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfPhoto")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfRecommendation']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfRecommendation")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfRecommendationPhoto']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfRecommendationPhoto")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfRecommendations']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfRecommendations")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfImportResult']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfImportResult")</annox:annotateClass>
        </jaxb:bindings>

        <jaxb:bindings node="xs:complexType[@name='AttachCaseFileRequest']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="AttachCaseFileRequest")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='AttachCaseFileResponse']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="AttachCaseFileResponse")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Attributes']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Attributes")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='CompletedInspectionFormMetaResponse']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="CompletedInspectionFormMetaResponse")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='CompletedInspectionPDFResponse']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="CompletedInspectionPDFResponse")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='CompletedInspectionPhotosResponse']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="CompletedInspectionPhotosResponse")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='CompletedInspectionRecsAndPhotosResponse']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="CompletedInspectionRecsAndPhotosResponse")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='CompletedInspectionsRequest']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="CompletedInspectionsRequest")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='CompletedInspectionsResult']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="CompletedInspectionsResult")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Credentials']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Credentials")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='DuplicateIssue']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="DuplicateIssue")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Error']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Error")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='FormMeta']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="FormMeta")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='FormMetaField']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="FormMetaField")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Hazard']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Hazard")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ImportInspectionsRequest']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ImportInspectionsRequest")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ImportResult']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ImportResult")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Inspection']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Inspection")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='InspectionExport']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="InspectionExport")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='InspectionInfoRequest']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="InspectionInfoRequest")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='InspectionUpdateResponse']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="InspectionUpdateResponse")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='MarkCompletedInspectionsExortedRequest']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="MarkCompletedInspectionsExortedRequest")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='MarkCompletedInspectionsExportedRequest']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="MarkCompletedInspectionsExportedRequest")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Message']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Message")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Photo']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Photo")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='PolicyHolder']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="PolicyHolder")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Recommendation']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Recommendation")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='RecommendationPhoto']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="RecommendationPhoto")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Recommendations']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Recommendations")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Underwriter']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Underwriter")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='Summary']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="Summary")</annox:annotateClass>
        </jaxb:bindings>
    </jaxb:bindings>

    <jaxb:bindings schemaLocation="../xsd/_base_serialization_arrays.xsd" node="/xs:schema">
        <jaxb:bindings node="xs:complexType[@name='ArrayOfstring']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfstring")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfKeyValueOfstringstring']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfKeyValueOfstringstring")</annox:annotateClass>
        </jaxb:bindings>
        <jaxb:bindings node="xs:complexType[@name='ArrayOfguid']">
            <annox:annotateClass>@jakarta.xml.bind.annotation.XmlRootElement(name="ArrayOfguid")</annox:annotateClass>
        </jaxb:bindings>
    </jaxb:bindings>

</jaxb:bindings>
