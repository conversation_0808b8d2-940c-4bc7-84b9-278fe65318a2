package com.bees360.firebase;

import static com.bees360.firebase.BaseEntityUtil.createFirebaseActivityEntity;
import static com.bees360.firebase.BaseEntityUtil.createMagicplanFirebaseActivityEntity;
import static com.bees360.firebase.BaseJobUtil.createFirebaseBatch;
import static com.bees360.firebase.BaseJobUtil.createFirebaseHover;
import static com.bees360.firebase.BaseJobUtil.createFirebaseIBeesMission;
import static com.bees360.firebase.BaseJobUtil.createFirebaseMagicplan;
import static com.bees360.firebase.BaseJobUtil.createFirebaseMission;
import static com.bees360.firebase.BaseJobUtil.createFirebaseProjectEntity;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.Comment;
import com.bees360.activity.impl.InMemoryActivityCommentManager;
import com.bees360.activity.impl.InMemoryStorage;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.firebase.domain.Batch;
import com.bees360.firebase.domain.Image;
import com.bees360.firebase.entity.FirebaseActivityEntity;
import com.bees360.firebase.entity.FirebaseCollection;
import com.bees360.firebase.enums.MissionStateEnum;
import com.bees360.firebase.listener.FirebaseImageUploadedListener;
import com.bees360.firebase.listener.config.FirebaseReRegisterListenerConfigs;
import com.bees360.job.JobDispatcher;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.job.registry.ResourceUploadedJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.job.util.BasicJobFuture;
import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.user.UserKeyProvider;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.GeoPoint;
import com.google.cloud.firestore.WriteBatch;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.cloud.FirestoreClient;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@ApplicationAutoConfig
@SpringBootTest()
@DirtiesContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringJUnitConfig
@Log4j2
class FirebaseServiceAppITest {
    private static final Function<String, String> PILOT_ID_CONVERTER =
            (pilotId) -> "converted_" + pilotId;

    @Import({
        FirebaseServiceApp.class,
        RabbitJobScheduler.class,
        RabbitJobDispatcher.class,
    })
    @Configuration
    static class Config {
        @Bean
        public Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @Bean
        FirebaseApi firebaseApi(Firestore firestore) {
            return new FirebaseApi(firestore);
        }

        @Bean
        JobDispatcher jobDispatcher(RabbitJobDispatcher jobDispatcher) {
            return jobDispatcher;
        }

        @Bean
        ActivityManager activityManager() {
            return new InMemoryActivityCommentManager(null, new InMemoryStorage());
        }

        @Bean
        UserKeyProvider userKeyProvider() {
            var userKeyProvider = Mockito.mock(UserKeyProvider.class);
            Mockito.when(userKeyProvider.findUserByKey(Mockito.any()))
                    .thenAnswer(
                            an -> {
                                var id = PILOT_ID_CONVERTER.apply(an.getArgument(0).toString());
                                return User.from(
                                        Message.UserMessage.newBuilder().setId(id).build());
                            });
            return userKeyProvider;
        }
    }

    @Setter @Getter private Runnable waiter;
    @Autowired private RabbitEventDispatcher eventDispatcher;
    @Autowired private FirebaseListenerManager firebaseListenerManager;
    @Autowired private final FirebaseApi firebaseApi;
    @Autowired private FirebaseReRegisterListenerConfigs firebaseReRegisterListenerConfigs;
    @Autowired JobDispatcher jobDispatcher;
    @Autowired InMemoryActivityCommentManager activityManager;

    FirebaseServiceAppITest(@Autowired FirebaseOptions firebaseOptions) throws SQLException {
        firebaseApi =
                new FirebaseApi(
                        FirestoreClient.getFirestore(
                                FirebaseApp.initializeApp(
                                        firebaseOptions, RandomStringUtils.randomAlphabetic(8))));
        setWaiter(
                () -> {
                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new IllegalStateException(e);
                    }
                });
    }

    @AfterAll
    void destroy() {
        firebaseListenerManager.close();
    }

    @Test
    void testMissionNestedImageUploaded() {
        String projectId = RandomStringUtils.randomNumeric(8);
        Image image1 = randomFirebaseImage(projectId);
        Image image2 = randomFirebaseImage(projectId);
        Set<String> received = new HashSet<>();

        var listener =
                new FirebaseImageUploadedListener(
                        job -> {
                            var executor =
                                    new AbstractJobExecutor<ResourceUploadedJob>() {

                                        @Override
                                        protected void handle(ResourceUploadedJob object) {
                                            log.info("Adding '{}'", object.getAlias());
                                            received.add(object.getAlias());
                                        }
                                    };
                            try {
                                executor.execute(job);
                            } catch (IOException e) {
                                throw new UncheckedIOException(e);
                            }
                            return new BasicJobFuture(job);
                        },
                        null,
                        null,
                        null);
        eventDispatcher.enlist(listener);

        try {
            // start batch upload image
            WriteBatch batch = firebaseApi.batch();
            batch.set(firebaseApi.collection("image").document(), image1);
            // nested image
            batch.set(
                    firebaseApi.collection("mission").document().collection("image").document(),
                    image2);
            FirebaseApi.exec(batch::commit);

            getWaiter().run();
            log.info("Received '{}'", Arrays.toString(received.toArray(String[]::new)));
            Assertions.assertTrue(received.contains(image1.getFileName()));
            Assertions.assertTrue(received.contains(image1.getFileNameLowerResolution()));
            Assertions.assertTrue(received.contains(image1.getFileNameMiddleResolution()));
            Assertions.assertTrue(received.contains(image2.getFileName()));
            Assertions.assertTrue(received.contains(image2.getFileNameLowerResolution()));
            Assertions.assertTrue(received.contains(image2.getFileNameMiddleResolution()));
        } finally {
            eventDispatcher.delist(listener);
        }
    }

    @Test
    void testReregister() {
        firebaseReRegisterListenerConfigs.registerListener();
        testMissionNestedImageUploaded();

        firebaseReRegisterListenerConfigs.registerListener();
        testMissionNestedImageUploaded();
    }

    private Image randomFirebaseImage(String projectId) {
        return Image.builder()
                .projectId(projectId)
                .fileName(
                        URLDecoder.decode(
                                "gs://image/origin/" + randomMD5(), StandardCharsets.UTF_8))
                .fileNameMiddleResolution(
                        URLDecoder.decode(
                                "gs://image/biggerThumbnails/" + randomMD5(),
                                StandardCharsets.UTF_8))
                .fileNameLowerResolution(
                        URLDecoder.decode(
                                "gs://image/thumbnails/" + randomMD5(), StandardCharsets.UTF_8))
                .originalFileName(RandomStringUtils.randomAscii(16))
                .categoryId(1)
                .relativeAltitude(RandomUtils.nextDouble())
                .imageHeight(RandomUtils.nextInt(400, 500))
                .imageWidth(RandomUtils.nextInt(400, 500))
                .fileSize(RandomUtils.nextLong())
                .weight(RandomUtils.nextDouble())
                .status(3)
                .gps(randomGps())
                .shootingTime(Timestamp.now())
                .tiffOrientation(RandomUtils.nextInt())
                .uploadTime(Timestamp.now())
                .updateTime(Timestamp.now())
                .build();
    }

    private String randomMD5() {
        return Base64.encodeBase64URLSafeString(DigestUtils.md5(RandomUtils.nextBytes(64)));
    }

    private GeoPoint randomGps() {
        return new GeoPoint(RandomUtils.nextDouble(0, 90), RandomUtils.nextDouble(0, 180));
    }

    @Test
    void testFirebaseProjectChanged() throws InterruptedException, ExecutionException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var projectEntity = createFirebaseProjectEntity(projectId);
        CountDownLatch latch = new CountDownLatch(1);
        var checkAndCounter =
                new FirebaseExecutor.FirebaseProjectChangedExecutor(
                        projectId,
                        projectEntity.getCreator_id(),
                        projectEntity.getCreator_name(),
                        projectEntity.getCreated_time(),
                        projectEntity.getUpdate_time(),
                        projectEntity.getNote(),
                        latch);
        jobDispatcher.enlist(checkAndCounter);
        String collectionName = FirebaseCollection.PROJECT_COLLECTION;
        firebaseApi.collection(collectionName).document().create(projectEntity);
        latch.await(5, TimeUnit.SECONDS);
        Assertions.assertEquals(0, latch.getCount());
    }

    @Test
    void testFirebaseActivityRecordAdded() throws InterruptedException, ExecutionException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        String entityId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        FirebaseActivityEntity activityRecord = createFirebaseActivityEntity(projectId, entityId);
        var content = activityRecord.getContent();
        // user id 9999 不会进行转换
        content.setCreatedBy("9999");
        String collectionName = FirebaseCollection.ACTIVITY_RECORD;
        var future = firebaseApi.collection(collectionName).document().create(activityRecord);
        future.get();
        Thread.sleep(2000);
        ActivityQuery query = new ActivityQuery();
        query.setProjectId(Long.valueOf(projectId));
        Assertions.assertEquals(1, activityManager.getActivities(query).size());
        var activity = activityManager.getActivities(query).get(0);
        assertActivityEquals(activityRecord.getContent(), activity);
    }

    @Test
    void testFirebaseActivityRecordAddedAndConvertedCreatedById()
            throws InterruptedException, ExecutionException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        String entityId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        FirebaseActivityEntity activityRecord = createFirebaseActivityEntity(projectId, entityId);
        var content = activityRecord.getContent();
        // 随机的 user id 会进行转换
        content.setCreatedBy(RandomStringUtils.randomAlphabetic(8));
        String collectionName = FirebaseCollection.ACTIVITY_RECORD;
        var future = firebaseApi.collection(collectionName).document().create(activityRecord);
        future.get();
        Thread.sleep(2000);
        ActivityQuery query = new ActivityQuery();
        query.setProjectId(Long.valueOf(projectId));
        Assertions.assertEquals(1, activityManager.getActivities(query).size());
        var activity = activityManager.getActivities(query).get(0);
        // 验证用户 id 应该被转换
        content.setCreatedBy(PILOT_ID_CONVERTER.apply(content.getCreatedBy()));
        assertActivityEquals(content, activity);
    }

    private void assertActivityEquals(
            FirebaseActivityEntity.FirebaseActivity firebaseActivity, Activity activity) {
        Assertions.assertEquals(
                firebaseActivity.getProjectId(), String.valueOf(activity.getProjectId()));
        Assertions.assertEquals(firebaseActivity.getEntityId(), activity.getEntityId());
        Assertions.assertEquals(firebaseActivity.getEntityCount(), activity.getEntityCount());
        Assertions.assertEquals(firebaseActivity.getEntityType(), activity.getEntityType());
        Assertions.assertEquals(firebaseActivity.getCreatedBy(), activity.getCreatedBy());
        Assertions.assertTrue(
                activity.getCreatedAt().toEpochMilli() < Instant.now().toEpochMilli());
        Assertions.assertEquals(firebaseActivity.getSource(), activity.getSource());
        Assertions.assertEquals(firebaseActivity.getAction(), activity.getAction());
        Assertions.assertEquals(firebaseActivity.getFieldName(), activity.getFiledName());
        Assertions.assertEquals(firebaseActivity.getFieldType(), activity.getFiledType());
        Assertions.assertEquals(firebaseActivity.getComment(), activity.getComment().getContent());
        List<Comment.Attachment> attachmentList = new ArrayList<>();
        activity.getComment().getAttachment().forEach(attachmentList::add);
        Assertions.assertEquals("filename", attachmentList.get(0).getFilename());
        Assertions.assertEquals("key", attachmentList.get(0).getUrl());
    }

    @Test
    void testMagicplanFirebaseActivityRecordAdded()
            throws InterruptedException, ExecutionException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        FirebaseActivityEntity activityRecord = createMagicplanFirebaseActivityEntity(projectId);

        String collectionName = FirebaseCollection.ACTIVITY_RECORD;
        var future = firebaseApi.collection(collectionName).document().create(activityRecord);
        future.get();
        Thread.sleep(1000);

        ActivityQuery query = new ActivityQuery();
        query.setProjectId(Long.valueOf(projectId));
        Assertions.assertEquals(1, activityManager.getActivities(query).size());
        var activity = activityManager.getActivities(query).get(0);
        Assertions.assertEquals(Long.parseLong(projectId), activity.getProjectId());
        Assertions.assertTrue(
                activity.getCreatedAt().toEpochMilli() < Instant.now().toEpochMilli());
    }

    @Test
    void testFirebaseBatchChanged() throws InterruptedException {
        var batchEntity = createFirebaseBatch();
        CountDownLatch latch = new CountDownLatch(1);
        var checkAndCounter =
                new FirebaseExecutor.FirebaseBatchChangedExecutor(
                        batchEntity.getStatus(),
                        batchEntity.getBasePay(),
                        batchEntity.getCreatedAt(),
                        batchEntity.getUpdatedAt(),
                        latch);
        jobDispatcher.enlist(checkAndCounter);
        String collectionName = Batch.COLLECTION_NAME;
        firebaseApi.collection(collectionName).document().create(batchEntity);
        latch.await(5, TimeUnit.SECONDS);
        Assertions.assertEquals(0, latch.getCount());
    }

    @Test
    void testFirebasePilotMissionChanged() throws InterruptedException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        String pilotId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var missionEntity = createFirebaseMission(projectId, pilotId);
        CountDownLatch latch = new CountDownLatch(1);
        var convertedPilotId = PILOT_ID_CONVERTER.apply(pilotId);
        var checkAndCounter =
                new FirebaseExecutor.FirebasePilotMissionChangedExecutor(
                        missionEntity.getMissionType(),
                        missionEntity.getLastUpdateTime(),
                        missionEntity.getStatus(),
                        missionEntity.getIsDeleted(),
                        missionEntity.getTasks(),
                        missionEntity.getTaskStatus(),
                        // pilot id converted
                        convertedPilotId,
                        latch);
        jobDispatcher.enlist(checkAndCounter);
        String collectionName = FirebaseCollection.MISSION_COLLECTION;
        firebaseApi.collection(collectionName).document().create(missionEntity);
        latch.await(5, TimeUnit.SECONDS);
        Assertions.assertEquals(0, latch.getCount());
        // 防止 checkAndCounter job executor 干扰testFirebasePilotMissionChangedAndPilotIdInBlackList测试
        jobDispatcher.delist(checkAndCounter);
    }

    @Test
    void testFirebasePilotMissionChangedAndPilotIdInBlackList() throws InterruptedException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        // id 9999 in black list should not be converted.
        String pilotId = "9999";
        var missionEntity = createFirebaseMission(projectId, pilotId);
        CountDownLatch latch = new CountDownLatch(1);
        var checkAndCounter =
                new FirebaseExecutor.FirebasePilotMissionChangedExecutor(
                        missionEntity.getMissionType(),
                        missionEntity.getLastUpdateTime(),
                        missionEntity.getStatus(),
                        missionEntity.getIsDeleted(),
                        missionEntity.getTasks(),
                        missionEntity.getTaskStatus(),
                        // pilot id not converted.
                        pilotId,
                        latch);
        jobDispatcher.enlist(checkAndCounter);
        String collectionName = FirebaseCollection.MISSION_COLLECTION;
        firebaseApi.collection(collectionName).document().create(missionEntity);
        latch.await(5, TimeUnit.SECONDS);
        Assertions.assertEquals(0, latch.getCount());
        jobDispatcher.delist(checkAndCounter);
    }

    @Test
    void testFirebasePilotMissionCompletedStuck() throws InterruptedException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        String pilotId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var missionEntity = createFirebaseMission(projectId, pilotId);
        CountDownLatch latch = new CountDownLatch(2);
        // 在变更mission状态到stuck之前会设置该future的值
        var beforeUpdateMissionFuture = new CompletableFuture<Instant>();
        var checkAndCounter =
                new FirebaseExecutor.FirebasePilotMissionCompletedStuckExecutor(
                        missionEntity.getPilotId(),
                        missionEntity.getProject().getProjectId(),
                        latch,
                        beforeUpdateMissionFuture);
        jobDispatcher.enlist(checkAndCounter);
        String collectionName = FirebaseCollection.MISSION_COLLECTION;
        String documentId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        firebaseApi.collection(collectionName).document(documentId).create(missionEntity);
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));

        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("status", MissionStateEnum.CHECK_OUT.getCode());
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));

        // 在更新状态之前的时间, 可以用此时间与 FirebaseMissionCompletedStuckV2 任务里的
        // statusUpdateTime对进行对比，校验状态更新时间的正确性。
        beforeUpdateMissionFuture.complete(Instant.now());

        // 从其他状态变为COMPLETED_STUCK状态时触发FirebaseMissionCompletedStuckV2(Type=ADDED)
        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("status", MissionStateEnum.COMPLETED_STUCK.getCode());

        // 状态为COMPLETED_STUCK时，即使改变其他字段也不会触发FirebaseMissionCompletedStuckV2(Type=MODIFIED)
        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("lastUpdateTime", Timestamp.now());

        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertEquals(1, latch.getCount());
    }

    @Test
    void testFirebasePilotMissionCheckOut() throws InterruptedException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        String pilotId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var missionEntity = createFirebaseMission(projectId, pilotId);
        CountDownLatch latch = new CountDownLatch(2);
        // 在变更mission状态到check out之前会设置该future的值
        var beforeUpdateMissionFuture = new CompletableFuture<Instant>();
        var convertedPilotId = "converted_" + missionEntity.getPilotId();
        var checkAndCounter =
                new FirebaseExecutor.FirebasePilotMissionCheckOutJobExecutor(
                        convertedPilotId,
                        missionEntity.getProject().getProjectId(),
                        latch,
                        beforeUpdateMissionFuture);
        jobDispatcher.enlist(checkAndCounter);
        String collectionName = FirebaseCollection.MISSION_COLLECTION;
        String documentId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        firebaseApi.collection(collectionName).document(documentId).create(missionEntity);
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));

        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("status", MissionStateEnum.CHECK_IN.getCode());
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));

        beforeUpdateMissionFuture.complete(Instant.now().minusMillis(100));

        // 从其他状态变为CHECK_OUT状态时触发FirebaseMissionCheckOutJob(Type=ADDED)
        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update(
                        "status",
                        MissionStateEnum.CHECK_OUT.getCode(),
                        "lastUpdateTime",
                        Timestamp.now());

        // 状态为CHECK_OUT时，即使改变其他字段也不会触发FirebaseMissionCheckOutJob(Type=MODIFIED)
        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("lastUpdateTime", Timestamp.now());

        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));
        Assertions.assertEquals(1, latch.getCount());
    }

    @Test
    void testFirebaseIBeesMissionChanged() throws InterruptedException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var iBeesMissionEntity = createFirebaseIBeesMission(projectId);
        CountDownLatch latch = new CountDownLatch(1);
        var checkAndCounter =
                new FirebaseExecutor.FirebaseIBeesMissionChangedExecutor(
                        iBeesMissionEntity.getProject(),
                        iBeesMissionEntity.getLastUpdateTime(),
                        iBeesMissionEntity.getStatus(),
                        iBeesMissionEntity.getIsDeleted(),
                        iBeesMissionEntity.getTasks(),
                        iBeesMissionEntity.getTaskStatus(),
                        iBeesMissionEntity.getCheckOutReason(),
                        iBeesMissionEntity.getCallRecord(),
                        iBeesMissionEntity.getFeedback(),
                        latch);
        jobDispatcher.enlist(checkAndCounter);
        String collectionName = FirebaseCollection.IBEES_MISSION_COLLECTION;
        firebaseApi.collection(collectionName).document().create(iBeesMissionEntity);
        latch.await(5, TimeUnit.SECONDS);
        Assertions.assertEquals(0, latch.getCount());
    }

    @Test
    void testFirebaseIBeesMissionCompleted() throws InterruptedException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var iBeesMissionEntity = createFirebaseIBeesMission(projectId);
        CountDownLatch latch = new CountDownLatch(1);
        // 在变更mission状态到completed之前会设置该future的值
        var beforeUpdateMissionFuture = new CompletableFuture<Instant>();
        var checkAndCounter =
                new FirebaseExecutor.FirebaseIBeesMissionCompletedExecutor(
                        projectId, latch, beforeUpdateMissionFuture);
        jobDispatcher.enlist(checkAndCounter);
        String collectionName = FirebaseCollection.IBEES_MISSION_COLLECTION;
        String documentId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        firebaseApi.collection(collectionName).document(documentId).create(iBeesMissionEntity);
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));

        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("status", MissionStateEnum.CHECK_IN.getCode());
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));

        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("status", MissionStateEnum.COMPLETED_STUCK.getCode());
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));
        // 在更新状态之前的时间, 可以用此时间与 FirebaseIBeesMissionCompletedV2 任务里的
        // statusUpdateTime对进行对比，校验状态更新时间的正确性。
        beforeUpdateMissionFuture.complete(Instant.now());
        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("status", MissionStateEnum.COMPLETED.getCode());
        latch.await(5, TimeUnit.SECONDS);
        Assertions.assertEquals(0, latch.getCount());
    }

    @Test
    void testFirebaseHoverJobChanged() throws InterruptedException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var hoverEntity = createFirebaseHover(projectId);
        CountDownLatch latch = new CountDownLatch(1);
        var checkAndCounter =
                new FirebaseExecutor.FirebaseHoverJobChangedExecutor(
                        hoverEntity.getProjectId(),
                        hoverEntity.getState(),
                        hoverEntity.getUpdateTime(),
                        hoverEntity.getBees360HoverState(),
                        hoverEntity.getDeliverableId(),
                        latch);
        jobDispatcher.enlist(checkAndCounter);

        String collectionName = FirebaseCollection.HOVER_JOB;
        String documentId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        firebaseApi.collection(collectionName).document(documentId).create(hoverEntity);
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));

        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("updateTime", Timestamp.now());
        latch.await(5, TimeUnit.SECONDS);
        Assertions.assertEquals(0, latch.getCount());
    }

    @Test
    void testFirebaseMagicplanChanged() throws InterruptedException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var magicplanEntity = createFirebaseMagicplan(projectId);
        CountDownLatch latch = new CountDownLatch(1);
        var checkAndCounter =
                new FirebaseExecutor.FirebaseMagicplanChangedExecutor(
                        magicplanEntity.getProjectId(),
                        magicplanEntity.getMissionId(),
                        magicplanEntity.getPdf(),
                        magicplanEntity.getUpdateTime(),
                        latch);
        jobDispatcher.enlist(checkAndCounter);

        String collectionName = FirebaseCollection.MAGICPLAN;
        String documentId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        firebaseApi.collection(collectionName).document(documentId).create(magicplanEntity);
        Assertions.assertFalse(latch.await(1, TimeUnit.SECONDS));

        firebaseApi
                .collection(collectionName)
                .document(documentId)
                .update("updateTime", Timestamp.now());
        latch.await(5, TimeUnit.SECONDS);
        Assertions.assertEquals(0, latch.getCount());
    }
}
