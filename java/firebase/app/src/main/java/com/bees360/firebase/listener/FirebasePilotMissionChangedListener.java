package com.bees360.firebase.listener;

import com.bees360.event.registry.FirebasePilotMissionChanged;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseMissionV2;
import com.bees360.job.util.EventTriggeredJobMultiple;
import com.google.gson.Gson;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
public class FirebasePilotMissionChangedListener
        extends EventTriggeredJobMultiple<FirebasePilotMissionChanged> {

    private final Duration retryDelay = Duration.ofMinutes(1);
    private static final Gson gson = new Gson();

    public FirebasePilotMissionChangedListener(@Autowired JobScheduler jobScheduler) {
        super(jobScheduler);
        log.info("Created '{}(JobScheduler={}'", this, this.jobScheduler);
    }

    @Override
    protected Iterable<Job> convert(FirebasePilotMissionChanged event) {
        var missionList =
                event.getDocumentsChanged().stream()
                        .map(json -> gson.fromJson(json, SerializableFirebaseMissionV2.class))
                        .filter(entity -> !entity.needIgnore())
                        .map(this::fillPilotId)
                        .collect(Collectors.toList());
        List<Job> jobs = new ArrayList<>();
        for (var mission : missionList) {
            Job job = JobPayloads.encode(String.valueOf(mission.hashCode()), mission);
            jobs.add(RetryableJob.of(job, 3, retryDelay, 1.5F));
        }
        return jobs;
    }

    private SerializableFirebaseMissionV2 fillPilotId(SerializableFirebaseMissionV2 mission) {
        if (mission.getPilotReference() == null) {
            return mission;
        }
        mission.setPilotId(mission.getPilotReference().getDocumentId());
        return mission;
    }
}
