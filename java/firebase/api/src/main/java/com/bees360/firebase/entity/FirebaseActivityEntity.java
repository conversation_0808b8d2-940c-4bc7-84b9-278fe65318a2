package com.bees360.firebase.entity;

import com.bees360.activity.Activity;
import com.bees360.activity.Message;
import com.bees360.activity.Message.ActivityMessage;
import com.bees360.user.Message.UserMessage;
import com.bees360.user.Pilot;
import com.bees360.util.CollectionUtils;
import com.bees360.util.DateTimes;
import com.google.cloud.Timestamp;
import com.google.common.base.Preconditions;

import lombok.Getter;
import lombok.Setter;

import org.apache.logging.log4j.util.Strings;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Getter
@Setter
public class FirebaseActivityEntity {
    public static final String webActivity = "WebActivity";
    private String activity_name;
    private FirebaseActivity content;
    private Timestamp create_time;
    private Timestamp update_time;

    @Getter
    @Setter
    public static class FirebaseActivity {
        String projectId;
        String project_id;
        String action;
        String createdBy;
        String comment;
        long createdAt;
        String entityId;
        String entityType;
        int entityCount;
        // 目前filedName和fieldName两个字段都还存在，需要兼容
        String filedName;
        String fieldName;
        // 目前filedType和fieldType两个字段都还存在，需要兼容
        String filedType;
        String fieldType;
        String value;
        String oldValue;
        String source;
        String level;
        String pilotId;
        String operationsManager;
        Set<String> visibility;
        List<FirebaseActivityAttachment> attachment;

        public Activity toActivity() {

            Preconditions.checkArgument(projectId != null, "Project Id should not be null");

            var activityBuilder = ActivityMessage.newBuilder();
            activityBuilder.setProjectId(Long.parseLong(projectId));
            Optional.ofNullable(action).ifPresent(activityBuilder::setAction);
            activityBuilder.setCreatedAt(DateTimes.toTimestamp(Instant.ofEpochMilli(createdAt)));
            Optional.ofNullable(createdBy)
                    .ifPresent(
                            createdBy ->
                                    activityBuilder.setCreatedBy(
                                            UserMessage.newBuilder().setId(createdBy).build()));

            var entityBuilder = ActivityMessage.Entity.newBuilder();
            entityBuilder.setCount(entityCount);
            Optional.ofNullable(entityId).ifPresent(entityBuilder::setId);
            Optional.ofNullable(entityType).ifPresent(entityBuilder::setType);
            Optional.ofNullable(getPilot()).ifPresent(e -> entityBuilder.setPilot(e.toMessage()));
            activityBuilder.setEntity(entityBuilder);

            var fieldBuilder = ActivityMessage.Field.newBuilder();
            // 目前filedName和fieldName两个字段都还存在，需要兼容
            Optional.ofNullable(filedName).ifPresent(fieldBuilder::setName);
            Optional.ofNullable(fieldName).ifPresent(fieldBuilder::setName);
            // 目前filedType和fieldType两个字段都还存在，需要兼容
            Optional.ofNullable(filedType).ifPresent(fieldBuilder::setType);
            Optional.ofNullable(fieldType).ifPresent(fieldBuilder::setType);
            Optional.ofNullable(value).ifPresent(fieldBuilder::setValue);
            Optional.ofNullable(oldValue).ifPresent(fieldBuilder::setOldValue);
            activityBuilder.setField(fieldBuilder);

            Optional.ofNullable(source).ifPresent(activityBuilder::setSource);
            Optional.ofNullable(level)
                    .ifPresent(
                            level ->
                                    activityBuilder.setLevel(
                                            ActivityMessage.ActivityLevel.valueOf(level)));
            if (!Strings.isBlank(comment) || CollectionUtils.isNotEmpty(attachment)) {
                var commentBuilder = Message.CommentMessage.newBuilder();
                commentBuilder.setProjectId(Long.parseLong(projectId));
                Optional.ofNullable(comment).ifPresent(commentBuilder::setContent);
                commentBuilder.setCreatedAt(DateTimes.toTimestamp(Instant.ofEpochMilli(createdAt)));
                Optional.ofNullable(createdBy)
                        .ifPresent(
                                createdBy ->
                                        commentBuilder.setCreatedBy(
                                                UserMessage.newBuilder().setId(createdBy).build()));
                Optional.ofNullable(source).ifPresent(commentBuilder::setSource);
                if (CollectionUtils.isNotEmpty(attachment)) {
                    attachment.forEach(
                            e ->
                                    commentBuilder.addAttachment(
                                            Message.CommentMessage.Attachment.newBuilder()
                                                    .setFilename(e.getFilename())
                                                    .setUrl(e.getKey())));
                }
                activityBuilder.setComment(commentBuilder);
            }
            Optional.ofNullable(visibility).ifPresent(activityBuilder::addAllVisibility);

            return Activity.of(activityBuilder.build());
        }

        public Pilot getPilot() {
            com.bees360.user.Message.UserMessage pilot =
                    com.bees360.user.Message.UserMessage.getDefaultInstance();
            com.bees360.user.Message.UserMessage om =
                    com.bees360.user.Message.UserMessage.getDefaultInstance();
            if (Strings.isNotBlank(pilotId)) {
                pilot = pilot.toBuilder().setId(pilotId).build();
            }
            if (Strings.isNotBlank(operationsManager)) {
                om = pilot.toBuilder().setId(operationsManager).build();
            }
            return Pilot.from(
                    com.bees360.user.Message.PilotMessage.newBuilder()
                            .setPilotUser(pilot)
                            .setOperationsManager(om)
                            .build());
        }

        @Getter
        @Setter
        public static class FirebaseActivityAttachment {
            private String filename;
            private String key;
            private String url;
        }
    }
}
