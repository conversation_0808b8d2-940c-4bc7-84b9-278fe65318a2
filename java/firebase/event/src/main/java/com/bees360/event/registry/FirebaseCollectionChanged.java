package com.bees360.event.registry;

import com.google.cloud.firestore.DocumentChange;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ToString
@Event
public class FirebaseCollectionChanged {

    @Getter @Setter private String collectionName;
    @Getter @Setter private DocumentChange.Type type;
    @Getter @Setter private List<String> documentsChanged;

    public FirebaseCollectionChanged() {}

    public FirebaseCollectionChanged(
            String collectionName, DocumentChange.Type type, List<String> documentsChanged) {
        this.collectionName = collectionName;
        this.type = type;
        this.documentsChanged = documentsChanged;
    }
}
