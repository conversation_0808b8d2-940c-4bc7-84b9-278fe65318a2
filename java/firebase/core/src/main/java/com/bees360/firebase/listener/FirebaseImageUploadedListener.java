package com.bees360.firebase.listener;

import com.bees360.event.registry.FirebaseImageUploaded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.firebase.domain.Image;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.ResourceUploadedJob;
import com.google.cloud.firestore.DocumentChange;
import com.google.gson.Gson;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
public class FirebaseImageUploadedListener
        extends AbstractNamedEventListener<FirebaseImageUploaded> {
    private final JobScheduler jobScheduler;
    private static final String GS_RESOURCE_PREFIX = "gs://";
    private static final Pattern IGNORE_PATTERN =
            Pattern.compile(
                    "^project/\\d+/images/(origin|thumbnails|biggerThumbnails)/((beespilot_app)|(beespilot_io)).*\\.((JPE?G)|(jpe?g))$");
    private static final Pattern PREFIX_PATTERN = Pattern.compile("^\\w+://.*$");
    private static final Gson gson = new Gson();
    private final Integer retryCount;
    private final Duration retryDelay;
    private final Float retryDelayIncreaseFactor;

    public FirebaseImageUploadedListener(
            JobScheduler jobScheduler,
            @Nullable Integer retryCount,
            @Nullable Duration retryDelay,
            @Nullable Float retryDelayIncreaseFactor) {
        this.jobScheduler = jobScheduler;
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.retryDelayIncreaseFactor = retryDelayIncreaseFactor;

        log.info("Created '{}(jobScheduler={})'", this, this.jobScheduler);
    }

    private List<Image> fetchImage(List<String> jsons) {
        return jsons.stream()
                .map(this::fetch)
                .filter(Objects::nonNull)
                .collect(Collectors.toUnmodifiableList());
    }

    private Image fetch(String json) {
        try {
            return gson.fromJson(json, Image.class);
        } catch (RuntimeException e) {
            log.error(
                    "Failed to decode image '{}', it will not be retried, please solve it"
                            + " manually.",
                    json,
                    e);
        }
        return null;
    }

    private void publishGSResourceUploaded(List<Image> images) {
        try {
            images.stream()
                    .filter(image -> !IGNORE_PATTERN.matcher(image.getFileName()).matches())
                    .map(this::asList)
                    .flatMap(List::stream)
                    .map(this::encode)
                    .forEach(jobScheduler::schedule);
        } catch (RuntimeException e) {
            log.error(
                    "Failed to publish gs resource uploaded event, it will not be retried, please"
                            + " solve it manually.",
                    e);
        }
    }

    private Job encode(Object o) {
        Job job = JobPayloads.encode(o);
        if (retryCount != null && retryCount > 1) {
            return RetryableJob.of(job, retryCount, retryDelay, retryDelayIncreaseFactor);
        }
        return job;
    }

    private List<ResourceUploadedJob> asList(Image image) {
        return Stream.of(
                        getResource(image.getProjectId(), image.getFileName()),
                        getResource(image.getProjectId(), image.getFileNameMiddleResolution()),
                        getResource(image.getProjectId(), image.getFileNameLowerResolution()))
                .collect(Collectors.toUnmodifiableList());
    }

    private ResourceUploadedJob getResource(String projectId, String resourceKey) {
        String key = getKey(projectId, resourceKey);
        String alias =
                PREFIX_PATTERN.matcher(resourceKey).matches()
                        ? resourceKey
                        : "bees360://" + resourceKey;
        return new ResourceUploadedJob(key, alias);
    }

    /** 在将图片资源同步到资源服务器时将图片的key进行转化。 将app上传的资源的key传换成业务中使用的key。 */
    protected String getKey(String projectId, String resourceKey) {
        return getImageKey(projectId, resourceKey);
    }

    /**
     * 如果图片的 URL 以 GS_RESOURCE_PREFIX 开头说明图片储存在谷歌云中, 应该将key前缀去掉
     *
     * @param projectId projectId
     * @param imagePath 格式为project/${projectId}/images/${type}/name 或者 gs://image/${type}/name
     * @return 将图片路径转化过后图片的key
     */
    public static String getImageKey(String projectId, String imagePath) {
        if (imagePath.startsWith(GS_RESOURCE_PREFIX)) {
            return imagePath.replaceFirst(GS_RESOURCE_PREFIX, StringUtils.EMPTY);
        }
        return imagePath;
    }

    @Override
    public void handle(FirebaseImageUploaded event) {
        if (DocumentChange.Type.ADDED.equals(event.getType())) {
            publishGSResourceUploaded(fetchImage(event.getDocumentsChanged()));
        }
        return;
    }
}
