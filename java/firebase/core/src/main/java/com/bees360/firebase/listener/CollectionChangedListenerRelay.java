package com.bees360.firebase.listener;

import com.bees360.event.EventPublisher;
import com.bees360.event.registry.Events;
import com.bees360.event.registry.FirebaseCollectionChanged;
import com.bees360.firebase.FirebaseListener;
import com.bees360.firebase.domain.FirebaseEntity;
import com.google.cloud.firestore.DocumentChange;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.FirestoreException;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.firestore.v1.RunQueryRequest;
import com.google.firestore.v1.StructuredQuery;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
public class CollectionChangedListenerRelay implements FirebaseListener {
    private final Gson gson = new GsonBuilder().serializeSpecialFloatingPointValues().create();
    private String collectionName;
    private String routingKey;

    private final EventPublisher eventPublisher;

    private final Function<Firestore, Query> queryBuilder;
    private final boolean skipFirstVisited;

    @Override
    public boolean skipFirstVisited() {
        return skipFirstVisited;
    }

    public CollectionChangedListenerRelay(
            EventPublisher eventPublisher,
            Function<Firestore, Query> queryBuilder,
            boolean skipFirstVisited) {
        this.eventPublisher = eventPublisher;
        this.queryBuilder = queryBuilder;
        this.skipFirstVisited = skipFirstVisited;
    }

    public CollectionChangedListenerRelay(
            EventPublisher eventPublisher,
            Function<Firestore, Query> queryBuilder,
            boolean skipFirstVisited,
            String routingKey) {
        this.eventPublisher = eventPublisher;
        this.queryBuilder = queryBuilder;
        this.skipFirstVisited = skipFirstVisited;
        this.routingKey = routingKey;
    }

    @Override
    public Query getQuery(Firestore firestore) {
        return queryBuilder.apply(firestore);
    }

    @Override
    public DocumentReference getDocumentReference(Firestore firestore) {
        return null;
    }

    @Override
    public void querySnapshotHandler(QuerySnapshot querySnapshot) {
        if (collectionName == null) {
            Query query = querySnapshot.getQuery();
            collectionName = initCollectionName(query);
        }
        List<DocumentChange> documentChanges = querySnapshot.getDocumentChanges();
        if (documentChanges.isEmpty()) {
            log.info("No changes for this listening round for collection:{}.", collectionName);
            return;
        }

        HashMap<DocumentChange.Type, List<String>> map = new HashMap<>();
        map.put(DocumentChange.Type.ADDED, new ArrayList<>());
        map.put(DocumentChange.Type.MODIFIED, new ArrayList<>());
        map.put(DocumentChange.Type.REMOVED, new ArrayList<>());
        documentChanges.forEach(
                doc -> {
                    DocumentChange.Type type = doc.getType();
                    QueryDocumentSnapshot document = doc.getDocument();
                    String id = document.getId();
                    String path = document.getReference().getPath();
                    Map<String, Object> data = document.getData();
                    // convert DocumentReference to String of form "/<collection>/<id>
                    HashMap<String, Object> newData = new HashMap<>(data);
                    data.forEach(
                            (k, v) -> {
                                if (v instanceof DocumentReference) {
                                    FirebaseEntity.Reference docRef =
                                            FirebaseEntity.fromFBDocumentReference(
                                                    (DocumentReference) v);
                                    newData.put(k + "Reference", docRef);
                                }
                            });
                    newData.put(FirebaseEntity.FIELD_ID, id);
                    newData.put(FirebaseEntity.FIELD_PATH, path);
                    newData.put(FirebaseEntity.FIELD_TYPE, type);
                    newData.put(FirebaseEntity.UPDATE_TIME, document.getUpdateTime());
                    String documentStr = gson.toJson(newData);
                    map.get(type).add(documentStr);
                });
        log.info(
                "Received firebase document changes, collection" + " name: {}, count: {}",
                collectionName,
                documentChanges.size());

        map.forEach((type, docs) -> publish(docs, type, routingKey));
    }

    private String initCollectionName(Query query) {
        RunQueryRequest runQueryRequest = query.toProto();
        StructuredQuery structuredQuery = runQueryRequest.getStructuredQuery();
        return structuredQuery.getFromList().stream()
                .map(StructuredQuery.CollectionSelector::getCollectionId)
                .collect(Collectors.joining("&"));
    }

    private void publish(List<String> documentList, DocumentChange.Type type, String routingKey) {
        if (documentList.isEmpty()) {
            return;
        }
        FirebaseCollectionChanged firebaseCollectionChanged =
                new FirebaseCollectionChanged(collectionName, type, documentList);
        String eventName;
        if (routingKey != null) {
            eventName = routingKey + "." + type.name();
        } else {
            eventName = "firebase_collection_changed." + collectionName + "." + type.name();
        }
        eventPublisher.publish(eventName, Events.encode(firebaseCollectionChanged));
        log.info(
                "Publish FirebaseCollectionChanged event, collection name: {}, eventName: {},"
                        + " document count: {}",
                collectionName,
                eventName,
                documentList.size());
    }

    @Override
    public void errorHandler(FirestoreException e) {
        log.error("Failed to listen on '{}'.", collectionName, e);
    }
}
