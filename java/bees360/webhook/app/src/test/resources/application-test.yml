spring:
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
  jooq:
    sql-dialect: POSTGRES
  profiles:
    include: actuator

server:
  port: 8080

infera:
  auth:
    token: FAKE_AUTH_TOKEN
  webhook:
    url:
      override: http://localhost:8080

twilio:
  auth:
    token: FAKE_AUTH_TOKEN
  webhook:
    url:
      override: http://localhost:8080

beespilot:
  auth:
    token: FAKE_AUTH_TOKEN
  webhook:
    url:
      override: http://localhost:8080

realms9:
  auth:
    token: FAKE_AUTH_TOKEN
  webhook:
    url:
      override: http://localhost:8080

authing:
  auth:
    token: FAKE_AUTH_TOKEN

hover:
  auth:
    token: "APIAuth 3704:"

webhook:
  realms9:
    publisher:
      authToken: "FAKE_AUTH_TOKEN"
      uriMap:
        jobCompleted: "http://localhost:8080/webhook/realms9/job_completed"
  app:
    rabbit:
      enabled: false
    beespilot:
      events:
        - beespilot-event-name: "user.add"
          routing-key: beespilot_event.beespilot_user_event.beespilot_user_created_event
        - beespilot-event-name: "user.update"
          routing-key: beespilot_event.beespilot_user_event.beespilot_user_updated_event
        - beespilot-event-name: "user.delete"
          routing-key: beespilot_event.beespilot_user_event.beespilot_user_deleted_event
        - beespilot-event-name: "json_array_nested_event_test"
          routing-key: json_array_nested_event
    event-converter:
      enabled: true
      pilot-id-black-list: [ "9999" ]
      events:
        - event-name: "pilot_next_day_show_confirm"
          pilot-id-paths: [ "pilot_id" ]
grpc:
  client:
    userKeyProvider:
      enabled: false
lago:
  auth:
    token: FAKE_AUTH_TOKEN
