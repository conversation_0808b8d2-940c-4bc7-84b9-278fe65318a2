package com.bees360.webhook.config;

import com.bees360.webhook.WebhookEventConverter;
import com.bees360.webhook.WebhookRegistryContext;
import com.bees360.webhook.WebhookValidator;
import com.bees360.webhook.deserializer.LagoEventConverter;
import com.bees360.webhook.validator.HttpRequestHeaderValidator;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Log4j2
@Configuration
public class LagoConfig {

    /** 增加serializeNulls,否则会导致后续校验时字段缺失 */
    public static final Gson gson =
            new GsonBuilder()
                    .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                    .serializeNulls()
                    .create();

    @Bean
    public WebhookValidator lagoWebhookValidator(@Value("${lago.auth.token}") String token) {
        return new HttpRequestHeaderValidator(token, "HmacSHA256", "X-Lago-Signature");
    }

    @Bean
    public WebhookRegistryContext lagoWebhookRegistryContext(
            WebhookValidator lagoWebhookValidator) {
        Map<String, WebhookEventConverter> map = new HashMap<>();
        map.put("lago", new LagoEventConverter(gson));
        WebhookRegistryContext lagoWebhookRegistry =
                new WebhookRegistryContext(lagoWebhookValidator, map);
        log.info("Created {}", lagoWebhookRegistry);
        return lagoWebhookRegistry;
    }
}
