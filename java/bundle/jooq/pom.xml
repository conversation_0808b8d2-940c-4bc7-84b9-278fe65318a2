<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bees360-bundle</artifactId>
        <groupId>com.bees360</groupId>
        <version>${revision}${changelist}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bees360-bundle-jooq</artifactId>

    <properties>
        <jooq-tables>
            bundle | bundle_contact | bundle_state_history | bundle_status_history | project_state_change_reason | bundle_days_old | bundle_tag | bundle_member | project_group | project_state_change_reason
        </jooq-tables>
        <jooq-package>com.bees360.jooq.persistent.bundle</jooq-package>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-bundle-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-util</artifactId>
        </dependency>

        <!-- test -->
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-bundle-api</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
            <type>test-jar</type>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen-maven</artifactId>
                <executions>
                    <execution>
                        <id>convergence</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
