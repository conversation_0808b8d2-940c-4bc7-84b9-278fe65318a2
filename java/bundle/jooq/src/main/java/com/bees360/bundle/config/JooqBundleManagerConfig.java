package com.bees360.bundle.config;

import com.bees360.address.AddressProvider;
import com.bees360.bundle.JooqBundleManager;
import com.bees360.bundle.JooqBundleMemberManager;
import com.bees360.bundle.JooqBundleStateManager;
import com.bees360.contract.ContractRepository;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.state.ProjectStateChangeReasonProvider;

import org.jooq.DSLContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    JooqConfig.class,
})
@Configuration
public class JooqBundleManagerConfig {
    @Bean
    JooqBundleManager jooqBundleManager(
            DSLContext dsl,
            AddressProvider addressProvider,
            ContractRepository jooqContractRepository,
            BatchProjectCreator batchProjectCreator,
            ExternalIntegrationProvider externalIntegrationProvider,
            ProjectGroupManager projectGroupManager,
            ProjectStateChangeReasonProvider projectStateChangeReasonProvider) {
        return new JooqBundleManager(
                dsl,
                addressProvider,
                jooqContractRepository,
                batchProjectCreator,
                externalIntegrationProvider,
                projectGroupManager,
                projectStateChangeReasonProvider);
    }

    @Bean
    JooqBundleMemberManager jooqBundleMemberManager(DSLContext dsl) {
        return new JooqBundleMemberManager(dsl);
    }

    @Bean
    JooqBundleStateManager jooqBundleStateManager(DSLContext dsl) {
        return new JooqBundleStateManager(dsl);
    }
}
