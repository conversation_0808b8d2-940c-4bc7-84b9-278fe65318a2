package com.bees360.bundle;

import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE;
import static com.bees360.jooq.persistent.bundle.Tables.PROJECT_STATE_CHANGE_REASON;

import static org.jooq.impl.DSL.choose;

import lombok.extern.log4j.Log4j2;

import org.jooq.DSLContext;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

@Log4j2
public class JooqBundleStateManager implements BundleStateManager {
    private final DSLContext dsl;

    public JooqBundleStateManager(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    @Transactional
    public boolean changeState(
            String bundleId, Message.BundleState state, String changeReason, String changedBy) {
        log.debug(
                "Changing state of bundle {} to {} by {} with reason {}",
                bundleId,
                state,
                changedBy,
                changeReason);
        var changeReasonCondition =
                (PROJECT_STATE_CHANGE_REASON
                                .ID
                                .eq(changeReason)
                                .or(PROJECT_STATE_CHANGE_REASON.KEY.eq(changeReason))
                                .or(PROJECT_STATE_CHANGE_REASON.DISPLAY_TEXT.eq(changeReason)))
                        .and(PROJECT_STATE_CHANGE_REASON.IS_DELETED.isFalse());
        var changeReasonId =
                dsl.select(PROJECT_STATE_CHANGE_REASON.ID)
                        .from(PROJECT_STATE_CHANGE_REASON)
                        .where(changeReasonCondition)
                        .orderBy(
                                choose().when(PROJECT_STATE_CHANGE_REASON.ID.eq(changeReason), 1)
                                        .when(
                                                PROJECT_STATE_CHANGE_REASON.DISPLAY_TEXT.eq(
                                                        changeReason),
                                                2)
                                        .when(PROJECT_STATE_CHANGE_REASON.KEY.eq(changeReason), 3)
                                        .otherwise(4)
                                        .asc())
                        .limit(1)
                        .forUpdate()
                        .fetchOne(PROJECT_STATE_CHANGE_REASON.ID);
        if (changeReasonId == null) {
            throw new IllegalArgumentException(
                    String.format("Change reason %s not existed", changeReason));
        }
        var condition = BUNDLE.STATE.ne(state.name()).and(BUNDLE.ID.eq(bundleId));
        return dsl.update(BUNDLE)
                        .set(BUNDLE.STATE, state.name())
                        .set(BUNDLE.LAST_STATE_CHANGE_REASON_ID, changeReasonId)
                        .set(BUNDLE.UPDATED_BY, changedBy)
                        .set(BUNDLE.UPDATED_AT, Instant.now())
                        .where(condition)
                        .execute()
                > 0;
    }
}
