package com.bees360.bundle;

import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE_MEMBER;
import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.project.member.RoleEnum;

import lombok.extern.log4j.Log4j2;

import org.jooq.DSLContext;
import org.jooq.tools.StringUtils;

import java.util.List;

@Log4j2
public class JooqBundleMemberManager implements BundleMemberManager {
    private final DSLContext dsl;

    public JooqBundleMemberManager(DSLContext dsl) {
        this.dsl = dsl;
        log.info("Created {}(dsl={}).", this, this.dsl);
    }

    @Override
    public boolean setMember(String bundleId, RoleEnum role, String userId, String opUserId) {
        log.debug(
                "Setting bundle member: bundleId {}, role {}, userId {}, opUserId {}",
                bundleId,
                role,
                userId,
                opUserId);
        if (StringUtils.isEmpty(userId)) {
            return dsl.update(BUNDLE_MEMBER)
                            .set(BUNDLE_MEMBER.DELETED, true)
                            .where(
                                    BUNDLE_MEMBER
                                            .BUNDLE_ID
                                            .eq(bundleId)
                                            .and(BUNDLE_MEMBER.ROLE.eq(role.getValue()))
                                            .and(BUNDLE_MEMBER.DELETED.eq(false)))
                            .execute()
                    > 0;
        }
        return dsl.insertInto(BUNDLE_MEMBER)
                        .set(BUNDLE_MEMBER.BUNDLE_ID, bundleId)
                        .set(BUNDLE_MEMBER.USER_ID, userId)
                        .set(BUNDLE_MEMBER.ROLE, role.getValue())
                        .set(BUNDLE_MEMBER.UPDATED_BY, opUserId)
                        .onConflict(BUNDLE_MEMBER.BUNDLE_ID, BUNDLE_MEMBER.ROLE)
                        .doUpdate()
                        .set(BUNDLE_MEMBER.DELETED, false)
                        .set(BUNDLE_MEMBER.USER_ID, userId)
                        .set(BUNDLE_MEMBER.UPDATED_BY, opUserId)
                        .execute()
                > 0;
    }

    @Override
    public List<Message.BundleMessage.Member> findByBundleId(String bundleId) {
        log.debug("Finding bundle member by bundleId: {}", bundleId);
        return dsl.select(BUNDLE_MEMBER.USER_ID, BUNDLE_MEMBER.ROLE)
                .from(BUNDLE_MEMBER)
                .where(BUNDLE_MEMBER.BUNDLE_ID.eq(bundleId).and(BUNDLE_MEMBER.DELETED.eq(false)))
                .fetch(
                        r -> {
                            var memberBuilder = Message.BundleMessage.Member.newBuilder();
                            acceptIfNotNull(
                                    memberBuilder::setUser,
                                    r.get(BUNDLE_MEMBER.USER_ID),
                                    id ->
                                            com.bees360.user.Message.UserMessage.newBuilder()
                                                    .setId(id)
                                                    .build());
                            acceptIfNotNull(memberBuilder::setRole, r.get(BUNDLE_MEMBER.ROLE));
                            return memberBuilder.build();
                        });
    }
}
