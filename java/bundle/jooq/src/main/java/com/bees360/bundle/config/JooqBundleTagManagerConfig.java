package com.bees360.bundle.config;

import com.bees360.bundle.JooqBundleTagManager;
import com.bees360.jooq.config.JooqConfig;
import com.bees360.project.tag.ProjectTagRepository;

import org.jooq.DSLContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    JooqConfig.class,
})
@Configuration
public class JooqBundleTagManagerConfig {
    @Bean
    JooqBundleTagManager jooqBundleTagManager(
            DSLContext dsl, ProjectTagRepository jooqProjectTagRepository) {
        return new JooqBundleTagManager(dsl, jooqProjectTagRepository);
    }
}
