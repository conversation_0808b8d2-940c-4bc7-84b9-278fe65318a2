package com.bees360.bundle;

import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE_TAG;

import com.bees360.api.Entity;
import com.bees360.bundle.Message.SyncBundleProjectResponse;
import com.bees360.project.tag.Message;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagRepository;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Log4j2
public class JooqBundleTagManager implements BundleTagManager {

    private final DSLContext dsl;
    private final ProjectTagRepository projectTagRepository;

    public JooqBundleTagManager(DSLContext dsl, ProjectTagRepository projectTagRepository) {
        this.dsl = dsl;
        this.projectTagRepository = projectTagRepository;
        log.info(
                "Created {}(dsl={}, projectTagRepository={}).",
                this,
                this.dsl,
                this.projectTagRepository);
    }

    @Override
    public List<ProjectTag> findByBundleId(
            String bundleId, @Nullable String companyId, @Nullable Message.ProjectTagType type) {
        log.debug(
                "Finding Bundle Tag with bundleId: {}, companyId: {}, type: {}.",
                bundleId,
                companyId,
                type);
        var finalTagType = type == null ? Message.ProjectTagType.UNKNOWN : type;
        var bundleTagIds =
                dsl.select()
                        .from(BUNDLE_TAG)
                        .where(BUNDLE_TAG.BUNDLE_ID.eq(bundleId))
                        .fetch(BUNDLE_TAG.TAG_ID);

        var tagStream = projectTagRepository.findAllById(bundleTagIds).stream();

        if (StringUtils.isNotBlank(companyId)) {
            tagStream = tagStream.filter(tag -> StringUtils.equals(tag.getCompanyId(), companyId));
        }

        return tagStream.filter(tag -> Objects.equals(tag.getType(), finalTagType)).toList();
    }

    @Override
    public int updateBundleTag(
            String bundleId,
            List<String> tagIds,
            Message.ProjectTagType type,
            String requestBy,
            String requestVia) {
        log.debug(
                "Updating Bundle Tag with bundleId: {}, tagIds: {}, type: {}, requestBy: {},"
                        + " requestVia: {}.",
                bundleId,
                tagIds,
                type,
                requestBy,
                requestVia);
        return dsl.transactionResult(
                trx -> {
                    var transactionDsl = trx.dsl();
                    var currentBundleTagIds =
                            transactionDsl
                                    .select()
                                    .from(BUNDLE_TAG)
                                    .where(BUNDLE_TAG.BUNDLE_ID.eq(bundleId))
                                    .fetch(BUNDLE_TAG.TAG_ID);

                    currentBundleTagIds =
                            projectTagRepository.findAllById(currentBundleTagIds).stream()
                                    .filter(tag -> Objects.equals(tag.getType(), type))
                                    .map(Entity::getId)
                                    .toList();

                    var addTagIds = CollectionUtils.removeAll(tagIds, currentBundleTagIds);
                    var deleteTagIds = CollectionUtils.removeAll(currentBundleTagIds, tagIds);

                    int affectedRowCount = 0;
                    var updateAt = Instant.now();
                    if (CollectionUtils.isNotEmpty(addTagIds)) {
                        var insertStep =
                                transactionDsl.insertInto(
                                        BUNDLE_TAG,
                                        BUNDLE_TAG.BUNDLE_ID,
                                        BUNDLE_TAG.TAG_ID,
                                        BUNDLE_TAG.UPDATED_VIA,
                                        BUNDLE_TAG.CREATED_BY,
                                        BUNDLE_TAG.CREATED_AT,
                                        BUNDLE_TAG.UPDATED_BY,
                                        BUNDLE_TAG.UPDATED_AT);
                        for (String addTagId : addTagIds) {
                            insertStep =
                                    insertStep.values(
                                            bundleId,
                                            addTagId,
                                            requestVia,
                                            requestBy,
                                            updateAt,
                                            requestBy,
                                            updateAt);
                        }
                        affectedRowCount += insertStep.execute();
                    }

                    if (CollectionUtils.isNotEmpty(deleteTagIds)) {
                        var deletedRowCount =
                                transactionDsl
                                        .delete(BUNDLE_TAG)
                                        .where(BUNDLE_TAG.BUNDLE_ID.eq(bundleId))
                                        .and(BUNDLE_TAG.TAG_ID.in(deleteTagIds))
                                        .execute();
                        affectedRowCount += deletedRowCount;
                    }

                    return affectedRowCount;
                });
    }

    @Override
    public SyncBundleProjectResponse syncBundleTag(String bundleId, String requestBy) {
        throw new UnsupportedOperationException();
    }
}
