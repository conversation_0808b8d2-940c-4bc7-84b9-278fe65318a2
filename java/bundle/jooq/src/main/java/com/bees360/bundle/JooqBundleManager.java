package com.bees360.bundle;

import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE;
import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE_CONTACT;
import static com.bees360.util.Functions.acceptIfNotNull;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.jsonArrayAgg;
import static org.jooq.impl.DSL.jsonEntry;
import static org.jooq.impl.DSL.jsonbObject;
import static org.jooq.impl.DSL.select;
import static org.jooq.impl.DSL.val;

import com.bees360.address.Address;
import com.bees360.address.AddressManager;
import com.bees360.bundle.Message.BundleMessage;
import com.bees360.bundle.Message.CreateBundleRequest;
import com.bees360.bundle.Message.CreateBundleResponse;
import com.bees360.contract.Contract;
import com.bees360.contract.ContractRepository;
import com.bees360.customer.Customer;
import com.bees360.policy.Policy;
import com.bees360.project.Building;
import com.bees360.project.Contact;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.member.Member;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.underwriting.Underwriting;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.bees360.util.Messages;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.StringValue;
import com.google.protobuf.util.JsonFormat;

import lombok.extern.log4j.Log4j2;

import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.JSON;
import org.jooq.JSONB;
import org.jooq.Record;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
public class JooqBundleManager implements BundleManager {
    private static final String BATCH_INTEGRATION_TYPE = "BATCH";
    static final String BUNDLE_PROJECT_GROUP_TYPE = "BUNDLE_PROJECT";

    private final DSLContext dsl;

    private final AddressManager addressManager;

    private final ContractRepository contractRepository;

    private final BatchProjectCreator batchProjectCreator;

    private final ExternalIntegrationProvider externalIntegrationProvider;

    private final ProjectGroupManager projectGroupManager;

    private static final String CONTACT_FIELD = "contacts";
    private static final JsonFormat.Printer printer =
            JsonFormat.printer()
                    .omittingInsignificantWhitespace()
                    .includingDefaultValueFields()
                    .printingEnumsAsInts();

    public JooqBundleManager(
            DSLContext dsl,
            AddressManager addressManager,
            ContractRepository contractRepository,
            BatchProjectCreator batchProjectCreator,
            ExternalIntegrationProvider externalIntegrationProvider,
            ProjectGroupManager projectGroupManager) {
        this.dsl = dsl;
        this.addressManager = addressManager;
        this.contractRepository = contractRepository;
        this.batchProjectCreator = batchProjectCreator;
        this.externalIntegrationProvider = externalIntegrationProvider;
        this.projectGroupManager = projectGroupManager;
        log.info(
                "Created {}(dsl={}, addressManager={}, contractRepository={},"
                        + " batchProjectCreator={}, externalIntegrationProvider={},"
                        + " projectGroupManager={}).",
                this,
                this.dsl,
                this.addressManager,
                this.contractRepository,
                this.batchProjectCreator,
                this.externalIntegrationProvider,
                this.projectGroupManager);
    }

    @Override
    public CreateBundleResponse createBundle(CreateBundleRequest request) {
        log.debug("Create bundle from request {}.", request);
        var createdBy = request.getCreateBy().getValue();
        // build contract
        var insuredBy = request.getInsuredBy().getValue();
        var processedBy = request.getProcessedBy().getValue();
        var contract = contractRepository.findByCompanyId(insuredBy, processedBy);

        // normalize main address
        var normalizedAddress =
                request.getBuildingAddressList().stream()
                        .filter(address -> address.getIsMain().getValue())
                        .findFirst()
                        .map(this::normalizeAddress)
                        .get();

        var serviceType = ServiceTypeEnum.valueOf(request.getServiceType().getNumber());

        var metadataPolicyBuilder = BundleMessage.Metadata.Policy.newBuilder();
        metadataPolicyBuilder.setIsRenewal(request.getIsRenewal());
        metadataPolicyBuilder.setType(request.getPolicyType());
        metadataPolicyBuilder.setTypeOfProperty(request.getTypeOfProperty());
        acceptIfNotNull(
                metadataPolicyBuilder::setPolicyEffectiveDate,
                request.hasPolicyEffectiveDate() ? request.getPolicyEffectiveDate() : null);

        var metadataBuilder = BundleMessage.Metadata.newBuilder();
        metadataBuilder.setPolicy(metadataPolicyBuilder);
        metadataBuilder.setNumberOfBuildings(request.getNumberOfBuildings());
        acceptIfNotNull(
                metadataBuilder::setYearBuilt,
                request.hasYearBuilt() ? request.getYearBuilt() : null);
        acceptIfNotNull(metadataBuilder::setNote, request.hasNote() ? request.getNote() : null);

        return dsl.transactionResult(
                trx -> {
                    var transactionDsl = trx.dsl();
                    var metadataJson = toJsonString(metadataBuilder);
                    var bundleId =
                            transactionDsl
                                    .insertInto(
                                            BUNDLE,
                                            BUNDLE.CONTRACT_ID,
                                            BUNDLE.ADDRESS_ID,
                                            BUNDLE.SERVICE_TYPE,
                                            BUNDLE.POLICY_NO,
                                            BUNDLE.INSPECTION_NO,
                                            BUNDLE.STATE,
                                            BUNDLE.STATUS,
                                            BUNDLE.METADATA,
                                            BUNDLE.CREATED_BY)
                                    .values(
                                            contract.getId(),
                                            normalizedAddress.getId(),
                                            serviceType.getCode(),
                                            request.getPolicyNo().getValue(),
                                            request.hasInspectionNo()
                                                    ? request.getInspectionNo().getValue()
                                                    : null,
                                            Message.BundleState.OPEN.name(),
                                            Message.BundleStatus.CREATED.name(),
                                            JSONB.valueOf(metadataJson),
                                            createdBy)
                                    .returning(BUNDLE.ID)
                                    .fetchOne(BUNDLE.ID);
                    log.debug("Created bundle {} with id {}.", request, bundleId);

                    var contactIds =
                            addBundleContacts(
                                    request.getContactList(), bundleId, createdBy, transactionDsl);
                    log.debug(
                            "Created bundle contact {} with id {}.",
                            request.getContactList(),
                            contactIds);

                    // create bundle reference projects
                    var projectBatchId = bundleProjectBatchId(bundleId);
                    var creationRequests = buildBatchProjectCreationRequest(request);

                    var creationResult =
                            batchProjectCreator.createBatch(
                                    projectBatchId,
                                    createdBy,
                                    creationRequests,
                                    false,
                                    request.getCreationChannel().getValue());
                    log.debug(
                            "Created bundle projects {} with result {}.",
                            creationRequests,
                            creationResult);

                    var createdProjectMap =
                            Iterables.toStream(
                                            externalIntegrationProvider.findAllByReference(
                                                    BATCH_INTEGRATION_TYPE,
                                                    List.of(projectBatchId)))
                                    .collect(
                                            Collectors.toMap(
                                                    ExternalIntegration::getSubReferenceNumber,
                                                    ExternalIntegration::getProjectId));
                    log.debug(
                            "Created bundle projects {} with projects {}.",
                            creationRequests,
                            createdProjectMap);
                    // build creation response
                    var responseBuilder =
                            CreateBundleResponse.newBuilder().setId(StringValue.of(bundleId));
                    creationResult.forEach(
                            (idx, status) -> {
                                var projectId = createdProjectMap.get(String.valueOf(idx));
                                var address = request.getBuildingAddress(idx);

                                var builder =
                                        CreateBundleResponse.CreateBundleProjectResponse
                                                .newBuilder()
                                                .setAddress(address)
                                                .setStatus(
                                                        status.toMessage().toBuilder()
                                                                .clearCause());
                                acceptIfNotNull(builder::setProjectId, projectId, StringValue::of);
                                responseBuilder.addResult(builder);
                            });

                    if (createdProjectMap.isEmpty()) {
                        log.info("Rollback bundle {} creation, cause no project created.", request);
                        transactionDsl.rollback().execute();
                        return responseBuilder.clearId().build();
                    }

                    // save reference project ids to BUNDLE_PROJECT group
                    projectGroupManager.addProjectToGroup(
                            bundleId,
                            BUNDLE_PROJECT_GROUP_TYPE,
                            createdProjectMap.values(),
                            createdBy);

                    // TODO save attachment as activity
                    return responseBuilder.build();
                });
    }

    private List<String> addBundleContacts(
            List<Message.BundleContactMessage> contacts,
            String bundleId,
            String createdBy,
            DSLContext dsl) {
        var insertStep =
                dsl.insertInto(
                        BUNDLE_CONTACT,
                        BUNDLE_CONTACT.BUNDLE_ID,
                        BUNDLE_CONTACT.ROLE,
                        BUNDLE_CONTACT.IS_PRIMARY,
                        BUNDLE_CONTACT.FULL_NAME,
                        BUNDLE_CONTACT.EMAIL,
                        BUNDLE_CONTACT.PHONE,
                        BUNDLE_CONTACT.CREATED_BY);
        for (var contact : contacts) {
            insertStep =
                    insertStep.values(
                            bundleId,
                            contact.getRole().getValue(),
                            contact.getIsPrimary().getValue(),
                            contact.getFullName().getValue(),
                            contact.hasEmail() ? contact.getEmail().getValue() : null,
                            contact.hasPhone() ? contact.getPhone().getValue() : null,
                            createdBy);
        }
        return insertStep.returning(BUNDLE_CONTACT.ID).fetch(BUNDLE_CONTACT.ID);
    }

    @Override
    public BundleMessage findById(String id) {
        log.debug("Finding Bundle by ID: {}", id);
        if (id == null || id.isEmpty()) {
            throw new IllegalArgumentException("Bundle ID cannot be null or empty.");
        }

        var condition = BUNDLE.ID.eq(id);
        var bundleResult = selectBundleByCondition(condition);
        return bundleResult.isEmpty() ? BundleMessage.getDefaultInstance() : bundleResult.get(0);
    }

    private List<BundleMessage> selectBundleByCondition(Condition condition) {
        log.debug("Select bundle by condition: {}", condition);
        var contactArrayAgg =
                jsonArrayAgg(
                        jsonbObject(
                                jsonEntry(
                                        val(BUNDLE_CONTACT.ID.getName()).cast(String.class),
                                        BUNDLE_CONTACT.ID.cast(String.class)),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.ROLE.getName()).cast(String.class),
                                        BUNDLE_CONTACT.ROLE),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.IS_PRIMARY.getName()).cast(String.class),
                                        BUNDLE_CONTACT.IS_PRIMARY),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.FULL_NAME.getName()).cast(String.class),
                                        BUNDLE_CONTACT.FULL_NAME),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.EMAIL.getName()).cast(String.class),
                                        BUNDLE_CONTACT.EMAIL),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.PHONE.getName()).cast(String.class),
                                        BUNDLE_CONTACT.PHONE)));
        var contactField =
                field(
                                select(contactArrayAgg)
                                        .from(BUNDLE_CONTACT)
                                        .where(BUNDLE_CONTACT.BUNDLE_ID.eq(BUNDLE.ID)))
                        .as(CONTACT_FIELD);

        var bundleSelect =
                dsl.select(
                                BUNDLE.ID,
                                BUNDLE.CONTRACT_ID,
                                BUNDLE.ADDRESS_ID,
                                BUNDLE.SERVICE_TYPE,
                                BUNDLE.POLICY_NO,
                                BUNDLE.INSPECTION_NO,
                                BUNDLE.STATE,
                                BUNDLE.STATUS,
                                BUNDLE.METADATA,
                                BUNDLE.CREATED_BY,
                                BUNDLE.CREATED_AT,
                                BUNDLE.UPDATED_BY,
                                BUNDLE.UPDATED_AT,
                                contactField)
                        .from(BUNDLE)
                        .where(condition);
        log.debug("Bundle selected: {} by condition: {}.", bundleSelect, condition);
        return bundleSelect.fetch(this::mapping);
    }

    private BundleMessage mapping(Record record) {
        log.debug("Mapping bundle record: {}", record);
        var builder = BundleMessage.newBuilder();
        acceptIfNotNull(builder::setId, record.get(BUNDLE.ID), StringValue::of);
        acceptIfNotNull(
                builder::setAddress,
                record.get(BUNDLE.ADDRESS_ID),
                id -> com.bees360.address.Message.AddressMessage.newBuilder().setId(id).build());
        acceptIfNotNull(
                builder::setContract,
                record.get(BUNDLE.CONTRACT_ID),
                id -> com.bees360.contract.Message.ContractMessage.newBuilder().setId(id).build());
        acceptIfNotNull(
                builder::setServiceType,
                record.get(BUNDLE.SERVICE_TYPE),
                com.bees360.project.Message.ServiceType::forNumber);
        acceptIfNotNull(builder::setPolicyNo, record.get(BUNDLE.POLICY_NO), StringValue::of);
        acceptIfNotNull(
                builder::setInspectionNo, record.get(BUNDLE.INSPECTION_NO), StringValue::of);
        acceptIfNotNull(builder::setState, record.get(BUNDLE.STATE), StringValue::of);
        acceptIfNotNull(builder::setStatus, record.get(BUNDLE.STATUS), StringValue::of);
        var contacts = getFromResult((JSON) record.get(CONTACT_FIELD));
        builder.addAllContact(contacts);
        var metadata = record.get(BUNDLE.METADATA);
        if (metadata != null) {
            var metadataBuilder = BundleMessage.Metadata.newBuilder();
            Messages.fromJsonString(metadata.data(), metadataBuilder);
            builder.setMetadata(metadataBuilder.build());
        }
        acceptIfNotNull(builder::setCreatedBy, record.get(BUNDLE.CREATED_BY), StringValue::of);
        acceptIfNotNull(
                builder::setCreatedAt, record.get(BUNDLE.CREATED_AT), DateTimes::toTimestamp);
        acceptIfNotNull(builder::setUpdatedBy, record.get(BUNDLE.UPDATED_BY), StringValue::of);
        acceptIfNotNull(
                builder::setUpdatedAt, record.get(BUNDLE.UPDATED_AT), DateTimes::toTimestamp);
        return builder.build();
    }

    private Address normalizeAddress(Message.BundleAddressMessage address) {
        var fullAddress =
                address.getStreetAddress()
                        + ", "
                        + address.getCity()
                        + ", "
                        + address.getState()
                        + " "
                        + address.getZip();
        var isGpsApproximate = address.hasLat() && address.hasLng();
        var addressBuilder =
                Address.AddressBuilder.newBuilder()
                        .setAddress(fullAddress)
                        .setStreetAddress(address.getStreetAddress().getValue())
                        .setCity(address.getCity().getValue())
                        .setState(address.getState().getValue())
                        .setCountry(address.getCountry().getValue())
                        .setZip(address.getZip().getValue())
                        .setIsGpsApproximate(isGpsApproximate);
        acceptIfNotNull(addressBuilder::setLat, address.getLat(), DoubleValue::getValue);
        acceptIfNotNull(addressBuilder::setLng, address.getLng(), DoubleValue::getValue);
        var nomalizedAddress = addressManager.normalize(addressBuilder.build());
        log.debug("Normalize address from {} to {}.", fullAddress, nomalizedAddress);
        return nomalizedAddress;
    }

    private String bundleProjectBatchId(String bundleId) {
        return "bundle-project-" + bundleId;
    }

    private List<ProjectCreationRequest> buildBatchProjectCreationRequest(
            CreateBundleRequest request) {
        var createBy = request.getCreateBy().getValue();
        return request.getBuildingAddressList().stream()
                .map(
                        address -> {
                            var builder = ProjectCreationRequest.ProjectBuilder.newBuilder();
                            var addressBuilder =
                                    Address.AddressBuilder.newBuilder()
                                            .setStreetAddress(address.getStreetAddress().getValue())
                                            .setCity(address.getCity().getValue())
                                            .setState(address.getState().getValue())
                                            .setCountry(address.getCountry().getValue())
                                            .setZip(address.getZip().getValue())
                                            .setLat(address.getLat().getValue())
                                            .setLng(address.getLng().getValue());
                            var buildingBuilder =
                                    Building.BuildingBuilder.newBuilder()
                                            .setBuildingType(request.getTypeOfProperty());
                            acceptIfNotNull(
                                    buildingBuilder::setYearBuilt,
                                    request.getYearBuilt(),
                                    Int32Value::getValue);
                            var policyBuilder =
                                    Policy.PolicyBuilder.newBuilder()
                                            .setBuilding(buildingBuilder.build())
                                            .setAddress(addressBuilder.build())
                                            .setIsRenewal(request.getIsRenewal().getValue())
                                            .setPolicyNo(request.getPolicyNo().getValue())
                                            .setType(request.getPolicyType().getValue());
                            acceptIfNotNull(
                                    policyBuilder::setPolicyEffectiveDate,
                                    request.getPolicyEffectiveDate(),
                                    DateTimes::toLocalDate);

                            var underwritingBuilder =
                                    Underwriting.UnderwritingBuilder.newBuilder()
                                            .setServiceType(
                                                    ServiceTypeEnum.valueOf(
                                                            request.getServiceType().getNumber()));

                            var contractBuilder =
                                    Contract.ContractBuilder.newBuilder()
                                            .setInsuredBy(
                                                    Customer.of(
                                                            com.bees360.customer.Message
                                                                    .CustomerMessage.newBuilder()
                                                                    .setId(
                                                                            request.getInsuredBy()
                                                                                    .getValue())
                                                                    .build()))
                                            .setProcessedBy(
                                                    Customer.of(
                                                            com.bees360.customer.Message
                                                                    .CustomerMessage.newBuilder()
                                                                    .setId(
                                                                            request.getProcessedBy()
                                                                                    .getValue())
                                                                    .build()));

                            var inspectionBuilder = Inspection.InspectionBuilder.newBuilder();
                            acceptIfNotNull(
                                    inspectionBuilder::setInspectionNo,
                                    request.getInspectionNo(),
                                    StringValue::getValue);

                            var creatorBuilder =
                                    ProjectMessage.Member.newBuilder()
                                            .setUser(
                                                    com.bees360.user.Message.UserMessage
                                                            .newBuilder()
                                                            .setId(createBy)
                                                            .build())
                                            .setRole(RoleEnum.CREATOR.getValue());

                            builder.setPolicy(policyBuilder.build())
                                    .setProjectType(ProjectTypeEnum.UNDERWRITING)
                                    .setUnderwriting(underwritingBuilder.build())
                                    .setContract(contractBuilder.build())
                                    .setCreatedBy(createBy)
                                    .setInspection(inspectionBuilder.build())
                                    .setContacts(buildProjectContact(request))
                                    .setMembers(List.of(Member.of(creatorBuilder.build())));
                            acceptIfNotNull(
                                    builder::setNote, request.getNote(), StringValue::getValue);
                            return builder.build();
                        })
                .toList();
    }

    private List<Contact> buildProjectContact(CreateBundleRequest request) {
        return request.getContactList().stream()
                .map(
                        contact -> {
                            var builder = ProjectMessage.Contact.newBuilder();
                            builder.setIsPrimary(contact.getIsPrimary().getValue());
                            acceptIfNotNull(
                                    builder::setFullName,
                                    contact.getFullName(),
                                    StringValue::getValue);
                            acceptIfNotNull(
                                    builder::setPrimaryEmail,
                                    contact.getEmail(),
                                    StringValue::getValue);
                            acceptIfNotNull(
                                    builder::setPrimaryPhone,
                                    contact.getPhone(),
                                    StringValue::getValue);
                            acceptIfNotNull(
                                    builder::setRole, contact.getRole(), StringValue::getValue);
                            return builder.build();
                        })
                .map(Contact::of)
                .toList();
    }

    public static List<Message.BundleContactMessage> getFromResult(JSON recordResult) {
        if (recordResult == null) {
            return Collections.emptyList();
        }
        var result = new Gson().fromJson(recordResult.data(), List.class);

        List<Message.BundleContactMessage> resultList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            var record = (LinkedTreeMap<String, Object>) result.get(i);
            var builder = Message.BundleContactMessage.newBuilder();
            acceptIfNotNull(
                    builder::setId,
                    (String) record.get(BUNDLE_CONTACT.ID.getName()),
                    StringValue::of);
            acceptIfNotNull(
                    builder::setRole,
                    (String) record.get(BUNDLE_CONTACT.ROLE.getName()),
                    StringValue::of);
            acceptIfNotNull(
                    builder::setIsPrimary,
                    (Boolean) record.get(BUNDLE_CONTACT.IS_PRIMARY.getName()),
                    BoolValue::of);
            acceptIfNotNull(
                    builder::setFullName,
                    (String) record.get(BUNDLE_CONTACT.FULL_NAME.getName()),
                    StringValue::of);
            acceptIfNotNull(
                    builder::setEmail,
                    (String) record.get(BUNDLE_CONTACT.EMAIL.getName()),
                    StringValue::of);
            acceptIfNotNull(
                    builder::setPhone,
                    (String) record.get(BUNDLE_CONTACT.PHONE.getName()),
                    StringValue::of);
            resultList.add(builder.build());
        }
        return resultList;
    }

    public static String toJsonString(MessageOrBuilder message) {
        try {
            return printer.print(message);
        } catch (InvalidProtocolBufferException e) {
            throw new IllegalArgumentException(e);
        }
    }
}
