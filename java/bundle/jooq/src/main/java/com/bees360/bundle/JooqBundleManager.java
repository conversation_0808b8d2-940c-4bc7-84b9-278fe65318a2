package com.bees360.bundle;

import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE;
import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE_CONTACT;
import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE_DAYS_OLD;
import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE_MEMBER;
import static com.bees360.jooq.persistent.bundle.Tables.BUNDLE_STATUS_HISTORY;
import static com.bees360.jooq.persistent.bundle.Tables.PROJECT_GROUP;
import static com.bees360.jooq.persistent.bundle.Tables.PROJECT_STATE_CHANGE_REASON;
import static com.bees360.util.Functions.acceptIfNotNull;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.jsonArrayAgg;
import static org.jooq.impl.DSL.jsonEntry;
import static org.jooq.impl.DSL.jsonbObject;
import static org.jooq.impl.DSL.select;
import static org.jooq.impl.DSL.val;

import com.bees360.address.Address;
import com.bees360.address.AddressProvider;
import com.bees360.building.Message.BuildingType;
import com.bees360.bundle.Message.BundleMessage;
import com.bees360.bundle.Message.CreateBundleRequest;
import com.bees360.bundle.Message.CreateBundleResponse;
import com.bees360.bundle.Message.SyncBundleProjectResponse;
import com.bees360.codec.GsonCodec;
import com.bees360.contract.Contract;
import com.bees360.contract.ContractRepository;
import com.bees360.customer.Customer;
import com.bees360.policy.Policy;
import com.bees360.project.Building;
import com.bees360.project.Contact;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.member.Member;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.state.ProjectStateChangeReasonProvider;
import com.bees360.project.underwriting.Underwriting;
import com.bees360.util.ByteStrings;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.bees360.util.Messages;
import com.google.common.base.Function;
import com.google.common.base.Preconditions;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.StringValue;
import com.google.protobuf.Timestamp;
import com.google.protobuf.util.JsonFormat;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSON;
import org.jooq.JSONB;
import org.jooq.Record;
import org.jooq.impl.DSL;

import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Log4j2
public class JooqBundleManager implements BundleManager {
    private static final String BATCH_INTEGRATION_TYPE = "BATCH";
    static final String BUNDLE_PROJECT_GROUP_TYPE = "BUNDLE_PROJECT";
    private static final Gson GSON = GsonCodec.DEFAULT_GSON_BUILDER.create();

    private final DSLContext dsl;

    private final AddressProvider addressProvider;

    private final ContractRepository contractRepository;

    private final BatchProjectCreator batchProjectCreator;

    private final ExternalIntegrationProvider externalIntegrationProvider;

    private final ProjectGroupManager projectGroupManager;

    private static final String CONTACT_FIELD = "contacts";
    private static final String BUNDLE_STATUS_HISTORY_FIELD = "status_history";
    private static final String BUNDLE_MEMBER_FIELD = "member_history";

    private static final JsonFormat.Printer printer =
            JsonFormat.printer()
                    .omittingInsignificantWhitespace()
                    .includingDefaultValueFields()
                    .printingEnumsAsInts();
    private final ProjectStateChangeReasonProvider changeReasonProvider;

    public JooqBundleManager(
            DSLContext dsl,
            AddressProvider addressProvider,
            ContractRepository contractRepository,
            BatchProjectCreator batchProjectCreator,
            ExternalIntegrationProvider externalIntegrationProvider,
            ProjectGroupManager projectGroupManager,
            ProjectStateChangeReasonProvider changeReasonProvider) {
        this.dsl = dsl;
        this.addressProvider = addressProvider;
        this.contractRepository = contractRepository;
        this.batchProjectCreator = batchProjectCreator;
        this.externalIntegrationProvider = externalIntegrationProvider;
        this.projectGroupManager = projectGroupManager;
        this.changeReasonProvider = changeReasonProvider;
        log.info(
                "Created {}(dsl={}, addressProvider={}, contractRepository={},"
                        + " batchProjectCreator={}, externalIntegrationProvider={},"
                        + " projectGroupManager={}, changeReasonProvider={})).",
                this,
                this.dsl,
                this.addressProvider,
                this.contractRepository,
                this.batchProjectCreator,
                this.externalIntegrationProvider,
                this.projectGroupManager,
                this.changeReasonProvider);
    }

    @Override
    public CreateBundleResponse createBundle(CreateBundleRequest request) {
        log.debug("Create bundle from request {}.", request);
        var createdBy = request.getCreateBy().getValue();
        // build contract
        var insuredBy = request.getInsuredBy().getValue();
        var processedBy = request.getProcessedBy().getValue();
        var contract = contractRepository.findByCompanyId(insuredBy, processedBy);
        var bundleCreatedReason =
                Iterables.toList(
                        changeReasonProvider.findByQuery(
                                List.of(), List.of("BUNDLE CREATED"), List.of()));
        if (bundleCreatedReason.isEmpty() || bundleCreatedReason.get(0) == null) {
            throw new IllegalStateException(
                    "Cannot find bundle created change reason with key 'BUNDLE CREATED', please"
                            + " create it first.");
        }

        // normalize main address
        var normalizedAddress =
                request.getBuildingAddressList().stream()
                        .filter(address -> address.getIsMain().getValue())
                        .findFirst()
                        .map(this::normalizeAddress)
                        .get();

        var serviceType = ServiceTypeEnum.valueOf(request.getServiceType().getNumber());

        var metadataPolicyBuilder = BundleMessage.Metadata.Policy.newBuilder();
        metadataPolicyBuilder.setIsRenewal(request.getIsRenewal());
        metadataPolicyBuilder.setType(request.getPolicyType());
        metadataPolicyBuilder.setTypeOfProperty(request.getTypeOfProperty());
        acceptIfNotNull(
                metadataPolicyBuilder::setPolicyEffectiveDate,
                request.hasPolicyEffectiveDate() ? request.getPolicyEffectiveDate() : null);

        var metadataBuilder = BundleMessage.Metadata.newBuilder();
        metadataBuilder.setPolicy(metadataPolicyBuilder);
        metadataBuilder.setNumberOfBuildings(request.getNumberOfBuildings());
        acceptIfNotNull(
                metadataBuilder::setYearBuilt,
                request.hasYearBuilt() ? request.getYearBuilt() : null);
        acceptIfNotNull(metadataBuilder::setNote, request.hasNote() ? request.getNote() : null);

        return dsl.transactionResult(
                trx -> {
                    var transactionDsl = trx.dsl();
                    var metadataJson = toJsonString(metadataBuilder);
                    var bundleId =
                            transactionDsl
                                    .insertInto(
                                            BUNDLE,
                                            BUNDLE.CONTRACT_ID,
                                            BUNDLE.ADDRESS_ID,
                                            BUNDLE.SERVICE_TYPE,
                                            BUNDLE.POLICY_NO,
                                            BUNDLE.INSPECTION_NO,
                                            BUNDLE.STATE,
                                            BUNDLE.LAST_STATE_CHANGE_REASON_ID,
                                            BUNDLE.STATUS,
                                            BUNDLE.METADATA,
                                            BUNDLE.CREATED_BY,
                                            BUNDLE.UPDATED_BY)
                                    .values(
                                            contract.getId(),
                                            normalizedAddress.getId(),
                                            serviceType.getCode(),
                                            request.getPolicyNo().getValue(),
                                            request.hasInspectionNo()
                                                    ? request.getInspectionNo().getValue()
                                                    : null,
                                            Message.BundleState.OPEN.name(),
                                            bundleCreatedReason.get(0).getId(),
                                            Message.BundleStatus.CREATED.name(),
                                            JSONB.valueOf(metadataJson),
                                            createdBy,
                                            createdBy)
                                    .returning(BUNDLE.ID)
                                    .fetchOne(BUNDLE.ID);
                    log.debug("Created bundle {} with id {}.", request, bundleId);

                    var creatorId = addBundleCreator(bundleId, createdBy);
                    log.debug("Created bundle creator {} with id {}.", createdBy, creatorId);
                    var contactIds =
                            addBundleContacts(
                                    request.getContactList(),
                                    List.of(bundleId),
                                    createdBy,
                                    transactionDsl);
                    log.debug(
                            "Created bundle contact {} with id {}.",
                            request.getContactList(),
                            contactIds);

                    // TODO save attachment as activity
                    return createBundleProjects(request, bundleId, createdBy, transactionDsl);
                });
    }

    private CreateBundleResponse createBundleProjects(
            CreateBundleRequest request,
            String bundleId,
            String createdBy,
            DSLContext transactionDsl) {
        // create bundle reference projects
        var projectBatchId = bundleProjectBatchId(bundleId, request);

        var creationRequests = buildBatchProjectCreationRequest(request);
        log.info(
                "Creating bundle projects with batch id {} and requests {}.",
                projectBatchId,
                creationRequests);

        var creationResult =
                batchProjectCreator.createBatch(
                        projectBatchId,
                        createdBy,
                        creationRequests,
                        false,
                        request.getCreationChannel().getValue());
        log.debug("Created bundle projects {} with result {}.", creationRequests, creationResult);

        var createdProjectMap =
                Iterables.toStream(
                                externalIntegrationProvider.findAllByReference(
                                        BATCH_INTEGRATION_TYPE, List.of(projectBatchId)))
                        .collect(
                                Collectors.toMap(
                                        ExternalIntegration::getSubReferenceNumber,
                                        ExternalIntegration::getProjectId));
        log.debug(
                "Created bundle projects {} with projects {}.",
                creationRequests,
                createdProjectMap);
        // build creation response
        var responseBuilder = CreateBundleResponse.newBuilder().setId(StringValue.of(bundleId));
        creationResult.forEach(
                (idx, status) -> {
                    var projectId = createdProjectMap.get(String.valueOf(idx));
                    var address = request.getBuildingAddress(idx);

                    var builder =
                            CreateBundleResponse.CreateBundleProjectResponse.newBuilder()
                                    .setAddress(address)
                                    .setStatus(status.toMessage().toBuilder().clearCause());
                    acceptIfNotNull(builder::setProjectId, projectId, StringValue::of);
                    responseBuilder.addResult(builder);
                });

        if (createdProjectMap.isEmpty()) {
            log.info("Rollback bundle {} creation, cause no project created.", request);
            transactionDsl.rollback().execute();
            return responseBuilder.clearId().build();
        }

        // save reference project ids to BUNDLE_PROJECT group
        projectGroupManager.addProjectToGroup(
                bundleId, BUNDLE_PROJECT_GROUP_TYPE, createdProjectMap.values(), createdBy);

        return responseBuilder.build();
    }

    @Override
    public CreateBundleResponse createBundleProject(
            String bundleId,
            List<Message.BundleAddressMessage> buildingAddresses,
            String creationChannel,
            String requestBy) {
        Preconditions.checkArgument(
                bundleId != null, "Fail to create bundle project: bundle id cannot be null.");
        Preconditions.checkArgument(
                CollectionUtils.isNotEmpty(buildingAddresses),
                "Fail to create bundle project: building addresses cannot be empty.");
        Preconditions.checkArgument(
                creationChannel != null,
                "Fail to create bundle project: creation channel cannot be null.");
        Preconditions.checkArgument(
                requestBy != null, "Fail to create bundle project: created by cannot be null.");
        var bundle = findById(bundleId);
        var bundleContract = contractRepository.findById(bundle.getContract().getId());
        var createRequestBuilder = CreateBundleRequest.newBuilder();
        createRequestBuilder.setInsuredBy(StringValue.of(bundleContract.getInsuredBy().getId()));
        createRequestBuilder.setProcessedBy(
                StringValue.of(bundleContract.getProcessedBy().getId()));
        createRequestBuilder.setServiceType(bundle.getServiceType());
        createRequestBuilder.setPolicyNo(bundle.getPolicyNo());
        createRequestBuilder.setPolicyEffectiveDate(
                bundle.getMetadata().getPolicy().getPolicyEffectiveDate());
        createRequestBuilder.setIsRenewal(bundle.getMetadata().getPolicy().getIsRenewal());
        createRequestBuilder.setPolicyType(bundle.getMetadata().getPolicy().getType());
        createRequestBuilder.setTypeOfProperty(
                bundle.getMetadata().getPolicy().getTypeOfProperty());
        createRequestBuilder.setInspectionNo(bundle.getInspectionNo());
        createRequestBuilder.setYearBuilt(bundle.getMetadata().getYearBuilt());
        createRequestBuilder.addAllContact(bundle.getContactList());
        createRequestBuilder.setNumberOfBuildings(bundle.getMetadata().getNumberOfBuildings());
        createRequestBuilder.setNote(bundle.getMetadata().getNote());
        createRequestBuilder.addAllBuildingAddress(buildingAddresses);
        createRequestBuilder.setCreateBy(StringValue.of(requestBy));
        createRequestBuilder.setCreationChannel(StringValue.of(creationChannel));

        return dsl.transactionResult(
                trx ->
                        createBundleProjects(
                                createRequestBuilder.build(), bundleId, requestBy, trx.dsl()));
    }

    @Override
    public boolean addBundleProject(String bundleId, List<String> projectIds, String requestBy) {
        projectGroupManager.addProjectToGroup(
                bundleId, BUNDLE_PROJECT_GROUP_TYPE, projectIds, requestBy);
        return true;
    }

    @Override
    public boolean removeBundleProject(String bundleId, List<String> projectIds, String requestBy) {
        var bundleProjects =
                projectGroupManager.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE);
        var bundleProjectIds = Iterables.toList(bundleProjects.getProjectIds());
        Preconditions.checkArgument(
                !CollectionUtils.containsAll(projectIds, bundleProjectIds),
                "Fail to remove bundle project: At least one project must be kept in the bundle.");
        projectGroupManager.deleteProjectInGroup(
                bundleId, BUNDLE_PROJECT_GROUP_TYPE, projectIds, requestBy);
        return true;
    }

    @Override
    public boolean updateBundle(Message.UpdateBundleRequest request, String requestBy) {
        var bundleIds = request.getBundleIdList();
        var updateStep =
                dsl.update(BUNDLE)
                        .set(BUNDLE.UPDATED_BY, requestBy)
                        .set(BUNDLE.UPDATED_AT, Instant.now());

        switch (request.getUpdateFieldCase()) {
            case SERVICE_TYPE -> updateStep =
                    updateStep.set(
                            BUNDLE.SERVICE_TYPE, request.getServiceType().getValue().getNumber());
            case INSPECTION_NO -> updateStep =
                    updateStep.set(BUNDLE.INSPECTION_NO, request.getInspectionNo().getValue());
            case NUMBER_OF_BUILDINGS -> {
                var setNumberOfBuildsField =
                        buildJsonbSetField(
                                BUNDLE.METADATA,
                                Messages.toJsonString(request.getNumberOfBuildings()),
                                "numberOfBuildings");
                updateStep = updateStep.set(BUNDLE.METADATA, setNumberOfBuildsField);
            }
            default -> throw new UnsupportedOperationException(
                    "Unsupported update bundle field: %s".formatted(request.getUpdateFieldCase()));
        }

        return updateStep.where(BUNDLE.ID.in(Iterables.toList(bundleIds))).execute() != 0;
    }

    @Override
    public boolean updateBundlePolicy(Message.UpdateBundlePolicyRequest request, String requestBy) {
        var bundleIds = request.getBundleIdList();
        var updateStep =
                dsl.update(BUNDLE)
                        .set(BUNDLE.UPDATED_BY, requestBy)
                        .set(BUNDLE.UPDATED_AT, Instant.now());

        switch (request.getUpdateFieldCase()) {
            case POLICY_NO -> updateStep =
                    updateStep.set(BUNDLE.POLICY_NO, request.getPolicyNo().getValue());
            case ADDRESS -> {
                var normalizedAddress = normalizeAddress(request.getAddress());
                updateStep = updateStep.set(BUNDLE.ADDRESS_ID, normalizedAddress.getId());
            }
            case YEAR_BUILT -> {
                var setYearBuiltField =
                        buildJsonbSetField(
                                BUNDLE.METADATA,
                                Messages.toJsonString(request.getYearBuilt()),
                                "yearBuilt");
                updateStep = updateStep.set(BUNDLE.METADATA, setYearBuiltField);
            }
            case IS_RENEWAL -> {
                var setRenewalField =
                        buildJsonbSetField(
                                BUNDLE.METADATA,
                                Messages.toJsonString(request.getIsRenewal()),
                                "policy",
                                "isRenewal");
                updateStep = updateStep.set(BUNDLE.METADATA, setRenewalField);
            }
            case EFFECTIVE_DATE -> {
                var setEffectiveDateField =
                        buildJsonbSetField(
                                BUNDLE.METADATA,
                                Messages.toJsonString(request.getEffectiveDate()),
                                "policy",
                                "policyEffectiveDate");
                updateStep = updateStep.set(BUNDLE.METADATA, setEffectiveDateField);
            }
            case POLICY_AND_PROPERTY_TYPE -> {
                var policyType = request.getPolicyAndPropertyType().getPolicyType();
                var propertyType =
                        BuildingType.forNumber(
                                request.getPolicyAndPropertyType().getPropertyType().getValue());
                var setPropertyTypeField =
                        buildJsonbSetField(
                                BUNDLE.METADATA,
                                GSON.toJson(propertyType.getNumber()),
                                "policy",
                                "typeOfProperty");
                var setPolicyTypeField =
                        buildJsonbSetField(
                                setPropertyTypeField,
                                Messages.toJsonStringIncludingDefaultValue(
                                        StringValue.of(policyType)),
                                "policy",
                                "type");
                updateStep = updateStep.set(BUNDLE.METADATA, setPolicyTypeField);
            }
            default -> throw new UnsupportedOperationException(
                    "Unsupported update bundle policy field: %s"
                            .formatted(request.getUpdateFieldCase()));
        }

        return updateStep.where(BUNDLE.ID.in(Iterables.toList(bundleIds))).execute() != 0;
    }

    @Override
    public boolean saveBundleContact(Message.UpdateBundleContactRequest request, String requestBy) {
        var bundleIds = request.getBundleIdList();
        var contact = request.getContact();
        if (!contact.hasId()) {
            var addedContact = addBundleContacts(List.of(contact), bundleIds, requestBy, dsl);
            return !addedContact.isEmpty();
        }
        return dsl.update(BUNDLE_CONTACT)
                        .set(BUNDLE_CONTACT.ROLE, contact.getRole().getValue())
                        .set(BUNDLE_CONTACT.IS_PRIMARY, contact.getIsPrimary().getValue())
                        .set(BUNDLE_CONTACT.FULL_NAME, contact.getFullName().getValue())
                        .set(
                                BUNDLE_CONTACT.EMAIL,
                                contact.hasEmail() ? contact.getEmail().getValue() : null)
                        .set(
                                BUNDLE_CONTACT.PHONE,
                                contact.hasPhone() ? contact.getPhone().getValue() : null)
                        .set(BUNDLE_CONTACT.UPDATED_BY, requestBy)
                        .set(BUNDLE_CONTACT.UPDATED_AT, Instant.now())
                        .where(BUNDLE_CONTACT.ID.eq(contact.getId().getValue()))
                        .execute()
                != 0;
    }

    @Override
    public SyncBundleProjectResponse syncBundleProject(
            Message.SyncBundleProjectRequest request, String requestBy) {
        throw new UnsupportedOperationException();
    }

    private String addBundleCreator(String bundleId, String createdBy) {
        return dsl.insertInto(BUNDLE_MEMBER)
                .set(BUNDLE_MEMBER.BUNDLE_ID, bundleId)
                .set(BUNDLE_MEMBER.USER_ID, createdBy)
                .set(BUNDLE_MEMBER.ROLE, RoleEnum.CREATOR.getValue())
                .set(BUNDLE_MEMBER.UPDATED_BY, createdBy)
                .returning(BUNDLE_MEMBER.ID)
                .fetchOne(BUNDLE_MEMBER.ID);
    }

    private List<String> addBundleContacts(
            List<Message.BundleContactMessage> contacts,
            Iterable<String> bundleIds,
            String createdBy,
            DSLContext dsl) {
        var insertStep =
                dsl.insertInto(
                        BUNDLE_CONTACT,
                        BUNDLE_CONTACT.BUNDLE_ID,
                        BUNDLE_CONTACT.ROLE,
                        BUNDLE_CONTACT.IS_PRIMARY,
                        BUNDLE_CONTACT.FULL_NAME,
                        BUNDLE_CONTACT.EMAIL,
                        BUNDLE_CONTACT.PHONE,
                        BUNDLE_CONTACT.CREATED_BY,
                        BUNDLE_CONTACT.UPDATED_BY);
        for (String bundleId : bundleIds) {
            for (var contact : contacts) {
                insertStep =
                        insertStep.values(
                                bundleId,
                                contact.getRole().getValue(),
                                contact.getIsPrimary().getValue(),
                                contact.getFullName().getValue(),
                                contact.hasEmail() ? contact.getEmail().getValue() : null,
                                contact.hasPhone() ? contact.getPhone().getValue() : null,
                                createdBy,
                                createdBy);
            }
        }
        return insertStep.returning(BUNDLE_CONTACT.ID).fetch(BUNDLE_CONTACT.ID);
    }

    @Override
    public BundleMessage findById(String id) {
        log.debug("Finding Bundle by ID: {}", id);
        if (id == null || id.isEmpty()) {
            throw new IllegalArgumentException("Bundle ID cannot be null or empty.");
        }

        var condition = BUNDLE.ID.eq(id);
        var bundleResult = selectBundleByCondition(condition, 1, 0);
        return bundleResult.isEmpty() ? null : bundleResult.get(0);
    }

    @Override
    public boolean updateStatus(
            String bundleId, Message.BundleStatus status, String updatedBy, Instant updatedAt) {
        log.debug("Updating bundle {} status: {} by {}.", bundleId, status, updatedBy);
        return dsl.update(BUNDLE)
                        .set(BUNDLE.STATUS, status.name())
                        .set(BUNDLE.UPDATED_BY, updatedBy)
                        .set(BUNDLE.UPDATED_AT, updatedAt)
                        .where(BUNDLE.ID.eq(bundleId))
                        .execute()
                > 0;
    }

    @Override
    public List<BundleMessage> findByQuery(Message.BundleRequestQuery query) {
        log.debug("Finding Bundle by query: {}", query);
        Preconditions.checkArgument(
                !Objects.equals(query, Message.BundleRequestQuery.getDefaultInstance()),
                "Query cannot be default instance.");
        Preconditions.checkArgument(query != null, "Query cannot be null.");
        Preconditions.checkArgument(query.getLimit() != 0, "Limit cannot be 0.");
        var condition = BUNDLE.IS_DELETED.isFalse();
        if (!query.getBundleIdList().isEmpty()) {
            condition = condition.and(BUNDLE.ID.in(query.getBundleIdList()));
        }
        if (!query.getProjectIdList().isEmpty()) {
            condition =
                    condition.andExists(
                            select(DSL.one())
                                    .from(PROJECT_GROUP)
                                    .where(BUNDLE.ID.cast(String.class).eq(PROJECT_GROUP.GROUP_KEY))
                                    .and(PROJECT_GROUP.GROUP_TYPE.eq("BUNDLE_PROJECT"))
                                    .and(PROJECT_GROUP.PROJECT_ID.in(query.getProjectIdList())));
        }
        return selectBundleByCondition(condition, query.getLimit(), query.getOffset());
    }

    @Override
    public boolean deleteById(String bundleId, String deletedBy) {
        return dsl.update(BUNDLE)
                        .set(BUNDLE.IS_DELETED, true)
                        .set(BUNDLE.UPDATED_BY, deletedBy)
                        .set(BUNDLE.UPDATED_AT, Instant.now())
                        .where(BUNDLE.ID.eq(bundleId))
                        .execute()
                > 0;
    }

    private List<BundleMessage> selectBundleByCondition(
            Condition condition, int limit, int offset) {
        log.debug(
                "Select bundle by condition: {} with limit {} and offset {}.",
                condition,
                limit,
                offset);
        var contactArrayAgg =
                jsonArrayAgg(
                        jsonbObject(
                                jsonEntry(
                                        val(BUNDLE_CONTACT.ID.getName()).cast(String.class),
                                        BUNDLE_CONTACT.ID.cast(String.class)),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.ROLE.getName()).cast(String.class),
                                        BUNDLE_CONTACT.ROLE),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.IS_PRIMARY.getName()).cast(String.class),
                                        BUNDLE_CONTACT.IS_PRIMARY),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.FULL_NAME.getName()).cast(String.class),
                                        BUNDLE_CONTACT.FULL_NAME),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.EMAIL.getName()).cast(String.class),
                                        BUNDLE_CONTACT.EMAIL),
                                jsonEntry(
                                        val(BUNDLE_CONTACT.PHONE.getName()).cast(String.class),
                                        BUNDLE_CONTACT.PHONE)));
        var contactField =
                field(
                                select(contactArrayAgg)
                                        .from(BUNDLE_CONTACT)
                                        .where(BUNDLE_CONTACT.BUNDLE_ID.eq(BUNDLE.ID)))
                        .as(CONTACT_FIELD);
        var statusHistoryField = getStatusHistoryField();
        var memberField = getMemberFiled();

        var bundleSelect =
                dsl.select(
                                BUNDLE.ID,
                                BUNDLE.CONTRACT_ID,
                                BUNDLE.ADDRESS_ID,
                                BUNDLE.SERVICE_TYPE,
                                BUNDLE.POLICY_NO,
                                BUNDLE.INSPECTION_NO,
                                BUNDLE.STATE,
                                BUNDLE.STATUS,
                                BUNDLE.METADATA,
                                BUNDLE.CREATED_BY,
                                BUNDLE.CREATED_AT,
                                BUNDLE.UPDATED_BY,
                                BUNDLE.UPDATED_AT,
                                BUNDLE.IS_CANCELED,
                                BUNDLE_DAYS_OLD.PRE_DAYS_OLD,
                                BUNDLE_DAYS_OLD.OPENED_OR_EFFECTIVE_AT,
                                PROJECT_STATE_CHANGE_REASON.DISPLAY_TEXT,
                                contactField,
                                statusHistoryField,
                                memberField)
                        .from(BUNDLE)
                        .leftJoin(BUNDLE_DAYS_OLD)
                        .on(BUNDLE.ID.eq(BUNDLE_DAYS_OLD.BUNDLE_ID))
                        .leftJoin(PROJECT_STATE_CHANGE_REASON)
                        .on(BUNDLE.LAST_STATE_CHANGE_REASON_ID.eq(PROJECT_STATE_CHANGE_REASON.ID))
                        .where(condition)
                        .limit(limit)
                        .offset(offset);
        log.debug("Bundle selected: {} by condition: {}.", bundleSelect, condition);
        return bundleSelect.fetch(this::mapping);
    }

    private Field<JSON> getStatusHistoryField() {
        var statusHistoryArrayAgg =
                jsonArrayAgg(
                        jsonbObject(
                                jsonEntry(
                                        val(BUNDLE_STATUS_HISTORY.ID.getName()).cast(String.class),
                                        BUNDLE_STATUS_HISTORY.ID.cast(String.class)),
                                jsonEntry(
                                        val(BUNDLE_STATUS_HISTORY.STATUS.getName())
                                                .cast(String.class),
                                        BUNDLE_STATUS_HISTORY.STATUS),
                                jsonEntry(
                                        val(BUNDLE_STATUS_HISTORY.COMMENT.getName())
                                                .cast(String.class),
                                        BUNDLE_STATUS_HISTORY.COMMENT),
                                jsonEntry(
                                        val(BUNDLE_STATUS_HISTORY.CREATED_AT.getName())
                                                .cast(String.class),
                                        BUNDLE_STATUS_HISTORY.CREATED_AT),
                                jsonEntry(
                                        val(BUNDLE_STATUS_HISTORY.CREATED_BY.getName())
                                                .cast(String.class),
                                        BUNDLE_STATUS_HISTORY.CREATED_BY)));
        return field(
                        select(statusHistoryArrayAgg)
                                .from(BUNDLE_STATUS_HISTORY)
                                .where(BUNDLE_STATUS_HISTORY.BUNDLE_ID.eq(BUNDLE.ID)))
                .as(BUNDLE_STATUS_HISTORY_FIELD);
    }

    private Field<JSON> getMemberFiled() {
        var memberArrayAgg =
                jsonArrayAgg(
                        jsonbObject(
                                jsonEntry(
                                        val(BUNDLE_MEMBER.USER_ID.getName()).cast(String.class),
                                        BUNDLE_MEMBER.USER_ID.cast(String.class)),
                                jsonEntry(
                                        val(BUNDLE_MEMBER.ROLE.getName()).cast(String.class),
                                        BUNDLE_MEMBER.ROLE)));
        return field(
                        select(memberArrayAgg)
                                .from(BUNDLE_MEMBER)
                                .where(
                                        BUNDLE_MEMBER
                                                .BUNDLE_ID
                                                .eq(BUNDLE.ID)
                                                .and(BUNDLE_MEMBER.DELETED.eq(false))))
                .as(BUNDLE_MEMBER_FIELD);
    }

    private BundleMessage mapping(Record record) {
        log.debug("Mapping bundle record: {}", record);
        var builder = BundleMessage.newBuilder();
        acceptIfNotNull(builder::setId, record.get(BUNDLE.ID), StringValue::of);
        acceptIfNotNull(
                builder::setAddress,
                record.get(BUNDLE.ADDRESS_ID),
                id -> com.bees360.address.Message.AddressMessage.newBuilder().setId(id).build());
        acceptIfNotNull(
                builder::setContract,
                record.get(BUNDLE.CONTRACT_ID),
                id -> com.bees360.contract.Message.ContractMessage.newBuilder().setId(id).build());
        acceptIfNotNull(
                builder::setServiceType,
                record.get(BUNDLE.SERVICE_TYPE),
                com.bees360.project.Message.ServiceType::forNumber);
        acceptIfNotNull(builder::setPolicyNo, record.get(BUNDLE.POLICY_NO), StringValue::of);
        acceptIfNotNull(
                builder::setInspectionNo, record.get(BUNDLE.INSPECTION_NO), StringValue::of);
        acceptIfNotNull(builder::setState, record.get(BUNDLE.STATE), Message.BundleState::valueOf);
        acceptIfNotNull(
                builder::setStatus, record.get(BUNDLE.STATUS), Message.BundleStatus::valueOf);
        acceptIfNotNull(builder::setIsCanceled, record.get(BUNDLE.IS_CANCELED), BoolValue::of);
        var contacts = getFromResult((JSON) record.get(CONTACT_FIELD));
        builder.addAllContact(contacts);
        var statusHistory =
                getStatusHistoryFromResult((JSON) record.get(BUNDLE_STATUS_HISTORY_FIELD));
        builder.addAllStatusHistory(statusHistory);
        var metadata = record.get(BUNDLE.METADATA);
        var metadataBuilder = BundleMessage.Metadata.newBuilder();
        if (metadata != null) {
            Messages.fromJsonString(metadata.data(), metadataBuilder);
        }
        var daysOld = countDaysOldFromRecord(record);
        metadataBuilder.setDaysOld(Int32Value.of(daysOld));
        builder.setMetadata(metadataBuilder.build());
        acceptIfNotNull(builder::setCreatedBy, record.get(BUNDLE.CREATED_BY), StringValue::of);
        acceptIfNotNull(
                builder::setCreatedAt, record.get(BUNDLE.CREATED_AT), DateTimes::toTimestamp);
        acceptIfNotNull(builder::setUpdatedBy, record.get(BUNDLE.UPDATED_BY), StringValue::of);
        acceptIfNotNull(
                builder::setUpdatedAt, record.get(BUNDLE.UPDATED_AT), DateTimes::toTimestamp);
        var member = getMemberFromResult((JSON) record.get(BUNDLE_MEMBER_FIELD));
        builder.addAllMember(member);
        acceptIfNotNull(
                builder::setLastStateChangeReason,
                record.get(PROJECT_STATE_CHANGE_REASON.DISPLAY_TEXT),
                text ->
                        com.bees360.project.statechangereason.Message
                                .ProjectStateChangeReasonMessage.newBuilder()
                                .setDisplayText(text)
                                .build());
        return builder.build();
    }

    private Address normalizeAddress(Message.BundleAddressMessage address) {
        var fullAddress =
                address.getStreetAddress()
                        + ", "
                        + address.getCity()
                        + ", "
                        + address.getState()
                        + " "
                        + address.getZip();
        var isGpsApproximate = address.hasLat() && address.hasLng();
        var addressBuilder =
                Address.AddressBuilder.newBuilder()
                        .setAddress(fullAddress)
                        .setStreetAddress(address.getStreetAddress().getValue())
                        .setCity(address.getCity().getValue())
                        .setState(address.getState().getValue())
                        .setCountry(address.getCountry().getValue())
                        .setZip(address.getZip().getValue())
                        .setIsGpsApproximate(isGpsApproximate);
        acceptIfNotNull(addressBuilder::setLat, address.getLat(), DoubleValue::getValue);
        acceptIfNotNull(addressBuilder::setLng, address.getLng(), DoubleValue::getValue);
        var nomalizedAddress = addressProvider.normalize(addressBuilder.build());
        log.debug("Normalize address from {} to {}.", fullAddress, nomalizedAddress);
        return nomalizedAddress;
    }

    private String bundleProjectBatchId(String bundleId, CreateBundleRequest request) {
        var creationRequestMd5 = ByteStrings.computeContentMD5(request.toByteString());
        return StringUtils.joinWith("-", "bundle", bundleId, creationRequestMd5);
    }

    private List<ProjectCreationRequest> buildBatchProjectCreationRequest(
            CreateBundleRequest request) {
        var createBy = request.getCreateBy().getValue();
        return request.getBuildingAddressList().stream()
                .map(
                        address -> {
                            var builder = ProjectCreationRequest.ProjectBuilder.newBuilder();
                            var addressBuilder =
                                    Address.AddressBuilder.newBuilder()
                                            .setStreetAddress(address.getStreetAddress().getValue())
                                            .setCity(address.getCity().getValue())
                                            .setState(address.getState().getValue())
                                            .setCountry(address.getCountry().getValue())
                                            .setZip(address.getZip().getValue())
                                            .setLat(address.getLat().getValue())
                                            .setLng(address.getLng().getValue());
                            var buildingBuilder =
                                    Building.BuildingBuilder.newBuilder()
                                            .setBuildingType(request.getTypeOfProperty());
                            acceptIfNotNull(
                                    buildingBuilder::setYearBuilt,
                                    request.getYearBuilt(),
                                    Int32Value::getValue);
                            var policyBuilder =
                                    Policy.PolicyBuilder.newBuilder()
                                            .setBuilding(buildingBuilder.build())
                                            .setAddress(addressBuilder.build())
                                            .setIsRenewal(request.getIsRenewal().getValue())
                                            .setPolicyNo(request.getPolicyNo().getValue())
                                            .setType(request.getPolicyType().getValue());
                            acceptIfNotNull(
                                    policyBuilder::setPolicyEffectiveDate,
                                    request.getPolicyEffectiveDate(),
                                    DateTimes::toLocalDate);

                            var underwritingBuilder =
                                    Underwriting.UnderwritingBuilder.newBuilder()
                                            .setServiceType(
                                                    ServiceTypeEnum.valueOf(
                                                            request.getServiceType().getNumber()));

                            var contractBuilder =
                                    Contract.ContractBuilder.newBuilder()
                                            .setInsuredBy(
                                                    Customer.of(
                                                            com.bees360.customer.Message
                                                                    .CustomerMessage.newBuilder()
                                                                    .setId(
                                                                            request.getInsuredBy()
                                                                                    .getValue())
                                                                    .build()))
                                            .setProcessedBy(
                                                    Customer.of(
                                                            com.bees360.customer.Message
                                                                    .CustomerMessage.newBuilder()
                                                                    .setId(
                                                                            request.getProcessedBy()
                                                                                    .getValue())
                                                                    .build()));

                            var inspectionBuilder = Inspection.InspectionBuilder.newBuilder();
                            acceptIfNotNull(
                                    inspectionBuilder::setInspectionNo,
                                    request.getInspectionNo(),
                                    StringValue::getValue);

                            var creatorBuilder =
                                    ProjectMessage.Member.newBuilder()
                                            .setUser(
                                                    com.bees360.user.Message.UserMessage
                                                            .newBuilder()
                                                            .setId(createBy)
                                                            .build())
                                            .setRole(RoleEnum.CREATOR.getValue());

                            builder.setPolicy(policyBuilder.build())
                                    .setProjectType(ProjectTypeEnum.UNDERWRITING)
                                    .setUnderwriting(underwritingBuilder.build())
                                    .setContract(contractBuilder.build())
                                    .setContacts(buildProjectContact(request))
                                    .setCreatedBy(createBy)
                                    .setInspection(inspectionBuilder.build())
                                    .setMembers(List.of(Member.of(creatorBuilder.build())));
                            acceptIfNotNull(
                                    builder::setNote, request.getNote(), StringValue::getValue);
                            return builder.build();
                        })
                .toList();
    }

    public static List<Message.BundleContactMessage> getFromResult(JSON recordResult) {
        Function<LinkedTreeMap<String, Object>, Message.BundleContactMessage> handler =
                record -> {
                    var builder = Message.BundleContactMessage.newBuilder();
                    acceptIfNotNull(
                            builder::setId,
                            (String) record.get(BUNDLE_CONTACT.ID.getName()),
                            StringValue::of);
                    acceptIfNotNull(
                            builder::setRole,
                            (String) record.get(BUNDLE_CONTACT.ROLE.getName()),
                            StringValue::of);
                    acceptIfNotNull(
                            builder::setIsPrimary,
                            (Boolean) record.get(BUNDLE_CONTACT.IS_PRIMARY.getName()),
                            BoolValue::of);
                    acceptIfNotNull(
                            builder::setFullName,
                            (String) record.get(BUNDLE_CONTACT.FULL_NAME.getName()),
                            StringValue::of);
                    acceptIfNotNull(
                            builder::setEmail,
                            (String) record.get(BUNDLE_CONTACT.EMAIL.getName()),
                            StringValue::of);
                    acceptIfNotNull(
                            builder::setPhone,
                            (String) record.get(BUNDLE_CONTACT.PHONE.getName()),
                            StringValue::of);
                    return builder.build();
                };
        return getFromResultJson(recordResult, handler);
    }

    private static List<BundleMessage.BundleStatusMessage> getStatusHistoryFromResult(
            JSON recordResult) {
        return getFromResultJson(
                recordResult,
                r -> {
                    var builder = BundleMessage.BundleStatusMessage.newBuilder();
                    acceptIfNotNull(
                            builder::setStatus,
                            (String) r.get(BUNDLE_STATUS_HISTORY.STATUS.getName()),
                            Message.BundleStatus::valueOf);
                    acceptIfNotNull(
                            builder::setCreatedAt,
                            (String) r.get(BUNDLE_STATUS_HISTORY.CREATED_AT.getName()),
                            JooqBundleManager::convertToProtobufTimestamp);
                    return builder.build();
                });
    }

    private static List<BundleMessage.Member> getMemberFromResult(JSON recordResult) {
        return getFromResultJson(
                recordResult,
                r -> {
                    var builder = BundleMessage.Member.newBuilder();
                    var user =
                            com.bees360.user.Message.UserMessage.newBuilder()
                                    .setId((String) r.get(BUNDLE_MEMBER.USER_ID.getName()))
                                    .build();
                    acceptIfNotNull(builder::setUser, user);
                    var role = (String) r.get(BUNDLE_MEMBER.ROLE.getName());
                    acceptIfNotNull(builder::setRole, role);
                    return builder.build();
                });
    }

    private static <T> List<T> getFromResultJson(
            JSON recordResult, Function<LinkedTreeMap<String, Object>, T> applier) {
        if (recordResult == null) {
            return Collections.emptyList();
        }
        var result = new Gson().fromJson(recordResult.data(), List.class);
        List<T> resultList = new ArrayList<>();
        for (Object o : result) {
            var record = (LinkedTreeMap<String, Object>) o;
            var element = applier.apply(record);
            resultList.add(element);
        }
        return resultList;
    }

    public static String toJsonString(MessageOrBuilder message) {
        try {
            return printer.print(message);
        } catch (InvalidProtocolBufferException e) {
            throw new IllegalArgumentException(e);
        }
    }

    private Field<JSONB> buildJsonbSetField(Field<JSONB> field, String value, String... paths) {
        var jsonbValue = JSONB.valueOf(value);
        return DSL.function(
                "jsonb_set",
                JSONB.class,
                field,
                DSL.val(paths, String[].class),
                DSL.val(jsonbValue, JSONB.class),
                DSL.val(true, Boolean.class));
    }

    /**
     * Convert ISO 8601 string to protobuf Timestamp.
     *
     * @param iso8601String ISO 8601 string
     * @return protobuf Timestamp
     */
    public static Timestamp convertToProtobufTimestamp(String iso8601String) {
        // Parse ISO 8601 string with timezone offset
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(iso8601String);
        Instant instant = offsetDateTime.toInstant();
        return DateTimes.toTimestamp(instant);
    }

    public static Integer countDaysOldFromRecord(Record r) {
        var preDaysOld = r.get(BUNDLE_DAYS_OLD.PRE_DAYS_OLD);
        if (preDaysOld == null) {
            return 0;
        }
        var recordOpenedAt = r.get(BUNDLE_DAYS_OLD.OPENED_OR_EFFECTIVE_AT);
        // Current project state is PROJECT_OPEN when lastOpenedAt is not null,
        // should count the days old of the current state additionally.
        var currentOpenedDaysOld =
                Optional.ofNullable(recordOpenedAt)
                        .map(
                                o -> {
                                    var countValue =
                                            Duration.between(o.toInstant(), Instant.now()).toDays();
                                    // Avoid negative value.
                                    // e.g. policy effective date is after now.
                                    return Math.max(countValue, 0);
                                })
                        .orElse(0L);
        return preDaysOld + currentOpenedDaysOld.intValue();
    }

    private List<Contact> buildProjectContact(CreateBundleRequest request) {
        return request.getContactList().stream()
                .map(
                        contact -> {
                            var builder = ProjectMessage.Contact.newBuilder();
                            builder.setIsPrimary(contact.getIsPrimary().getValue());
                            builder.setFullName(contact.getFullName().getValue());
                            builder.setPrimaryEmail(contact.getEmail().getValue());
                            builder.setPrimaryPhone(contact.getPhone().getValue());
                            builder.setRole(contact.getRole().getValue());
                            return builder.build();
                        })
                .map(Contact::of)
                .toList();
    }
}
