package com.bees360.bundle;

import com.bees360.address.AddressProvider;
import com.bees360.bundle.config.JooqBundleManagerConfig;
import com.bees360.contract.ContractRepository;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.state.ProjectStateChangeReasonProvider;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@Import({
    JooqBundleManagerConfig.class,
})
@SpringBootTest(classes = {JooqBundleMemberManagerTest.Config.class})
public class JooqBundleMemberManagerTest extends AbstractBundleMemberManagerTest {
    @Configuration
    static class Config {}

    @MockitoBean AddressProvider addressManager;

    @MockitoBean ContractRepository contractRepository;

    @MockitoBean BatchProjectCreator batchProjectCreator;

    @MockitoBean ExternalIntegrationProvider externalIntegrationProvider;

    @MockitoBean ProjectGroupManager projectGroupManager;
    @MockitoBean ProjectStateChangeReasonProvider projectStateChangeReasonProvider;

    @MockitoBean ProjectIIManager projectIIManager;

    public JooqBundleMemberManagerTest(
            @Autowired BundleManager bundleManager,
            @Autowired AddressProvider mockAddressProvider,
            @Autowired ContractRepository mockContractRepository,
            @Autowired BatchProjectCreator mockBatchProjectCreator,
            @Autowired ExternalIntegrationProvider mockExternalIntegrationProvider,
            @Autowired ProjectGroupManager mockProjectGroupManager,
            @Autowired ProjectIIManager projectIIManager,
            @Autowired ProjectStateChangeReasonProvider projectStateChangeReasonProvider,
            @Autowired BundleMemberManager bundleMemberManager) {
        super(
                bundleManager,
                mockAddressProvider,
                mockContractRepository,
                mockBatchProjectCreator,
                mockExternalIntegrationProvider,
                mockProjectGroupManager,
                projectIIManager,
                projectStateChangeReasonProvider,
                bundleMemberManager);
    }

    @Test
    public void testUpdateBundleMember() {
        super.testUpdateBundleMember();
    }

    @Test
    public void testFindBundleMember() {
        super.testFindBundleMember();
    }
}
