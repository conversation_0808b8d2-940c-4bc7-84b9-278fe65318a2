package com.bees360.bundle;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.bees360.address.AddressProvider;
import com.bees360.bundle.config.JooqBundleManagerConfig;
import com.bees360.contract.ContractRepository;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.state.ProjectStateChangeReasonProvider;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@Import({
    JooqBundleManagerConfig.class,
})
@SpringBootTest(classes = {JooqBundleManagerTest.Config.class})
public class JooqBundleManagerTest extends AbstractBundleManagerTest {

    @Configuration
    static class Config {}

    @MockitoBean AddressProvider addressManager;

    @MockitoBean ContractRepository contractRepository;

    @MockitoBean BatchProjectCreator batchProjectCreator;

    @MockitoBean ExternalIntegrationProvider externalIntegrationProvider;

    @MockitoBean ProjectGroupManager projectGroupManager;
    @MockitoBean ProjectStateChangeReasonProvider projectStateChangeReasonProvider;

    @MockitoBean ProjectIIManager projectIIManager;

    public JooqBundleManagerTest(
            @Autowired BundleManager bundleManager,
            @Autowired AddressProvider mockAddressProvider,
            @Autowired ContractRepository mockContractRepository,
            @Autowired BatchProjectCreator mockBatchProjectCreator,
            @Autowired ExternalIntegrationProvider mockExternalIntegrationProvider,
            @Autowired ProjectGroupManager mockProjectGroupManager,
            @Autowired ProjectIIManager mockProjectIIManager,
            @Autowired ProjectStateChangeReasonProvider projectStateChangeReasonProvider,
            @Autowired BundleStateManager bundleStateManager) {
        super(
                bundleManager,
                mockAddressProvider,
                mockContractRepository,
                mockBatchProjectCreator,
                mockExternalIntegrationProvider,
                mockProjectGroupManager,
                mockProjectIIManager,
                projectStateChangeReasonProvider,
                bundleStateManager);
    }

    @Test
    public void testCreateBundleWithNoFailure() {
        super.testCreateBundleWithNoFailure();
    }

    @Test
    public void testFindByIdShouldSucceed() {
        super.testFindByIdShouldSucceed();
    }

    @Test
    public void testFindByEmptyOrNullShouldThrow() {
        super.testFindByEmptyOrNullShouldThrow();
    }

    @Test
    public void testUpdateServiceType() {
        super.testUpdateServiceType();
    }

    @Test
    public void testUpdateYearBuilt() {
        super.testUpdateYearBuilt();
    }

    @Test
    public void testUpdateNumberOfBuildings() {
        super.testUpdateNumberOfBuildings();
    }

    @Test
    public void testUpdatePolicyNumber() {
        super.testUpdatePolicyNumber();
    }

    @Test
    public void testUpdatePolicyEffectiveDate() {
        super.testUpdatePolicyEffectiveDate();
    }

    @Test
    public void testUpdatePolicyRenewal() {
        super.testUpdatePolicyRenewal();
    }

    @Test
    public void testUpdatePolicyTypeAndPropertyType() {
        super.testUpdatePolicyTypeAndPropertyType();
    }

    @Test
    public void testUpdateInspectionNumber() {
        super.testUpdateInspectionNumber();
    }

    @Test
    public void testUpdateAddress() {
        super.testUpdateAddress();
    }

    @Test
    public void testUpdateContact() {
        super.testUpdateContact();
    }

    @Test
    public void testCreateBundleProject() {
        super.testCreateBundleProject();
    }

    @Test
    public void testAddBundleProject() {
        super.testAddBundleProject();
    }

    @Test
    public void testRemoveBundleProject() {
        super.testRemoveBundleProject();
    }

    @Test
    public void testRemoveAllBundleProjectShouldFail() {
        super.testRemoveAllBundleProjectShouldFail();
    }

    @Test
    public void testFindByInvalidQueryShouldThrow() {
        assertThrows(
                IllegalArgumentException.class,
                () -> bundleManager.findByQuery(Message.BundleRequestQuery.getDefaultInstance()));
        assertThrows(IllegalArgumentException.class, () -> bundleManager.findByQuery(null));
    }

    @Test
    public void testChangeBundleState() {
        super.testChangeBundleState();
    }
}
