package com.bees360.bundle;

import com.bees360.bundle.config.JooqBundleTagManagerConfig;
import com.bees360.project.tag.ProjectTagRepository;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(classes = {JooqBundleTagManagerTest.Config.class})
public class JooqBundleTagManagerTest extends AbstractBundleTagManagerTest {

    @Import({
        JooqBundleTagManagerConfig.class,
    })
    @Configuration
    static class Config {}

    @MockitoBean ProjectTagRepository projectTagRepository;

    public JooqBundleTagManagerTest(
            @Autowired BundleTagManager bundleTagManager,
            @Autowired ProjectTagRepository projectTagRepository) {
        super(bundleTagManager, projectTagRepository);
    }

    @Test
    public void testUpdateBundleTag() {
        super.testUpdateBundleTag();
    }

    @Test
    public void testFindBundleTag() {
        super.testFindBundleTag();
    }
}
