package com.bees360.bundle;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.project.member.RoleEnum;
import com.bees360.util.DateTimes;
import com.google.protobuf.BoolValue;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.context.annotation.Import;

@GrpcService
@Import({
    ExceptionTranslateInterceptor.class,
})
@Log4j2
public class GrpcBundleService extends BundleServiceGrpc.BundleServiceImplBase {
    private final BundleManager bundleManager;
    private final BundleTagManager bundleTagManager;
    private final BundleMemberManager bundleMemberManager;
    private final BundleStateManager bundleStateManager;

    public GrpcBundleService(
            BundleManager grpcBundleManager,
            BundleTagManager grpcBundleTagManager,
            BundleMemberManager bundleMemberManager,
            BundleStateManager grpcBundleStateManager) {
        this.bundleManager = grpcBundleManager;
        this.bundleTagManager = grpcBundleTagManager;
        this.bundleMemberManager = bundleMemberManager;
        this.bundleStateManager = grpcBundleStateManager;

        log.info(
                "Created {}(bundleManager={}, bundleTagManager={},bundleMemberManager={}).",
                this,
                this.bundleManager,
                this.bundleTagManager,
                this.bundleMemberManager);
    }

    @Override
    public void create(
            Message.CreateBundleRequest request,
            StreamObserver<Message.CreateBundleResponse> responseObserver) {
        var response = bundleManager.createBundle(request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void findById(
            StringValue request, StreamObserver<Message.BundleMessage> responseObserver) {
        log.debug("Finding Bundle by ID: {}", request);
        if (request == null || request.getValue().isEmpty()) {
            throw new IllegalArgumentException("Bundle ID cannot be null or empty.");
        }
        var bundle = bundleManager.findById(request.getValue());
        responseObserver.onNext(
                bundle == null ? Message.BundleMessage.getDefaultInstance() : bundle);
        responseObserver.onCompleted();
    }

    @Override
    public void updateBundle(
            Message.UpdateBundleRequest request, StreamObserver<BoolValue> responseObserver) {
        var requestBy = request.getRequestBy().getValue();
        var response = bundleManager.updateBundle(request, requestBy);
        responseObserver.onNext(BoolValue.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void updateBundlePolicy(
            Message.UpdateBundlePolicyRequest request, StreamObserver<BoolValue> responseObserver) {
        var requestBy = request.getRequestBy().getValue();
        var response = bundleManager.updateBundlePolicy(request, requestBy);
        responseObserver.onNext(BoolValue.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void updateBundleContact(
            Message.UpdateBundleContactRequest request,
            StreamObserver<BoolValue> responseObserver) {
        var requestBy = request.getRequestBy().getValue();
        var response = bundleManager.saveBundleContact(request, requestBy);
        responseObserver.onNext(BoolValue.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void syncBundleProject(
            Message.SyncBundleProjectRequest request,
            StreamObserver<Message.SyncBundleProjectResponse> responseObserver) {
        var requestBy = request.getRequestBy().getValue();
        var bundleId = request.getBundleId().getValue();
        var syncField = request.getSyncField();
        Message.SyncBundleProjectResponse response;
        switch (syncField) {
            case TAG -> response = bundleTagManager.syncBundleTag(bundleId, requestBy);
            default -> response = bundleManager.syncBundleProject(request, requestBy);
        }
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void createBundleProject(
            Message.CreateBundleProjectRequest request,
            StreamObserver<Message.CreateBundleResponse> responseObserver) {
        var bundleId = request.getBundleId().getValue();
        var buildingAddresses = request.getAddressList();
        var creationChannel = request.getCreationChannel().getValue();
        var requestBy = request.getRequestBy().getValue();
        var response =
                bundleManager.createBundleProject(
                        bundleId, buildingAddresses, creationChannel, requestBy);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void addBundleProject(
            Message.UpdateBundleProjectRequest request,
            StreamObserver<BoolValue> responseObserver) {
        var bundleId = request.getBundleId().getValue();
        var projectIds = request.getProjectIdList();
        var requestBy = request.getRequestBy().getValue();
        var response = bundleManager.addBundleProject(bundleId, projectIds, requestBy);
        responseObserver.onNext(BoolValue.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void removeBundleProject(
            Message.UpdateBundleProjectRequest request,
            StreamObserver<BoolValue> responseObserver) {
        var bundleId = request.getBundleId().getValue();
        var projectIds = request.getProjectIdList();
        var requestBy = request.getRequestBy().getValue();
        var response = bundleManager.removeBundleProject(bundleId, projectIds, requestBy);
        responseObserver.onNext(BoolValue.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void updateBundleStatus(
            Message.UpdateBundleStatusRequest request, StreamObserver<BoolValue> responseObserver) {
        log.debug("Updating bundle status: {}", request);
        var result =
                bundleManager.updateStatus(
                        request.getBundleId(0),
                        request.getStatus(),
                        request.getRequestBy().getValue(),
                        DateTimes.toInstant(request.getRequestAt()));
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }

    @Override
    public void updateBundleMember(
            Message.UpdateBundleMemberRequest request, StreamObserver<BoolValue> responseObserver) {
        log.debug("Updating bundle member: {}", request);
        var result =
                bundleMemberManager.setMember(
                        request.getBundleId(),
                        RoleEnum.valueOf(request.getRole()),
                        request.getUserId(),
                        request.getOpUserId());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }

    @Override
    public void findBundleMember(
            StringValue request, StreamObserver<Message.BundleMemberList> responseObserver) {
        log.debug("Finding bundle member by bundleId: {}", request);
        var member = bundleMemberManager.findByBundleId(request.getValue());
        responseObserver.onNext(Message.BundleMemberList.newBuilder().addAllMember(member).build());
        responseObserver.onCompleted();
    }

    @Override
    public void findByQuery(
            Message.BundleRequestQuery request,
            StreamObserver<Message.BundleList> responseObserver) {
        log.debug("Finding bundle by query: {}", request);
        var bundles = bundleManager.findByQuery(request);
        responseObserver.onNext(Message.BundleList.newBuilder().addAllBundle(bundles).build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateBundleState(
            Message.UpdateBundleStateRequest request, StreamObserver<BoolValue> responseObserver) {
        log.debug("Updating bundle state: {}", request);
        var result =
                bundleStateManager.changeState(
                        request.getBundleId().getValue(),
                        request.getState(),
                        request.getChangeReason().getValue(),
                        request.getRequestBy().getValue());
        responseObserver.onNext(BoolValue.of(result));
        responseObserver.onCompleted();
    }
}
