package com.bees360.bundle;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.project.tag.Message.ProjectTagListResponse;
import com.bees360.project.tag.ProjectTag;
import com.google.protobuf.Int32Value;

import io.grpc.stub.StreamObserver;

import lombok.extern.log4j.Log4j2;

import net.devh.boot.grpc.server.service.GrpcService;

import org.springframework.context.annotation.Import;

@GrpcService
@Import({
    ExceptionTranslateInterceptor.class,
})
@Log4j2
public class GrpcBundleTagService extends BundleTagServiceGrpc.BundleTagServiceImplBase {

    private final BundleTagManager bundleTagManager;

    public GrpcBundleTagService(BundleTagManager grpcBundleTagManager) {
        this.bundleTagManager = grpcBundleTagManager;
        log.info("Created {}(bundleTagManager={}).", this, this.bundleTagManager);
    }

    @Override
    public void updateBundleTag(
            Message.BundleTagUpdateRequest request, StreamObserver<Int32Value> responseObserver) {
        log.debug("Updating bundle tag with request: {}.", request);
        var bundleId = request.hasBundleId() ? request.getBundleId().getValue() : null;
        var tagIds = request.getTagIdsList();
        var tagType = request.getType();
        var updatedBy = request.hasRequestBy() ? request.getRequestBy().getValue() : null;

        var response =
                bundleTagManager.updateBundleTag(bundleId, tagIds, tagType, updatedBy, "WEB");
        responseObserver.onNext(Int32Value.of(response));
        responseObserver.onCompleted();
    }

    @Override
    public void findByBundleId(
            Message.BundleTagQueryRequest request,
            StreamObserver<ProjectTagListResponse> responseObserver) {
        log.debug("Finding bundle tag with request: {}.", request);
        var bundleId = request.hasBundleId() ? request.getBundleId().getValue() : null;
        var companyId = request.hasCompanyId() ? request.getCompanyId().getValue() : null;
        var tagType = request.getType();

        var response =
                bundleTagManager.findByBundleId(bundleId, companyId, tagType).stream()
                        .map(ProjectTag::toMessage)
                        .toList();
        var responseBuilder = ProjectTagListResponse.newBuilder().addAllTags(response);
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }
}
