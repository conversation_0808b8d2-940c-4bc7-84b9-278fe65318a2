package com.bees360.bundle;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.bundle.Message.BundleList;
import com.bees360.bundle.Message.BundleMessage;
import com.bees360.bundle.Message.BundleRequestQuery;
import com.google.protobuf.StringValue;

import io.grpc.stub.StreamObserver;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

@ExtendWith(MockitoExtension.class)
public class GrpcBundleServiceTest {

    @Mock private BundleManager bundleManager;

    @Mock private BundleTagManager bundleTagManager;

    @Mock private BundleMemberManager bundleMemberManager;

    @InjectMocks private GrpcBundleService grpcBundleService;

    @Captor private ArgumentCaptor<BundleList> bundleListCaptor;

    @Mock private BundleStateManager bundleStateManager;

    private BundleRequestQuery request;

    @BeforeEach
    void setUp() {
        request = Message.BundleRequestQuery.newBuilder().build();
    }

    @Test
    void testFindByQuery_ReturnsBundles() {
        // Arrange
        BundleMessage bundle1 =
                Message.BundleMessage.newBuilder().setId(StringValue.of("1")).build();
        BundleMessage bundle2 =
                Message.BundleMessage.newBuilder().setId(StringValue.of("2")).build();
        List<BundleMessage> bundles = List.of(bundle1, bundle2);
        when(bundleManager.findByQuery(request)).thenReturn(bundles);

        StreamObserver<BundleList> responseObserver = mock(StreamObserver.class);

        // Act
        grpcBundleService.findByQuery(request, responseObserver);

        // Assert
        verify(responseObserver).onNext(bundleListCaptor.capture());
        assertEquals(bundles, bundleListCaptor.getValue().getBundleList());
        verify(responseObserver).onCompleted();
    }

    @Test
    void testFindByQuery_ReturnsEmptyList() {
        // Arrange
        List<BundleMessage> bundles = List.of();
        when(bundleManager.findByQuery(request)).thenReturn(bundles);

        StreamObserver<BundleList> responseObserver = mock(StreamObserver.class);

        // Act
        grpcBundleService.findByQuery(request, responseObserver);

        // Assert
        verify(responseObserver).onNext(bundleListCaptor.capture());
        assertTrue(bundleListCaptor.getValue().getBundleList().isEmpty());
        verify(responseObserver).onCompleted();
    }

    @Test
    void testFindByQuery_ThrowsException_ShouldNotCallOnCompleted() {
        // Arrange
        when(bundleManager.findByQuery(request))
                .thenThrow(new IllegalArgumentException("Database error"));

        StreamObserver<BundleList> responseObserver = mock(StreamObserver.class);

        // Act & Assert
        assertThrows(
                RuntimeException.class,
                () -> {
                    grpcBundleService.findByQuery(request, responseObserver);
                });

        verify(responseObserver, never()).onNext(any());
        verify(responseObserver, never()).onCompleted();
    }
}
