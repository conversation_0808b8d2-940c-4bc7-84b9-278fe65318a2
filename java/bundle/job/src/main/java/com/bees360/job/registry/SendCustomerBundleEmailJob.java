package com.bees360.job.registry;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Collection;

@JobPayload
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class SendCustomerBundleEmailJob {
    private String mailSender;
    private String templateKey;
    private String variablesJson;
    private Collection<String> toRecipients;
    private Collection<String> ccRecipients;
    private Collection<String> bccRecipients;
}
