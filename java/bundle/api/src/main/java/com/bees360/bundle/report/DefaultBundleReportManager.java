package com.bees360.bundle.report;

import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportGroupManager;
import com.google.common.base.Preconditions;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Log4j2
public class DefaultBundleReportManager implements BundleReportManager {

    public static final String REPORT_GROUP_TYPE_BUNDLE = "GROUP_BUNDLE";

    private final ReportGroupManager reportGroupManager;

    public DefaultBundleReportManager(ReportGroupManager reportGroupManager) {
        this.reportGroupManager = reportGroupManager;
        log.info("Created {}(reportGroupManager={}).", this, this.reportGroupManager);
    }

    @Override
    public List<Report> find(
            String bundleId,
            @Nullable String reportType,
            @Nullable Message.ReportMessage.Status status) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(bundleId),
                "Fail to get bundle report: bundle id can not be blank.");
        List<Report> reports = new ArrayList<>();
        for (Report report :
                reportGroupManager.findReportInGroup(
                        bundleId, REPORT_GROUP_TYPE_BUNDLE, reportType, status)) {
            reports.add(report);
        }
        return reports;
    }

    @Override
    public List<Report> findInHistory(
            String bundleId,
            @Nullable String reportType,
            @Nullable Message.ReportMessage.Status status) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(bundleId),
                "Fail to get bundle report: bundle id can not be blank.");
        List<Report> reports = new ArrayList<>();
        for (Report report :
                reportGroupManager.findReportInHistory(
                        bundleId, REPORT_GROUP_TYPE_BUNDLE, reportType, status)) {
            reports.add(report);
        }
        return reports;
    }
}
