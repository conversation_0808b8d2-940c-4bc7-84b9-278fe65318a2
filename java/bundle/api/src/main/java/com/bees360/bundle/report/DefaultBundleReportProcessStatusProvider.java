package com.bees360.bundle.report;

import com.bees360.report.Message;
import com.bees360.report.Message.ReportProcessStatus.Type;
import com.bees360.report.ReportProcessStatusProvider;
import com.google.common.base.Preconditions;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Log4j2
public class DefaultBundleReportProcessStatusProvider implements BundleReportProcessStatusProvider {

    public static final String REPORT_GROUP_TYPE_BUNDLE = "GROUP_BUNDLE";

    private final ReportProcessStatusProvider reportProcessStatusProvider;

    public DefaultBundleReportProcessStatusProvider(
            ReportProcessStatusProvider reportProcessStatusProvider) {
        this.reportProcessStatusProvider = reportProcessStatusProvider;
        log.info(
                "Created {}(reportProcessStatusProvider={}).",
                this,
                this.reportProcessStatusProvider);
    }

    @Nullable
    @Override
    public List<Message.ReportProcessStatus> getReportProcessStatus(
            String bundleId, String reportTypeCode, Type type) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(bundleId),
                "Fail to get bundle report process status: bundle id can not be blank.");
        Preconditions.checkArgument(
                type != null
                        && !Type.UNKNOWN_PROCESS_TYPE.equals(type)
                        && !Type.UNRECOGNIZED.equals(type),
                "Fail to get bundle report process status: process type is illegal.");
        return reportProcessStatusProvider.getReportProcessStatus(
                bundleId, REPORT_GROUP_TYPE_BUNDLE, reportTypeCode, type);
    }
}
