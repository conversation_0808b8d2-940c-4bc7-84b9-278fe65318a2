package com.bees360.bundle;

public interface BundleStateManager {

    /**
     * Change bundle state.
     *
     * @param bundleId bundle id
     * @param state new state
     * @param changeReason change reason
     * @param changedBy changed by
     * @return true if state changed successfully, false otherwise
     */
    boolean changeState(
            String bundleId, Message.BundleState state, String changeReason, String changedBy);
}
