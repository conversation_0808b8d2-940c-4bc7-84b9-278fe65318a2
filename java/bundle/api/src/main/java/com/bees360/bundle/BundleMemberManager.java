package com.bees360.bundle;

import com.bees360.project.member.RoleEnum;

import java.util.List;

public interface BundleMemberManager {
    /**
     * Set a member for a bundle, if one already exists, update it. If userId is empty, remove the
     * member.
     *
     * @param bundleId bundle id
     * @param role user role
     * @param userId user id
     * @param opUserId the person who created member
     * @return true means the creation is successful, false means the member cannot be created
     */
    boolean setMember(String bundleId, RoleEnum role, String userId, String opUserId);

    /**
     * Find all members for a bundle.
     *
     * @param bundleId bundle id
     * @return list of members
     */
    List<Message.BundleMessage.Member> findByBundleId(String bundleId);
}
