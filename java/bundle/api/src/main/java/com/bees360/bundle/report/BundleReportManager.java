package com.bees360.bundle.report;

import com.bees360.report.Message;
import com.bees360.report.Report;

import jakarta.annotation.Nullable;

import java.util.List;

public interface BundleReportManager {

    /**
     * Find reports by bundle ID, report type and status
     *
     * @param bundleId The ID of the bundle
     * @param reportType The type of report to find
     * @param status The status of reports to find
     * @return A list of reports matching all criteria
     */
    List<Report> find(
            String bundleId,
            @Nullable String reportType,
            @Nullable Message.ReportMessage.Status status);

    /**
     * Find historical reports based on bundle ID, report type and status
     *
     * @param bundleId The ID of the bundle
     * @param reportType The type of report to find
     * @param status The status of reports to find
     * @return A list of historical reports matching the criteria
     */
    List<Report> findInHistory(
            String bundleId,
            @Nullable String reportType,
            @Nullable Message.ReportMessage.Status status);
}
