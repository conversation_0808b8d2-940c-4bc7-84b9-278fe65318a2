package com.bees360.bundle;

import com.bees360.bundle.Message.CreateBundleRequest;
import com.bees360.bundle.Message.CreateBundleResponse;
import com.bees360.bundle.Message.SyncBundleProjectResponse;

import java.time.Instant;
import java.util.List;

public interface BundleManager extends BundleProvider {
    CreateBundleResponse createBundle(CreateBundleRequest request);

    boolean updateBundle(Message.UpdateBundleRequest request, String requestBy);

    boolean updateBundlePolicy(Message.UpdateBundlePolicyRequest request, String requestBy);

    boolean saveBundleContact(Message.UpdateBundleContactRequest request, String requestBy);

    SyncBundleProjectResponse syncBundleProject(
            Message.SyncBundleProjectRequest request, String requestBy);

    CreateBundleResponse createBundleProject(
            String bundleId,
            List<Message.BundleAddressMessage> buildingAddresses,
            String creationChannel,
            String requestBy);

    boolean addBundleProject(String bundleId, List<String> projectIds, String requestBy);

    boolean removeBundleProject(String bundleId, List<String> projectIds, String requestBy);

    /**
     * Update bundle status
     *
     * @param bundleId bundle id
     * @param status bundle status
     * @param updatedBy updated by
     * @param updatedAt updated at
     * @return true if updated successfully, false otherwise
     * @throws IllegalArgumentException if bundle current status is greater than or equal to status
     */
    boolean updateStatus(
            String bundleId, Message.BundleStatus status, String updatedBy, Instant updatedAt);

    /**
     * Delete bundle by id
     *
     * @param bundleId the id of the bundle to delete
     * @param deletedBy the user who is deleting the bundle
     * @return true if the bundle was successfully deleted, false otherwise
     */
    boolean deleteById(String bundleId, String deletedBy);
}
