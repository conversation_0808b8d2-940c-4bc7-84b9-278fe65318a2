package com.bees360.bundle;

import com.bees360.bundle.Message.SyncBundleProjectResponse;
import com.bees360.project.tag.Message;

import java.util.List;

public interface BundleTagManager extends BundleTagProvider {
    int updateBundleTag(
            String bundleId,
            List<String> tagIds,
            Message.ProjectTagType type,
            String requestBy,
            String requestVia);

    SyncBundleProjectResponse syncBundleTag(String bundleId, String requestBy);
}
