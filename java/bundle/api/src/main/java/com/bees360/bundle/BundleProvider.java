package com.bees360.bundle;

import com.bees360.bundle.Message.BundleMessage;

import java.util.List;

public interface BundleProvider {
    /**
     * Retrieves a BundleMessage by its unique identifier.
     *
     * @param id the unique identifier of the BundleMessage
     * @return the corresponding BundleMessage if found, or null if no such message exists
     */
    BundleMessage findById(String id);

    /**
     * Retrieves a list of BundleMessages based on the provided query parameters.
     *
     * @param query the query parameters to filter the BundleMessages
     * @return a list of BundleMessages that match the query criteria
     */
    List<BundleMessage> findByQuery(Message.BundleRequestQuery query);
}
