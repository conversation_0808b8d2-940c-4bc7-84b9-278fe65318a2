package com.bees360.bundle.report;

import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportProcessor;
import com.bees360.report.ReportResource;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.stream.Collectors;

@Log4j2
public class DefaultBundleReportProcessor implements BundleReportProcessor {

    public static final String REPORT_GROUP_TYPE_BUNDLE = "GROUP_BUNDLE";

    private final ReportProcessor reportProcessor;
    private final ReportManager reportManager;

    public DefaultBundleReportProcessor(
            ReportProcessor reportProcessor, ReportManager reportManager) {
        this.reportProcessor = reportProcessor;
        this.reportManager = reportManager;
        log.info(
                "Created {}(reportProcessor={}, reportManager={}).",
                this,
                this.reportProcessor,
                this.reportManager);
    }

    @Override
    public ListenableFuture<Void> mergeReport(
            String bundleId, String reportTypeCode, Iterable<String> reportIds, String mergedBy) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(bundleId),
                "Fail to merge bundle report: bundle id cannot be blank.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(reportTypeCode),
                "Fail to merge bundle report: report type cannot be blank.");
        Preconditions.checkArgument(
                reportIds != null && reportIds.iterator().hasNext(),
                "Fail to merge bundle report: report id cannot be empty.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(mergedBy),
                "Fail to merge report: mergedBy cannot be blank.");
        var reports = reportManager.findAllById(reportIds);
        var reportResourceIds = Iterables.toStream(reports).map(this::mapReportResourceId).toList();
        var future =
                reportProcessor.mergeReport(
                        bundleId,
                        REPORT_GROUP_TYPE_BUNDLE,
                        reportTypeCode,
                        reportResourceIds,
                        mergedBy);
        SettableFuture<Void> settableFuture = SettableFuture.create();
        Futures.addCallback(
                future,
                new FutureCallback<>() {
                    @Override
                    public void onSuccess(Void result) {
                        log.info(
                                "Successfully merged bundle {} report by {} for bundle {},"
                                        + " reportIds: {}.",
                                reportTypeCode,
                                mergedBy,
                                bundleId,
                                reportIds);
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        settableFuture.setException(t);
                        log.warn(
                                "Fail to merge bundle {} report by {} for bundle {}, reportIds:"
                                        + " {}.",
                                reportTypeCode,
                                mergedBy,
                                bundleId,
                                reportIds);
                    }
                },
                MoreExecutors.directExecutor());

        return settableFuture;
    }

    private String mapReportResourceId(Report report) {
        var resources =
                Iterables.toStream(Objects.requireNonNull(report.getResources()))
                        .collect(Collectors.toMap(ReportResource::getType, r -> r, (r1, r2) -> r1));
        return resources.containsKey(Message.ReportMessage.Resource.Type.ORIGIN)
                ? resources.get(Message.ReportMessage.Resource.Type.ORIGIN).getId()
                : resources.get(Message.ReportMessage.Resource.Type.COMPRESSED).getId();
    }
}
