package com.bees360.bundle;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.address.AddressProvider;
import com.bees360.contract.ContractRepository;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.state.ProjectStateChangeReasonProvider;

public class AbstractBundleMemberManagerTest extends AbstractBundleManagerTest {

    private final BundleMemberManager bundleMemberManager;

    public AbstractBundleMemberManagerTest(
            BundleManager bundleManager,
            AddressProvider mockAddressProvider,
            ContractRepository mockContractRepository,
            BatchProjectCreator mockBatchProjectCreator,
            ExternalIntegrationProvider mockExternalIntegrationProvider,
            ProjectGroupManager mockProjectGroupManager,
            ProjectIIManager projectIIManager,
            ProjectStateChangeReasonProvider projectStateChangeReasonProvider,
            BundleMemberManager bundleMemberManager) {
        super(
                bundleManager,
                mockAddressProvider,
                mockContractRepository,
                mockBatchProjectCreator,
                mockExternalIntegrationProvider,
                mockProjectGroupManager,
                projectIIManager,
                projectStateChangeReasonProvider,
                null);
        this.bundleMemberManager = bundleMemberManager;
    }

    public void testUpdateBundleMember() {
        var bundleId = createRandomBundle().getId().getValue();
        var userId = randomId();
        var role = RoleEnum.UNDERWRITER;
        assertTrue(bundleMemberManager.setMember(bundleId, role, userId, userId));
        var bundle = bundleManager.findById(bundleId);
        var member =
                bundle.getMemberList().stream()
                        .filter(m -> m.getRole().equals(RoleEnum.UNDERWRITER.getValue()))
                        .findFirst()
                        .orElse(null);
        assertNotNull(member);
        assertEquals(member.getUser().getId(), userId);
        var newUserId = randomId();
        assertTrue(bundleMemberManager.setMember(bundleId, role, newUserId, userId));
        bundle = bundleManager.findById(bundleId);
        member =
                bundle.getMemberList().stream()
                        .filter(m -> m.getRole().equals(RoleEnum.UNDERWRITER.getValue()))
                        .findFirst()
                        .orElse(null);
        assertNotNull(member);
        assertEquals(member.getUser().getId(), newUserId);
    }

    public void testFindBundleMember() {
        var bundleId = createRandomBundle().getId().getValue();
        var userId = randomId();
        var role = RoleEnum.UNDERWRITER;
        assertTrue(bundleMemberManager.setMember(bundleId, role, userId, userId));
        var bundle = bundleManager.findById(bundleId);
        var member =
                bundle.getMemberList().stream()
                        .filter(m -> m.getRole().equals(RoleEnum.UNDERWRITER.getValue()))
                        .findFirst()
                        .orElse(null);
        assertNotNull(member);
        assertEquals(member.getUser().getId(), userId);
        var bundleMember =
                bundleMemberManager.findByBundleId(bundleId).stream()
                        .filter(bm -> bm.getRole().equals(RoleEnum.UNDERWRITER.getValue()))
                        .findFirst()
                        .orElse(null);
        assertNotNull(bundleMember);
        assertEquals(bundleMember.getUser().getId(), userId);
    }
}
