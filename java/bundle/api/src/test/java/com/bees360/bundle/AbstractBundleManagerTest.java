package com.bees360.bundle;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertIterableEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.bees360.address.Address;
import com.bees360.address.AddressProvider;
import com.bees360.api.ApiStatus;
import com.bees360.contract.Contract;
import com.bees360.contract.ContractRepository;
import com.bees360.customer.Customer;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message.ServiceType;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.Message.ProjectGroupMessage;
import com.bees360.project.group.ProjectGroup;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.state.ProjectStateChangeReasonProvider;
import com.bees360.util.DateTimes;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
public class AbstractBundleManagerTest {
    protected static final RandomStringUtils secureRandomStringUtils = RandomStringUtils.secure();
    public static final String BUNDLE_PROJECT_GROUP_TYPE = "BUNDLE_PROJECT";
    protected final BundleManager bundleManager;
    protected final AddressProvider addressProvider;
    protected final ContractRepository contractRepository;
    protected final BatchProjectCreator batchProjectCreator;
    protected final ExternalIntegrationProvider externalIntegrationProvider;
    protected final ProjectGroupManager projectGroupManager;
    protected final ProjectIIManager projectIIManager;
    protected final ProjectStateChangeReasonProvider projectStateChangeReasonProvider;
    private final BundleStateManager bundleStateManager;

    public AbstractBundleManagerTest(
            BundleManager bundleManager,
            AddressProvider mockAddressProvider,
            ContractRepository mockContractRepository,
            BatchProjectCreator mockBatchProjectCreator,
            ExternalIntegrationProvider mockExternalIntegrationProvider,
            ProjectGroupManager mockProjectGroupManager,
            ProjectIIManager projectIIManager,
            ProjectStateChangeReasonProvider projectStateChangeReasonProvider,
            BundleStateManager bundleStateManager) {
        this.bundleManager = bundleManager;
        this.addressProvider = mockAddressProvider;
        this.contractRepository = mockContractRepository;
        this.batchProjectCreator = mockBatchProjectCreator;
        this.externalIntegrationProvider = mockExternalIntegrationProvider;
        this.projectGroupManager = mockProjectGroupManager;
        this.projectIIManager = projectIIManager;
        this.projectStateChangeReasonProvider = projectStateChangeReasonProvider;
        this.bundleStateManager = bundleStateManager;
    }

    public Message.CreateBundleResponse createRandomBundle() {
        var creationRequest = randomCreateBundleRequest();
        var insuredBy = creationRequest.getInsuredBy().getValue();
        var processedBy = creationRequest.getProcessedBy().getValue();
        var contract = randomContract(insuredBy, processedBy);
        var mainBundleAddress =
                creationRequest.getBuildingAddressList().stream()
                        .filter(address -> address.getIsMain().getValue())
                        .findFirst()
                        .get();
        var address = randomAddress(mainBundleAddress);
        var mockIntegration = new ArrayList<ExternalIntegration>();
        var mockProjectCreationResult = new HashMap<Integer, ApiStatus>();
        var mockProjectGroup = ProjectGroupMessage.newBuilder();

        for (var idx = 0; idx < creationRequest.getNumberOfBuildings().getValue(); idx++) {
            var projectId = randomId();
            mockProjectCreationResult.put(idx, ApiStatus.OK);
            mockIntegration.add(
                    ExternalIntegration.from(
                            com.bees360.project.Message.IntegrationMessage.newBuilder()
                                    .setIntegrationType("BATCH")
                                    .setSubReferenceNumber(String.valueOf(idx))
                                    .setProjectId(projectId)
                                    .build()));
            mockProjectGroup.addProjectId(projectId);
        }

        Mockito.doReturn(address).when(addressProvider).normalize(Mockito.any(Address.class));
        Mockito.doReturn(contract)
                .when(contractRepository)
                .findByCompanyId(Mockito.eq(insuredBy), Mockito.eq(processedBy));
        Mockito.doReturn(mockProjectCreationResult)
                .when(batchProjectCreator)
                .createBatch(
                        Mockito.any(),
                        Mockito.eq(creationRequest.getCreateBy().getValue()),
                        Mockito.any(),
                        Mockito.eq(false),
                        Mockito.eq("WEB"));
        Mockito.doReturn(mockIntegration)
                .when(externalIntegrationProvider)
                .findAllByReference(Mockito.eq("BATCH"), Mockito.any());
        Mockito.doNothing()
                .when(projectGroupManager)
                .addProjectToGroup(
                        Mockito.anyString(),
                        Mockito.eq(BUNDLE_PROJECT_GROUP_TYPE),
                        Mockito.any(),
                        Mockito.any());
        doMockCreatedReason();

        var response = bundleManager.createBundle(creationRequest);
        Mockito.doReturn(address).when(addressProvider).findById(Mockito.eq(address.getId()));
        Mockito.doReturn(contract).when(contractRepository).findById(Mockito.eq(contract.getId()));
        mockProjectGroup.setKey(response.getId().getValue()).setType(BUNDLE_PROJECT_GROUP_TYPE);
        Mockito.doReturn(ProjectGroup.from(mockProjectGroup.build()))
                .when(projectGroupManager)
                .findByGroupKey(
                        Mockito.eq(mockProjectGroup.getKey()),
                        Mockito.eq(mockProjectGroup.getType()));
        var mockProjects = new ArrayList<ProjectII>();
        mockProjectGroup.getProjectIdList().forEach(projectId -> {});

        mockProjectGroup
                .getProjectIdList()
                .forEach(
                        projectId -> {
                            ProjectII mockProject =
                                    ProjectII.from(
                                            com.bees360.project.Message.ProjectMessage.newBuilder()
                                                    .setId(projectId)
                                                    .build());
                            Mockito.doReturn(mockProject)
                                    .when(projectIIManager)
                                    .findById(Mockito.eq(projectId));
                            mockProjects.add(mockProject);
                        });
        Mockito.doReturn(mockProjects)
                .when(projectIIManager)
                .findAllById(mockProjectGroup.getProjectIdList());
        return response;
    }

    public void testCreateBundleWithNoFailure() {
        var creationRequest = randomCreateBundleRequest();
        var insuredBy = creationRequest.getInsuredBy().getValue();
        var processedBy = creationRequest.getProcessedBy().getValue();
        var contract = randomContract(insuredBy, processedBy);
        var mainBundleAddress =
                creationRequest.getBuildingAddressList().stream()
                        .filter(address -> address.getIsMain().getValue())
                        .findFirst()
                        .get();
        var address = randomAddress(mainBundleAddress);
        var mockIntegration = new ArrayList<ExternalIntegration>();
        var mockProjectCreationResult = new HashMap<Integer, ApiStatus>();
        ArgumentCaptor<List<ProjectCreationRequest>> projectCreationRequestCaptor =
                ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<Iterable<String>> projectGroupCaptor =
                ArgumentCaptor.forClass(Iterable.class);

        for (var idx = 0; idx < creationRequest.getNumberOfBuildings().getValue(); idx++) {
            mockProjectCreationResult.put(idx, ApiStatus.OK);
            mockIntegration.add(
                    ExternalIntegration.from(
                            com.bees360.project.Message.IntegrationMessage.newBuilder()
                                    .setIntegrationType("BATCH")
                                    .setSubReferenceNumber(String.valueOf(idx))
                                    .setProjectId(randomId())
                                    .build()));
        }

        Mockito.doReturn(address).when(addressProvider).normalize(Mockito.any(Address.class));
        Mockito.doReturn(contract)
                .when(contractRepository)
                .findByCompanyId(Mockito.eq(insuredBy), Mockito.eq(processedBy));
        Mockito.doReturn(mockProjectCreationResult)
                .when(batchProjectCreator)
                .createBatch(
                        Mockito.any(),
                        Mockito.eq(creationRequest.getCreateBy().getValue()),
                        projectCreationRequestCaptor.capture(),
                        Mockito.eq(false),
                        Mockito.eq("WEB"));
        Mockito.doReturn(mockIntegration)
                .when(externalIntegrationProvider)
                .findAllByReference(Mockito.eq("BATCH"), Mockito.any());
        Mockito.doNothing()
                .when(projectGroupManager)
                .addProjectToGroup(
                        Mockito.anyString(),
                        Mockito.eq(BUNDLE_PROJECT_GROUP_TYPE),
                        projectGroupCaptor.capture(),
                        Mockito.any());
        doMockCreatedReason();
        var response = bundleManager.createBundle(creationRequest);

        assertNotNull(response.getId());
        response.getResultList()
                .forEach(
                        result -> {
                            assertNotNull(result.getProjectId());
                            assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                            assertTrue(
                                    creationRequest
                                            .getBuildingAddressList()
                                            .contains(result.getAddress()));
                        });

        // assert project id matches
        var createdProjectIds =
                response.getResultList().stream()
                        .map(Message.CreateBundleResponse.CreateBundleProjectResponse::getProjectId)
                        .map(StringValue::getValue)
                        .toList();
        createdProjectIds.forEach(
                projectId ->
                        assertTrue(
                                mockIntegration.stream()
                                        .anyMatch(
                                                integration ->
                                                        integration
                                                                .getProjectId()
                                                                .equals(projectId))));
        // assert project group match
        Mockito.verify(projectGroupManager, Mockito.times(1))
                .addProjectToGroup(
                        Mockito.anyString(),
                        Mockito.eq(BUNDLE_PROJECT_GROUP_TYPE),
                        Mockito.any(),
                        Mockito.eq(creationRequest.getCreateBy().getValue()));
        assertIterableEquals(createdProjectIds, projectGroupCaptor.getValue());
        // assert project creation request match bundle creation request
        projectCreationRequestCaptor
                .getValue()
                .forEach(
                        projectCreationRequest -> {
                            assertEquals(
                                    creationRequest.getInsuredBy().getValue(),
                                    projectCreationRequest.getContract().getInsuredBy().getId());
                            assertEquals(
                                    creationRequest.getProcessedBy().getValue(),
                                    projectCreationRequest.getContract().getProcessedBy().getId());
                            assertEquals(
                                    creationRequest.getServiceType().getNumber(),
                                    projectCreationRequest
                                            .getUnderwriting()
                                            .getServiceType()
                                            .getCode());
                            assertEquals(
                                    creationRequest.getPolicyNo().getValue(),
                                    projectCreationRequest.getPolicy().getPolicyNo());
                            assertEquals(
                                    DateTimes.toLocalDate(creationRequest.getPolicyEffectiveDate()),
                                    projectCreationRequest.getPolicy().getPolicyEffectiveDate());
                            assertEquals(
                                    creationRequest.getIsRenewal().getValue(),
                                    projectCreationRequest.getPolicy().isRenewal());
                            assertEquals(
                                    creationRequest.getPolicyType().getValue(),
                                    projectCreationRequest.getPolicy().getType());
                            assertEquals(
                                    creationRequest.getTypeOfProperty(),
                                    projectCreationRequest.getPolicy().getBuilding().getType());
                            assertEquals(
                                    creationRequest.getInspectionNo().getValue(),
                                    projectCreationRequest.getInspection().getInspectionNo());
                            assertEquals(
                                    creationRequest.getYearBuilt().getValue(),
                                    projectCreationRequest
                                            .getPolicy()
                                            .getBuilding()
                                            .getYearBuilt());
                            assertAddressExists(
                                    creationRequest.getBuildingAddressList(),
                                    projectCreationRequest.getPolicy().getAddress());
                        });
    }

    public void testFindByIdShouldSucceed() {
        var creationRequest = randomCreateBundleRequest();
        var insuredBy = creationRequest.getInsuredBy().getValue();
        var processedBy = creationRequest.getProcessedBy().getValue();
        var contract = randomContract(insuredBy, processedBy);
        var mainBundleAddress =
                creationRequest.getBuildingAddressList().stream()
                        .filter(address -> address.getIsMain().getValue())
                        .findFirst()
                        .get();
        var address = randomAddress(mainBundleAddress);
        var mockIntegration = new ArrayList<ExternalIntegration>();
        var mockProjectCreationResult = new HashMap<Integer, ApiStatus>();
        var mockProjectGroup = ProjectGroupMessage.newBuilder();
        ArgumentCaptor<List<ProjectCreationRequest>> projectCreationRequestCaptor =
                ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<Iterable<String>> projectGroupCaptor =
                ArgumentCaptor.forClass(Iterable.class);

        for (var idx = 0; idx < creationRequest.getNumberOfBuildings().getValue(); idx++) {
            mockProjectCreationResult.put(idx, ApiStatus.OK);
            mockIntegration.add(
                    ExternalIntegration.from(
                            com.bees360.project.Message.IntegrationMessage.newBuilder()
                                    .setIntegrationType("BATCH")
                                    .setSubReferenceNumber(String.valueOf(idx))
                                    .setProjectId(randomId())
                                    .build()));
        }

        Mockito.doReturn(address).when(addressProvider).normalize(Mockito.any(Address.class));
        Mockito.doReturn(contract)
                .when(contractRepository)
                .findByCompanyId(Mockito.eq(insuredBy), Mockito.eq(processedBy));
        Mockito.doReturn(mockProjectCreationResult)
                .when(batchProjectCreator)
                .createBatch(
                        Mockito.any(),
                        Mockito.eq(creationRequest.getCreateBy().getValue()),
                        projectCreationRequestCaptor.capture(),
                        Mockito.eq(false),
                        Mockito.eq("WEB"));
        Mockito.doReturn(mockIntegration)
                .when(externalIntegrationProvider)
                .findAllByReference(Mockito.eq("BATCH"), Mockito.any());
        Mockito.doNothing()
                .when(projectGroupManager)
                .addProjectToGroup(
                        Mockito.anyString(),
                        Mockito.eq(BUNDLE_PROJECT_GROUP_TYPE),
                        projectGroupCaptor.capture(),
                        Mockito.any());
        doMockCreatedReason();

        var response = bundleManager.createBundle(creationRequest);
        Mockito.doReturn(address).when(addressProvider).findById(Mockito.eq(address.getId()));
        Mockito.doReturn(contract).when(contractRepository).findById(Mockito.eq(contract.getId()));
        mockProjectGroup.setKey(response.getId().getValue()).setType(BUNDLE_PROJECT_GROUP_TYPE);
        Mockito.doReturn(ProjectGroup.from(mockProjectGroup.build()))
                .when(projectGroupManager)
                .findByGroupKey(
                        Mockito.eq(mockProjectGroup.getKey()),
                        Mockito.eq(mockProjectGroup.getType()));
        var mockProjects = new ArrayList<ProjectII>();
        mockProjectGroup
                .getProjectIdList()
                .forEach(
                        projectId -> {
                            ProjectII mockProject =
                                    ProjectII.from(
                                            com.bees360.project.Message.ProjectMessage.newBuilder()
                                                    .setId(projectId)
                                                    .build());
                            mockProjects.add(mockProject);
                        });
        Mockito.doReturn(mockProjects)
                .when(projectIIManager)
                .findAllById(mockProjectGroup.getProjectIdList());
        var id = response.getId();
        var bundle = bundleManager.findById(id.getValue());
        assertNotNull(bundle);
        assertEquals(String.valueOf(Long.parseLong(address.getId())), bundle.getAddress().getId());
        assertEquals(
                String.valueOf(Long.parseLong(contract.getId())), bundle.getContract().getId());
        assertEquals(
                String.valueOf(bundle.getMember(0).getUser().getId()),
                creationRequest.getCreateBy().getValue());
        var nonBundleFound = bundleManager.findById("ID_NOT_EXIST");
        assertNull(nonBundleFound);
    }

    public void testFindByEmptyOrNullShouldThrow() {
        assertThrows(
                IllegalArgumentException.class,
                () -> bundleManager.findById(null),
                "Bundle id cannot be null");
        assertThrows(
                IllegalArgumentException.class,
                () -> bundleManager.findById(""),
                "Bundle id cannot be empty");
    }

    public void testUpdateServiceType() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        assertTrue(
                bundleManager.updateBundle(
                        Message.UpdateBundleRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setServiceType(
                                        Message.UpdateBundleRequest.UpdateServiceTypeRequest
                                                .newBuilder()
                                                .setValue(ServiceType.FOUR_POINT_UNDERWRITING)
                                                .setReason(StringValue.of("unit test"))
                                                .build())
                                .build(),
                        "11000"));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(originalBundle.getServiceType(), updatedBundle.getServiceType());
        assertEquals(ServiceType.FOUR_POINT_UNDERWRITING, updatedBundle.getServiceType());
    }

    public void testUpdateNumberOfBuildings() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        int newNumber = originalBundle.getMetadata().getNumberOfBuildings().getValue() + 1;
        var requestBy = "11000";
        assertTrue(
                bundleManager.updateBundle(
                        Message.UpdateBundleRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setNumberOfBuildings(Int32Value.of(newNumber))
                                .build(),
                        requestBy));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(
                originalBundle.getMetadata().getNumberOfBuildings(),
                updatedBundle.getMetadata().getNumberOfBuildings());
        assertEquals(newNumber, updatedBundle.getMetadata().getNumberOfBuildings().getValue());
    }

    public void testUpdatePolicyNumber() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        String newPolicyNumber = "NEW-" + originalBundle.getPolicyNo();
        var requestBy = "11000";
        assertTrue(
                bundleManager.updateBundlePolicy(
                        Message.UpdateBundlePolicyRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setPolicyNo(StringValue.of(newPolicyNumber))
                                .build(),
                        requestBy));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(originalBundle.getPolicyNo(), updatedBundle.getPolicyNo());
        assertEquals(newPolicyNumber, updatedBundle.getPolicyNo().getValue());
    }

    public void testUpdateYearBuilt() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        int newYearBuilt = originalBundle.getMetadata().getYearBuilt().getValue() + 1;
        var requestBy = "11000";
        assertTrue(
                bundleManager.updateBundlePolicy(
                        Message.UpdateBundlePolicyRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setYearBuilt(Int32Value.of(newYearBuilt))
                                .build(),
                        requestBy));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(
                originalBundle.getMetadata().getYearBuilt(),
                updatedBundle.getMetadata().getYearBuilt());
        assertEquals(newYearBuilt, updatedBundle.getMetadata().getYearBuilt().getValue());
    }

    public void testUpdatePolicyEffectiveDate() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        LocalDate newDate =
                DateTimes.toLocalDate(
                                originalBundle.getMetadata().getPolicy().getPolicyEffectiveDate())
                        .plusDays(10);
        var requestBy = "11000";
        assertTrue(
                bundleManager.updateBundlePolicy(
                        Message.UpdateBundlePolicyRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setEffectiveDate(DateTimes.toProtoDate(newDate))
                                .build(),
                        requestBy));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(
                originalBundle.getMetadata().getPolicy().getPolicyEffectiveDate(),
                updatedBundle.getMetadata().getPolicy().getPolicyEffectiveDate());
        assertEquals(
                newDate,
                DateTimes.toLocalDate(
                        updatedBundle.getMetadata().getPolicy().getPolicyEffectiveDate()));
    }

    public void testUpdatePolicyRenewal() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        var newIsRenewal = !originalBundle.getMetadata().getPolicy().getIsRenewal().getValue();
        var requestBy = "11000";
        assertTrue(
                bundleManager.updateBundlePolicy(
                        Message.UpdateBundlePolicyRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setIsRenewal(BoolValue.of(newIsRenewal))
                                .build(),
                        requestBy));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(
                originalBundle.getMetadata().getPolicy().getIsRenewal(),
                updatedBundle.getMetadata().getPolicy().getIsRenewal());
        assertEquals(
                newIsRenewal, updatedBundle.getMetadata().getPolicy().getIsRenewal().getValue());
    }

    public void testUpdatePolicyTypeAndPropertyType() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        String newPolicyType = "Commercial Property Insurance";
        var newPropertyType = com.bees360.building.Message.BuildingType.GENERAL_COMMERCIAL;
        var requestBy = "11000";
        assertTrue(
                bundleManager.updateBundlePolicy(
                        Message.UpdateBundlePolicyRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setPolicyAndPropertyType(
                                        Message.UpdateBundlePolicyRequest
                                                .UpdatePolicyTypeAndPropertyTypeRequest.newBuilder()
                                                .setPolicyType(newPolicyType)
                                                .setPropertyType(
                                                        Int32Value.of(newPropertyType.getNumber()))
                                                .build())
                                .build(),
                        requestBy));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(
                originalBundle.getMetadata().getPolicy().getType(),
                updatedBundle.getMetadata().getPolicy().getType());
        assertEquals(newPolicyType, updatedBundle.getMetadata().getPolicy().getType().getValue());
    }

    public void testUpdateInspectionNumber() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        String newInspectionNumber = "NEW-" + originalBundle.getInspectionNo().getValue();
        var requestBy = "11000";
        assertTrue(
                bundleManager.updateBundle(
                        Message.UpdateBundleRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setInspectionNo(StringValue.of(newInspectionNumber))
                                .build(),
                        requestBy));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(originalBundle.getInspectionNo(), updatedBundle.getInspectionNo());
        assertEquals(newInspectionNumber, updatedBundle.getInspectionNo().getValue());
    }

    public void testUpdateAddress() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        var newBundleAddress = randomAddress(true);
        var newAddress = randomAddress(newBundleAddress);
        var newAddressId = newAddress.getId();
        var requestBy = "11000";
        Mockito.doReturn(newAddress).when(addressProvider).normalize(Mockito.any(Address.class));
        Mockito.doReturn(newAddress).when(addressProvider).findById(newAddressId);

        assertTrue(
                bundleManager.updateBundlePolicy(
                        Message.UpdateBundlePolicyRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setAddress(newBundleAddress)
                                .build(),
                        requestBy));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(originalBundle.getAddress(), updatedBundle.getAddress());
        assertEquals(newAddressId, updatedBundle.getAddress().getId());
    }

    public void testUpdateContact() {
        var bundleId = createRandomBundle().getId().getValue();
        var originalBundle = bundleManager.findById(bundleId);
        var originalContact = originalBundle.getContactList().get(0);
        var newContact =
                originalContact.toBuilder()
                        .setEmail(StringValue.of(secureRandomStringUtils.nextAlphanumeric(12)))
                        .setPhone(StringValue.of(secureRandomStringUtils.nextNumeric(10)))
                        .build();
        var requestBy = "11000";
        assertTrue(
                bundleManager.saveBundleContact(
                        Message.UpdateBundleContactRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setContact(newContact)
                                .build(),
                        requestBy));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(
                originalBundle.getContactList().get(0).getEmail(),
                updatedBundle.getContactList().get(0).getEmail());
        var updatedContact =
                updatedBundle.getContactList().stream()
                        .filter(contact -> contact.getFullName().equals(newContact.getFullName()))
                        .findFirst()
                        .orElseThrow(
                                () ->
                                        new IllegalStateException(
                                                "Updated contact not found in bundle"));
        assertEquals(newContact.getEmail().getValue(), updatedContact.getEmail().getValue());
        assertEquals(newContact.getPhone().getValue(), updatedContact.getPhone().getValue());
        assertEquals(newContact.getRole().getValue(), updatedContact.getRole().getValue());
        assertEquals(
                newContact.getIsPrimary().getValue(), updatedContact.getIsPrimary().getValue());
    }

    public void testCreateBundleProject() {
        var bundleId = createRandomBundle().getId().getValue();
        var buildingAddresses = new ArrayList<Message.BundleAddressMessage>();
        var mockProjectCreationResult = new HashMap<Integer, ApiStatus>();
        var mockIntegration = new ArrayList<ExternalIntegration>();
        ArgumentCaptor<List<ProjectCreationRequest>> projectCreationRequestCaptor =
                ArgumentCaptor.forClass(List.class);

        for (var idx = 0; idx < 3; idx++) {
            buildingAddresses.add(randomAddress(idx == 0));
            mockProjectCreationResult.put(idx, ApiStatus.OK);
            mockIntegration.add(
                    ExternalIntegration.from(
                            com.bees360.project.Message.IntegrationMessage.newBuilder()
                                    .setIntegrationType("BATCH")
                                    .setSubReferenceNumber(String.valueOf(idx))
                                    .setProjectId(randomId())
                                    .build()));
        }

        Mockito.doReturn(mockProjectCreationResult)
                .when(batchProjectCreator)
                .createBatch(
                        Mockito.any(),
                        Mockito.any(String.class),
                        projectCreationRequestCaptor.capture(),
                        Mockito.eq(false),
                        Mockito.eq("WEB"));
        Mockito.doReturn(mockIntegration)
                .when(externalIntegrationProvider)
                .findAllByReference(Mockito.eq("BATCH"), Mockito.any());

        Mockito.clearInvocations(projectGroupManager);
        var creationChannel = "WEB";
        var requestBy = "11000";
        var response =
                bundleManager.createBundleProject(
                        bundleId, buildingAddresses, creationChannel, requestBy);
        var createdProjectMap =
                mockIntegration.stream()
                        .collect(
                                Collectors.toMap(
                                        ExternalIntegration::getSubReferenceNumber,
                                        ExternalIntegration::getProjectId));
        assertEquals(bundleId, response.getId().getValue());
        response.getResultList()
                .forEach(
                        result -> {
                            assertTrue(
                                    createdProjectMap.containsValue(
                                            result.getProjectId().getValue()));
                            assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        Mockito.verify(projectGroupManager, Mockito.times(1))
                .addProjectToGroup(
                        Mockito.eq(bundleId),
                        Mockito.eq(BUNDLE_PROJECT_GROUP_TYPE),
                        Mockito.any(),
                        Mockito.eq(requestBy));
    }

    public void testAddBundleProject() {
        var bundleId = createRandomBundle().getId().getValue();
        var bundle = bundleManager.findById(bundleId);
        var projectIds = new ArrayList<String>();
        for (var idx = 0; idx < 3; idx++) {
            projectIds.add(randomId());
        }
        var requestBy = "11000";

        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(
                                    ProjectII.from(
                                            com.bees360.project.Message.ProjectMessage.newBuilder()
                                                    .setId(projectId)
                                                    .setContract(
                                                            com.bees360.contract.Message
                                                                    .ContractMessage.newBuilder()
                                                                    .setId(
                                                                            bundle.getContract()
                                                                                    .getId())
                                                                    .build())
                                                    .build()))
                            .when(projectIIManager)
                            .findById(projectId);
                });

        assertTrue(bundleManager.addBundleProject(bundleId, projectIds, requestBy));
        Mockito.verify(projectGroupManager, Mockito.times(1))
                .addProjectToGroup(bundleId, BUNDLE_PROJECT_GROUP_TYPE, projectIds, requestBy);
    }

    public void testAddBundleProjectWithMismatchContract() {
        var bundleId = createRandomBundle().getId().getValue();
        var bundle = bundleManager.findById(bundleId);
        var projectIds = new ArrayList<String>();
        for (var idx = 0; idx < 3; idx++) {
            projectIds.add(randomId());
        }
        var requestBy = "11000";

        var mockProjects = new ArrayList<ProjectII>();
        projectIds.forEach(
                projectId -> {
                    ProjectII mockProject =
                            ProjectII.from(
                                    com.bees360.project.Message.ProjectMessage.newBuilder()
                                            .setId(projectId)
                                            .setContract(
                                                    com.bees360.contract.Message.ContractMessage
                                                            .newBuilder()
                                                            .setId(
                                                                    bundle.getContract().getId()
                                                                            + "1")
                                                            .build())
                                            .build());
                    mockProjects.add(mockProject);
                });
        Mockito.doReturn(mockProjects).when(projectIIManager).findAllById(projectIds);

        assertThrows(
                IllegalArgumentException.class,
                () -> bundleManager.addBundleProject(bundleId, projectIds, requestBy));
        Mockito.verify(projectGroupManager, Mockito.times(0))
                .addProjectToGroup(bundleId, BUNDLE_PROJECT_GROUP_TYPE, projectIds, requestBy);
    }

    public void testRemoveBundleProject() {
        var bundleId = createRandomBundle().getId().getValue();
        var projectIds = new ArrayList<String>();
        for (var idx = 0; idx < 3; idx++) {
            projectIds.add(randomId());
        }
        var mockBundleProjectGroup =
                ProjectGroup.from(
                        ProjectGroupMessage.newBuilder()
                                .setKey(bundleId)
                                .setType(BUNDLE_PROJECT_GROUP_TYPE)
                                .addAllProjectId(projectIds)
                                .addProjectId(randomId())
                                .build());
        Mockito.doReturn(mockBundleProjectGroup)
                .when(projectGroupManager)
                .findByGroupKey(Mockito.eq(bundleId), Mockito.eq(BUNDLE_PROJECT_GROUP_TYPE));

        var requestBy = "11000";
        assertTrue(bundleManager.removeBundleProject(bundleId, projectIds, requestBy));
        Mockito.verify(projectGroupManager, Mockito.times(1))
                .deleteProjectInGroup(bundleId, BUNDLE_PROJECT_GROUP_TYPE, projectIds, requestBy);
    }

    public void testChangeBundleState() {
        var bundleId = createRandomBundle().getId().getValue();
        bundleStateManager.changeState(bundleId, Message.BundleState.CLOSE, "COMPLETED", "10000");
        var bundle = bundleManager.findById(bundleId);
        assertEquals(Message.BundleState.CLOSE, bundle.getState());
        // only get display text for the last state change reason
        assertEquals("Already Completed", bundle.getLastStateChangeReason().getDisplayText());
    }

    public void testRemoveAllBundleProjectShouldFail() {
        var bundleId = createRandomBundle().getId().getValue();
        var projectIds = new ArrayList<String>();
        for (var idx = 0; idx < 3; idx++) {
            projectIds.add(randomId());
        }
        var mockBundleProjectGroup =
                ProjectGroup.from(
                        ProjectGroupMessage.newBuilder()
                                .setKey(bundleId)
                                .setType(BUNDLE_PROJECT_GROUP_TYPE)
                                .addAllProjectId(projectIds)
                                .build());
        Mockito.doReturn(mockBundleProjectGroup)
                .when(projectGroupManager)
                .findByGroupKey(Mockito.eq(bundleId), Mockito.eq(BUNDLE_PROJECT_GROUP_TYPE));

        var requestBy = "11000";
        assertThrows(
                IllegalArgumentException.class,
                () -> bundleManager.removeBundleProject(bundleId, projectIds, requestBy));
        Mockito.verify(projectGroupManager, Mockito.times(0))
                .deleteProjectInGroup(bundleId, BUNDLE_PROJECT_GROUP_TYPE, projectIds, requestBy);
    }

    private void assertAddressExists(
            List<Message.BundleAddressMessage> bundleAddresses, Address projectAddresses) {
        assertTrue(
                bundleAddresses.stream()
                        .anyMatch(
                                address ->
                                        projectAddresses
                                                        .getStreetAddress()
                                                        .equals(
                                                                address.getStreetAddress()
                                                                        .getValue())
                                                && projectAddresses
                                                        .getCity()
                                                        .equals(address.getCity().getValue())
                                                && projectAddresses
                                                        .getState()
                                                        .equals(address.getState().getValue())
                                                && projectAddresses
                                                        .getCountry()
                                                        .equals(address.getCountry().getValue())
                                                && projectAddresses
                                                        .getZip()
                                                        .equals(address.getZip().getValue())
                                                && projectAddresses
                                                        .getLat()
                                                        .equals(address.getLat().getValue())
                                                && projectAddresses
                                                        .getLng()
                                                        .equals(address.getLng().getValue())));
    }

    static Address randomAddress(Message.BundleAddressMessage bundleAddress) {
        var fullAddress =
                bundleAddress.getStreetAddress()
                        + ", "
                        + bundleAddress.getCity()
                        + ", "
                        + bundleAddress.getState()
                        + " "
                        + bundleAddress.getZip();
        return Address.from(
                com.bees360.address.Message.AddressMessage.newBuilder()
                        .setId(randomId())
                        .setAddress(fullAddress)
                        .setStreetAddress(bundleAddress.getStreetAddress().getValue())
                        .setCity(bundleAddress.getCity().getValue())
                        .setState(bundleAddress.getState().getValue())
                        .setCountry(bundleAddress.getCountry().getValue())
                        .setZip(bundleAddress.getZip().getValue())
                        .setLat(bundleAddress.getLat().getValue())
                        .setLng(bundleAddress.getLng().getValue())
                        .setIsGpsApproximate(true)
                        .build());
    }

    static Contract randomContract(String insuredBy, String processedBy) {
        return Contract.ContractBuilder.newBuilder()
                .setId(randomId())
                .setInsuredBy(
                        Customer.of(
                                com.bees360.customer.Message.CustomerMessage.newBuilder()
                                        .setId(insuredBy)
                                        .setName(secureRandomStringUtils.nextAlphabetic(10))
                                        .addRole(
                                                com.bees360.customer.Message.CustomerMessage
                                                        .CustomerRole.INSURANCE_CARRIER)
                                        .build()))
                .setProcessedBy(
                        Customer.of(
                                com.bees360.customer.Message.CustomerMessage.newBuilder()
                                        .setId(processedBy)
                                        .setName(secureRandomStringUtils.nextAlphabetic(10))
                                        .setKey(secureRandomStringUtils.nextAlphabetic(10))
                                        .addRole(
                                                com.bees360.customer.Message.CustomerMessage
                                                        .CustomerRole.REPAIR_SERVICE_PROVIDER)
                                        .build()))
                .build();
    }

    static Message.BundleAddressMessage randomAddress(boolean isMain) {
        return Message.BundleAddressMessage.newBuilder()
                .setStreetAddress(StringValue.of(secureRandomStringUtils.nextAlphabetic(12)))
                .setCity(StringValue.of(secureRandomStringUtils.nextAlphabetic(4)))
                .setState(StringValue.of(secureRandomStringUtils.nextAlphabetic(2)))
                .setCountry(StringValue.of("US"))
                .setZip(StringValue.of(secureRandomStringUtils.nextAlphanumeric(5)))
                .setLat(DoubleValue.of(RandomUtils.secure().randomDouble()))
                .setLng(DoubleValue.of(RandomUtils.secure().randomDouble()))
                .setIsMain(BoolValue.of(isMain))
                .build();
    }

    static Message.BundleContactMessage randomBundleContact(String role, boolean isPrimary) {
        return Message.BundleContactMessage.newBuilder()
                .setFullName(StringValue.of(secureRandomStringUtils.nextAlphabetic(12)))
                .setEmail(StringValue.of(secureRandomStringUtils.nextAlphanumeric(12)))
                .setPhone(StringValue.of(secureRandomStringUtils.nextAlphanumeric(12)))
                .setRole(StringValue.of(role))
                .setIsPrimary(BoolValue.of(isPrimary))
                .build();
    }

    static Message.CreateBundleRequest randomCreateBundleRequest() {
        return Message.CreateBundleRequest.newBuilder()
                .setInsuredBy(StringValue.of(randomId()))
                .setProcessedBy(StringValue.of(randomId()))
                .setServiceType(ServiceType.COMMERCIAL_UNDERWRITING)
                .setPolicyNo(StringValue.of(secureRandomStringUtils.nextAlphanumeric(10)))
                .setPolicyEffectiveDate(DateTimes.toProtoDate(LocalDate.now()))
                .setIsRenewal(BoolValue.of(false))
                .setPolicyType(StringValue.of("Homeowners Insurance"))
                .setTypeOfProperty(
                        com.bees360.building.Message.BuildingType.RESIDENTIAL_SINGLE_FAMILY)
                .setInspectionNo(StringValue.of(secureRandomStringUtils.nextAlphanumeric(10)))
                .setYearBuilt(
                        Int32Value.of(Integer.parseInt(secureRandomStringUtils.nextNumeric(4))))
                .addAllContact(
                        List.of(
                                randomBundleContact("Insured", true),
                                randomBundleContact("Agent", false)))
                .setNumberOfBuildings(Int32Value.of(3))
                .setNote(StringValue.of(secureRandomStringUtils.nextAlphanumeric(64)))
                .addAllBuildingAddress(
                        List.of(randomAddress(true), randomAddress(false), randomAddress(false)))
                .setCreateBy(StringValue.of(secureRandomStringUtils.nextNumeric(4)))
                .setCreationChannel(StringValue.of("WEB"))
                .setAttachment(
                        Message.BundleAttachmentMessage.newBuilder()
                                .setDescription(StringValue.of("attachment description"))
                                .addAllAttachment(
                                        List.of(randomBundleAttachment(), randomBundleAttachment()))
                                .build())
                .build();
    }

    static Message.BundleAttachmentMessage.Attachment randomBundleAttachment() {
        return Message.BundleAttachmentMessage.Attachment.newBuilder()
                .setFilename(StringValue.of(secureRandomStringUtils.nextAlphanumeric(10)))
                .setUrl(StringValue.of("test://test"))
                .build();
    }

    static String randomId() {
        return "1" + secureRandomStringUtils.nextNumeric(5);
    }

    protected void doMockCreatedReason() {
        var bundleCreatedReason = mock(ProjectStateChangeReason.class);
        when(bundleCreatedReason.getId()).thenReturn(secureRandomStringUtils.nextNumeric(2));
        when(bundleCreatedReason.getDisplayText()).thenReturn("Bundle Created");
        when(bundleCreatedReason.getKey()).thenReturn("BUNDLE CREATED");
        when(bundleCreatedReason.getType())
                .thenReturn(
                        com.bees360.project.statechangereason.Message
                                .ProjectStateChangeReasonMessage.ProjectStateChangeReasonType.OPEN);
        Mockito.doReturn(List.of(bundleCreatedReason))
                .when(projectStateChangeReasonProvider)
                .findByQuery(List.of(), List.of("BUNDLE CREATED"), List.of());
    }
}
