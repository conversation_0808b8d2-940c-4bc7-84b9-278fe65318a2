package com.bees360.bundle;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.api.Entity;
import com.bees360.project.tag.Message;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagRepository;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.stream.IntStreams;
import org.mockito.Mockito;

import java.util.List;

public class AbstractBundleTagManagerTest {
    protected static final RandomStringUtils secureRandomStringUtils = RandomStringUtils.secure();
    protected final BundleTagManager bundleTagManager;
    protected final ProjectTagRepository projectTagRepository;

    public AbstractBundleTagManagerTest(
            BundleTagManager bundleTagManager, ProjectTagRepository projectTagRepository) {
        this.bundleTagManager = bundleTagManager;
        this.projectTagRepository = projectTagRepository;
    }

    public void testUpdateBundleTag() {
        var bundleId = randomId();
        var companyId = randomId();
        var tagType = Message.ProjectTagType.UNDERWRITING;
        var tags =
                IntStreams.range(3).mapToObj(id -> randomProjectTag(companyId, tagType)).toList();
        var tagIds = tags.stream().map(Entity::getId).toList();
        var updatedBy = "10000";
        var requestVia = "WEB";

        Mockito.doReturn(List.of()).when(projectTagRepository).findAllById(Mockito.any());
        // add tags
        var affectedCount =
                bundleTagManager.updateBundleTag(bundleId, tagIds, tagType, updatedBy, requestVia);
        assertEquals(3, affectedCount);
        // remove part of tags
        Mockito.doReturn(tags).when(projectTagRepository).findAllById(Mockito.any());
        affectedCount =
                bundleTagManager.updateBundleTag(
                        bundleId, tagIds.subList(0, 2), tagType, updatedBy, requestVia);
        assertEquals(1, affectedCount);
        // recover tags
        Mockito.doReturn(tags.subList(0, 2)).when(projectTagRepository).findAllById(Mockito.any());
        affectedCount =
                bundleTagManager.updateBundleTag(bundleId, tagIds, tagType, updatedBy, requestVia);
        assertEquals(1, affectedCount);
        // remove all tags
        Mockito.doReturn(tags).when(projectTagRepository).findAllById(Mockito.any());
        affectedCount =
                bundleTagManager.updateBundleTag(
                        bundleId, List.of(), tagType, updatedBy, requestVia);
        assertEquals(3, affectedCount);
    }

    public void testFindBundleTag() {
        var bundleId = randomId();
        var tagType = Message.ProjectTagType.UNDERWRITING;
        var companyId = randomId();
        var tags =
                IntStreams.range(3).mapToObj(id -> randomProjectTag(companyId, tagType)).toList();
        var tagMessages = tags.stream().map(ProjectTag::toMessage).toList();
        var tagIds = tags.stream().map(Entity::getId).toList();
        var updatedBy = "10000";

        Mockito.doReturn(List.of()).when(projectTagRepository).findAllById(Mockito.any());
        // add tags
        var affectedCount =
                bundleTagManager.updateBundleTag(bundleId, tagIds, tagType, updatedBy, "WEB");
        assertEquals(3, affectedCount);

        Mockito.doReturn(tags).when(projectTagRepository).findAllById(Mockito.any());
        var bundleUWTags = bundleTagManager.findByBundleId(bundleId, companyId, tagType);
        assertEquals(bundleUWTags.stream().map(ProjectTag::toMessage).toList(), tagMessages);
        var bundleUnknownTags = bundleTagManager.findByBundleId(bundleId, companyId, null);
        assertTrue(CollectionUtils.isEmpty(bundleUnknownTags));
    }

    static ProjectTag randomProjectTag(String companyId, Message.ProjectTagType type) {
        return ProjectTag.of(
                Message.ProjectTagMessage.newBuilder()
                        .setId(randomId())
                        .setCompanyId(companyId)
                        .setType(type)
                        .build());
    }

    static String randomId() {
        return "1" + secureRandomStringUtils.nextNumeric(8);
    }
}
