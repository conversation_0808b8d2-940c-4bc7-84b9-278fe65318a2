package com.bees360.bundle.report;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.report.Message;
import com.bees360.report.ReportProcessStatusProvider;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

@ExtendWith(MockitoExtension.class)
class DefaultBundleReportProcessStatusProviderTest {

    private ReportProcessStatusProvider reportProcessStatusProvider;

    private DefaultBundleReportProcessStatusProvider bundleReportProcessStatusProvider;

    @BeforeEach
    void setUp() {
        reportProcessStatusProvider = mock(ReportProcessStatusProvider.class);
        bundleReportProcessStatusProvider =
                new DefaultBundleReportProcessStatusProvider(reportProcessStatusProvider);
    }

    @Test
    void getReportProcessStatusShouldThrowExceptionWhenBundleIdIsBlank() {
        // Given
        String bundleId = "";
        String reportType = "REPORT_TYPE";
        Message.ReportProcessStatus.Type type = Message.ReportProcessStatus.Type.MERGE;

        // When / Then
        assertThatThrownBy(
                        () ->
                                bundleReportProcessStatusProvider.getReportProcessStatus(
                                        bundleId, reportType, type))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage(
                        "Fail to get bundle report process status: bundle id can not be blank.");
    }

    @Test
    void getReportProcessStatusShouldThrowExceptionWhenTypeIsNull() {
        // Given
        String bundleId = "123";
        String reportType = "REPORT_TYPE";
        Message.ReportProcessStatus.Type type = null;

        // When / Then
        assertThatThrownBy(
                        () ->
                                bundleReportProcessStatusProvider.getReportProcessStatus(
                                        bundleId, reportType, type))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to get bundle report process status: process type is illegal.");
    }

    @Test
    void getReportProcessStatusShouldThrowExceptionWhenTypeIsUnknown() {
        // Given
        String bundleId = "123";
        String reportType = "REPORT_TYPE";
        Message.ReportProcessStatus.Type type =
                Message.ReportProcessStatus.Type.UNKNOWN_PROCESS_TYPE;

        // When / Then
        assertThatThrownBy(
                        () ->
                                bundleReportProcessStatusProvider.getReportProcessStatus(
                                        bundleId, reportType, type))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to get bundle report process status: process type is illegal.");
    }

    @Test
    void getReportProcessStatusShouldThrowExceptionWhenTypeIsUnrecognized() {
        // Given
        String bundleId = "123";
        String reportType = "REPORT_TYPE";
        Message.ReportProcessStatus.Type type = Message.ReportProcessStatus.Type.UNRECOGNIZED;

        // When / Then
        assertThatThrownBy(
                        () ->
                                bundleReportProcessStatusProvider.getReportProcessStatus(
                                        bundleId, reportType, type))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to get bundle report process status: process type is illegal.");
    }

    @Test
    void getReportProcessStatusShouldCallDelegateWithCorrectParameters() {
        // Given
        String bundleId = "123";
        String reportType = "REPORT_TYPE";
        Message.ReportProcessStatus.Type type = Message.ReportProcessStatus.Type.MERGE;

        List<Message.ReportProcessStatus> mockResult =
                List.of(mock(Message.ReportProcessStatus.class));
        when(reportProcessStatusProvider.getReportProcessStatus(
                        bundleId,
                        DefaultBundleReportProcessStatusProvider.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        type))
                .thenReturn(mockResult);

        // When
        List<Message.ReportProcessStatus> result =
                bundleReportProcessStatusProvider.getReportProcessStatus(
                        bundleId, reportType, type);

        // Then
        assertThat(result).isSameAs(mockResult);
        verify(reportProcessStatusProvider)
                .getReportProcessStatus(
                        bundleId,
                        DefaultBundleReportProcessStatusProvider.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        type);
    }

    @Test
    void getReportProcessStatusShouldWorkWithValidProcessType() {
        // Given
        String bundleId = "123";
        String reportType = "REPORT_TYPE";
        Message.ReportProcessStatus.Type type =
                Message.ReportProcessStatus.Type.MERGE; // Using existing type

        List<Message.ReportProcessStatus> mockResult =
                List.of(mock(Message.ReportProcessStatus.class));
        when(reportProcessStatusProvider.getReportProcessStatus(
                        bundleId,
                        DefaultBundleReportProcessStatusProvider.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        type))
                .thenReturn(mockResult);

        // When
        List<Message.ReportProcessStatus> result =
                bundleReportProcessStatusProvider.getReportProcessStatus(
                        bundleId, reportType, type);

        // Then
        assertThat(result).isSameAs(mockResult);
        verify(reportProcessStatusProvider)
                .getReportProcessStatus(
                        bundleId,
                        DefaultBundleReportProcessStatusProvider.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        type);
    }
}
