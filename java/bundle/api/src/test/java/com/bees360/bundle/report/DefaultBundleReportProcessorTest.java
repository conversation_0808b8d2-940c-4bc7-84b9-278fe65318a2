package com.bees360.bundle.report;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportProcessor;
import com.bees360.report.ReportResource;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

@ExtendWith(MockitoExtension.class)
class DefaultBundleReportProcessorTest {

    private ReportProcessor reportProcessor;

    private ReportManager reportManager;

    private DefaultBundleReportProcessor bundleReportProcessor;

    @BeforeEach
    void setUp() {
        reportProcessor = mock(ReportProcessor.class);
        reportManager = mock(ReportManager.class);
        bundleReportProcessor = new DefaultBundleReportProcessor(reportProcessor, reportManager);
    }

    @Test
    void mergeReportShouldThrowExceptionWhenBundleIdIsBlank() {
        // Given
        String bundleId = "";
        String reportTypeCode = "TYPE_CODE";
        List<String> reportIds = List.of("report1", "report2");
        String mergedBy = "user123";

        // When / Then
        assertThatThrownBy(
                        () ->
                                bundleReportProcessor.mergeReport(
                                        bundleId, reportTypeCode, reportIds, mergedBy))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to merge bundle report: bundle id cannot be blank.");
    }

    @Test
    void mergeReportShouldThrowExceptionWhenReportTypeCodeIsBlank() {
        // Given
        String bundleId = "123";
        String reportTypeCode = "";
        List<String> reportIds = List.of("report1", "report2");
        String mergedBy = "user123";

        // When / Then
        assertThatThrownBy(
                        () ->
                                bundleReportProcessor.mergeReport(
                                        bundleId, reportTypeCode, reportIds, mergedBy))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to merge bundle report: report type cannot be blank.");
    }

    @Test
    void mergeReportShouldThrowExceptionWhenReportIdsIsNull() {
        // Given
        String bundleId = "123";
        String reportTypeCode = "TYPE_CODE";
        Iterable<String> reportIds = null;
        String mergedBy = "user123";

        // When / Then
        assertThatThrownBy(
                        () ->
                                bundleReportProcessor.mergeReport(
                                        bundleId, reportTypeCode, reportIds, mergedBy))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to merge bundle report: report id cannot be empty.");
    }

    @Test
    void mergeReportShouldThrowExceptionWhenReportIdsIsEmpty() {
        // Given
        String bundleId = "123";
        String reportTypeCode = "TYPE_CODE";
        List<String> reportIds = List.of();
        String mergedBy = "user123";

        // When / Then
        assertThatThrownBy(
                        () ->
                                bundleReportProcessor.mergeReport(
                                        bundleId, reportTypeCode, reportIds, mergedBy))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to merge bundle report: report id cannot be empty.");
    }

    @Test
    void mergeReportShouldThrowExceptionWhenMergedByIsBlank() {
        // Given
        String bundleId = "123";
        String reportTypeCode = "TYPE_CODE";
        List<String> reportIds = List.of("report1", "report2");
        String mergedBy = "";

        // When / Then
        assertThatThrownBy(
                        () ->
                                bundleReportProcessor.mergeReport(
                                        bundleId, reportTypeCode, reportIds, mergedBy))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to merge report: mergedBy cannot be blank.");
    }

    @Test
    void mergeReportShouldCallReportProcessorWithCorrectParameters() {
        // Given
        String bundleId = "123";
        String reportTypeCode = "TYPE_CODE";
        List<String> reportIds = List.of("report1", "report2");
        String mergedBy = "user123";

        // Mock reports
        Report report1 = mock(Report.class);
        Report report2 = mock(Report.class);
        List<Report> reports = List.of(report1, report2);
        doReturn(reports).when(reportManager).findAllById(any());

        // Mock resources for report1
        ReportResource resource1Origin = mock(ReportResource.class);
        when(resource1Origin.getType()).thenReturn(Message.ReportMessage.Resource.Type.ORIGIN);
        when(resource1Origin.getId()).thenReturn("resource1-origin-id");

        doReturn(List.of(resource1Origin)).when(report1).getResources();

        // Mock resources for report2
        ReportResource resource2Compressed = mock(ReportResource.class);
        when(resource2Compressed.getType())
                .thenReturn(Message.ReportMessage.Resource.Type.COMPRESSED);
        when(resource2Compressed.getId()).thenReturn("resource2-compressed-id");

        doReturn(List.of(resource2Compressed)).when(report2).getResources();

        when(reportProcessor.mergeReport(
                        eq(bundleId),
                        eq(DefaultBundleReportProcessor.REPORT_GROUP_TYPE_BUNDLE),
                        eq(reportTypeCode),
                        any(Iterable.class),
                        eq(mergedBy)))
                .thenReturn(Futures.immediateFuture(null));

        // When
        ListenableFuture<Void> result =
                bundleReportProcessor.mergeReport(bundleId, reportTypeCode, reportIds, mergedBy);

        // Then
        assertThat(result).isNotNull();
        verify(reportManager).findAllById(reportIds);
        verify(reportProcessor)
                .mergeReport(
                        eq(bundleId),
                        eq(DefaultBundleReportProcessor.REPORT_GROUP_TYPE_BUNDLE),
                        eq(reportTypeCode),
                        argThat(
                                (Iterable<String> ids) -> {
                                    List<String> idList = Iterables.toList(ids);
                                    return idList.contains("resource1-origin-id")
                                            && idList.contains("resource2-compressed-id");
                                }),
                        eq(mergedBy));
    }

    @Test
    void mergeReportShouldHandleFailure() {
        // Given
        String bundleId = "123";
        String reportTypeCode = "TYPE_CODE";
        List<String> reportIds = List.of("report1", "report2");
        String mergedBy = "user123";

        // Mock reports
        Report report1 = mock(Report.class);
        Report report2 = mock(Report.class);
        List<Report> reports = List.of(report1, report2);
        doReturn(reports).when(reportManager).findAllById(reportIds);

        // Mock resources for report1
        ReportResource resource1Origin = mock(ReportResource.class);
        when(resource1Origin.getType()).thenReturn(Message.ReportMessage.Resource.Type.ORIGIN);
        when(resource1Origin.getId()).thenReturn("resource1-origin-id");
        doReturn(List.of(resource1Origin)).when(report1).getResources();

        // Mock resources for report2
        ReportResource resource2Compressed = mock(ReportResource.class);
        when(resource2Compressed.getType())
                .thenReturn(Message.ReportMessage.Resource.Type.COMPRESSED);
        when(resource2Compressed.getId()).thenReturn("resource2-compressed-id");
        doReturn(List.of(resource2Compressed)).when(report2).getResources();

        SettableFuture<Void> future = SettableFuture.create();
        future.setException(new RuntimeException("Processing failed"));
        when(reportProcessor.mergeReport(
                        eq(bundleId),
                        eq(DefaultBundleReportProcessor.REPORT_GROUP_TYPE_BUNDLE),
                        eq(reportTypeCode),
                        any(Iterable.class),
                        eq(mergedBy)))
                .thenReturn(future);

        // When
        ListenableFuture<Void> result =
                bundleReportProcessor.mergeReport(bundleId, reportTypeCode, reportIds, mergedBy);

        // Then
        assertThat(result).isNotNull();
        // We can't easily test the exception propagation in the callback without complex mocking
        // but we've verified the main flow
        verify(reportManager).findAllById(reportIds);
    }
}
