package com.bees360.bundle.report;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportGroupManager;
import com.bees360.util.Iterables;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

@ExtendWith(MockitoExtension.class)
class DefaultBundleReportManagerTest {

    private ReportGroupManager reportGroupManager;

    private DefaultBundleReportManager bundleReportManager;

    @BeforeEach
    void setUp() {
        reportGroupManager = mock(ReportGroupManager.class);
        bundleReportManager = new DefaultBundleReportManager(reportGroupManager);
    }

    @Test
    void findShouldThrowExceptionWhenBundleIdIsBlank() {
        // Given
        String bundleId = "";
        String reportType = "GROUP_BUNDLE";
        Message.ReportMessage.Status status = Message.ReportMessage.Status.GENERATED;

        // When / Then
        assertThatThrownBy(() -> bundleReportManager.find(bundleId, reportType, status))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to get bundle report: bundle id can not be blank.");
    }

    @Test
    void findShouldCallReportGroupManagerWithCorrectParameters() {
        // Given
        String bundleId = randomId();
        String reportType = "GROUP_BUNDLE";
        Message.ReportMessage.Status status = Message.ReportMessage.Status.GENERATED;

        List<Report> mockReports = List.of(mock(Report.class));
        doReturn(mockReports)
                .when(reportGroupManager)
                .findReportInGroup(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);

        // When
        Iterable<? extends Report> result = bundleReportManager.find(bundleId, reportType, status);

        // Then
        assertTrue(CollectionUtils.isEqualCollection(Iterables.toList(result), mockReports));
        verify(reportGroupManager)
                .findReportInGroup(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);
    }

    @Test
    void findShouldWorkWithNullReportType() {
        // Given
        String bundleId = randomId();
        String reportType = null;
        Message.ReportMessage.Status status = Message.ReportMessage.Status.GENERATED;

        List<Report> mockReports = List.of(mock(Report.class));
        doReturn(mockReports)
                .when(reportGroupManager)
                .findReportInGroup(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);

        // When
        Iterable<? extends Report> result = bundleReportManager.find(bundleId, reportType, status);

        // Then
        assertTrue(CollectionUtils.isEqualCollection(Iterables.toList(result), mockReports));
        verify(reportGroupManager)
                .findReportInGroup(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);
    }

    @Test
    void findShouldWorkWithNullStatus() {
        // Given
        String bundleId = randomId();
        String reportType = "GROUP_BUNDLE";
        Message.ReportMessage.Status status = null;

        List<Report> mockReports = List.of(mock(Report.class));
        doReturn(mockReports)
                .when(reportGroupManager)
                .findReportInGroup(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);

        // When
        Iterable<? extends Report> result = bundleReportManager.find(bundleId, reportType, status);

        // Then
        assertTrue(CollectionUtils.isEqualCollection(Iterables.toList(result), mockReports));
        verify(reportGroupManager)
                .findReportInGroup(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);
    }

    @Test
    void findShouldWorkWithAllNullParameters() {
        // Given
        String bundleId = randomId();
        String reportType = null;
        Message.ReportMessage.Status status = null;

        List<Report> mockReports = List.of(mock(Report.class));
        doReturn(mockReports)
                .when(reportGroupManager)
                .findReportInGroup(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);

        // When
        Iterable<? extends Report> result = bundleReportManager.find(bundleId, reportType, status);

        // Then
        assertTrue(CollectionUtils.isEqualCollection(Iterables.toList(result), mockReports));
        verify(reportGroupManager)
                .findReportInGroup(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);
    }

    @Test
    void findInHistoryShouldThrowExceptionWhenBundleIdIsBlank() {
        // Given
        String bundleId = "";
        String reportType = "GROUP_BUNDLE";
        Message.ReportMessage.Status status = Message.ReportMessage.Status.GENERATED;

        // When / Then
        assertThatThrownBy(() -> bundleReportManager.findInHistory(bundleId, reportType, status))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Fail to get bundle report: bundle id can not be blank.");
    }

    @Test
    void findInHistoryShouldCallReportGroupManagerWithCorrectParameters() {
        // Given
        String bundleId = randomId();
        String reportType = "GROUP_BUNDLE";
        Message.ReportMessage.Status status = Message.ReportMessage.Status.GENERATED;

        List<Report> mockReports = List.of(mock(Report.class));
        doReturn(mockReports)
                .when(reportGroupManager)
                .findReportInHistory(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);

        // When
        Iterable<? extends Report> result =
                bundleReportManager.findInHistory(bundleId, reportType, status);

        // Then
        assertTrue(CollectionUtils.isEqualCollection(Iterables.toList(result), mockReports));
        verify(reportGroupManager)
                .findReportInHistory(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);
    }

    @Test
    void findInHistoryShouldWorkWithNullReportType() {
        // Given
        String bundleId = randomId();
        String reportType = null;
        Message.ReportMessage.Status status = Message.ReportMessage.Status.GENERATED;

        List<Report> mockReports = List.of(mock(Report.class));
        doReturn(mockReports)
                .when(reportGroupManager)
                .findReportInHistory(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);

        // When
        Iterable<? extends Report> result =
                bundleReportManager.findInHistory(bundleId, reportType, status);

        // Then
        assertTrue(CollectionUtils.isEqualCollection(Iterables.toList(result), mockReports));
        verify(reportGroupManager)
                .findReportInHistory(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);
    }

    @Test
    void findInHistoryShouldWorkWithNullStatus() {
        // Given
        String bundleId = randomId();
        String reportType = "GROUP_BUNDLE";
        Message.ReportMessage.Status status = null;

        List<Report> mockReports = List.of(mock(Report.class));
        doReturn(mockReports)
                .when(reportGroupManager)
                .findReportInHistory(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);

        // When
        Iterable<? extends Report> result =
                bundleReportManager.findInHistory(bundleId, reportType, status);

        // Then
        assertTrue(CollectionUtils.isEqualCollection(Iterables.toList(result), mockReports));
        verify(reportGroupManager)
                .findReportInHistory(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);
    }

    @Test
    void findInHistoryShouldWorkWithAllNullParameters() {
        // Given
        String bundleId = randomId();
        String reportType = null;
        Message.ReportMessage.Status status = null;

        List<Report> mockReports = List.of(mock(Report.class));
        doReturn(mockReports)
                .when(reportGroupManager)
                .findReportInHistory(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);

        // When
        Iterable<? extends Report> result =
                bundleReportManager.findInHistory(bundleId, reportType, status);

        // Then
        assertTrue(CollectionUtils.isEqualCollection(Iterables.toList(result), mockReports));
        verify(reportGroupManager)
                .findReportInHistory(
                        bundleId,
                        DefaultBundleReportManager.REPORT_GROUP_TYPE_BUNDLE,
                        reportType,
                        status);
    }

    private String randomId() {
        return "1" + RandomStringUtils.secure().nextNumeric(10);
    }
}
