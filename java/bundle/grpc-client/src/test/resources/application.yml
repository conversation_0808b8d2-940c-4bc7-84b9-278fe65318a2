spring:
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
  jooq:
    sql-dialect: POSTGRES

grpc:
  server:
    port: -1
  client:
    bundleManager:
      address: "in-process:${grpc.server.in-process-name}"
      negotiation-type: plaintext
    bundleTagManager:
      address: "in-process:${grpc.server.in-process-name}"
      negotiation-type: plaintext
