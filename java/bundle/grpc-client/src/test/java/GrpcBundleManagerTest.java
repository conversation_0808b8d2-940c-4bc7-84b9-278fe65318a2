import com.bees360.address.AddressProvider;
import com.bees360.bundle.AbstractBundleManagerTest;
import com.bees360.bundle.BundleManager;
import com.bees360.bundle.BundleStateManager;
import com.bees360.bundle.GrpcBundleService;
import com.bees360.bundle.JooqBundleManager;
import com.bees360.bundle.JooqBundleStateManager;
import com.bees360.bundle.config.GrpcBundleManagerConfig;
import com.bees360.bundle.config.JooqBundleManagerConfig;
import com.bees360.contract.ContractRepository;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.state.ProjectStateChangeReasonProvider;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(
        classes = GrpcBundleManagerTest.Config.class,
        properties = {"grpc.server.in-process-name=GrpcBundleManagerTest"})
@DirtiesContext
public class GrpcBundleManagerTest extends AbstractBundleManagerTest {

    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class,
    })
    @Import({
        GrpcBundleManagerConfig.class,
        JooqBundleManagerConfig.class,
        ExceptionTranslateInterceptor.class,
    })
    @Configuration
    static class Config {

        @Bean
        GrpcBundleService grpcBundleService(
                JooqBundleManager bundleManager, JooqBundleStateManager jooqBundleStateManager) {
            return new GrpcBundleService(bundleManager, null, null, jooqBundleStateManager);
        }
    }

    @MockitoBean AddressProvider addressProvider;

    @MockitoBean ContractRepository contractRepository;

    @MockitoBean BatchProjectCreator batchProjectCreator;

    @MockitoBean ExternalIntegrationProvider externalIntegrationProvider;

    @MockitoBean ProjectGroupManager projectGroupManager;

    @MockitoBean ProjectIIManager projectIIManager;

    @MockitoBean ProjectStateChangeReasonProvider projectStateChangeReasonProvider;

    public GrpcBundleManagerTest(
            @Autowired @Qualifier("grpcBundleManagerClient") BundleManager bundleManager,
            @Autowired AddressProvider mockAddressProvider,
            @Autowired ContractRepository mockContractRepository,
            @Autowired BatchProjectCreator mockBatchProjectCreator,
            @Autowired ExternalIntegrationProvider mockExternalIntegrationProvider,
            @Autowired ProjectGroupManager mockProjectGroupManager,
            @Autowired ProjectIIManager mockProjectIIManager,
            @Autowired ProjectStateChangeReasonProvider projectStateChangeReasonProvider,
            @Autowired BundleStateManager grpcBundleStateClient) {
        super(
                bundleManager,
                mockAddressProvider,
                mockContractRepository,
                mockBatchProjectCreator,
                mockExternalIntegrationProvider,
                mockProjectGroupManager,
                mockProjectIIManager,
                projectStateChangeReasonProvider,
                grpcBundleStateClient);
    }

    @Test
    public void testCreateBundleWithNoFailure() {
        super.testCreateBundleWithNoFailure();
    }

    @Test
    public void testFindByIdShouldSucceed() {
        super.testFindByIdShouldSucceed();
    }

    @Test
    public void testFindByEmptyOrNullShouldThrow() {
        super.testFindByEmptyOrNullShouldThrow();
    }

    @Test
    public void testUpdateServiceType() {
        super.testUpdateServiceType();
    }

    @Test
    public void testUpdateYearBuilt() {
        super.testUpdateYearBuilt();
    }

    @Test
    public void testUpdateNumberOfBuildings() {
        super.testUpdateNumberOfBuildings();
    }

    @Test
    public void testUpdatePolicyNumber() {
        super.testUpdatePolicyNumber();
    }

    @Test
    public void testUpdatePolicyEffectiveDate() {
        super.testUpdatePolicyEffectiveDate();
    }

    @Test
    public void testUpdatePolicyRenewal() {
        super.testUpdatePolicyRenewal();
    }

    @Test
    public void testUpdatePolicyTypeAndPropertyType() {
        super.testUpdatePolicyTypeAndPropertyType();
    }

    @Test
    public void testUpdateInspectionNumber() {
        super.testUpdateInspectionNumber();
    }

    @Test
    public void testUpdateAddress() {
        super.testUpdateAddress();
    }

    @Test
    public void testUpdateContact() {
        super.testUpdateContact();
    }

    @Test
    public void testCreateBundleProject() {
        super.testCreateBundleProject();
    }

    @Test
    public void testAddBundleProject() {
        super.testAddBundleProject();
    }

    @Test
    public void testRemoveBundleProject() {
        super.testRemoveBundleProject();
    }

    @Test
    public void testRemoveAllBundleProjectShouldFail() {
        super.testRemoveAllBundleProjectShouldFail();
    }
}
