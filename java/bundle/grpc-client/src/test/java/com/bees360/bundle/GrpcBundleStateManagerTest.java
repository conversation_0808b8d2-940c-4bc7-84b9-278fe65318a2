package com.bees360.bundle;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.bees360.bundle.BundleServiceGrpc.BundleServiceBlockingStub;
import com.bees360.grpc.GrpcApi;
import com.google.protobuf.BoolValue;
import com.google.protobuf.StringValue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.function.Function;

/**
 * Unit tests for {@link GrpcBundleStateManager#changeState(String, Message.BundleState, String,
 * String)}.
 */
class GrpcBundleStateManagerTest {

    @Mock private GrpcApi<BundleServiceBlockingStub> mockGrpcApi;

    @Mock private BundleServiceBlockingStub mockStub;

    private GrpcBundleStateManager stateManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        stateManager = new GrpcBundleStateManager(mockGrpcApi);
    }

    /**
     * Test case: Successful state change. Expected: Returns true when the gRPC call succeeds and
     * returns true.
     */
    @Test
    void testChangeState_Success() {
        // Arrange
        String bundleId = "123";
        Message.BundleState state = Message.BundleState.OPEN;
        String changeReason = "Test reason";
        String changedBy = "user-456";

        Message.UpdateBundleStateRequest request =
                Message.UpdateBundleStateRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setState(state)
                        .setChangeReason(StringValue.of(changeReason))
                        .setRequestBy(StringValue.of(changedBy))
                        .build();

        when(mockGrpcApi.apply(any(Function.class)))
                .thenAnswer(
                        invocation -> {
                            Function<BundleServiceBlockingStub, BoolValue> function =
                                    invocation.getArgument(0);
                            return function.apply(mockStub);
                        });

        when(mockStub.updateBundleState(request)).thenReturn(BoolValue.of(true));

        // Act
        boolean result = stateManager.changeState(bundleId, state, changeReason, changedBy);

        // Assert
        assertTrue(result);
        verify(mockGrpcApi).apply(any(Function.class));
        verify(mockStub).updateBundleState(request);
    }

    /**
     * Test case: Failed state change. Expected: Returns false when the gRPC call succeeds but
     * returns false.
     */
    @Test
    void testChangeState_Failure() {
        // Arrange
        String bundleId = "123";
        Message.BundleState state = Message.BundleState.CLOSE;
        String changeReason = "Test reason";
        String changedBy = "user-456";

        Message.UpdateBundleStateRequest request =
                Message.UpdateBundleStateRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setState(state)
                        .setChangeReason(StringValue.of(changeReason))
                        .setRequestBy(StringValue.of(changedBy))
                        .build();

        when(mockGrpcApi.apply(any(Function.class)))
                .thenAnswer(
                        invocation -> {
                            Function<BundleServiceBlockingStub, BoolValue> function =
                                    invocation.getArgument(0);
                            return function.apply(mockStub);
                        });

        when(mockStub.updateBundleState(request)).thenReturn(BoolValue.of(false));

        // Act
        boolean result = stateManager.changeState(bundleId, state, changeReason, changedBy);

        // Assert
        assertFalse(result);
        verify(mockGrpcApi).apply(any(Function.class));
        verify(mockStub).updateBundleState(request);
    }

    /** Test case: gRPC call throws an exception. Expected: Exception is propagated. */
    @Test
    void testChangeState_Exception() {
        // Arrange
        String bundleId = "123";
        Message.BundleState state = Message.BundleState.PAUSE;
        String changeReason = "Test reason";
        String changedBy = "user-456";

        Message.UpdateBundleStateRequest request =
                Message.UpdateBundleStateRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setState(state)
                        .setChangeReason(StringValue.of(changeReason))
                        .setRequestBy(StringValue.of(changedBy))
                        .build();

        when(mockGrpcApi.apply(any(Function.class)))
                .thenAnswer(
                        invocation -> {
                            Function<BundleServiceBlockingStub, BoolValue> function =
                                    invocation.getArgument(0);
                            return function.apply(mockStub);
                        });

        when(mockStub.updateBundleState(request))
                .thenThrow(new RuntimeException("gRPC call failed"));

        // Act & Assert
        assertThrows(
                RuntimeException.class,
                () -> {
                    stateManager.changeState(bundleId, state, changeReason, changedBy);
                });

        verify(mockGrpcApi).apply(any(Function.class));
        verify(mockStub).updateBundleState(request);
    }
}
