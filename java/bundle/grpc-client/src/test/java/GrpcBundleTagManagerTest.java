import com.bees360.bundle.AbstractBundleTagManagerTest;
import com.bees360.bundle.GrpcBundleTagManager;
import com.bees360.bundle.GrpcBundleTagService;
import com.bees360.bundle.JooqBundleTagManager;
import com.bees360.bundle.config.GrpcBundleTagManagerConfig;
import com.bees360.bundle.config.JooqBundleTagManagerConfig;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.project.tag.ProjectTagRepository;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(
        classes = GrpcBundleTagManagerTest.Config.class,
        properties = {"grpc.server.in-process-name=GrpcBundleTagManagerTest"})
@DirtiesContext
public class GrpcBundleTagManagerTest extends AbstractBundleTagManagerTest {

    @MockitoBean ProjectTagRepository projectTagRepository;

    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class,
    })
    @Import({
        GrpcBundleTagManagerConfig.class,
        JooqBundleTagManagerConfig.class,
        ExceptionTranslateInterceptor.class,
    })
    @Configuration
    static class Config {

        @Bean
        GrpcBundleTagService grpcBundleTagService(JooqBundleTagManager bundleTagManager) {
            return new GrpcBundleTagService(bundleTagManager);
        }
    }

    public GrpcBundleTagManagerTest(
            @Autowired GrpcBundleTagManager bundleTagManager,
            @Autowired ProjectTagRepository projectTagRepository) {
        super(bundleTagManager, projectTagRepository);
    }

    @Test
    public void testUpdateBundleTag() {
        super.testUpdateBundleTag();
    }

    @Test
    public void testFindBundleTag() {
        super.testFindBundleTag();
    }
}
