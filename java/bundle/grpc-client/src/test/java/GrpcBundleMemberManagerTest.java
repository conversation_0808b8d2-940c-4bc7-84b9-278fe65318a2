import com.bees360.address.AddressProvider;
import com.bees360.bundle.AbstractBundleMemberManagerTest;
import com.bees360.bundle.BundleManager;
import com.bees360.bundle.BundleMemberManager;
import com.bees360.bundle.GrpcBundleService;
import com.bees360.bundle.JooqBundleManager;
import com.bees360.bundle.JooqBundleMemberManager;
import com.bees360.bundle.config.GrpcBundleManagerConfig;
import com.bees360.bundle.config.JooqBundleManagerConfig;
import com.bees360.contract.ContractRepository;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.state.ProjectStateChangeReasonProvider;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(
        classes = GrpcBundleMemberManagerTest.Config.class,
        properties = {"grpc.server.in-process-name=GrpcBundleMemberManagerTest"})
@DirtiesContext
public class GrpcBundleMemberManagerTest extends AbstractBundleMemberManagerTest {
    @ImportAutoConfiguration({
        GrpcServerAutoConfiguration.class,
        GrpcServerFactoryAutoConfiguration.class,
        GrpcClientAutoConfiguration.class,
    })
    @Import({
        GrpcBundleManagerConfig.class,
        JooqBundleManagerConfig.class,
        ExceptionTranslateInterceptor.class,
    })
    @Configuration
    static class Config {

        @Bean
        GrpcBundleService grpcBundleService(
                JooqBundleManager bundleManager, JooqBundleMemberManager jooqBundleMemberManager) {
            return new GrpcBundleService(bundleManager, null, jooqBundleMemberManager, null);
        }
    }

    @MockitoBean AddressProvider addressProvider;

    @MockitoBean ContractRepository contractRepository;

    @MockitoBean BatchProjectCreator batchProjectCreator;

    @MockitoBean ExternalIntegrationProvider externalIntegrationProvider;

    @MockitoBean ProjectGroupManager projectGroupManager;

    @MockitoBean ProjectIIManager projectIIManager;

    @MockitoBean ProjectStateChangeReasonProvider projectStateChangeReasonProvider;

    public GrpcBundleMemberManagerTest(
            @Autowired @Qualifier("grpcBundleManagerClient") BundleManager bundleManager,
            @Autowired AddressProvider mockAddressProvider,
            @Autowired ContractRepository mockContractRepository,
            @Autowired BatchProjectCreator mockBatchProjectCreator,
            @Autowired ExternalIntegrationProvider mockExternalIntegrationProvider,
            @Autowired ProjectGroupManager mockProjectGroupManager,
            @Autowired ProjectIIManager projectIIManager,
            @Autowired ProjectStateChangeReasonProvider projectStateChangeReasonProvider,
            @Autowired @Qualifier("grpcBundleMemberClient")
                    BundleMemberManager bundleMemberManager) {
        super(
                bundleManager,
                mockAddressProvider,
                mockContractRepository,
                mockBatchProjectCreator,
                mockExternalIntegrationProvider,
                mockProjectGroupManager,
                projectIIManager,
                projectStateChangeReasonProvider,
                bundleMemberManager);
    }

    @Test
    public void testFindBundleMember() {
        super.testFindBundleMember();
    }

    @Test
    public void testUpdateBundleMember() {
        super.testUpdateBundleMember();
    }
}
