package com.bees360.bundle.config;

import com.bees360.bundle.BundleServiceGrpc;
import com.bees360.bundle.BundleTagServiceGrpc;
import com.bees360.bundle.GrpcBundleTagManager;
import com.bees360.grpc.ExceptionTranslatedGrpcApi;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcBundleTagManagerConfig {

    @GrpcClient("bundleTagManager")
    BundleTagServiceGrpc.BundleTagServiceBlockingStub bundleTagServiceBlockingStub;

    @GrpcClient("bundleManager")
    BundleServiceGrpc.BundleServiceBlockingStub bundleServiceBlockingStub;

    @Bean
    public GrpcBundleTagManager grpcBundleTagManagerClient() {
        return new GrpcBundleTagManager(
                ExceptionTranslatedGrpcApi.of(bundleTagServiceBlockingStub),
                ExceptionTranslatedGrpcApi.of(bundleServiceBlockingStub));
    }
}
