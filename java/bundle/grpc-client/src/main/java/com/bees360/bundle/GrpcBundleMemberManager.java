package com.bees360.bundle;

import com.bees360.grpc.GrpcApi;
import com.bees360.project.member.RoleEnum;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

import java.util.List;

@Log4j2
public class GrpcBundleMemberManager implements BundleMemberManager {
    private final GrpcApi<BundleServiceGrpc.BundleServiceBlockingStub> api;

    public GrpcBundleMemberManager(GrpcApi<BundleServiceGrpc.BundleServiceBlockingStub> api) {
        this.api = api;
        log.info("Created {}(api={}).", this, this.api);
    }

    @Override
    public boolean setMember(String bundleId, RoleEnum role, String userId, String opUserId) {
        log.debug(
                "Setting bundle member: bundleId {}, role {}, userId {}, opUserId {}",
                bundleId,
                role,
                userId,
                opUserId);
        var request =
                Message.UpdateBundleMemberRequest.newBuilder()
                        .setBundleId(bundleId)
                        .setRole(role.getValue())
                        .setUserId(userId)
                        .setOpUserId(opUserId)
                        .build();
        return api.apply(stub -> stub.updateBundleMember(request)).getValue();
    }

    @Override
    public List<Message.BundleMessage.Member> findByBundleId(String bundleId) {
        log.debug("Finding bundle member by bundleId: {}", bundleId);
        var response = api.apply(stub -> stub.findBundleMember(StringValue.of(bundleId)));
        return response.getMemberList();
    }
}
