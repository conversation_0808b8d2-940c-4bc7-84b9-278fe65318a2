package com.bees360.bundle;

import com.bees360.bundle.Message.BundleProjectSyncFieldType;
import com.bees360.bundle.Message.BundleTagQueryRequest;
import com.bees360.bundle.Message.BundleTagUpdateRequest;
import com.bees360.bundle.Message.SyncBundleProjectResponse;
import com.bees360.grpc.GrpcApi;
import com.bees360.project.tag.Message;
import com.bees360.project.tag.ProjectTag;
import com.bees360.util.Functions;
import com.google.protobuf.StringValue;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import java.util.List;

@Log4j2
public class GrpcBundleTagManager implements BundleTagManager {

    private final GrpcApi<BundleTagServiceGrpc.BundleTagServiceBlockingStub> api;

    private final GrpcApi<BundleServiceGrpc.BundleServiceBlockingStub> bundleApi;

    public GrpcBundleTagManager(
            GrpcApi<BundleTagServiceGrpc.BundleTagServiceBlockingStub> api,
            GrpcApi<BundleServiceGrpc.BundleServiceBlockingStub> bundleApi) {
        this.api = api;
        this.bundleApi = bundleApi;
        log.info("Created {}(api={}, bundleApi={}).", this, this.api, this.bundleApi);
    }

    @Override
    public int updateBundleTag(
            String bundleId,
            List<String> tagIds,
            Message.ProjectTagType type,
            String requestBy,
            String requestVia) {
        log.debug(
                "Updating Bundle Tag with bundleId: {}, tagIds: {}, type: {}, requestBy: {},"
                        + " requestVia: {}.",
                bundleId,
                tagIds,
                type,
                requestBy,
                requestVia);
        var request =
                BundleTagUpdateRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .addAllTagIds(tagIds)
                        .setType(type)
                        .setRequestBy(StringValue.of(requestBy))
                        .setRequestVia(StringValue.of(requestVia))
                        .build();
        return api.apply(stub -> stub.updateBundleTag(request)).getValue();
    }

    @Override
    public SyncBundleProjectResponse syncBundleTag(String bundleId, String requestBy) {
        log.debug("Syncing bundle {} tag by {}.", bundleId, requestBy);
        var syncRequestBuilder = com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder();
        syncRequestBuilder.setBundleId(StringValue.of(bundleId));
        syncRequestBuilder.setRequestBy(StringValue.of(requestBy));
        syncRequestBuilder.setSyncField(BundleProjectSyncFieldType.TAG);
        return bundleApi.apply(stub -> stub.syncBundleProject(syncRequestBuilder.build()));
    }

    @Override
    public List<ProjectTag> findByBundleId(
            String bundleId, @Nullable String companyId, @Nullable Message.ProjectTagType type) {
        log.debug(
                "Finding Bundle Tag with bundleId: {}, companyId: {}, type: {}.",
                bundleId,
                companyId,
                type);
        var requestBuilder =
                BundleTagQueryRequest.newBuilder().setBundleId(StringValue.of(bundleId));
        Functions.acceptIfNotNull(requestBuilder::setCompanyId, companyId, StringValue::of);
        Functions.acceptIfNotNull(requestBuilder::setType, type);
        return api.apply(stub -> stub.findByBundleId(requestBuilder.build())).getTagsList().stream()
                .map(ProjectTag::of)
                .toList();
    }
}
