package com.bees360.bundle;

import com.bees360.grpc.GrpcApi;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class GrpcBundleStateManager implements BundleStateManager {
    private final GrpcApi<BundleServiceGrpc.BundleServiceBlockingStub> api;

    public GrpcBundleStateManager(GrpcApi<BundleServiceGrpc.BundleServiceBlockingStub> api) {
        this.api = api;
    }

    @Override
    public boolean changeState(
            String bundleId, Message.BundleState state, String changeReason, String changedBy) {
        log.debug(
                "Changing state of bundle {} to {} by {} with reason {}",
                bundleId,
                state,
                changedBy,
                changeReason);
        var request =
                Message.UpdateBundleStateRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setState(state)
                        .setChangeReason(StringValue.of(changeReason))
                        .setRequestBy(StringValue.of(changedBy))
                        .build();
        return api.apply(stub -> stub.updateBundleState(request)).getValue();
    }
}
