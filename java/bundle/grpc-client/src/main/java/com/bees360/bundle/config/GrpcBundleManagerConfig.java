package com.bees360.bundle.config;

import com.bees360.bundle.BundleServiceGrpc;
import com.bees360.bundle.GrpcBundleManager;
import com.bees360.bundle.GrpcBundleMemberManager;
import com.bees360.bundle.GrpcBundleStateManager;
import com.bees360.grpc.ExceptionTranslatedGrpcApi;

import net.devh.boot.grpc.client.inject.GrpcClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcBundleManagerConfig {
    @GrpcClient("bundleManager")
    BundleServiceGrpc.BundleServiceBlockingStub bundleServiceBlockingStub;

    @Bean
    public GrpcBundleManager grpcBundleManagerClient() {
        return new GrpcBundleManager(ExceptionTranslatedGrpcApi.of(bundleServiceBlockingStub));
    }

    @Bean
    public GrpcBundleMemberManager grpcBundleMemberClient() {
        return new GrpcBundleMemberManager(
                ExceptionTranslatedGrpcApi.of(bundleServiceBlockingStub));
    }

    @Bean
    public GrpcBundleStateManager grpcBundleStateClient() {
        return new GrpcBundleStateManager(ExceptionTranslatedGrpcApi.of(bundleServiceBlockingStub));
    }
}
