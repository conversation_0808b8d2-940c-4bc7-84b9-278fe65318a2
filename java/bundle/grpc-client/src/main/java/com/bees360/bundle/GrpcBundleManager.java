package com.bees360.bundle;

import com.bees360.grpc.GrpcApi;
import com.bees360.util.DateTimes;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

import java.time.Instant;
import java.util.List;

@Log4j2
public class GrpcBundleManager implements BundleManager {
    private final GrpcApi<BundleServiceGrpc.BundleServiceBlockingStub> api;

    public GrpcBundleManager(GrpcApi<BundleServiceGrpc.BundleServiceBlockingStub> api) {
        this.api = api;
        log.info("Created {}(api={}).", this, this.api);
    }

    @Override
    public Message.CreateBundleResponse createBundle(Message.CreateBundleRequest request) {
        log.debug("Creating bundle with request: {}", request);
        return api.apply(stub -> stub.create(request));
    }

    @Override
    public Message.CreateBundleResponse createBundleProject(
            String bundleId,
            List<Message.BundleAddressMessage> buildingAddresses,
            String creationChannel,
            String requestBy) {
        log.debug(
                "Creating Bundle Project with bundleId: {}, creationChannel: {}, requestBy: {}",
                bundleId,
                creationChannel,
                requestBy);
        var request =
                Message.CreateBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .addAllAddress(buildingAddresses)
                        .setCreationChannel(StringValue.of(creationChannel))
                        .setRequestBy(StringValue.of(requestBy))
                        .build();
        return api.apply(stub -> stub.createBundleProject(request));
    }

    @Override
    public boolean addBundleProject(String bundleId, List<String> projectIds, String requestBy) {
        log.debug(
                "Adding Bundle Project with bundleId: {}, projectIds: {}, requestBy: {}",
                bundleId,
                projectIds,
                requestBy);
        var request =
                Message.UpdateBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .addAllProjectId(projectIds)
                        .setRequestBy(StringValue.of(requestBy))
                        .build();
        return api.apply(stub -> stub.addBundleProject(request)).getValue();
    }

    @Override
    public boolean removeBundleProject(String bundleId, List<String> projectIds, String requestBy) {
        log.debug(
                "Removing Bundle Project with bundleId: {}, projectIds: {}, requestBy: {}",
                bundleId,
                projectIds,
                requestBy);
        var request =
                Message.UpdateBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .addAllProjectId(projectIds)
                        .setRequestBy(StringValue.of(requestBy))
                        .build();
        return api.apply(stub -> stub.removeBundleProject(request)).getValue();
    }

    @Override
    public Message.BundleMessage findById(String id) {
        log.debug("Finding Bundle by ID: {}", id);
        if (id == null || id.isEmpty()) {
            throw new IllegalArgumentException("Bundle ID cannot be null or empty.");
        }
        var bundle = api.apply(stub -> stub.findById(StringValue.of(id)));
        return Message.BundleMessage.getDefaultInstance().equals(bundle) ? null : bundle;
    }

    @Override
    public List<Message.BundleMessage> findByQuery(Message.BundleRequestQuery query) {
        log.debug("Finding Bundle by query: {}", query);
        return api.apply(stub -> stub.findByQuery(query)).getBundleList();
    }

    @Override
    public boolean updateBundle(Message.UpdateBundleRequest request, String requestBy) {
        log.debug("Updating bundle by {} with request: {}", requestBy, request);
        var updateRequestBuilder = request.toBuilder();
        if (!updateRequestBuilder.hasRequestBy()) {
            updateRequestBuilder.setRequestBy(StringValue.of(requestBy));
        }
        return api.apply(stub -> stub.updateBundle(updateRequestBuilder.build())).getValue();
    }

    @Override
    public boolean updateBundlePolicy(Message.UpdateBundlePolicyRequest request, String requestBy) {
        log.debug("Updating bundle policy by {} with request: {}", requestBy, request);
        var updateRequestBuilder = request.toBuilder();
        if (!updateRequestBuilder.hasRequestBy()) {
            updateRequestBuilder.setRequestBy(StringValue.of(requestBy));
        }
        return api.apply(stub -> stub.updateBundlePolicy(updateRequestBuilder.build())).getValue();
    }

    @Override
    public boolean saveBundleContact(Message.UpdateBundleContactRequest request, String requestBy) {
        log.debug("Saving bundle contact by {} with request: {}", requestBy, request);
        var updateRequestBuilder = request.toBuilder();
        if (!updateRequestBuilder.hasRequestBy()) {
            updateRequestBuilder.setRequestBy(StringValue.of(requestBy));
        }
        return api.apply(stub -> stub.updateBundleContact(updateRequestBuilder.build())).getValue();
    }

    @Override
    public Message.SyncBundleProjectResponse syncBundleProject(
            Message.SyncBundleProjectRequest request, String requestBy) {
        log.debug("Syncing bundle project by {} with request: {}", requestBy, request);
        var syncRequestBuilder = request.toBuilder();
        if (!syncRequestBuilder.hasRequestBy()) {
            syncRequestBuilder.setRequestBy(StringValue.of(requestBy));
        }
        return api.apply(stub -> stub.syncBundleProject(syncRequestBuilder.build()));
    }

    @Override
    public boolean updateStatus(
            String bundleId, Message.BundleStatus status, String updatedBy, Instant updatedAt) {
        var request = Message.UpdateBundleStatusRequest.newBuilder();
        request.addBundleId(bundleId);
        request.setStatus(status);
        request.setRequestBy(StringValue.of(updatedBy));
        request.setRequestAt(DateTimes.toTimestamp(updatedAt));
        var result = api.apply(stub -> stub.updateBundleStatus(request.build()));
        return result.getValue();
    }

    @Override
    public boolean deleteById(String bundleId, String deletedBy) {
        throw new UnsupportedOperationException(
                "Delete operation is not supported in gRPC Bundle Manager.");
    }
}
