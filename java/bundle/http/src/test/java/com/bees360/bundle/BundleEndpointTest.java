package com.bees360.bundle;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.bundle.Message.BundleList;
import com.bees360.bundle.Message.BundleMessage;
import com.bees360.bundle.Message.BundleRequestQuery;
import com.google.protobuf.StringValue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

/** Unit tests for the getBundleByQuery method in BundleEndpoint. */
@ExtendWith(MockitoExtension.class)
public class BundleEndpointTest {

    @Mock private BundleManager bundleManager;

    @Mock private BundleMemberManager bundleMemberManager;

    @InjectMocks private BundleEndpoint endpoint;

    private String userId = "testUser";

    private static final String PROJECT_ID = "project1";
    private static final String BUNDLE_ID = "bundle1";

    @BeforeEach
    void setUp() {
        // Initialize mocks if needed
    }

    /**
     * Test case: Query is empty (equals default instance). Expected: IllegalArgumentException is
     * thrown.
     */
    @Test
    public void testGetBundleByQuery_EmptyQuery_ThrowsException() {
        var query = new BundleQueryParam();
        assertThrows(
                IllegalArgumentException.class,
                () -> {
                    endpoint.getBundleByQuery(userId, query);
                });
    }

    /** Test case: Query has limit 0. Expected: Limit is set to 100 before calling bundleManager. */
    @Test
    public void testGetBundleByQuery_LimitZero_CallsWithDefaultLimit() {
        // Prepare
        BundleMessage bundle = BundleMessage.newBuilder().setId(StringValue.of(BUNDLE_ID)).build();
        List<BundleMessage> bundles = List.of(bundle);

        var originalQuery = new BundleQueryParam();
        originalQuery.setProjectId(List.of(PROJECT_ID));

        when(bundleManager.findByQuery(any(BundleRequestQuery.class))).thenReturn(bundles);

        ArgumentCaptor<BundleRequestQuery> queryCaptor =
                ArgumentCaptor.forClass(BundleRequestQuery.class);

        // Act
        BundleList result = endpoint.getBundleByQuery(userId, originalQuery);

        // Verify
        verify(bundleManager).findByQuery(queryCaptor.capture());
        assertEquals(100, queryCaptor.getValue().getLimit());
        assertEquals(bundles, result.getBundleList());
    }

    /**
     * Test case: Valid query with non-zero limit. Expected: Query is used as-is, and result is
     * returned.
     */
    @Test
    public void testGetBundleByQuery_ValidQuery_ReturnsBundleList() {
        // Prepare
        BundleMessage bundle = BundleMessage.newBuilder().setId(StringValue.of(BUNDLE_ID)).build();
        List<BundleMessage> bundles = Arrays.asList(bundle);

        var query = new BundleQueryParam();
        query.setProjectId(List.of(PROJECT_ID));
        query.setLimit(50);

        when(bundleManager.findByQuery(eq(query.toMessage()))).thenReturn(bundles);

        // Act
        BundleList result = endpoint.getBundleByQuery(userId, query);

        // Verify
        verify(bundleManager).findByQuery(query.toMessage());
        assertEquals(bundles, result.getBundleList());
    }
}
