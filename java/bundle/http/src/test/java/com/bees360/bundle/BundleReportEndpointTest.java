package com.bees360.bundle;

import static org.mockito.ArgumentMatchers.eq;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.bees360.auth.CustomTokenReader;
import com.bees360.auth.DefaultUserDetails;
import com.bees360.auth.config.JwtResourceServerConfig;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.bundle.Message.MergeBundleReportRequest;
import com.bees360.bundle.config.BundleHttpSecurityConfig;
import com.bees360.bundle.report.BundleReportManager;
import com.bees360.bundle.report.BundleReportProcessStatusProvider;
import com.bees360.bundle.report.BundleReportProcessor;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.CorsConfig;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportTypeEnum;
import com.bees360.user.Account;
import com.bees360.user.Message.AccountMessage;
import com.bees360.user.User;
import com.bees360.user.util.MockUser;
import com.google.protobuf.StringValue;

import lombok.Data;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.client.OAuth2ClientProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.server.authorization.client.InMemoryRegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@DirtiesContext
@SpringBootTest(
        classes = {
            BundleReportEndpointTest.Config.class,
        },
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@EnableAutoConfiguration
public class BundleReportEndpointTest {

    @Configuration
    @ApplicationAutoConfig(
            exclude = {
                DataSourceAutoConfiguration.class,
            })
    @Import({
        JwtResourceServerConfig.class,
        BundleHttpSecurityConfig.class,
        BundleReportEndpoint.class,
        CorsConfig.class,
        ProtoHttpMessageConverterConfig.class,
        ApiExceptionHandler.class,
    })
    static class Config {
        @Configuration
        @ConfigurationProperties(value = "auth")
        @Data
        static class Properties {
            private List<OAuth2ClientProperties.Registration> clients;
        }

        @Bean
        PasswordEncoder bcryptPasswordEncoder() {
            return new BCryptPasswordEncoder();
        }

        @Bean
        public RegisteredClientRepository registeredClientRepository(
                Properties properties, PasswordEncoder passwordEncoder) {
            List<RegisteredClient> clients = new ArrayList<>();
            for (OAuth2ClientProperties.Registration clientProperties : properties.clients) {
                RegisteredClient.Builder clientBuilder =
                        RegisteredClient.withId(UUID.randomUUID().toString())
                                .clientId(clientProperties.getClientId())
                                .clientSecret(
                                        passwordEncoder.encode(clientProperties.getClientSecret()))
                                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS);
                Optional.ofNullable(clientProperties.getScope())
                        .orElse(new HashSet<>())
                        .forEach(clientBuilder::scope);
                clients.add(clientBuilder.build());
            }
            return new InMemoryRegisteredClientRepository(clients);
        }
    }

    @MockitoBean BundleMemberManager bundleMemberManager;
    @MockitoBean BundleManager bundleManager;
    @MockitoBean BundleReportManager bundleReportManager;
    @MockitoBean BundleReportProcessor bundleReportProcessor;
    @MockitoBean BundleReportProcessStatusProvider bundleReportProcessStatusProvider;
    @MockitoBean CustomTokenReader customTokenReader;

    @LocalServerPort private int port;

    @Autowired private WebApplicationContext webApplicationContext;

    MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc =
                MockMvcBuilders.webAppContextSetup(webApplicationContext)
                        .apply(SecurityMockMvcConfigurers.springSecurity())
                        .build();
    }

    @Test
    void testGetBundleReport() throws Exception {
        // Given
        String bundleId = "456";
        String type = String.valueOf(ReportTypeEnum.FUR.getCode());

        Report report1 = Mockito.mock(Report.class);
        Message.ReportMessage reportMessage1 =
                Message.ReportMessage.newBuilder().setType(type).build();
        Mockito.when(report1.toMessage()).thenReturn(reportMessage1);

        Report report2 = Mockito.mock(Report.class);
        Message.ReportMessage reportMessage2 =
                Message.ReportMessage.newBuilder().setType(type).build();
        Mockito.when(report2.toMessage()).thenReturn(reportMessage2);

        List<Report> reports = List.of(report1, report2);
        Mockito.doReturn(reports).when(bundleReportManager).find(bundleId, type, null);

        // When & Then
        mockMvc.perform(
                        get("/bundle/{bundleId}/report", bundleId)
                                .param("type", type)
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(mockUser()))))
                .andExpect(status().isOk());

        Mockito.verify(bundleReportManager).find(bundleId, type, null);
    }

    @Test
    void testGetBundleReportInHistory() throws Exception {
        // Given
        String bundleId = "456";
        String type = "REPORT_TYPE";

        Report report1 = Mockito.mock(Report.class);
        Message.ReportMessage reportMessage1 = Message.ReportMessage.newBuilder().build();
        Mockito.when(report1.toMessage()).thenReturn(reportMessage1);

        Report report2 = Mockito.mock(Report.class);
        Message.ReportMessage reportMessage2 = Message.ReportMessage.newBuilder().build();
        Mockito.when(report2.toMessage()).thenReturn(reportMessage2);

        List<Report> reports = List.of(report1, report2);
        Mockito.doReturn(reports).when(bundleReportManager).findInHistory(bundleId, type, null);

        // When & Then
        mockMvc.perform(
                        get("/bundle/{bundleId}/report/history", bundleId)
                                .param("type", type)
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(mockUser()))))
                .andExpect(status().isOk());

        Mockito.verify(bundleReportManager).findInHistory(bundleId, type, null);
    }

    @Test
    void testGetBundleReportProcessStatus() throws Exception {
        // Given
        String bundleId = "456";
        String reportType = String.valueOf(ReportTypeEnum.FUR.getCode());
        Message.ReportProcessStatus.Type processType = Message.ReportProcessStatus.Type.MERGE;

        List<Message.ReportProcessStatus> processStatusList =
                List.of(
                        Message.ReportProcessStatus.newBuilder()
                                .setGroupKey(bundleId)
                                .setGroupType("GROUP_BUNDLE")
                                .setReportType(reportType)
                                .setType(processType)
                                .build());
        Mockito.when(
                        bundleReportProcessStatusProvider.getReportProcessStatus(
                                bundleId, reportType, processType))
                .thenReturn(processStatusList);

        // When & Then
        mockMvc.perform(
                        get("/bundle/{bundleId}/report/process-status", bundleId)
                                .param("reportType", reportType)
                                .param("processType", processType.name())
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(mockUser()))))
                .andExpect(status().isOk());

        Mockito.verify(bundleReportProcessStatusProvider)
                .getReportProcessStatus(bundleId, reportType, processType);
    }

    @Test
    void testMergeReport() throws Exception {
        // Given
        String bundleId = "456";
        String reportType = "REPORT_TYPE";
        List<String> reportIds = List.of("report1", "report2");

        MergeBundleReportRequest request =
                MergeBundleReportRequest.newBuilder()
                        .setReportType(reportType)
                        .addAllReportId(reportIds)
                        .build();

        // When & Then
        User user = mockUser();
        mockMvc.perform(
                        post("/bundle/{bundleId}/report/merge", bundleId)
                                .contentType(MediaType.APPLICATION_PROTOBUF_VALUE)
                                .content(request.toByteArray())
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(user))))
                .andExpect(status().isOk());

        Mockito.verify(bundleReportProcessor)
                .mergeReport(bundleId, reportType, reportIds, user.getId());
    }

    @Test
    void testMergeReportWithCustomMergeBy() throws Exception {
        // Given
        String bundleId = "456";
        String customMergeBy = "customMergeBy";
        String reportType = String.valueOf(ReportTypeEnum.FUR.getCode());
        List<String> reportIds = List.of("report1", "report2");

        MergeBundleReportRequest request =
                MergeBundleReportRequest.newBuilder()
                        .setReportType(reportType)
                        .addAllReportId(reportIds)
                        .setMergeBy(StringValue.of(customMergeBy))
                        .build();

        // When & Then
        User user = mockUser();
        mockMvc.perform(
                        post("/bundle/{bundleId}/report/merge", bundleId)
                                .contentType(MediaType.APPLICATION_PROTOBUF)
                                .content(request.toByteArray())
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(user))))
                .andExpect(status().isOk());

        Mockito.verify(bundleReportProcessor)
                .mergeReport(bundleId, reportType, reportIds, customMergeBy);
    }

    private UserDetails getUser(User user) {
        final Account account =
                Account.from(
                        AccountMessage.newBuilder()
                                .setId(Objects.requireNonNull(user.getId()))
                                .setUser(user.toMessage())
                                .build());
        return new DefaultUserDetails(account);
    }

    User mockUser() {
        var user = MockUser.random();
        user.setEmail("<EMAIL>");
        var group = user.addRandomGroup();
        group.addAuthority("ROLE_ADMIN");
        Mockito.doReturn("1062").when(customTokenReader).getByKey(eq("company_id"));
        Mockito.doReturn(Set.of("bees360.com")).when(customTokenReader).getScope();
        return user;
    }
}
