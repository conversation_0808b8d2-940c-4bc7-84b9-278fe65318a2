package com.bees360.bundle;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.bees360.auth.CustomTokenReader;
import com.bees360.auth.DefaultUserDetails;
import com.bees360.auth.config.JwtResourceServerConfig;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.bundle.Message.BundleMessage;
import com.bees360.bundle.config.BundleHttpSecurityConfig;
import com.bees360.contract.Message.ContractMessage;
import com.bees360.http.config.CorsConfig;
import com.bees360.project.member.RoleEnum;
import com.bees360.user.Account;
import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.user.util.MockUser;

import lombok.Data;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.client.OAuth2ClientProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.server.authorization.client.InMemoryRegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@DirtiesContext
@SpringBootTest(
        classes = {
            BundleAccessSecurityTest.Config.class,
        },
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@EnableAutoConfiguration
@EnableConfigurationProperties
public class BundleAccessSecurityTest {

    @Configuration
    @ApplicationAutoConfig(
            exclude = {
                DataSourceAutoConfiguration.class,
            })
    @Import({
        JwtResourceServerConfig.class,
        BundleHttpSecurityConfig.class,
        BundleEndpoint.class,
        BundleTagEndpoint.class,
        CorsConfig.class,
    })
    static class Config {
        @Configuration
        @ConfigurationProperties(value = "auth")
        @Data
        static class Properties {
            private List<OAuth2ClientProperties.Registration> clients;
        }

        @Bean
        PasswordEncoder bcryptPasswordEncoder() {
            return new BCryptPasswordEncoder();
        }

        @Bean
        public RegisteredClientRepository registeredClientRepository(
                Properties properties, PasswordEncoder passwordEncoder) {
            List<RegisteredClient> clients = new ArrayList<>();
            for (OAuth2ClientProperties.Registration clientProperties : properties.clients) {
                RegisteredClient.Builder clientBuilder =
                        RegisteredClient.withId(UUID.randomUUID().toString())
                                .clientId(clientProperties.getClientId())
                                .clientSecret(
                                        passwordEncoder.encode(clientProperties.getClientSecret()))
                                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS);
                Optional.ofNullable(clientProperties.getScope())
                        .orElse(new HashSet<>())
                        .forEach(clientBuilder::scope);
                clients.add(clientBuilder.build());
            }
            return new InMemoryRegisteredClientRepository(clients);
        }
    }

    @MockitoBean BundleManager bundleManager;
    @MockitoBean BundleTagManager bundleTagManager;
    @MockitoBean CustomTokenReader customTokenReader;
    @MockitoBean BundleMemberManager bundleMemberManager;
    @MockitoBean BundleStateManager bundleStateManager;

    @LocalServerPort private int port;

    @Autowired private WebApplicationContext webApplicationContext;

    MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc =
                MockMvcBuilders.webAppContextSetup(webApplicationContext)
                        .apply(SecurityMockMvcConfigurers.springSecurity())
                        .build();
    }

    @Test
    void testFindBundleTagByAdmin() throws Exception {
        var bundleId = "1234";
        mockMvc.perform(
                        get("/bundle/{bundleId}/tag", bundleId)
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(mockAdminUser(Set.of("bees360.ai"))))))
                .andExpect(status().isOk());
    }

    @Test
    void testFindBundleTagByInsuredByCompanyAdmin() throws Exception {
        var bundleId = "1235";
        var mockBundleBuilder = BundleMessage.newBuilder();
        var mockContractBuilder = ContractMessage.newBuilder();
        var mockInsuredBy = com.bees360.customer.Message.CustomerMessage.newBuilder().setId("1062");
        var mockProcessedBy =
                com.bees360.customer.Message.CustomerMessage.newBuilder().setId("1063");

        Mockito.doReturn(
                        mockBundleBuilder
                                .setContract(
                                        mockContractBuilder
                                                .setInsuredBy(mockInsuredBy)
                                                .setProcessedBy(mockProcessedBy))
                                .build())
                .when(bundleManager)
                .findById(bundleId);
        mockMvc.perform(
                        get("/bundle/{bundleId}/tag", bundleId)
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(mockCompanyAdminUser(Set.of("bees360.ai"))))))
                .andExpect(status().isOk());
    }

    @Test
    void testFindBundleTagByProcessedByCompanyAdmin() throws Exception {
        var bundleId = "1235";
        var mockBundleBuilder = BundleMessage.newBuilder();
        var mockContractBuilder = ContractMessage.newBuilder();
        var mockInsuredBy = com.bees360.customer.Message.CustomerMessage.newBuilder().setId("1063");
        var mockProcessedBy =
                com.bees360.customer.Message.CustomerMessage.newBuilder().setId("1062");

        Mockito.doReturn(
                        mockBundleBuilder
                                .setContract(
                                        mockContractBuilder
                                                .setInsuredBy(mockInsuredBy)
                                                .setProcessedBy(mockProcessedBy))
                                .build())
                .when(bundleManager)
                .findById(bundleId);
        mockMvc.perform(
                        get("/bundle/{bundleId}/tag", bundleId)
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(mockCompanyAdminUser(Set.of("bees360.ai"))))))
                .andExpect(status().isOk());
    }

    @Test
    void testFindBundleTagByNormalUser() throws Exception {
        var bundleId = "1235";
        var mockBundleBuilder = BundleMessage.newBuilder();
        var mockContractBuilder = ContractMessage.newBuilder();
        var mockInsuredBy = com.bees360.customer.Message.CustomerMessage.newBuilder().setId("1063");
        var mockProcessedBy =
                com.bees360.customer.Message.CustomerMessage.newBuilder().setId("1062");

        Mockito.doReturn(
                        mockBundleBuilder
                                .setContract(
                                        mockContractBuilder
                                                .setInsuredBy(mockInsuredBy)
                                                .setProcessedBy(mockProcessedBy))
                                .build())
                .when(bundleManager)
                .findById(bundleId);
        mockMvc.perform(
                        get("/bundle/{bundleId}/tag", bundleId)
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(mockNormalUser(Set.of("bees360.ai"))))))
                .andExpect(status().isForbidden());
    }

    @Test
    void testFindBundleTagByBundleMember() throws Exception {
        var bundleId = "1235";
        var mockBundleBuilder = BundleMessage.newBuilder();
        var mockMemberBuilder = BundleMessage.Member.newBuilder();
        var user = mockNormalUser(Set.of("bees360.ai"));
        var underwriterId = user.getId();
        Mockito.doReturn(
                        mockBundleBuilder
                                .addMember(
                                        mockMemberBuilder
                                                .setUser(
                                                        Message.UserMessage.newBuilder()
                                                                .setId(underwriterId)
                                                                .build())
                                                .setRole(RoleEnum.UNDERWRITER.getValue())
                                                .build())
                                .build())
                .when(bundleManager)
                .findById(bundleId);
        mockMvc.perform(
                        get("/bundle/{bundleId}/tag", bundleId)
                                .remoteAddress("http://127.0.0.1:" + port)
                                .accept(MediaType.APPLICATION_JSON)
                                .with(user(getUser(user))))
                .andExpect(status().isOk());
    }

    private UserDetails getUser(User user) {
        final Account account =
                Account.from(
                        Message.AccountMessage.newBuilder()
                                .setId(Objects.requireNonNull(user.getId()))
                                .setUser(user.toMessage())
                                .build());
        return new DefaultUserDetails(account);
    }

    User mockAdminUser(Set<String> scopes) {
        var user = MockUser.random();
        user.setEmail("<EMAIL>");
        var group = user.addRandomGroup();
        group.addAuthority("ROLE_ADMIN");
        Mockito.doReturn("1062").when(customTokenReader).getByKey(Mockito.eq("company_id"));
        Mockito.doReturn(scopes).when(customTokenReader).getScope();
        return user;
    }

    User mockCompanyAdminUser(Set<String> scopes) {
        var user = MockUser.random();
        user.setEmail("<EMAIL>");
        var group = user.addRandomGroup();
        group.addAuthority("ROLE_COMPANY_ADMIN");
        Mockito.doReturn("1062").when(customTokenReader).getByKey(Mockito.eq("company_id"));
        Mockito.doReturn(scopes).when(customTokenReader).getScope();
        return user;
    }

    User mockNormalUser(Set<String> scopes) {
        var user = MockUser.random();
        user.setEmail("<EMAIL>");
        var group = user.addRandomGroup();
        group.addAuthority("ROLE_PILOT");
        Mockito.doReturn("1062").when(customTokenReader).getByKey(Mockito.eq("company_id"));
        Mockito.doReturn(scopes).when(customTokenReader).getScope();
        return user;
    }
}
