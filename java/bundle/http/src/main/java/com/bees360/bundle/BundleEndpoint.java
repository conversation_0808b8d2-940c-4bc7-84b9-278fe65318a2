package com.bees360.bundle;

import com.bees360.bundle.Message.CreateBundleRequest;
import com.bees360.bundle.Message.CreateBundleResponse;
import com.bees360.bundle.Message.SyncBundleProjectRequest;
import com.bees360.bundle.Message.SyncBundleProjectResponse;
import com.bees360.bundle.Message.UpdateBundleContactRequest;
import com.bees360.bundle.Message.UpdateBundlePolicyRequest;
import com.bees360.bundle.Message.UpdateBundleRequest;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.project.member.RoleEnum;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;
import java.util.NoSuchElementException;

@Import({
    ProtoHttpMessageConverterConfig.class,
    ApiExceptionHandler.class,
})
@Log4j2
@RestController
@RequestMapping("/${http.bundle.endpoint:bundle}")
public class BundleEndpoint {

    public static final String WEB_CREATION_CHANNEL = "WEB";
    private final BundleManager bundleManager;
    private final BundleMemberManager bundleMemberManager;
    private final BundleStateManager bundleStateManager;

    public BundleEndpoint(
            BundleManager bundleManager,
            BundleMemberManager bundleMemberManager,
            BundleStateManager bundleStateManager) {
        this.bundleManager = bundleManager;
        this.bundleMemberManager = bundleMemberManager;
        this.bundleStateManager = bundleStateManager;
        log.info(
                "Created {}(bundleManager={},bundleMemberManager={},bundleStateManager={}).",
                this,
                this.bundleManager,
                this.bundleMemberManager,
                this.bundleStateManager);
    }

    @PostMapping("")
    public CreateBundleResponse createBundle(
            @AuthenticationPrincipal(expression = "id") String userId,
            @RequestBody CreateBundleRequest request) {
        log.info("Creating bundle by {}, with request {}.", userId, request);
        request =
                request.toBuilder()
                        .setCreationChannel(StringValue.of(WEB_CREATION_CHANNEL))
                        .setCreateBy(StringValue.of(userId))
                        .build();
        return bundleManager.createBundle(request);
    }

    @GetMapping("/{bundleId:\\d+}")
    public Message.BundleMessage getBundle(@PathVariable String bundleId) {
        var bundle = bundleManager.findById(bundleId);
        if (Message.BundleMessage.getDefaultInstance().equals(bundle)) {
            throw new NoSuchElementException(
                    String.format("Bundle with id %s does not exists.", bundleId));
        }
        return bundle;
    }

    @PutMapping("/{bundleId:\\d+}")
    public void updateBundle(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestBody UpdateBundleRequest request) {
        log.info("Updating bundle field for {} by {}, with request {}.", bundleId, request, userId);
        request = request.toBuilder().addAllBundleId(List.of(bundleId)).build();
        var result = bundleManager.updateBundle(request, userId);
        if (!result) {
            throw new NoSuchElementException(
                    String.format("Bundle with id %s does not exists.", bundleId));
        }
    }

    @PutMapping("/{bundleId:\\d+}/policy")
    public void updateBundlePolicy(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestBody UpdateBundlePolicyRequest request) {
        log.info(
                "Updating bundle policy for {} by {}, with request {}.", bundleId, userId, request);
        request = request.toBuilder().addAllBundleId(List.of(bundleId)).build();
        var result = bundleManager.updateBundlePolicy(request, userId);
        if (!result) {
            throw new NoSuchElementException(
                    String.format("Bundle with id %s does not exists.", bundleId));
        }
    }

    @PutMapping("/{bundleId:\\d+}/contact")
    public void updateBundleContact(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestBody UpdateBundleContactRequest request) {
        log.info(
                "Updating bundle contact for {} by {}, with request {}.",
                bundleId,
                userId,
                request);
        request = request.toBuilder().addAllBundleId(List.of(bundleId)).build();
        var result = bundleManager.saveBundleContact(request, userId);
        if (!result) {
            throw new NoSuchElementException(
                    String.format("Bundle with id %s does not exists.", bundleId));
        }
    }

    @PostMapping("/{bundleId:\\d+}/sync-project")
    public SyncBundleProjectResponse syncBundleProject(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestBody SyncBundleProjectRequest request) {
        log.info("Syncing bundle project for {} by {}.", bundleId, userId);
        request = request.toBuilder().setBundleId(StringValue.of(bundleId)).build();
        return bundleManager.syncBundleProject(request, userId);
    }

    @PostMapping("/{bundleId:\\d+}/project")
    public Message.CreateBundleResponse createBundleProject(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestBody Message.CreateBundleProjectRequest request) {
        log.info(
                "Creating bundle project with request={}, userId={}, bundleId={}.",
                request,
                userId,
                bundleId);
        return bundleManager.createBundleProject(
                bundleId, request.getAddressList(), WEB_CREATION_CHANNEL, userId);
    }

    @PutMapping("/{bundleId:\\d+}/project")
    public void addBundleProject(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestParam(name = "projectId") List<String> projectIds) {
        log.info(
                "Adding bundle project with projectIds={}, userId={}, bundleId={}.",
                projectIds,
                userId,
                bundleId);
        var response = bundleManager.addBundleProject(bundleId, projectIds, userId);
        if (!response) {
            throw new NoSuchElementException(
                    String.format("Bundle with id %s does not exists.", bundleId));
        }
    }

    @DeleteMapping("/{bundleId:\\d+}/project")
    public void removeBundleProject(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestParam(name = "projectId") List<String> projectIds) {
        log.info(
                "Removing bundle project with projectIds={}, userId={}, bundleId={}.",
                projectIds,
                userId,
                bundleId);
        var response = bundleManager.removeBundleProject(bundleId, projectIds, userId);
        if (!response) {
            throw new NoSuchElementException(
                    String.format("Bundle with id %s does not exists.", bundleId));
        }
    }

    @PutMapping("/{bundleId:\\d+}/status/{status}")
    public boolean updateBundleStatus(
            @PathVariable String bundleId,
            @PathVariable Message.BundleStatus status,
            @AuthenticationPrincipal(expression = "id") String userId) {
        return bundleManager.updateStatus(bundleId, status, userId, Instant.now());
    }

    @PutMapping("/{bundleId:\\d+}/member")
    public void setBundleMember(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestBody Message.UpdateBundleMemberRequest request) {
        log.info(
                "Setting bundle member with request={}, userId={}, bundleId={}.",
                request,
                userId,
                bundleId);
        var role =
                StringUtils.isNumeric(request.getRole())
                        ? RoleEnum.valueOf(Integer.parseInt(request.getRole()))
                        : RoleEnum.valueOf(request.getRole());
        bundleMemberManager.setMember(bundleId, role, request.getUserId(), userId);
    }

    @GetMapping("/{bundleId:\\d+}/member")
    public Message.BundleMemberList getBundleMember(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId) {
        log.info("Getting bundle member with userId={}, bundleId={}.", userId, bundleId);
        return Message.BundleMemberList.newBuilder()
                .addAllMember(bundleMemberManager.findByBundleId(bundleId))
                .build();
    }

    @GetMapping
    public Message.BundleList getBundleByQuery(
            @AuthenticationPrincipal(expression = "id") String userId, BundleQueryParam request) {

        return Message.BundleList.newBuilder()
                .addAllBundle(bundleManager.findByQuery(request.toMessage()))
                .build();
    }

    @PutMapping("/{bundleId:\\d+}/state")
    public boolean updateBundleState(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestParam Message.BundleState state,
            @RequestParam(value = "change-reason") String changeReason) {
        return bundleStateManager.changeState(bundleId, state, changeReason, userId);
    }
}
