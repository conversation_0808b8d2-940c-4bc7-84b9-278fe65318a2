package com.bees360.bundle;

import com.bees360.auth.CustomTokenReader;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

@Log4j2
public class HttpBundleAccessSecurity implements BundleAccessSecurity {

    private final BundleManager bundleManager;
    private final CustomTokenReader customTokenReader;

    public HttpBundleAccessSecurity(
            BundleManager bundleManager, CustomTokenReader customTokenReader) {
        this.bundleManager = bundleManager;
        this.customTokenReader = customTokenReader;
        log.info(
                "Created {}(bundleManager={}, customTokenReader={}).",
                this,
                this.bundleManager,
                this.customTokenReader);
    }

    @Override
    public boolean isBundleAccessible(String bundleId) {
        log.debug("Judging is bundle {} accessible.", bundleId);
        return true;
        //        var authentication = SecurityContextHolder.getContext().getAuthentication();
        //        var userId = getUserId(authentication);
        //        var userCompanyId = customTokenReader.getByKey("company_id");
        //        log.debug(
        //                "Judging is bundle {} accessible by user {}, company {}.",
        //                bundleId,
        //                userId,
        //                userCompanyId);
        //        if (StringUtils.isAnyBlank(userId, userCompanyId)) {
        //            log.debug(
        //                    "Access denied: userId or companyId is blank. userId={},
        // companyId={}",
        //                    userId,
        //                    userCompanyId);
        //            return false;
        //        }
        //
        //        var isAdmin = hasRole(authentication, "ROLE_ADMIN");
        //        var isCompanyAdmin = hasRole(authentication, "ROLE_COMPANY_ADMIN");
        //        log.debug(
        //                "Bundle access control checks: isAdmin={}, isCompanyAdmin={},",
        //                isAdmin,
        //                isCompanyAdmin);
        //        if (isAdmin) {
        //            return true;
        //        }
        //        var bundle = bundleManager.findById(bundleId);
        //        if (bundle == null) {
        //            throw new NoSuchElementException("bundle %s not found.".formatted(bundleId));
        //        }
        //        return isCompanyAdmin && isBundleManagedBy(bundle, userCompanyId)
        //                || isBundleMember(bundle, userId);
    }

    private static boolean hasRole(Authentication authentication, String role) {
        return authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(authority -> StringUtils.equals(authority, role));
    }

    @Override
    public boolean isBundleOperable(String bundleId) {
        // TODO add bundle state judgement
        return true;
    }

    private static boolean isBundleMember(Message.BundleMessage bundle, String userId) {
        return bundle.getMemberList().stream()
                .anyMatch(member -> StringUtils.equals(member.getUser().getId(), userId));
    }

    private boolean isBundleManagedBy(Message.BundleMessage bundle, String userCompanyId) {
        var bundleId = bundle.getId().getValue();

        var insuredBy = bundle.getContract().getInsuredBy();
        var processedBy = bundle.getContract().getProcessedBy();
        log.debug(
                "bundle access control checks: bundleId={}, userCompanyId={}, insuredBy={},"
                        + " processedBy={}.",
                bundleId,
                userCompanyId,
                insuredBy.getId(),
                processedBy.getId());
        return StringUtils.equals(insuredBy.getId(), userCompanyId)
                || StringUtils.equals(processedBy.getId(), userCompanyId);
    }

    private String getUserId(Authentication authentication) {
        if (authentication == null || authentication.getPrincipal() == null) {
            log.debug(
                    "Authentication is null or no 'principal' in authentication: {}.",
                    authentication);
            return null;
        }
        if (authentication.getPrincipal() instanceof co.realms9.bifrost.User user) {
            return user.getId();
        }
        if (authentication.getPrincipal() instanceof com.bees360.user.User user) {
            return user.getId();
        }
        log.warn(
                "The 'principal' in authentication({}) is {} and not a instance of User Class.",
                authentication.getClass(),
                authentication.getPrincipal().getClass());
        return null;
    }
}
