package com.bees360.bundle;

import com.bees360.auth.CustomTokenReader;
import com.bees360.contract.Message.ContractMessage;
import com.bees360.customer.Message.CustomerMessage;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.project.tag.Message.ProjectTagListResponse;
import com.bees360.project.tag.Message.ProjectTagType;
import com.bees360.project.tag.ProjectTag;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@Import({
    ProtoHttpMessageConverterConfig.class,
    ApiExceptionHandler.class,
})
@Log4j2
@RestController
@RequestMapping("/${http.bundle.endpoint:bundle}")
public class BundleTagEndpoint {

    public static final String WEB_REQUEST_SCOPE = "WEB";
    public static final String AI_REQUEST_SCOPE = "AI";
    private final BundleTagManager bundleTagManager;
    private final BundleManager bundleManager;
    private final CustomTokenReader customTokenReader;

    public BundleTagEndpoint(
            BundleTagManager bundleTagManager,
            BundleManager bundleManager,
            CustomTokenReader customTokenReader) {
        this.bundleTagManager = bundleTagManager;
        this.bundleManager = bundleManager;
        this.customTokenReader = customTokenReader;
        log.info(
                "Created {}(bundleTagManager={}, customTokenReader={}).",
                this,
                this.bundleTagManager,
                this.customTokenReader);
    }

    @GetMapping("/{bundleId:\\d+}/tag")
    public ProjectTagListResponse findTagByBundleId(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestParam(required = false, defaultValue = "0") Integer type) {
        log.debug(
                "Finding bundle tag with bundleId={}, userId={}, tagType={}.",
                bundleId,
                userId,
                type);
        var scope = getRequestScope();
        var companyId = customTokenReader.getByKey("company_id");
        if (StringUtils.equals(scope, WEB_REQUEST_SCOPE)) {
            var bundle = bundleManager.findById(bundleId);
            companyId =
                    Optional.ofNullable(bundle)
                            .map(Message.BundleMessage::getContract)
                            .map(ContractMessage::getInsuredBy)
                            .map(CustomerMessage::getId)
                            .orElse(companyId);
        }

        var tagList =
                bundleTagManager
                        .findByBundleId(bundleId, companyId, ProjectTagType.forNumber(type))
                        .stream()
                        .map(ProjectTag::toMessage)
                        .toList();
        return ProjectTagListResponse.newBuilder().addAllTags(tagList).build();
    }

    @PutMapping("/{bundleId:\\d+}/tag")
    public void updateTag(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestBody Message.BundleTagUpdateRequest request) {
        log.debug(
                "Updating bundle tag with bundleId={}, userId={}, request={}.",
                bundleId,
                userId,
                request);
        var tagIds = request.getTagIdsList();
        var type = request.getType();
        var scope = getRequestScope();
        bundleTagManager.updateBundleTag(bundleId, tagIds, type, userId, scope);
    }

    private String getRequestScope() {
        var scopes = customTokenReader.getScope();
        if (scopes.contains("bees360.com")) {
            return WEB_REQUEST_SCOPE;
        } else if (scopes.contains("bees360.ai") || scopes.contains("beespilot.io")) {
            return AI_REQUEST_SCOPE;
        } else {
            throw new UnsupportedOperationException();
        }
    }
}
