package com.bees360.bundle.config;

import com.bees360.auth.CustomTokenReader;
import com.bees360.bundle.BundleManager;
import com.bees360.bundle.HttpBundleAccessSecurity;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BundleHttpSecurityConfig {

    @Bean("BAS")
    public HttpBundleAccessSecurity bundleAccessSecurity(
            BundleManager bundleManager, CustomTokenReader customTokenReader) {
        return new HttpBundleAccessSecurity(bundleManager, customTokenReader);
    }
}
