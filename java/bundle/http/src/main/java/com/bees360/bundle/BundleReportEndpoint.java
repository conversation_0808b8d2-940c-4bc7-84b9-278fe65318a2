package com.bees360.bundle;

import com.bees360.bundle.Message.MergeBundleReportRequest;
import com.bees360.bundle.report.BundleReportManager;
import com.bees360.bundle.report.BundleReportProcessStatusProvider;
import com.bees360.bundle.report.BundleReportProcessor;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.springframework.context.annotation.Import;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Import({
    ProtoHttpMessageConverterConfig.class,
    ApiExceptionHandler.class,
})
@Log4j2
@RestController
@RequestMapping("/${http.bundle.endpoint:bundle}")
public class BundleReportEndpoint {

    private final BundleReportManager bundleReportManager;
    private final BundleReportProcessor bundleReportProcessor;
    private final BundleReportProcessStatusProvider bundleReportProcessStatusProvider;

    public BundleReportEndpoint(
            BundleReportManager bundleReportManager,
            BundleReportProcessor bundleReportProcessor,
            BundleReportProcessStatusProvider bundleReportProcessStatusProvider) {
        this.bundleReportManager = bundleReportManager;
        this.bundleReportProcessor = bundleReportProcessor;
        this.bundleReportProcessStatusProvider = bundleReportProcessStatusProvider;
        log.info(
                "Created {}(bundleReportManager={}, bundleReportProcessor={},"
                        + " bundleReportProcessStatusProvider={})",
                this,
                this.bundleReportManager,
                this.bundleReportProcessor,
                this.bundleReportProcessStatusProvider);
    }

    @GetMapping("/{bundleId:\\d+}/report")
    public Message.FindReportResponse getBundleReport(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestParam(required = false) String type) {
        log.debug("Get {} report for bundle {} by {}.", type, bundleId, userId);
        var reports =
                Iterables.transform(
                        bundleReportManager.find(bundleId, type, null), Report::toMessage);
        return Message.FindReportResponse.newBuilder().addAllReportMessage(reports).build();
    }

    @GetMapping("/{bundleId:\\d+}/report/history")
    public Message.FindReportResponse getBundleReportInHistory(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestParam(required = false) String type) {
        log.debug("Get {} report history for bundle {} by {}.", type, bundleId, userId);
        var reports =
                Iterables.transform(
                        bundleReportManager.findInHistory(bundleId, type, null), Report::toMessage);
        return Message.FindReportResponse.newBuilder().addAllReportMessage(reports).build();
    }

    @GetMapping("/{bundleId:\\d+}/report/process-status")
    public Message.ReportProcessStatusListResponse getBundleReportProcessStatus(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) Message.ReportProcessStatus.Type processType) {
        log.debug(
                "Get {} report {} status for bundle {} by {}.",
                processType,
                reportType,
                bundleId,
                userId);
        var statuses =
                bundleReportProcessStatusProvider.getReportProcessStatus(
                        bundleId, reportType, processType);
        return Message.ReportProcessStatusListResponse.newBuilder()
                .addAllReportProcessStatus(statuses)
                .build();
    }

    @PostMapping("/{bundleId:\\d+}/report/merge")
    public void mergeReport(
            @AuthenticationPrincipal(expression = "id") String userId,
            @PathVariable String bundleId,
            @RequestBody MergeBundleReportRequest request) {
        var reportType = request.getReportType();
        var reportIds = request.getReportIdList();
        log.info(
                "Merge {} report for bundle {} by {}, reportIds: {}.",
                reportType,
                bundleId,
                userId,
                reportIds);
        userId = request.hasMergeBy() ? request.getMergeBy().getValue() : userId;
        bundleReportProcessor.mergeReport(bundleId, reportType, reportIds, userId);
    }
}
