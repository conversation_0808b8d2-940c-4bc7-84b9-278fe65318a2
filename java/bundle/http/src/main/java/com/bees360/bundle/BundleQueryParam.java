package com.bees360.bundle;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.google.common.base.Preconditions;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
public class BundleQueryParam {
    private List<String> projectId;
    private List<String> bundleId;
    private int offset;
    private int limit;

    public Message.BundleRequestQuery toMessage() {
        Message.BundleRequestQuery.Builder builder = Message.BundleRequestQuery.newBuilder();
        acceptIfNotNull(builder::addAllProjectId, getProjectId());
        acceptIfNotNull(builder::addAllBundleId, getBundleId());
        Preconditions.checkArgument(
                !Message.BundleRequestQuery.getDefaultInstance().equals(builder.build()),
                "Query cannot be empty.");

        // set limit and offset, default limit is 100
        builder.setLimit(limit == 0 ? 100 : limit).setOffset(offset);
        return builder.build();
    }
}
