package com.bees360.bundle.notification;

import com.bees360.bundle.Message;
import com.bees360.customer.Customer;
import com.bees360.job.registry.SendCustomerBundleEmailJob;

import java.util.function.BiFunction;

public class SendCustomerBundleEmailJobProvider {
    private final BiFunction<String, String, CustomerBundleEmailProperties>
            customerBundleEmailsPropertiesProvider;
    private final BundleEmailRecipientSelector recipientSelector;

    public SendCustomerBundleEmailJobProvider(
            BiFunction<String, String, CustomerBundleEmailProperties>
                    customerBundleEmailsPropertiesProvider,
            BundleEmailRecipientSelector recipientSelector) {
        this.customerBundleEmailsPropertiesProvider = customerBundleEmailsPropertiesProvider;
        this.recipientSelector = recipientSelector;
    }

    public SendCustomerBundleEmailJob bundleEmailJob(
            Message.BundleMessage bundle,
            Customer customer,
            String emailKey,
            BundleEmailContext bundleEmailContext) {
        var config =
                customerBundleEmailsPropertiesProvider.apply(customer.getCompanyKey(), emailKey);
        var toRecipient = recipientSelector.select(bundle, config.getToSelector());
        var ccRecipient = recipientSelector.select(bundle, config.getCcSelector());
        var bccRecipient = recipientSelector.select(bundle, config.getBccSelector());
        var variables = bundleEmailContext.getEmailVariables(bundle);
        return new SendCustomerBundleEmailJob()
                .setMailSender("no-reply-sender")
                .setTemplateKey(config.getTemplateKey())
                .setVariablesJson(variables)
                .setToRecipients(toRecipient)
                .setCcRecipients(ccRecipient)
                .setBccRecipients(bccRecipient);
    }
}
