package com.bees360.bundle;

import static com.bees360.bundle.AggregateBundleManager.getBundleProjectCurrentStatus;
import static com.bees360.project.statechangereason.Message.ProjectStateChangeReasonMessage.ProjectStateChangeReasonType.UNDEFINED;

import com.bees360.project.state.StateChangeReasonGroupProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import java.util.Map;
import java.util.Objects;

@Log4j2
public class AggregateBundleStateManager implements BundleStateManager {
    private final BundleStateManager bundleStateManager;
    private final StateChangeReasonGroupProvider stateChangeReasonGroupProvider;
    private final BundleManager bundleManager;

    public AggregateBundleStateManager(
            BundleStateManager bundleStateManager,
            StateChangeReasonGroupProvider stateChangeReasonGroupProvider,
            BundleManager bundleManager) {
        this.bundleStateManager = bundleStateManager;
        this.stateChangeReasonGroupProvider = stateChangeReasonGroupProvider;
        this.bundleManager = bundleManager;
    }

    @Override
    public boolean changeState(
            String bundleId, Message.BundleState state, String changeReason, String changedBy) {
        log.debug(
                "Changing state of bundle {} to {} by {} with reason {}",
                bundleId,
                state,
                changedBy,
                changeReason);
        var currentBundle = bundleManager.findById(bundleId);
        var bundleProjects = currentBundle.getProjectList();
        if (bundleProjects.isEmpty()) {
            throw new IllegalArgumentException(
                    String.format(
                            "Bundle %s cannot be updated without projects. Please add projects to"
                                    + " the bundle.",
                            bundleId));
        }
        if (Objects.equals(state, Message.BundleState.CLOSE)) {
            var canceledReason =
                    Iterables.toStream(
                                    stateChangeReasonGroupProvider.findByGroupAndType(
                                            "Project Canceled",
                                            "CHANGE_REASON_CATEGORY",
                                            UNDEFINED))
                            .anyMatch(
                                    r ->
                                            Objects.equals(r.getId(), changeReason)
                                                    || Objects.equals(
                                                            r.getDisplayText(), changeReason)
                                                    || Objects.equals(r.getKey(), changeReason));
            // Not check project status while canceling
            if (canceledReason) {
                return bundleStateManager.changeState(bundleId, state, changeReason, changedBy);
            }
            var projectStatusMap = getBundleProjectCurrentStatus(currentBundle.getProjectList());
            var nonMatchingProjects =
                    projectStatusMap.entrySet().stream()
                            .filter(
                                    entry ->
                                            !entry.getValue()
                                                    .equals(
                                                            com.bees360.project.Message
                                                                    .ProjectStatus
                                                                    .RETURNED_TO_CLIENT))
                            .map(Map.Entry::getKey)
                            .toList();
            if (!nonMatchingProjects.isEmpty()) {
                throw new IllegalArgumentException(
                        "Failed to Close: Associated projects %s are not yet completed."
                                .formatted(nonMatchingProjects));
            }
        }
        return bundleStateManager.changeState(bundleId, state, changeReason, changedBy);
    }
}
