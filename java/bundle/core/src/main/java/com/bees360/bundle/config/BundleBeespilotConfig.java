package com.bees360.bundle.config;

import com.bees360.beespilot.BeespilotBundleManager;
import com.bees360.beespilot.BeespilotBundleParamEnum;
import com.bees360.beespilot.HttpBeespilotBundleManager;
import com.bees360.bundle.BundleManager;
import com.bees360.bundle.event.AddBeespilotBundleProjectOnProjectGroupAdded;
import com.bees360.bundle.event.CreateBeespilotBundleOnBundleCreated;
import com.bees360.bundle.event.RemoveBeespilotBundleProjectOnProjectGroupRemoved;
import com.bees360.bundle.event.UpdateBeespilotBundleOnBundleUpdated;
import com.bees360.event.autoconfig.EnableEventAutoRegister;
import com.bees360.http.HttpClient;
import com.bees360.project.group.ProjectGroupManager;

import lombok.Data;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
@EnableEventAutoRegister
@ConditionalOnProperty(prefix = "http.beespilot.bundle", name = "enabled", havingValue = "true")
public class BundleBeespilotConfig {
    @Data
    static class Properties {
        private List<Path> path = new ArrayList<>();

        @Data
        static class Path {
            private BeespilotBundleParamEnum paramEnum;
            private String path;
        }
    }

    @Bean
    @ConfigurationProperties(value = "http.beespilot.bundle")
    Properties beespilotBundlePath() {
        return new Properties();
    }

    @Bean
    public HttpBeespilotBundleManager beespilotBundleManager(
            HttpClient beespilotHttpClient,
            @Value("${http.beespilot.bundle.endpoint}") String endpoint,
            Properties pathMap) {
        var context = URI.create(endpoint);
        var map =
                pathMap.getPath().stream()
                        .collect(
                                Collectors.toMap(
                                        Properties.Path::getParamEnum, Properties.Path::getPath));
        return new HttpBeespilotBundleManager(context, map, beespilotHttpClient);
    }

    @Bean
    public CreateBeespilotBundleOnBundleCreated createBeespilotBundleOnBundleCreated(
            BeespilotBundleManager beespilotBundleManager, BundleManager bundleManager) {
        return new CreateBeespilotBundleOnBundleCreated(beespilotBundleManager, bundleManager);
    }

    @Bean
    public UpdateBeespilotBundleOnBundleUpdated updateBeespilotBundleOnBundleUpdated(
            BeespilotBundleManager beespilotBundleManager, BundleManager bundleManager) {
        return new UpdateBeespilotBundleOnBundleUpdated(beespilotBundleManager, bundleManager);
    }

    @Bean
    public AddBeespilotBundleProjectOnProjectGroupAdded
            addBeespilotBundleProjectOnProjectGroupAdded(
                    ProjectGroupManager projectGroupManager,
                    BeespilotBundleManager beespilotBundleManager) {
        return new AddBeespilotBundleProjectOnProjectGroupAdded(
                projectGroupManager, beespilotBundleManager);
    }

    @Bean
    public RemoveBeespilotBundleProjectOnProjectGroupRemoved
            removeBeespilotBundleProjectOnProjectGroupRemoved(
                    ProjectGroupManager projectGroupManager,
                    BeespilotBundleManager beespilotBundleManager) {
        return new RemoveBeespilotBundleProjectOnProjectGroupRemoved(
                projectGroupManager, beespilotBundleManager);
    }
}
