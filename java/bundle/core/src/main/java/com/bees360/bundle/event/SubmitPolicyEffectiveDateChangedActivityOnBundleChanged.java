package com.bees360.bundle.event;

import static com.bees360.bundle.BundleActivities.ACTIVITY_SOURCE_WEB;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.util.DateTimes;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/** 监听BundleChanged事件，处理保单生效日期变更并提交活动记录 */
@Log4j2
public class SubmitPolicyEffectiveDateChangedActivityOnBundleChanged
        extends AbstractNamedEventListener<BundleChanged> {

    public static final DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern("MM/dd/yyyy");

    private final ActivityManager activityManager;

    public SubmitPolicyEffectiveDateChangedActivityOnBundleChanged(
            ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={}).", this, this.activityManager);
    }

    @Override
    public void handle(BundleChanged event) throws IOException {
        log.debug("Received bundle changed event {}.", event);
        var oldBundleMeta = event.getOldValue().getMetadata();
        var oldEffectiveDate =
                oldBundleMeta.getPolicy().hasPolicyEffectiveDate()
                        ? DateTimes.toLocalDate(oldBundleMeta.getPolicy().getPolicyEffectiveDate())
                        : null;
        var newBundle = event.getNewValue();
        var newBundleMeta = event.getNewValue().getMetadata();
        var newEffectiveDate =
                newBundleMeta.getPolicy().hasPolicyEffectiveDate()
                        ? DateTimes.toLocalDate(newBundleMeta.getPolicy().getPolicyEffectiveDate())
                        : null;
        var updatedBy = newBundle.getUpdatedBy();
        var updatedAt = newBundle.getUpdatedAt();

        if (Objects.equals(oldEffectiveDate, newEffectiveDate)) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        event.getNewValue().getId(),
                        Message.ActivityMessage.FieldName.POLICY_EFFECTIVE_DATE.name(),
                        null,
                        Message.ActivityMessage.FieldType.STRING,
                        oldEffectiveDate,
                        newEffectiveDate,
                        date -> date.format(DATE_FORMATTER),
                        updatedBy,
                        updatedAt,
                        ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle policy effective date changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
