package com.bees360.bundle.notification;

import com.bees360.bundle.Message;
import com.bees360.codec.GsonCodec;
import com.bees360.user.UserProvider;
import com.google.common.collect.Maps;
import com.google.gson.Gson;

import java.util.Map;

public class BundleEmailContextBundleCreated implements BundleEmailContext {
    private final Gson gson = GsonCodec.DEFAULT_GSON_BUILDER.create();
    private final UserProvider userProvider;

    public BundleEmailContextBundleCreated(UserProvider userProvider) {
        this.userProvider = userProvider;
    }

    @Override
    public String getEmailVariables(Message.BundleMessage bundle) {
        var creator = userProvider.getUser(bundle.getCreatedBy().getValue());
        Map<String, Object> variables = Maps.newHashMap();
        variables.put("creatorName", creator.getName());
        variables.put("creatorEmail", creator.getEmail());
        variables.put("bundleId", bundle.getId().getValue());
        variables.put("address", bundle.getAddress().getAddress());
        return gson.toJson(variables);
    }
}
