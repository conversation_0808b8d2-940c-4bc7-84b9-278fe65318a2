package com.bees360.bundle.event;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleTagChanged;
import com.bees360.event.registry.BundleTagRemoved;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagRepository;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;

/** 监听BundleTagRemoved事件，处理标签移除后提交相关活动记录 */
@Log4j2
public class SubmitActivityOnBundleTagRemoved extends AbstractNamedEventListener<BundleTagRemoved> {

    private final ActivityManager activityManager;
    private final ProjectTagRepository projectTagRepository;

    public SubmitActivityOnBundleTagRemoved(
            ActivityManager activityManager, ProjectTagRepository projectTagRepository) {
        this.activityManager = activityManager;
        this.projectTagRepository = projectTagRepository;
        log.info(
                "Created {}(activityManager={}, projectTagRepository={}).",
                this,
                this.activityManager,
                this.projectTagRepository);
    }

    @Override
    public void handle(BundleTagRemoved event) throws IOException {
        log.debug("Received {}.", event);
        var tags = event.getList();
        if (CollectionUtils.isEmpty(tags)) {
            return;
        }

        var firstTag = tags.get(0);
        var bundleId = firstTag.getBundleId();
        var updatedBy = firstTag.getUpdatedBy();
        var updatedAt = firstTag.getUpdatedAt();
        var updatedVia = firstTag.getUpdatedVia();

        var tagIds = tags.stream().map(BundleTagChanged.BundleTagChangedObject::getTagId).toList();
        var tagNameList =
                projectTagRepository.findAllById(tagIds).stream()
                        .map(ProjectTag::getTitle)
                        .toList();

        var activity =
                BundleActivities.buildTagChangedActivity(
                        bundleId,
                        tagNameList,
                        updatedBy,
                        updatedAt,
                        Message.ActivityMessage.ActionType.DELETE,
                        updatedVia);
        log.debug("Submit bundle tag removed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
