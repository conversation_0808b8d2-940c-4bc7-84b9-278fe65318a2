package com.bees360.bundle.event;

import com.bees360.bundle.BundleManager;
import com.bees360.bundle.BundleProjectManager;
import com.bees360.event.registry.BundleStateChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class UpdateBundleProjectOnBundleClosed
        extends AbstractNamedEventListener<BundleStateChanged> {
    private final BundleManager bundleManager;
    private final BundleProjectManager bundleProjectManager;
    private final ProjectIIManager projectIIManager;
    private final String TEST_OR_DEMO_CASE = "TEST OR DEMO CASE";
    private final String DUPLICATE_INSPECTION = "DUPLICATE INSPECTION";

    public UpdateBundleProjectOnBundleClosed(
            BundleManager bundleManager,
            BundleProjectManager bundleProjectManager,
            ProjectIIManager projectIIManager) {
        this.bundleManager = bundleManager;
        this.bundleProjectManager = bundleProjectManager;
        this.projectIIManager = projectIIManager;
        log.info(
                "Created {}(bundleManager={}, bundleProjectManager={}, projectIIManager={}).",
                this,
                this.bundleManager,
                this.bundleProjectManager,
                this.projectIIManager);
    }

    @Override
    public void handle(BundleStateChanged event) throws IOException {
        log.debug(
                "Received bundle state changed event: {}, start to update bundle project.", event);
        var changeReason = event.getStateChangeReason().key();
        var bundle = bundleManager.findById(event.getBundleId());
        // bundle complete will not sync to any projects
        if (!bundle.getIsCanceled().getValue()) {
            return;
        }
        var result =
                bundleProjectManager.updateProjectState(
                        event.getBundleId(),
                        Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE,
                        event.getStateChangeReason().id(),
                        event.getUpdatedBy());
        log.debug("Updated project state result: {}", result);
        if (changeReason.equals(DUPLICATE_INSPECTION)) {
            // update project status to Canceled
            bundleManager.updateStatus(
                    event.getBundleId(),
                    com.bees360.bundle.Message.BundleStatus.CANCELLED,
                    event.getUpdatedBy(),
                    event.getUpdatedAt());
        }
    }
}
