package com.bees360.bundle.event;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleStateChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.util.function.Function;

/** 监听Bundle状态变化事件，并在状态变化时提交相应的活动记录。 */
@Log4j2
public class SubmitActivityOnBundleStateChanged
        extends AbstractNamedEventListener<BundleStateChanged> {
    private final ActivityManager activityManager;

    public SubmitActivityOnBundleStateChanged(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={})", this, this.activityManager);
    }

    @Override
    public void handle(BundleStateChanged event) throws IOException {
        var bundleId = event.getBundleId();
        var oldState = event.getOldState();
        var newState = event.getState();
        var updatedBy = event.getUpdatedBy();
        var updatedAt = Instant.now();

        if (StringUtils.equals(oldState, newState)) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        bundleId,
                        Message.ActivityMessage.FieldName.STATE.name(),
                        null,
                        Message.ActivityMessage.FieldType.STRING,
                        oldState,
                        newState,
                        Function.identity(),
                        updatedBy,
                        updatedAt,
                        BundleActivities.ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle state changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
