package com.bees360.bundle.event;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.address.Address;
import com.bees360.address.AddressProvider;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Optional;
import java.util.function.Function;

/** 监听BundleChanged事件，当地址ID发生变化时提交地址变更活动记录。 */
@Log4j2
public class SubmitAddressChangedActivityOnBundleChanged
        extends AbstractNamedEventListener<BundleChanged> {

    private final ActivityManager activityManager;
    private final AddressProvider addressProvider;

    public SubmitAddressChangedActivityOnBundleChanged(
            ActivityManager activityManager, AddressProvider addressProvider) {
        this.activityManager = activityManager;
        this.addressProvider = addressProvider;
        log.info(
                "Created {}(activityManager={}, addressProvider={}).",
                this,
                this.activityManager,
                this.addressProvider);
    }

    @Override
    public void handle(BundleChanged event) throws IOException {
        log.debug("Received bundle changed event {}.", event);
        var oldBundle = event.getOldValue();
        var oldAddressId = oldBundle.getAddressId();
        var newBundle = event.getNewValue();
        var newAddressId = newBundle.getAddressId();

        if (StringUtils.equals(oldAddressId, newAddressId)) {
            return;
        }

        var oldAddress =
                Optional.ofNullable(addressProvider.findById(oldAddressId))
                        .map(Address::getAddress)
                        .orElse(null);
        var newAddress =
                Optional.ofNullable(addressProvider.findById(newAddressId))
                        .map(Address::getAddress)
                        .orElse(null);

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        event.getBundleId(),
                        Message.ActivityMessage.FieldName.ADDRESS.name(),
                        null,
                        Message.ActivityMessage.FieldType.STRING,
                        oldAddress,
                        newAddress,
                        Function.identity(),
                        newBundle.getUpdatedBy(),
                        newBundle.getUpdatedAt(),
                        BundleActivities.ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle address changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
