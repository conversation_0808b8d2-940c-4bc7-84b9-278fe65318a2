package com.bees360.bundle.event;

import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_CONTACT;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleContactAdded;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.time.Instant;
import java.util.function.Function;

/** 监听BundleContactAdded事件，处理联系人添加后提交相关活动记录到ActivityManager */
@Log4j2
public class SubmitActivityOnBundleContactAdded
        extends AbstractNamedEventListener<BundleContactAdded> {

    private final ActivityManager activityManager;

    public SubmitActivityOnBundleContactAdded(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={})", this, this.activityManager);
    }

    @Override
    public void handle(BundleContactAdded event) throws IOException {
        log.debug("Received event {}", event);
        var bundleId = event.getNewValue().getBundleId();
        var role = event.getNewValue().getRole();
        var activity =
                BundleActivities.buildFieldChangedActivity(
                        bundleId,
                        ACTIVITY_FIELD_NAME_CONTACT,
                        role,
                        Message.ActivityMessage.FieldType.STRING,
                        null,
                        event.getNewValue().getFullName(),
                        Function.identity(),
                        event.getNewValue().getCreatedBy(),
                        Instant.now(),
                        BundleActivities.ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle contact added activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
