package com.bees360.bundle.notification;

import static com.bees360.util.CollectionUtils.toMap;

import com.bees360.bundle.Message;
import com.bees360.codec.GsonCodec;
import com.bees360.contract.Contract;
import com.bees360.customer.Customer;
import com.bees360.project.ContactRoleEnum;
import com.bees360.util.Defaults;
import com.google.common.collect.Maps;
import com.google.gson.Gson;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class BundleEmailContextBundleCompleted implements BundleEmailContext {
    private final Gson gson = GsonCodec.DEFAULT_GSON_BUILDER.create();

    @Override
    public String getEmailVariables(Message.BundleMessage bundle) {

        Map<String, Object> variables = Maps.newHashMap();
        variables.put("bundleId", bundle.getId().getValue());
        variables.put("policyNumber", bundle.getPolicyNo().getValue());
        variables.put("address", bundle.getAddress().getAddress());
        var contract = Contract.from(bundle.getContract());
        variables.put("insuredBy", toMap(contract.getInsuredBy()));
        bundle.getContactList().stream()
                .filter(
                        c ->
                                StringUtils.equals(
                                        c.getRole().getValue(), ContactRoleEnum.INSURED.getName()))
                .findFirst()
                .ifPresent(
                        insured -> variables.put("insuredName", insured.getFullName().getValue()));
        return gson.toJson(variables);
    }

    private Map<String, Object> toMap(Customer customer) {
        return Map.of("name", Defaults.transformOrDefaultIfNull(customer, Customer::getName, ""));
    }
}
