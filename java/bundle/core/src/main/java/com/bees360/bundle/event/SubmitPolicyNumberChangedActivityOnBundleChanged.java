package com.bees360.bundle.event;

import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_DISPLAY_NAME_POLICY_NUMBER;
import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_POLICY_NUMBER;
import static com.bees360.bundle.BundleActivities.ACTIVITY_SOURCE_WEB;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.function.Function;

/** 监听BundleChanged事件，在保单号变更时提交活动记录 */
@Log4j2
public class SubmitPolicyNumberChangedActivityOnBundleChanged
        extends AbstractNamedEventListener<BundleChanged> {

    private final ActivityManager activityManager;

    public SubmitPolicyNumberChangedActivityOnBundleChanged(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={}).", this, this.activityManager);
    }

    @Override
    public void handle(BundleChanged event) throws IOException {
        log.debug("Received bundle changed event {}.", event);
        var oldBundle = event.getOldValue();
        var oldPolicyNo = oldBundle.getPolicyNo();
        var newBundle = event.getNewValue();
        var newPolicyNo = newBundle.getPolicyNo();

        if (StringUtils.equals(oldPolicyNo, newPolicyNo)) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        newBundle.getId(),
                        ACTIVITY_FIELD_NAME_POLICY_NUMBER,
                        ACTIVITY_FIELD_DISPLAY_NAME_POLICY_NUMBER,
                        Message.ActivityMessage.FieldType.STRING,
                        oldPolicyNo,
                        newPolicyNo,
                        Function.identity(),
                        newBundle.getUpdatedBy(),
                        newBundle.getUpdatedAt(),
                        ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle policy number changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
