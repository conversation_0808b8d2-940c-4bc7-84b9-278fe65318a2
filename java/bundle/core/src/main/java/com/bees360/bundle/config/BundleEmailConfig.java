package com.bees360.bundle.config;

import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.bundle.BundleProvider;
import com.bees360.bundle.event.SendEmailOnBundleCanceled;
import com.bees360.bundle.event.SendEmailOnBundleCompleted;
import com.bees360.bundle.event.SendEmailOnBundleCreated;
import com.bees360.bundle.job.SendCustomerBundleEmailJobExecutor;
import com.bees360.bundle.notification.BundleEmailContext;
import com.bees360.bundle.notification.BundleEmailContextBundleCanceled;
import com.bees360.bundle.notification.BundleEmailContextBundleCompleted;
import com.bees360.bundle.notification.BundleEmailContextBundleCreated;
import com.bees360.bundle.notification.BundleEmailRecipientSelector;
import com.bees360.bundle.notification.CustomerBundleEmailProperties;
import com.bees360.bundle.notification.DefaultBundleEmailRecipientSelector;
import com.bees360.bundle.notification.SendCustomerBundleEmailJobProvider;
import com.bees360.job.JobScheduler;
import com.bees360.mail.MailSenderProvider;
import com.bees360.mail.util.MailMessageFactory;
import com.bees360.user.UserProvider;
import com.bees360.util.retry.RetryProperties;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;

@Log4j2
@Configuration
public class BundleEmailConfig {

    @Data
    static class CustomerBundleEmailsPropertiesConfig {
        private Map<String, CustomerBundleEmailProperties> defaultBundleEmails = new HashMap<>();
        private Map<String, CustomerBundleEmailsProperties> customers = new HashMap<>();
    }

    @Bean
    @RefreshableConfigurationProperties(prefix = "project.app.customer-bundle-emails")
    CustomerBundleEmailsPropertiesConfig customerBundleEmailsPropertiesConfig() {
        return new CustomerBundleEmailsPropertiesConfig();
    }

    /** Customer key to customer bundle email configuration mapping. */
    @Data
    static class CustomerBundleEmailsProperties {
        private Map<String, CustomerBundleEmailProperties> bundleEmails = new HashMap<>();
    }

    @Bean
    BiFunction<String, String, CustomerBundleEmailProperties>
            customerBundleEmailsPropertiesProvider(
                    CustomerBundleEmailsPropertiesConfig properties) {
        log.info(
                "Created customerBundleEmailsPropertiesProvider with properties {}",
                properties.toString());
        return (customerKey, emailKey) -> {
            var defaultProperties = properties.getDefaultBundleEmails().get(emailKey);
            var customerProperties =
                    Optional.ofNullable(properties.getCustomers().get(customerKey))
                            .map(CustomerBundleEmailsProperties::getBundleEmails)
                            .map(map -> map.get(emailKey))
                            .orElse(null);
            if (defaultProperties == null && customerProperties == null) {
                return null;
            }
            var emailProperties = new CustomerBundleEmailProperties();
            Boolean isSubscribed = null;
            if (defaultProperties != null) {
                isSubscribed = defaultProperties.isSubscribed();
                emailProperties.setTemplateKey(defaultProperties.getTemplateKey());
                emailProperties.getToSelector().addAll(defaultProperties.getToSelector());
                emailProperties.getCcSelector().addAll(defaultProperties.getCcSelector());
                emailProperties.getBccSelector().addAll(defaultProperties.getBccSelector());
            }
            if (customerProperties != null) {
                isSubscribed =
                        Optional.ofNullable(customerProperties.getSubscribed())
                                .orElse(isSubscribed);
                if (StringUtils.isNotBlank(customerProperties.getTemplateKey())) {
                    emailProperties.setTemplateKey(customerProperties.getTemplateKey());
                }
                // to
                if (customerProperties.isToSelectorOverrideDefault()) {
                    emailProperties.setToSelector(customerProperties.getToSelector());
                } else {
                    emailProperties.getToSelector().addAll(customerProperties.getToSelector());
                }
                // cc
                if (customerProperties.isCcSelectorOverrideDefault()) {
                    emailProperties.setCcSelector(customerProperties.getCcSelector());
                } else {
                    emailProperties.getCcSelector().addAll(customerProperties.getCcSelector());
                }
                // bcc
                if (customerProperties.isBccSelectorOverrideDefault()) {
                    emailProperties.setBccSelector(customerProperties.getBccSelector());
                } else {
                    emailProperties.getBccSelector().addAll(customerProperties.getBccSelector());
                }
            }

            emailProperties.setSubscribed(isSubscribed);
            return emailProperties;
        };
    }

    @Bean("customerBundleEmailsJobRetryProperties")
    RetryProperties customerBundleEmailsJobRetryProperties() {
        return new RetryProperties().setRetryCount(1).setRetryDelay(Duration.ofSeconds(5));
    }

    @Bean
    BundleEmailContext bundleEmailContextBundleCreated(UserProvider userProvider) {
        return new BundleEmailContextBundleCreated(userProvider);
    }

    @Bean
    BundleEmailContext bundleEmailContextBundleCompleted() {
        return new BundleEmailContextBundleCompleted();
    }

    @Bean
    BundleEmailContext bundleEmailContextBundleCanceled() {
        return new BundleEmailContextBundleCanceled();
    }

    @Bean
    BundleEmailRecipientSelector defaultBundleEmailRecipientSelector() {
        return new DefaultBundleEmailRecipientSelector();
    }

    @Bean
    SendCustomerBundleEmailJobExecutor sendCustomerBundleEmailJobExecutor(
            MailSenderProvider mailSenderProvider, MailMessageFactory clientMailMessageFactory) {
        return new SendCustomerBundleEmailJobExecutor(mailSenderProvider, clientMailMessageFactory);
    }

    @Bean
    SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider(
            @Qualifier("customerBundleEmailsPropertiesProvider")
                    BiFunction<String, String, CustomerBundleEmailProperties>
                            customerBundleEmailsPropertiesProvider,
            BundleEmailRecipientSelector defaultBundleEmailRecipientSelector) {
        return new SendCustomerBundleEmailJobProvider(
                customerBundleEmailsPropertiesProvider, defaultBundleEmailRecipientSelector);
    }

    @Bean
    SendEmailOnBundleCreated sendEmailOnBundleCreated(
            JobScheduler jobScheduler,
            BundleProvider bundleManager,
            @Qualifier("customerBundleEmailsPropertiesProvider")
                    BiFunction<String, String, CustomerBundleEmailProperties>
                            customerBundleEmailsPropertiesProvider,
            RetryProperties customerBundleEmailsJobRetryProperties,
            SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider,
            BundleEmailContext bundleEmailContextBundleCreated) {
        return new SendEmailOnBundleCreated(
                jobScheduler,
                bundleManager,
                customerBundleEmailsPropertiesProvider,
                customerBundleEmailsJobRetryProperties,
                sendCustomerBundleEmailJobProvider,
                bundleEmailContextBundleCreated);
    }

    @Bean
    SendEmailOnBundleCompleted sendEmailOnBundleCompleted(
            JobScheduler jobScheduler,
            BundleProvider bundleManager,
            @Qualifier("customerBundleEmailsPropertiesProvider")
                    BiFunction<String, String, CustomerBundleEmailProperties>
                            customerBundleEmailsPropertiesProvider,
            RetryProperties customerBundleEmailsJobRetryProperties,
            SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider,
            BundleEmailContext bundleEmailContextBundleCompleted) {
        return new SendEmailOnBundleCompleted(
                jobScheduler,
                bundleManager,
                customerBundleEmailsPropertiesProvider,
                customerBundleEmailsJobRetryProperties,
                sendCustomerBundleEmailJobProvider,
                bundleEmailContextBundleCompleted);
    }

    @Bean
    SendEmailOnBundleCanceled sendEmailOnBundleCanceled(
            JobScheduler jobScheduler,
            BundleProvider bundleManager,
            @Qualifier("customerBundleEmailsPropertiesProvider")
                    BiFunction<String, String, CustomerBundleEmailProperties>
                            customerBundleEmailsPropertiesProvider,
            SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider,
            RetryProperties customerBundleEmailsJobRetryProperties,
            BundleEmailContext bundleEmailContextBundleCanceled) {
        return new SendEmailOnBundleCanceled(
                jobScheduler,
                bundleManager,
                customerBundleEmailsPropertiesProvider,
                sendCustomerBundleEmailJobProvider,
                customerBundleEmailsJobRetryProperties,
                bundleEmailContextBundleCanceled);
    }
}
