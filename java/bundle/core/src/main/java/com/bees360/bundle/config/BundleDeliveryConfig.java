package com.bees360.bundle.config;

import com.bees360.bundle.BundleManager;
import com.bees360.bundle.event.MergeReportOnBundleStatusChanged;
import com.bees360.bundle.report.BundleReportProcessor;
import com.bees360.customer.CustomerProvider;
import com.bees360.event.autoconfig.EnableEventAutoRegister;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.project.report.UniqueProjectReportProvider;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableEventAutoRegister
public class BundleDeliveryConfig {

    @Bean
    MergeReportOnBundleStatusChanged mergeReportOnBundleStatusChanged(
            BundleProperties properties,
            CustomerProvider customerProvider,
            BundleManager bundleManager,
            ProjectReportProvider projectReportProvider,
            BundleReportProcessor bundleReportProcessor) {
        return new MergeReportOnBundleStatusChanged(
                bundleManager,
                bundleReportProcessor,
                customerProvider,
                new UniqueProjectReportProvider(projectReportProvider),
                properties.getDelivery().getMergeReport().getTriggerStatuses(),
                properties.getDelivery().getSystemUserId());
    }
}
