package com.bees360.bundle.config;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.CommentManager;
import com.bees360.address.AddressProvider;
import com.bees360.bundle.event.SubmitActivityOnBundleContactAdded;
import com.bees360.bundle.event.SubmitActivityOnBundleCreated;
import com.bees360.bundle.event.SubmitActivityOnBundleMemberChanged;
import com.bees360.bundle.event.SubmitActivityOnBundleStateChanged;
import com.bees360.bundle.event.SubmitActivityOnBundleStatusChanged;
import com.bees360.bundle.event.SubmitActivityOnBundleTagAdded;
import com.bees360.bundle.event.SubmitActivityOnBundleTagRemoved;
import com.bees360.bundle.event.SubmitAddressChangedActivityOnBundleChanged;
import com.bees360.bundle.event.SubmitContactEmailChangedActivityOnBundleContactChanged;
import com.bees360.bundle.event.SubmitContactNameChangedActivityOnBundleContactChanged;
import com.bees360.bundle.event.SubmitContactPhoneChangedActivityOnBundleContactChanged;
import com.bees360.bundle.event.SubmitInspectionNumberChangedActivityOnBundleChanged;
import com.bees360.bundle.event.SubmitPolicyEffectiveDateChangedActivityOnBundleChanged;
import com.bees360.bundle.event.SubmitPolicyNumberChangedActivityOnBundleChanged;
import com.bees360.bundle.event.SubmitPolicyRenewalChangedActivityOnBundleChanged;
import com.bees360.bundle.event.SubmitPolicyTypeChangedActivityOnBundleChanged;
import com.bees360.bundle.event.SubmitPropertyTypeChangedActivityOnBundleChanged;
import com.bees360.bundle.event.SubmitYearBuiltChangedActivityOnBundleChanged;
import com.bees360.event.autoconfig.EnableEventAutoRegister;
import com.bees360.project.tag.ProjectTagRepository;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableEventAutoRegister
public class BundleActivityConfig {

    @Bean
    SubmitActivityOnBundleCreated submitActivityOnBundleCreated(
            ActivityManager activityManager, CommentManager commentManager) {
        return new SubmitActivityOnBundleCreated(activityManager, commentManager);
    }

    @Bean
    SubmitActivityOnBundleContactAdded submitActivityOnBundleContactAdded(
            ActivityManager activityManager) {
        return new SubmitActivityOnBundleContactAdded(activityManager);
    }

    @Bean
    SubmitActivityOnBundleMemberChanged submitActivityOnBundleMemberChanged(
            ActivityManager activityManager) {
        return new SubmitActivityOnBundleMemberChanged(activityManager);
    }

    @Bean
    SubmitActivityOnBundleStateChanged submitActivityOnBundleStateChanged(
            ActivityManager activityManager) {
        return new SubmitActivityOnBundleStateChanged(activityManager);
    }

    @Bean
    SubmitActivityOnBundleStatusChanged submitActivityOnBundleStatusChanged(
            ActivityManager activityManager) {
        return new SubmitActivityOnBundleStatusChanged(activityManager);
    }

    @Bean
    SubmitActivityOnBundleTagAdded submitActivityOnBundleTagAdded(
            ActivityManager activityManager, ProjectTagRepository projectTagRepository) {
        return new SubmitActivityOnBundleTagAdded(activityManager, projectTagRepository);
    }

    @Bean
    SubmitActivityOnBundleTagRemoved submitActivityOnBundleTagRemoved(
            ActivityManager activityManager, ProjectTagRepository projectTagRepository) {
        return new SubmitActivityOnBundleTagRemoved(activityManager, projectTagRepository);
    }

    @Bean
    SubmitAddressChangedActivityOnBundleChanged submitAddressChangedActivityOnBundleChanged(
            ActivityManager activityManager, AddressProvider addressProvider) {
        return new SubmitAddressChangedActivityOnBundleChanged(activityManager, addressProvider);
    }

    @Bean
    SubmitContactEmailChangedActivityOnBundleContactChanged
            submitContactEmailChangedActivityOnBundleContactChanged(
                    ActivityManager activityManager) {
        return new SubmitContactEmailChangedActivityOnBundleContactChanged(activityManager);
    }

    @Bean
    SubmitContactNameChangedActivityOnBundleContactChanged
            submitContactNameChangedActivityOnBundleContactChanged(
                    ActivityManager activityManager) {
        return new SubmitContactNameChangedActivityOnBundleContactChanged(activityManager);
    }

    @Bean
    SubmitContactPhoneChangedActivityOnBundleContactChanged
            submitContactPhoneChangedActivityOnBundleContactChanged(
                    ActivityManager activityManager) {
        return new SubmitContactPhoneChangedActivityOnBundleContactChanged(activityManager);
    }

    @Bean
    SubmitInspectionNumberChangedActivityOnBundleChanged
            submitInspectionNumberChangedActivityOnBundleChanged(ActivityManager activityManager) {
        return new SubmitInspectionNumberChangedActivityOnBundleChanged(activityManager);
    }

    @Bean
    SubmitPolicyNumberChangedActivityOnBundleChanged
            submitPolicyNumberChangedActivityOnBundleChanged(ActivityManager activityManager) {
        return new SubmitPolicyNumberChangedActivityOnBundleChanged(activityManager);
    }

    @Bean
    SubmitPolicyRenewalChangedActivityOnBundleChanged
            submitPolicyRenewalChangedActivityOnBundleChanged(ActivityManager activityManager) {
        return new SubmitPolicyRenewalChangedActivityOnBundleChanged(activityManager);
    }

    @Bean
    SubmitPolicyTypeChangedActivityOnBundleChanged submitPolicyTypeChangedActivityOnBundleChanged(
            ActivityManager activityManager) {
        return new SubmitPolicyTypeChangedActivityOnBundleChanged(activityManager);
    }

    @Bean
    SubmitPolicyEffectiveDateChangedActivityOnBundleChanged
            submitPropertyEffectiveDateChangedActivityOnBundleChanged(
                    ActivityManager activityManager) {
        return new SubmitPolicyEffectiveDateChangedActivityOnBundleChanged(activityManager);
    }

    @Bean
    SubmitPropertyTypeChangedActivityOnBundleChanged
            submitPropertyTypeChangedActivityOnBundleChanged(ActivityManager activityManager) {
        return new SubmitPropertyTypeChangedActivityOnBundleChanged(activityManager);
    }

    @Bean
    SubmitYearBuiltChangedActivityOnBundleChanged submitYearBuiltChangedActivityOnBundleChanged(
            ActivityManager activityManager) {
        return new SubmitYearBuiltChangedActivityOnBundleChanged(activityManager);
    }
}
