package com.bees360.bundle.event;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.CommentManager;
import com.bees360.bundle.BundleActivities;
import com.bees360.bundle.Message;
import com.bees360.event.registry.BundleCreated;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.util.Optional;

/** 处理Bundle创建事件，提交相关活动和评论 */
@Log4j2
public class SubmitActivityOnBundleCreated extends AbstractNamedEventListener<BundleCreated> {
    private final ActivityManager activityManager;
    private final CommentManager commentManager;

    public SubmitActivityOnBundleCreated(
            ActivityManager activityManager, CommentManager commentManager) {
        this.activityManager = activityManager;
        this.commentManager = commentManager;
        log.info(
                "Created {}(activityManager={}, commentManager={})",
                this,
                this.activityManager,
                this.commentManager);
    }

    @Override
    public void handle(BundleCreated event) throws IOException {
        log.debug("Received event: {}", event);
        var bundleId = event.getId();
        var createdBy = event.getCreatedBy();
        var createdAt = event.getCreatedAt();
        var note =
                Optional.ofNullable(event.getMetadata())
                                .map(Message.BundleMessage.Metadata::hasNote)
                                .orElse(false)
                        ? event.getMetadata().getNote().getValue()
                        : null;

        // submit bundle created event
        var createdActivity =
                BundleActivities.buildCreatedActivity(
                        bundleId, createdBy, createdAt, BundleActivities.ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle created activity {}.", createdActivity.toMessage());
        activityManager.submitActivity(createdActivity);
        // submit bundle note comment
        if (StringUtils.isEmpty(note)) {
            return;
        }

        var bundleNote =
                BundleActivities.buildComment(
                        bundleId,
                        note,
                        createdBy,
                        Instant.now(),
                        BundleActivities.ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle note comment {}.", bundleNote.toMessage());
        commentManager.addComment(bundleNote);
    }
}
