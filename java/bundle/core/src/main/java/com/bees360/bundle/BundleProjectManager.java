package com.bees360.bundle;

import static com.bees360.bundle.JooqBundleManager.BUNDLE_PROJECT_GROUP_TYPE;
import static com.bees360.project.Message.ProjectStatus.ASSIGNED_TO_PILOT;
import static com.bees360.project.Message.ProjectStatus.CUSTOMER_CONTACTED;
import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.api.ApiException;
import com.bees360.api.ApiStatus;
import com.bees360.api.UnknownException;
import com.bees360.policy.PolicyManager;
import com.bees360.project.BuildingManager;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectPolicyManager;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.group.ProjectGroupProvider;
import com.bees360.project.member.MemberManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.project.tag.Message.ProjectTagType;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagManager;
import com.bees360.util.Iterables;
import com.google.protobuf.BoolValue;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.tuple.Pair;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;

@Log4j2
public class BundleProjectManager {

    private final ProjectGroupProvider projectGroupProvider;
    private final ProjectIIManager projectIIManager;
    private final PolicyManager policyManager;
    private final BuildingManager buildingManager;
    private final ProjectPolicyManager projectPolicyManager;
    private final ProjectStatusManager projectStatusManager;
    private final ProjectTagManager projectTagManager;
    private final MemberManager memberManager;
    private final MemberManager webMemberManager;
    private final ContactManager contactManager;
    private final ProjectStateManager projectStateManager;

    public BundleProjectManager(
            ProjectGroupProvider projectGroupProvider,
            ProjectIIManager projectIIManager,
            PolicyManager policyManager,
            BuildingManager buildingManager,
            ProjectPolicyManager projectPolicyManager,
            ProjectStatusManager projectStatusManager,
            ProjectTagManager projectTagManager,
            MemberManager memberManager,
            MemberManager webMemberManager,
            ContactManager contactManager,
            ProjectStateManager projectStateManager) {
        this.projectGroupProvider = projectGroupProvider;
        this.projectIIManager = projectIIManager;
        this.policyManager = policyManager;
        this.buildingManager = buildingManager;
        this.projectPolicyManager = projectPolicyManager;
        this.projectStatusManager = projectStatusManager;
        this.projectTagManager = projectTagManager;
        this.memberManager = memberManager;
        this.webMemberManager = webMemberManager;
        this.contactManager = contactManager;
        this.projectStateManager = projectStateManager;
        log.info(
                "Created {}(projectGroupProvider={}, projectIIManager={}, policyManager={},"
                        + " buildingManager={}, projectPolicyManager={}, projectStatusManager={},"
                        + " projectTagManager={}, memberManager={},"
                        + " webMemberManager={},contactManager={},projectStateManager={}.)",
                this,
                this.projectGroupProvider,
                this.projectIIManager,
                this.policyManager,
                this.buildingManager,
                this.projectPolicyManager,
                this.projectStatusManager,
                this.projectTagManager,
                this.memberManager,
                this.webMemberManager,
                this.contactManager,
                this.projectStateManager);
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateInspectionNumber(
            String bundleId, String inspectionNo, String requestBy) {
        log.debug(
                "updateInspectionNo called with bundleId={}, inspectionNo={}," + " requestBy={}",
                bundleId,
                inspectionNo,
                requestBy);
        return updateBundleProjects(
                bundleId,
                inspectionNo,
                (projectId, newInspectionNo) ->
                        projectIIManager.updateInspectionNumber(
                                projectId, newInspectionNo, requestBy),
                project ->
                        "update project %s inspection number failed for unknown reason."
                                .formatted(project.getId()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updatePolicyNumber(
            String bundleId, String policyNo, String requestBy) {
        log.debug(
                "updatePolicyNo called with bundleId={}, policyNo={}," + " requestBy={}",
                bundleId,
                policyNo,
                requestBy);
        return updateBundleProjects(
                bundleId,
                policyNo,
                (projectId, newPolicyNo) ->
                        projectPolicyManager.updatePolicyNumber(projectId, newPolicyNo, requestBy),
                project ->
                        "update project %s policy number failed for unknown reason."
                                .formatted(project.getId()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updatePolicyEffectiveDate(
            String bundleId, LocalDate effectiveDate, String requestBy) {
        log.debug(
                "updatePolicyEffectiveDate called with bundleId={}, effectiveDate={},"
                        + " requestBy={}",
                bundleId,
                effectiveDate,
                requestBy);
        return updateBundleProjects(
                bundleId,
                effectiveDate,
                (projectId, newEffectiveDate) ->
                        projectPolicyManager.updatePolicyEffectiveDate(
                                projectId, newEffectiveDate, requestBy),
                project ->
                        "update project %s policy effective date failed for unknown reason."
                                .formatted(project.getId()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateYearBuilt(
            String bundleId, Integer yearBuilt, String requestBy) {
        log.debug(
                "updateYearBuilt called with bundleId={}, yearBuilt={}," + " requestBy={}",
                bundleId,
                yearBuilt,
                requestBy);
        return updateBundleProjects(
                bundleId,
                yearBuilt,
                (projectId, newYearBuilt) ->
                        projectPolicyManager.updateYearBuilt(projectId, newYearBuilt, requestBy),
                project ->
                        "update project %s year built failed for unknown reason."
                                .formatted(project.getId()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updatePolicyRenewal(
            String bundleId, Boolean isRenewal, String requestBy) {
        log.debug(
                "updatePolicyRenewal called with bundleId={}, isRenewal={}," + " requestBy={}",
                bundleId,
                isRenewal,
                requestBy);
        return updateBundleProjects(
                bundleId,
                isRenewal,
                projectIIManager::updateProjectPolicyRenewal,
                project ->
                        "update project %s policy renewal failed for unknown reason."
                                .formatted(project.getId()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse>
            updatePolicyTypeAndPropertyType(
                    String bundleId, String policyType, Integer propertyType, String requestBy) {
        log.debug(
                "updatePolicyAndPropertyType called with bundleId={}, policyType={},"
                        + " propertyType={}, requestBy={}",
                bundleId,
                policyType,
                propertyType,
                requestBy);
        return updateBundleProjects(
                bundleId,
                Pair.of(policyType, propertyType),
                (projectId, newValue) ->
                        projectPolicyManager.updatePolicyAndPropertyType(
                                projectId, newValue.getKey(), newValue.getValue(), requestBy),
                project ->
                        "update project %s policy and property type failed for unknown reason."
                                .formatted(project.getId()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateServiceType(
            String bundleId, ServiceTypeEnum serviceType, String requestBy) {
        log.debug(
                "updateServiceType called with bundleId={}, serviceType={}," + " requestBy={}",
                bundleId,
                serviceType,
                requestBy);
        return updateBundleProjects(
                bundleId,
                serviceType,
                (projectId, newServiceType) ->
                        projectIIManager.updateServiceTypeById(projectId, serviceType, requestBy),
                project ->
                        "update project %s service type failed for unknown reason."
                                .formatted(project.getId()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateProjectStatus(
            String bundleId,
            com.bees360.project.Message.ProjectStatus status,
            Map<String, com.bees360.project.Message.ProjectStatus> currentProjectStatusMap,
            String updatedBy,
            Instant updatedAt) {
        log.debug(
                "updateProjectStatus called with bundleId={}, status={},"
                        + " currentProjectStatusMap={}, updatedBy={}",
                bundleId,
                status,
                currentProjectStatusMap,
                updatedBy);
        return updateBundleProjects(
                bundleId,
                status,
                (projectId, newStatus) -> {
                    if (currentProjectStatusMap.get(projectId) == null) {
                        return false;
                    }
                    var currentProjectStatus = currentProjectStatusMap.get(projectId);
                    // Skip project status downgrade except ASSIGNED_TO_PILOT to CUSTOMER_CONTACTED
                    if (currentProjectStatus.getNumber() >= status.getNumber()
                            && !(Objects.equals(currentProjectStatus, ASSIGNED_TO_PILOT)
                                    && !Objects.equals(status, CUSTOMER_CONTACTED))) {
                        return true;
                    }
                    return projectStatusManager.updateStatus(
                            projectId, newStatus, updatedBy, updatedAt);
                },
                project -> String.format("update project %s status failed.", project));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateProjectState(
            String bundleId,
            com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum state,
            String changeReason,
            String updatedBy) {
        log.debug(
                "updateProjectState called with bundleId={}, state={}, changeReason={},"
                        + " updatedBy={}",
                bundleId,
                state,
                changeReason,
                updatedBy);
        return updateBundleProjects(
                bundleId,
                state,
                (projectId, newState) -> {
                    projectStateManager.changeProjectState(
                            projectId, newState, changeReason, updatedBy);
                    return true;
                },
                project -> String.format("update project %s state failed.", project),
                project ->
                        !Objects.equals(
                                com.bees360.project.Message.ProjectMessage.ProjectState
                                        .ProjectStateEnum.PROJECT_CLOSE,
                                project.getCurrentState().getState()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateProjectTag(
            String bundleId,
            List<ProjectTag> tags,
            ProjectTagType type,
            String requestBy,
            String requestVia) {
        log.debug(
                "updateProjectTag called with bundleId={}, tags={}, type={}, requestBy={},"
                        + " requestVia={}.",
                bundleId,
                tags.stream().map(ProjectTag::getId).toList(),
                type,
                requestBy,
                requestVia);
        return updateBundleProjects(
                bundleId,
                tags,
                (projectId, newTags) -> {
                    projectTagManager.updateProjectTag(
                            projectId, newTags, type, requestBy, requestVia);
                    return true;
                },
                project ->
                        "update project %s tag failed for unknown reason."
                                .formatted(project.getId()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateProjectMember(
            String bundleId, RoleEnum role, String userId, String updatedBy) {
        log.debug(
                "updateProjectMember called with bundleId={}, role={}, userId={}, updatedBy={}",
                bundleId,
                role,
                userId,
                updatedBy);
        return updateBundleProjects(
                bundleId,
                userId,
                (projectId, newUserId) -> {
                    if (newUserId == null) {
                        return memberManager.removeMember(projectId, role, updatedBy);
                    } else {
                        return webMemberManager.setMember(projectId, newUserId, role, updatedBy);
                    }
                },
                project ->
                        String.format(
                                "update project %s member %s failed.", project, role.getValue()));
    }

    public List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateProjectContact(
            String bundleId, Message.BundleContactMessage contact, String updatedBy) {
        log.debug(
                "updateProjectContact called with bundleId={}, contact={}, updatedBy={}",
                bundleId,
                contact,
                updatedBy);
        return updateBundleProjects(
                bundleId,
                contact,
                (projectId, bundleContact) -> {
                    var contactId =
                            Iterables.toStream(contactManager.findByProjectId(projectId))
                                    .filter(
                                            c ->
                                                    c.getRole()
                                                            .equals(
                                                                    bundleContact
                                                                            .getRole()
                                                                            .getValue()))
                                    .min(
                                            Comparator.comparingInt(
                                                    (Contact c) -> Integer.parseInt(c.getId())))
                                    .map(Contact::getId)
                                    .orElse(null);
                    var projectContact =
                            buildProjectContactFromBundleContact(bundleContact, contactId);
                    // If project contact exists, update the first one; otherwise add a new one.
                    if (contactId != null) {
                        contactManager.updateContact(projectContact, updatedBy);
                    } else {
                        contactManager.addContact(projectId, projectContact, updatedBy);
                    }
                    return true;
                },
                project ->
                        String.format(
                                "update project %s contact %s failed.",
                                project, contact.getRole()));
    }

    private Contact buildProjectContactFromBundleContact(
            Message.BundleContactMessage contact, String contactId) {
        var builder = com.bees360.project.Message.ProjectMessage.Contact.newBuilder();
        acceptIfNotNull(builder::setFullName, contact.getFullName(), StringValue::getValue);
        acceptIfNotNull(builder::setPrimaryEmail, contact.getEmail(), StringValue::getValue);
        acceptIfNotNull(builder::setPrimaryPhone, contact.getPhone(), StringValue::getValue);
        acceptIfNotNull(builder::setRole, contact.getRole(), StringValue::getValue);
        acceptIfNotNull(builder::setIsPrimary, contact.getIsPrimary(), BoolValue::getValue);
        acceptIfNotNull(builder::setId, contactId);
        return Contact.of(builder.build());
    }

    private <T> List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateBundleProjects(
            String bundleId,
            T newValue,
            java.util.function.BiFunction<String, T, Boolean> updateFunction,
            Function<ProjectII, String> unknownErrorMsgSupplier) {
        return updateBundleProjects(
                bundleId, newValue, updateFunction, unknownErrorMsgSupplier, project -> true);
    }

    private <T> List<Message.SyncBundleProjectResponse.SyncProjectResponse> updateBundleProjects(
            String bundleId,
            T newValue,
            java.util.function.BiFunction<String, T, Boolean> updateFunction,
            Function<ProjectII, String> unknownErrorMsgSupplier,
            Predicate<ProjectII> projectPredicate) {
        log.debug(
                "updateBundleProjects called with bundleId={}, newValue={},"
                        + " unknownErrorMsgSupplier={}, projectPredicate={}",
                bundleId,
                newValue,
                unknownErrorMsgSupplier,
                projectPredicate);
        var bundleProjects =
                projectGroupProvider.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE);
        var bundleProjectIds = Iterables.toList(bundleProjects.getProjectIds());
        var responseList = new ArrayList<Message.SyncBundleProjectResponse.SyncProjectResponse>();
        var projects = projectIIManager.findAllById(bundleProjectIds);
        for (var project : projects) {
            if (!projectPredicate.test(project)) {
                log.debug(
                        "Project {} does not match the predicate, skipping update.",
                        project.getId());
                continue;
            }
            var projectId = project.getId();
            var executeStatus =
                    executeSyncProject(
                            () -> updateFunction.apply(projectId, newValue),
                            unknownErrorMsgSupplier.apply(project));
            responseList.add(buildSyncProjectResponse(projectId, executeStatus));
        }
        return responseList;
    }

    private ApiStatus executeSyncProject(Supplier<Boolean> function, String errorMessage) {
        try {
            return function.get()
                    ? ApiStatus.OK
                    : ApiStatus.fromThrowable(new UnknownException(errorMessage));
        } catch (ApiException | IllegalArgumentException e) {
            return ApiStatus.fromThrowable(e);
        }
    }

    private Message.SyncBundleProjectResponse.SyncProjectResponse buildSyncProjectResponse(
            String projectId, ApiStatus status) {
        return Message.SyncBundleProjectResponse.SyncProjectResponse.newBuilder()
                .setProjectId(projectId)
                .setStatus(status.toMessage().toBuilder().clearCause())
                .build();
    }
}
