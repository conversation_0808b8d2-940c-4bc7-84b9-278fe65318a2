package com.bees360.bundle.job;

import com.bees360.job.registry.SendCustomerBundleEmailJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.mail.MailSender;
import com.bees360.mail.MailSenderProvider;
import com.bees360.mail.util.MailMessageFactory;

import lombok.extern.log4j.Log4j2;

import java.util.Collection;
import java.util.Map;

@Log4j2
public class SendCustomerBundleEmailJobExecutor
        extends AbstractJobExecutor<SendCustomerBundleEmailJob> {

    private final MailSenderProvider mailSenderProvider;
    private final MailMessageFactory mailMessageFactory;

    public SendCustomerBundleEmailJobExecutor(
            MailSenderProvider mailSenderProvider, MailMessageFactory mailMessageFactory) {
        this.mailSenderProvider = mailSenderProvider;
        this.mailMessageFactory = mailMessageFactory;
    }

    private void sendMail(
            MailSender mailSender,
            Collection<String> toRecipients,
            Collection<String> ccRecipients,
            Collection<String> bccRecipients,
            String variablesJson,
            String templateKey) {

        var mail =
                mailMessageFactory.create(
                        toRecipients,
                        ccRecipients,
                        bccRecipients,
                        templateKey,
                        variablesJson,
                        Map.of());
        mailSender.send(mail);
        log.debug(
                "send email, to:{}, cc:{}, bcc:{},templateKey:{}, variablesJson:{}",
                toRecipients,
                ccRecipients,
                bccRecipients,
                templateKey,
                variablesJson);
    }

    @Override
    protected void handle(SendCustomerBundleEmailJob job) {
        log.info("Try to send email with job: {}", job);
        var mailSender = mailSenderProvider.get(job.getMailSender());
        sendMail(
                mailSender,
                job.getToRecipients(),
                job.getCcRecipients(),
                job.getBccRecipients(),
                job.getVariablesJson(),
                job.getTemplateKey());
    }
}
