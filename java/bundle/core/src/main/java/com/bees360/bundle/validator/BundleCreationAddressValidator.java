package com.bees360.bundle.validator;

import com.bees360.bundle.Message;
import com.bees360.bundle.Message.BundleAddressMessage;
import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Predicate;

@Log4j2
public class BundleCreationAddressValidator implements Predicate<Message.CreateBundleRequest> {

    @Override
    public boolean test(Message.CreateBundleRequest request) {
        log.debug("Validating addresses for bundle creation request: {}", request);
        var mainAddressCount = 0;
        for (BundleAddressMessage address : request.getBuildingAddressList()) {
            Preconditions.checkArgument(
                    StringUtils.isNotBlank(address.getStreetAddress().getValue()),
                    "Create bundle failed: Street address can not be empty.");
            Preconditions.checkArgument(
                    StringUtils.isNotBlank(address.getCity().getValue()),
                    "Create bundle failed: City can not be empty.");
            Preconditions.checkArgument(
                    StringUtils.isNotBlank(address.getState().getValue()),
                    "Create bundle failed: State can not be empty.");
            Preconditions.checkArgument(
                    StringUtils.isNotBlank(address.getCountry().getValue()),
                    "Create bundle failed: Country can not be empty.");
            Preconditions.checkArgument(
                    StringUtils.isNotBlank(address.getZip().getValue()),
                    "Create bundle failed: Zip can not be empty.");
            mainAddressCount += address.getIsMain().getValue() ? 1 : 0;
        }

        if (mainAddressCount > 1) {
            throw new IllegalArgumentException(
                    "Create bundle failed: Only one address can be marked as main.");
        } else if (mainAddressCount < 1) {
            throw new IllegalArgumentException(
                    "Create bundle failed: At least one address must be marked as main.");
        }
        return true;
    }
}
