package com.bees360.bundle.config;

import com.bees360.bundle.BundleManager;
import com.bees360.bundle.BundleProjectManager;
import com.bees360.bundle.BundleStateManager;
import com.bees360.bundle.event.UpdateBundleProjectOnBundleClosed;
import com.bees360.bundle.event.UpdateBundleStateOnBundleReturnToClient;
import com.bees360.event.autoconfig.EnableEventAutoRegister;
import com.bees360.project.ProjectIIManager;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableEventAutoRegister
public class BundleStateConfig {
    @Bean
    public UpdateBundleStateOnBundleReturnToClient updateBundleStateOnBundleReturnToClient(
            BundleStateManager bundleStateManager) {
        return new UpdateBundleStateOnBundleReturnToClient(bundleStateManager);
    }

    @Bean
    public UpdateBundleProjectOnBundleClosed updateBundleProjectOnBundleClosed(
            BundleManager bundleManager,
            BundleProjectManager bundleProjectManager,
            ProjectIIManager projectIIManager) {
        return new UpdateBundleProjectOnBundleClosed(
                bundleManager, bundleProjectManager, projectIIManager);
    }
}
