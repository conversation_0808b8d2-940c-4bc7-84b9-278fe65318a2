package com.bees360.bundle.config;

import com.bees360.bundle.Message;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class BundleProperties {

    private CreationProperties creation = new CreationProperties();
    private StatusProperties status = new StatusProperties();
    private BundleDeliveryProperties delivery = new BundleDeliveryProperties();

    @Data
    public static class CreationProperties {
        private Map<Integer, String> errorCodeMessageMapping;
        private String defaultErrorMessage =
                "Network Issue. Please try again. If the issue persists, contact customer support.";
    }

    @Data
    public static class StatusProperties {
        private Map<Message.BundleStatus, List<Message.BundleStatus>> statusTransitionMapping;
    }

    @Data
    public static class BundleDeliveryProperties {

        private String systemUserId;
        private ReportMergeProperties mergeReport = new ReportMergeProperties();

        @Data
        public static class ReportMergeProperties {
            private List<Message.BundleStatus> triggerStatuses = new ArrayList<>();
        }
    }
}
