package com.bees360.bundle.event;

import com.bees360.bundle.BundleStateManager;
import com.bees360.bundle.Message;
import com.bees360.event.registry.BundleStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Objects;

@Log4j2
public class UpdateBundleStateOnBundleReturnToClient
        extends AbstractNamedEventListener<BundleStatusChanged> {
    private final BundleStateManager bundleStateManager;

    public UpdateBundleStateOnBundleReturnToClient(BundleStateManager bundleStateManager) {
        this.bundleStateManager = bundleStateManager;
        log.info("Created {}(bundleManager={}).", this, this.bundleStateManager);
    }

    @Override
    public void handle(BundleStatusChanged event) throws IOException {
        log.debug("Received bundle status changed event: {}, start to update bundle state.", event);
        if (!Objects.equals(event.getStatus(), Message.BundleStatus.RETURNED_TO_CLIENT.name())) {
            return;
        }
        bundleStateManager.changeState(
                event.getBundleId(), Message.BundleState.CLOSE, "COMPLETED", event.getUpdatedBy());
    }
}
