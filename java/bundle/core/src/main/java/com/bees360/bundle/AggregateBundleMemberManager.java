package com.bees360.bundle;

import com.bees360.project.member.RoleEnum;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.stream.Collectors;

@Log4j2
public class AggregateBundleMemberManager implements BundleMemberManager {
    private final BundleMemberManager bundleMemberManager;
    private final UserProvider userProvider;

    public AggregateBundleMemberManager(
            BundleMemberManager bundleMemberManager, UserProvider userProvider) {
        this.bundleMemberManager = bundleMemberManager;
        this.userProvider = userProvider;
        log.info(
                "Created {}(bundleMemberManager={}, userProvider={}).",
                this,
                this.bundleMemberManager,
                this.userProvider);
    }

    @Override
    public boolean setMember(String bundleId, RoleEnum role, String userId, String opUserId) {
        log.debug(
                "Setting bundle member: bundleId {}, role {}, userId {}, opUserId {}",
                bundleId,
                role,
                userId,
                opUserId);
        return bundleMemberManager.setMember(bundleId, role, userId, opUserId);
    }

    @Override
    public List<Message.BundleMessage.Member> findByBundleId(String bundleId) {
        log.debug("Finding bundle member by bundleId: {}", bundleId);
        var members = bundleMemberManager.findByBundleId(bundleId);
        var memberIdList = Iterables.toStream(members).map(m -> m.getUser().getId()).toList();
        Iterable<? extends User> user;
        try {
            user = userProvider.findUserById(memberIdList);
        } catch (Exception e) {
            log.error("Failed to find user by id.", e);
            return members;
        }
        var memberMap =
                Iterables.toStream(user).collect(Collectors.toMap(User::getId, User::toMessage));
        return Iterables.toStream(members)
                .map(
                        m -> {
                            var filledMember = m.toBuilder();
                            var filledUser = memberMap.get(m.getUser().getId());
                            if (filledUser == null) {
                                throw new IllegalStateException(
                                        String.format(
                                                "User %s not found for bundle %s member %s.",
                                                m.getUser().getId(), bundleId, m.getRole()));
                            }
                            return filledMember.setUser(filledUser).build();
                        })
                .toList();
    }
}
