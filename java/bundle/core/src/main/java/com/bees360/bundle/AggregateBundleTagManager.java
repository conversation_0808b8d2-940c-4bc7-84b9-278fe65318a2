package com.bees360.bundle;

import com.bees360.bundle.Message.SyncBundleProjectResponse;
import com.bees360.project.tag.Message;
import com.bees360.project.tag.ProjectTag;
import com.bees360.status.Message.StatusMessage;

import lombok.extern.log4j.Log4j2;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
public class AggregateBundleTagManager implements BundleTagManager {

    private static final String WEB_REQUEST_VIA = "WEB";
    private static final String AI_REQUEST_VIA = "AI";
    private final BundleTagManager bundleTagManager;
    private final BundleProjectManager bundleProjectManager;

    public AggregateBundleTagManager(
            BundleTagManager bundleTagManager, BundleProjectManager bundleProjectManager) {
        this.bundleTagManager = bundleTagManager;
        this.bundleProjectManager = bundleProjectManager;
        log.info(
                "Created {}(bundleTagManager={}, bundleProjectManager={}).",
                this,
                this.bundleTagManager,
                this.bundleProjectManager);
    }

    @Override
    public int updateBundleTag(
            String bundleId,
            List<String> tagIds,
            Message.ProjectTagType type,
            String requestBy,
            String requestVia) {
        log.debug(
                "Updating Bundle Tag with bundleId: {}, tagIds: {}, type: {}, requestBy: {},"
                        + " requestVia: {}.",
                bundleId,
                tagIds,
                type,
                requestBy,
                requestVia);
        return bundleTagManager.updateBundleTag(bundleId, tagIds, type, requestBy, requestVia);
    }

    @Override
    public SyncBundleProjectResponse syncBundleTag(String bundleId, String requestBy) {
        log.debug("Sync bundle tag for {} by {}.", bundleId, requestBy);
        var unknownTags =
                bundleTagManager.findByBundleId(bundleId, null, Message.ProjectTagType.UNKNOWN);
        var uwTags =
                bundleTagManager.findByBundleId(
                        bundleId, null, Message.ProjectTagType.UNDERWRITING);
        var projectResponseList = new ArrayList<SyncBundleProjectResponse.SyncProjectResponse>();
        var syncUnknownTagRes =
                bundleProjectManager
                        .updateProjectTag(
                                bundleId,
                                unknownTags,
                                Message.ProjectTagType.UNKNOWN,
                                requestBy,
                                WEB_REQUEST_VIA)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        SyncBundleProjectResponse.SyncProjectResponse::getProjectId,
                                        SyncBundleProjectResponse.SyncProjectResponse::toBuilder));
        var syncUWTagRes =
                bundleProjectManager
                        .updateProjectTag(
                                bundleId,
                                uwTags,
                                Message.ProjectTagType.UNDERWRITING,
                                requestBy,
                                AI_REQUEST_VIA)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        SyncBundleProjectResponse.SyncProjectResponse::getProjectId,
                                        SyncBundleProjectResponse.SyncProjectResponse::toBuilder));
        for (var projectId : syncUnknownTagRes.keySet()) {
            var unknownTagRes =
                    syncUnknownTagRes.getOrDefault(
                            projectId,
                            SyncBundleProjectResponse.SyncProjectResponse.getDefaultInstance()
                                    .toBuilder());
            var uwTagRes =
                    syncUWTagRes.getOrDefault(
                            projectId,
                            SyncBundleProjectResponse.SyncProjectResponse.getDefaultInstance()
                                    .toBuilder());
            if (!uwTagRes.getStatus().getCode().equals(StatusMessage.Code.OK)) {
                unknownTagRes.setStatus(uwTagRes.getStatus());
            }
            projectResponseList.add(uwTagRes.build());
        }
        return com.bees360.bundle.Message.SyncBundleProjectResponse.getDefaultInstance().toBuilder()
                .addAllResult(projectResponseList)
                .build();
    }

    @Override
    public List<ProjectTag> findByBundleId(
            String bundleId, @Nullable String companyId, @Nullable Message.ProjectTagType type) {
        log.debug(
                "Finding Bundle Tag with bundleId: {}, companyId: {}, type: {}.",
                bundleId,
                companyId,
                type);
        return bundleTagManager.findByBundleId(bundleId, companyId, type);
    }
}
