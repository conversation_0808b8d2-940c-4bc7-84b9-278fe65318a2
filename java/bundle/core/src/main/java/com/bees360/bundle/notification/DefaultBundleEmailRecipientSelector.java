package com.bees360.bundle.notification;

import com.bees360.bundle.Message;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class DefaultBundleEmailRecipientSelector implements BundleEmailRecipientSelector {

    @Override
    public Collection<String> select(
            Message.BundleMessage bundle, Collection<RecipientSelector> selector) {
        if (selector == null) {
            return List.of();
        }
        List<String> result = new ArrayList<>();
        for (RecipientSelector recipientSelector : selector) {
            switch (recipientSelector.getType()) {
                case "email" -> result.add(recipientSelector.getValue());
                case "member" -> bundle.getMemberList().stream()
                        .filter(m -> m.getRole().equals(recipientSelector.getValue()))
                        .findFirst()
                        .ifPresent(m -> result.add(m.getUser().getEmail()));
                case "contact" -> bundle.getContactList().stream()
                        .filter(
                                c ->
                                        c.getRole().getValue().equals(recipientSelector.getValue())
                                                && c.getIsPrimary().getValue())
                        .findFirst()
                        .ifPresent(c -> result.add(c.getEmail().getValue()));
            }
        }
        return result;
    }
}
