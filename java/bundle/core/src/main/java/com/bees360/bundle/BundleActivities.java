package com.bees360.bundle;

import com.bees360.activity.Activity;
import com.bees360.activity.Comment;
import com.bees360.activity.Message;
import com.bees360.user.Message.UserMessage;
import com.bees360.util.DateTimes;
import com.bees360.util.Functions;

import java.time.Instant;
import java.util.List;
import java.util.function.Function;

public class BundleActivities {

    public static final String ACTIVITY_SOURCE_WEB = "WEB";
    public static final String ACTIVITY_ENTITY_TYPE_BUNDLE = "BUNDLE";
    public static final String ACTIVITY_FIELD_NAME_INSPECTION_NUMBER = "INSPECTION_NUMBER";
    public static final String ACTIVITY_FIELD_NAME_POLICY_NUMBER = "POLICY_NUMBER";
    public static final String ACTIVITY_FIELD_NAME_POLICY_RENEWAL = "POLICY_RENEWAL";
    public static final String ACTIVITY_FIELD_NAME_POLICY_TYPE = "POLICY_TYPE";
    public static final String ACTIVITY_FIELD_NAME_PROPERTY_TYPE = "PROPERTY_TYPE";
    public static final String ACTIVITY_FIELD_NAME_YEAR_BUILT = "YEAR_BUILT";
    public static final String ACTIVITY_FIELD_NAME_CONTACT = "CONTACT";
    public static final String ACTIVITY_FIELD_NAME_SERVICE_TYPE = "SERVICE_TYPE";
    public static final String ACTIVITY_FIELD_NAME_BUNDLE_PROJECT = "BUNDLE_PROJECT";
    public static final String ACTIVITY_FIELD_DISPLAY_NAME_INSPECTION_NUMBER = "inspection number";
    public static final String ACTIVITY_FIELD_DISPLAY_NAME_POLICY_NUMBER = "policy number";
    public static final String ACTIVITY_FIELD_DISPLAY_NAME_POLICY_RENEWAL = "renewal";
    public static final String ACTIVITY_FIELD_DISPLAY_NAME_POLICY_TYPE = "policy type";
    public static final String ACTIVITY_FIELD_DISPLAY_NAME_PROPERTY_TYPE = "property type";
    public static final String ACTIVITY_FIELD_DISPLAY_NAME_YEAR_BUILT = "year built";
    public static final String ACTIVITY_FIELD_DISPLAY_NAME_PATTERN_CONTACT_NAME =
            "contact name of %s";
    public static final String ACTIVITY_FIELD_DISPLAY_NAME_PATTERN_CONTACT_PHONE =
            "contact phone of %s";
    public static final String ACTIVITY_FIELD_DISPLAY_NAME_PATTERN_CONTACT_EMAIL =
            "contact email of %s";

    public static <T> Activity buildFieldChangedActivity(
            String bundleId,
            String fieldName,
            String fieldDisplayName,
            Message.ActivityMessage.FieldType fieldType,
            T oldValue,
            T newValue,
            Function<T, String> valueMapper,
            String updatedBy,
            Instant updatedAt,
            String source) {
        return buildFieldChangedActivity(
                bundleId,
                fieldName,
                fieldDisplayName,
                fieldType,
                oldValue,
                newValue,
                valueMapper,
                updatedBy,
                updatedAt,
                source,
                null);
    }

    public static <T> Activity buildFieldChangedActivity(
            String bundleId,
            String fieldName,
            String fieldDisplayName,
            Message.ActivityMessage.FieldType fieldType,
            T oldValue,
            T newValue,
            Function<T, String> valueMapper,
            String updatedBy,
            Instant updatedAt,
            String source,
            Message.CommentMessage comment) {

        var activityBuilder = Message.ActivityMessage.newBuilder();

        var entityBuilder =
                Message.ActivityMessage.Entity.newBuilder()
                        .setId(bundleId)
                        .setCount(1)
                        .setType(ACTIVITY_ENTITY_TYPE_BUNDLE);

        var fieldBuilder =
                Message.ActivityMessage.Field.newBuilder()
                        .setName(fieldName)
                        .setType(fieldType.name());
        Functions.acceptIfNotNull(fieldBuilder::setDisplayName, fieldDisplayName);
        Functions.acceptIfNotNull(fieldBuilder::setOldValue, oldValue, valueMapper);
        Functions.acceptIfNotNull(fieldBuilder::setValue, newValue, valueMapper);

        Functions.acceptIfNotNull(activityBuilder::setComment, comment);

        return Activity.from(
                activityBuilder
                        .setBundleId(Long.parseLong(bundleId))
                        .setEntity(entityBuilder.build())
                        .setField(fieldBuilder.build())
                        .setCreatedAt(DateTimes.toTimestamp(updatedAt))
                        .setCreatedBy(UserMessage.newBuilder().setId(updatedBy).build())
                        .setAction(Message.ActivityMessage.ActionType.CHANGE.name())
                        .setSource(source)
                        .setScopeType(Message.ScopeType.BUNDLE)
                        .build());
    }

    public static Activity buildCreatedActivity(
            String bundleId, String createdBy, Instant createdAt, String source) {

        return Activity.from(
                Message.ActivityMessage.newBuilder()
                        .setAction(Message.ActivityMessage.ActionType.CREATE.name())
                        .setEntity(
                                Message.ActivityMessage.Entity.newBuilder()
                                        .setId(bundleId)
                                        .setType(ACTIVITY_ENTITY_TYPE_BUNDLE)
                                        .setCount(1)
                                        .build())
                        .setBundleId(Long.parseLong(bundleId))
                        .setCreatedBy(UserMessage.newBuilder().setId(createdBy).build())
                        .setCreatedAt(DateTimes.toTimestamp(createdAt))
                        .setSource(source)
                        .setScopeType(Message.ScopeType.BUNDLE)
                        .build());
    }

    public static Activity buildMemberChangedActivity(
            String bundleId,
            String role,
            String newUserId,
            String createdBy,
            Instant createdAt,
            String source) {
        return Activity.from(
                Message.ActivityMessage.newBuilder()
                        .setBundleId(Long.parseLong(bundleId))
                        .setAction(Message.ActivityMessage.ActionType.CHANGE.name())
                        .setEntity(
                                Message.ActivityMessage.Entity.newBuilder()
                                        .setType(Message.ActivityMessage.EntityType.MEMBER.name())
                                        .setId(newUserId)
                                        .build())
                        .setField(
                                Message.ActivityMessage.Field.newBuilder()
                                        .setType(Message.ActivityMessage.FieldType.STRING.name())
                                        .setValue(role)
                                        .build())
                        .setCreatedBy(UserMessage.newBuilder().setId(createdBy).build())
                        .setCreatedAt(DateTimes.toTimestamp(createdAt))
                        .setSource(source)
                        .setScopeType(Message.ScopeType.BUNDLE)
                        .build());
    }

    public static Activity buildTagChangedActivity(
            String bundleId,
            List<String> tagTitleList,
            String createdBy,
            Instant createdAt,
            Message.ActivityMessage.ActionType actionType,
            String source) {
        String tags = String.join(";;", tagTitleList);
        return Activity.from(
                Message.ActivityMessage.newBuilder()
                        .setBundleId(Long.parseLong(bundleId))
                        .setAction(actionType.name())
                        .setEntity(
                                Message.ActivityMessage.Entity.newBuilder()
                                        .setId(bundleId)
                                        .setType(ACTIVITY_ENTITY_TYPE_BUNDLE)
                                        .build())
                        .setField(
                                Message.ActivityMessage.Field.newBuilder()
                                        .setName(Message.ActivityMessage.FieldName.TAG.name())
                                        .setType(Message.ActivityMessage.FieldType.STRING.name())
                                        .setValue(tags)
                                        .build())
                        .setSource(source)
                        .setScopeType(Message.ScopeType.BUNDLE)
                        .setCreatedBy(UserMessage.newBuilder().setId(createdBy).build())
                        .setCreatedAt(DateTimes.toTimestamp(createdAt))
                        .build());
    }

    public static Activity buildBundleProjectChangedActivity(
            String bundleId,
            List<String> projectIdList,
            String createdBy,
            Instant createdAt,
            Message.ActivityMessage.ActionType actionType,
            String source) {
        String projects = String.join(";;", projectIdList);
        return Activity.from(
                Message.ActivityMessage.newBuilder()
                        .setBundleId(Long.parseLong(bundleId))
                        .setAction(actionType.name())
                        .setEntity(
                                Message.ActivityMessage.Entity.newBuilder()
                                        .setId(bundleId)
                                        .setType(ACTIVITY_ENTITY_TYPE_BUNDLE)
                                        .build())
                        .setField(
                                Message.ActivityMessage.Field.newBuilder()
                                        .setName(ACTIVITY_FIELD_NAME_BUNDLE_PROJECT)
                                        .setType(Message.ActivityMessage.FieldType.STRING.name())
                                        .setValue(projects)
                                        .build())
                        .setSource(source)
                        .setScopeType(Message.ScopeType.BUNDLE)
                        .setCreatedBy(UserMessage.newBuilder().setId(createdBy).build())
                        .setCreatedAt(DateTimes.toTimestamp(createdAt))
                        .build());
    }

    public static Comment buildComment(
            String bundleId, String content, String createdBy, Instant createdAt, String source) {
        return Comment.from(
                Message.CommentMessage.newBuilder()
                        .setBundleId(Long.parseLong(bundleId))
                        .setCreatedBy(UserMessage.newBuilder().setId(createdBy).build())
                        .setContent(content)
                        .setCreatedAt(DateTimes.toTimestamp(createdAt))
                        .setSource(source)
                        .setScopeType(Message.ScopeType.BUNDLE)
                        .build());
    }
}
