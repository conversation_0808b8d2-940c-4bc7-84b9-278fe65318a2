package com.bees360.bundle.config;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.CommentManager;
import com.bees360.address.AddressProvider;
import com.bees360.api.ApiStatus;
import com.bees360.apolloconfig.annotation.RefreshableConfigurationProperties;
import com.bees360.bundle.AggregateBundleManager;
import com.bees360.bundle.AggregateBundleMemberManager;
import com.bees360.bundle.AggregateBundleStateManager;
import com.bees360.bundle.AggregateBundleTagManager;
import com.bees360.bundle.BundleManager;
import com.bees360.bundle.BundleProjectManager;
import com.bees360.bundle.BundleTagManager;
import com.bees360.bundle.GrpcBundleService;
import com.bees360.bundle.GrpcBundleTagService;
import com.bees360.bundle.JooqBundleManager;
import com.bees360.bundle.JooqBundleMemberManager;
import com.bees360.bundle.JooqBundleStateManager;
import com.bees360.bundle.JooqBundleTagManager;
import com.bees360.bundle.Message;
import com.bees360.bundle.report.BundleReportProcessor;
import com.bees360.bundle.report.DefaultBundleReportProcessor;
import com.bees360.bundle.validator.BundleCreationAddressValidator;
import com.bees360.bundle.validator.BundleCreationBasicInfoValidator;
import com.bees360.bundle.validator.BundleCreationContactValidator;
import com.bees360.bundle.validator.BundleCreationContractValidator;
import com.bees360.contact.PrimaryContactRecordProvider;
import com.bees360.contract.ContractRepository;
import com.bees360.customer.CustomerProvider;
import com.bees360.policy.PolicyManager;
import com.bees360.project.BuildingManager;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.ProjectPolicyManager;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.group.ProjectGroupProvider;
import com.bees360.project.member.MemberManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.state.StateChangeReasonGroupProvider;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.project.tag.ProjectTagManager;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportProcessor;
import com.bees360.status.Message.StatusMessage;
import com.bees360.user.UserProvider;

import org.apache.commons.lang3.function.TriFunction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.function.Predicate;

@Import({
    GrpcBundleService.class,
    GrpcBundleTagService.class,
    JooqBundleManagerConfig.class,
    JooqBundleTagManagerConfig.class,
})
@Configuration
@EnableConfigurationProperties
public class BundleServiceConfig {

    @Bean
    @RefreshableConfigurationProperties(prefix = "bundle")
    public BundleProperties bundleProperties() {
        return new BundleProperties();
    }

    @Bean
    public Function<ApiStatus, String> bundleCreationExceptionMessageResolver(
            BundleProperties properties) {
        return status -> {
            var creationProperties = properties.getCreation();
            if (StatusMessage.Code.UNKNOWN.equals(status.getCode())) {
                return creationProperties.getDefaultErrorMessage();
            }
            return creationProperties
                    .getErrorCodeMessageMapping()
                    .getOrDefault(status.getCode().getNumber(), status.getDescription());
        };
    }

    @Bean
    public Predicate<Message.CreateBundleRequest> bundleCreationValidator(
            CustomerProvider customerProvider,
            ContractRepository jooqContractRepository,
            @Qualifier("policyTypePropertyTypeValidator")
                    TriFunction<String, String, Integer, Integer> validator) {

        var basicInfoValidator = new BundleCreationBasicInfoValidator(validator);
        var addressValidator = new BundleCreationAddressValidator();
        var contactValidator = new BundleCreationContactValidator();
        var contractValidator =
                new BundleCreationContractValidator(customerProvider, jooqContractRepository);

        return creationRequest ->
                basicInfoValidator.test(creationRequest)
                        && addressValidator.test(creationRequest)
                        && contactValidator.test(creationRequest)
                        && contractValidator.test(creationRequest);
    }

    @Bean
    BundleProjectManager bundleProjectManager(
            ProjectGroupProvider projectGroupProvider,
            ProjectIIManager projectIIManager,
            PolicyManager jooqFullPolicyManager,
            BuildingManager buildingManager,
            ProjectPolicyManager projectPolicyManager,
            ProjectStatusManager projectStatusManager,
            ProjectTagManager projectTagManager,
            MemberManager userFillProjectMemberManager,
            MemberManager grpcMemberManager,
            ContactManager contactManager,
            ProjectStateManager grpcProjectStateManager) {
        return new BundleProjectManager(
                projectGroupProvider,
                projectIIManager,
                jooqFullPolicyManager,
                buildingManager,
                projectPolicyManager,
                projectStatusManager,
                projectTagManager,
                userFillProjectMemberManager,
                grpcMemberManager,
                contactManager,
                grpcProjectStateManager);
    }

    @Bean
    public BiPredicate<Message.BundleStatus, Message.BundleStatus> bundleStatusTransitionValidator(
            BundleProperties bundleProperties) {
        var statusTransitionMapping = bundleProperties.getStatus().getStatusTransitionMapping();
        return (currentStatus, targetStatus) -> {
            var allowedStatusList = statusTransitionMapping.getOrDefault(currentStatus, List.of());
            return allowedStatusList.contains(targetStatus);
        };
    }

    @Bean(name = {"bundleManager", "grpcBundleManager"})
    public AggregateBundleManager bundleManager(
            JooqBundleManager jooqBundleManager,
            @Qualifier("bundleCreationValidator")
                    Predicate<Message.CreateBundleRequest> bundleCreationValidator,
            @Qualifier("bundleCreationExceptionMessageResolver")
                    Function<ApiStatus, String> bundleCreationExceptionMessageResolver,
            ProjectGroupManager projectGroupManager,
            AddressProvider addressProvider,
            ProjectIIRepository projectIIRepository,
            ContractRepository jooqContractRepository,
            BundleProjectManager bundleProjectManager,
            CommentManager commentManager,
            @Qualifier("bundleStatusTransitionValidator")
                    BiPredicate<Message.BundleStatus, Message.BundleStatus>
                            bundleStatusTransitionValidator,
            BundleTagManager bundleTagManager,
            ContactManager contactManager,
            PrimaryContactRecordProvider primaryContactRecordProvider,
            ActivityManager activityManager) {
        return new AggregateBundleManager(
                jooqBundleManager,
                bundleCreationValidator,
                bundleCreationExceptionMessageResolver,
                projectGroupManager,
                addressProvider,
                projectIIRepository,
                jooqContractRepository,
                bundleProjectManager,
                commentManager,
                bundleStatusTransitionValidator,
                bundleTagManager,
                contactManager,
                primaryContactRecordProvider,
                activityManager);
    }

    @Bean(name = {"bundleTagManager", "grpcBundleTagManager"})
    public AggregateBundleTagManager bundleTagManager(
            JooqBundleTagManager jooqBundleTagManager, BundleProjectManager bundleProjectManager) {
        return new AggregateBundleTagManager(jooqBundleTagManager, bundleProjectManager);
    }

    @Bean(name = {"bundleMemberManager", "grpcBundleMemberManager"})
    public AggregateBundleMemberManager aggregateBundleMemberManager(
            JooqBundleMemberManager jooqBundleMemberManager, UserProvider userProvider) {
        return new AggregateBundleMemberManager(jooqBundleMemberManager, userProvider);
    }

    @Bean
    public BundleReportProcessor bundleReportProcessor(
            ReportProcessor reportProcessor, ReportManager reportManager) {
        return new DefaultBundleReportProcessor(reportProcessor, reportManager);
    }

    @Bean(name = {"bundleStateManager", "grpcBundleStateManager"})
    public AggregateBundleStateManager aggregateBundleStateManager(
            JooqBundleStateManager jooqBundleStateManager,
            StateChangeReasonGroupProvider stateChangeReasonGroupProvider,
            BundleManager bundleManager) {
        return new AggregateBundleStateManager(
                jooqBundleStateManager, stateChangeReasonGroupProvider, bundleManager);
    }
}
