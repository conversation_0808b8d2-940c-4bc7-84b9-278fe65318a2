package com.bees360.bundle.notification;

import com.bees360.bundle.Message;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Collection;

public interface BundleEmailRecipientSelector {
    @Getter
    @Setter
    @Accessors(chain = true)
    class RecipientSelector {
        private String type;
        private String value;
    }

    Collection<String> select(Message.BundleMessage bundle, Collection<RecipientSelector> selector);
}
