package com.bees360.bundle.event;

import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_DISPLAY_NAME_POLICY_TYPE;
import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_POLICY_TYPE;
import static com.bees360.bundle.BundleActivities.ACTIVITY_SOURCE_WEB;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.function.Function;

/** 监听BundleChanged事件，当策略类型发生变化时提交活动记录。 */
@Log4j2
public class SubmitPolicyTypeChangedActivityOnBundleChanged
        extends AbstractNamedEventListener<BundleChanged> {

    private final ActivityManager activityManager;

    public SubmitPolicyTypeChangedActivityOnBundleChanged(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={}).", this, this.activityManager);
    }

    @Override
    public void handle(BundleChanged event) throws IOException {
        log.debug("Received bundle changed event {}.", event);
        var oldBundleMeta = event.getOldValue().getMetadata();
        var oldPolicyType =
                oldBundleMeta.getPolicy().hasType()
                        ? oldBundleMeta.getPolicy().getType().getValue()
                        : null;
        var newBundle = event.getNewValue();
        var newBundleMeta = event.getNewValue().getMetadata();
        var newPolicyType =
                newBundleMeta.getPolicy().hasType()
                        ? newBundleMeta.getPolicy().getType().getValue()
                        : null;

        if (StringUtils.equals(oldPolicyType, newPolicyType)) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        newBundle.getId(),
                        ACTIVITY_FIELD_NAME_POLICY_TYPE,
                        ACTIVITY_FIELD_DISPLAY_NAME_POLICY_TYPE,
                        Message.ActivityMessage.FieldType.STRING,
                        oldPolicyType,
                        newPolicyType,
                        Function.identity(),
                        newBundle.getUpdatedBy(),
                        newBundle.getUpdatedAt(),
                        ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle policy type changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
