package com.bees360.bundle.event;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleTagAdded;
import com.bees360.event.registry.BundleTagChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagRepository;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;

/** 监听BundleTagAdded事件，处理标签添加操作并提交相关活动记录。 */
@Log4j2
public class SubmitActivityOnBundleTagAdded extends AbstractNamedEventListener<BundleTagAdded> {

    private final ActivityManager activityManager;
    private final ProjectTagRepository projectTagRepository;

    public SubmitActivityOnBundleTagAdded(
            ActivityManager activityManager, ProjectTagRepository projectTagRepository) {
        this.activityManager = activityManager;
        this.projectTagRepository = projectTagRepository;
        log.info(
                "Created {}(activityManager={}, projectTagRepository={}).",
                this,
                this.activityManager,
                this.projectTagRepository);
    }

    @Override
    public void handle(BundleTagAdded event) throws IOException {
        log.debug("Received {}.", event);
        var tags = event.getList();
        if (CollectionUtils.isEmpty(tags)) {
            return;
        }

        var firstTag = tags.get(0);
        var bundleId = firstTag.getBundleId();
        var updatedBy = firstTag.getUpdatedBy();
        var updatedAt = firstTag.getUpdatedAt();
        var updatedVia = firstTag.getUpdatedVia();

        var tagIds = tags.stream().map(BundleTagChanged.BundleTagChangedObject::getTagId).toList();
        var tagNameList =
                projectTagRepository.findAllById(tagIds).stream()
                        .map(ProjectTag::getTitle)
                        .toList();

        var activity =
                BundleActivities.buildTagChangedActivity(
                        bundleId,
                        tagNameList,
                        updatedBy,
                        updatedAt,
                        Message.ActivityMessage.ActionType.CREATE,
                        updatedVia);
        log.debug("Submit bundle tag added activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
