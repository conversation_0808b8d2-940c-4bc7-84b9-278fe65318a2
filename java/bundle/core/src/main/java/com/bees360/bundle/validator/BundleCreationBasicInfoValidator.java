package com.bees360.bundle.validator;

import com.bees360.building.Message.BuildingType;
import com.bees360.bundle.Message;
import com.bees360.project.Message.ServiceType;
import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.function.TriFunction;

import java.util.function.Predicate;

@Log4j2
public class BundleCreationBasicInfoValidator implements Predicate<Message.CreateBundleRequest> {

    private final TriFunction<String, String, Integer, Integer> validator;

    public BundleCreationBasicInfoValidator(
            TriFunction<String, String, Integer, Integer> validator) {
        this.validator = validator;
        log.info("Created {}(validator={}).", this, this.validator);
    }

    @Override
    public boolean test(Message.CreateBundleRequest request) {
        log.debug("Validating basic info for bundle creation request: {}", request);
        Preconditions.checkArgument(
                !ServiceType.UNRECOGNIZED.equals(request.getServiceType()),
                "Create bundle failed: Service type can not be null.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(request.getPolicyNo().getValue()),
                "Create bundle failed: Policy number can not be empty.");
        Preconditions.checkArgument(
                request.hasIsRenewal(), "Create bundle failed: Policy renewal can not be null.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(request.getPolicyType().getValue()),
                "Create bundle failed: Missing Policy Type. Please fill in and try again.");
        Preconditions.checkArgument(
                !BuildingType.UNRECOGNIZED.equals(request.getTypeOfProperty()),
                "Create bundle failed: Missing Type of Property. Please fill in and try again.");

        validator.apply(
                request.getCreationChannel().getValue(),
                request.getPolicyType().getValue(),
                request.getTypeOfProperty().getNumber());

        Preconditions.checkArgument(
                request.hasNumberOfBuildings(),
                "Create bundle failed: Number of Buildings can not be null.");
        Preconditions.checkArgument(
                request.getBuildingAddressCount() > 0,
                "Create bundle failed: At least one address is required.");
        Preconditions.checkArgument(
                request.hasInsuredBy(), "Create bundle failed: Insurance Company can not be null.");
        Preconditions.checkArgument(
                request.hasProcessedBy(), "Create bundle failed: Process Company can not be null.");

        return true;
    }
}
