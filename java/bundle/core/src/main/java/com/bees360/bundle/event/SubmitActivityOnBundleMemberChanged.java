package com.bees360.bundle.event;

import com.bees360.activity.ActivityManager;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleMemberChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Optional;

/** 监听BundleMemberChanged事件，处理成员变更并提交相关活动记录 */
@Log4j2
public class SubmitActivityOnBundleMemberChanged
        extends AbstractNamedEventListener<BundleMemberChanged> {

    private final ActivityManager activityManager;

    public SubmitActivityOnBundleMemberChanged(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={})", this, this.activityManager);
    }

    @Override
    public void handle(BundleMemberChanged event) throws IOException {
        log.debug("Received {}", event);
        var bundleId = event.getBundleId();
        var memberRole = event.getNewValue().getRole();

        var oldUserId =
                Optional.ofNullable(event.getOldValue())
                        .map(BundleMemberChanged.BundleMember::getUserId)
                        .orElse(null);
        var newUserId = event.getNewValue().getUserId();

        if (StringUtils.equals(oldUserId, newUserId)) {
            return;
        }

        var activity =
                BundleActivities.buildMemberChangedActivity(
                        bundleId,
                        memberRole,
                        newUserId,
                        event.getNewValue().getUpdatedBy(),
                        event.getNewValue().getUpdatedAt(),
                        BundleActivities.ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle member changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
