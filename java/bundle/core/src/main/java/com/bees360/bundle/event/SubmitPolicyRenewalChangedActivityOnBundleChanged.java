package com.bees360.bundle.event;

import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_DISPLAY_NAME_POLICY_RENEWAL;
import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_POLICY_RENEWAL;
import static com.bees360.bundle.BundleActivities.ACTIVITY_SOURCE_WEB;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Objects;

/** 监听保单续保状态变更事件并提交相应的活动记录 */
@Log4j2
public class SubmitPolicyRenewalChangedActivityOnBundleChanged
        extends AbstractNamedEventListener<BundleChanged> {

    private final ActivityManager activityManager;

    public SubmitPolicyRenewalChangedActivityOnBundleChanged(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={}).", this, this.activityManager);
    }

    @Override
    public void handle(BundleChanged event) throws IOException {
        log.debug("Received bundle changed event {}.", event);
        var oldBundleMeta = event.getOldValue().getMetadata();
        var oldPolicyRenewal =
                oldBundleMeta.getPolicy().hasIsRenewal()
                        ? oldBundleMeta.getPolicy().getIsRenewal().getValue()
                        : null;
        var newBundle = event.getNewValue();
        var newBundleMeta = event.getNewValue().getMetadata();
        var newPolicyRenewal =
                newBundleMeta.getPolicy().hasIsRenewal()
                        ? newBundleMeta.getPolicy().getIsRenewal().getValue()
                        : null;

        if (Objects.equals(oldPolicyRenewal, newPolicyRenewal)) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        event.getNewValue().getId(),
                        ACTIVITY_FIELD_NAME_POLICY_RENEWAL,
                        ACTIVITY_FIELD_DISPLAY_NAME_POLICY_RENEWAL,
                        Message.ActivityMessage.FieldType.STRING,
                        oldPolicyRenewal,
                        newPolicyRenewal,
                        renewal -> renewal != null ? (renewal ? "Y" : "N") : null,
                        newBundle.getUpdatedBy(),
                        newBundle.getUpdatedAt(),
                        ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle policy renewal changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
