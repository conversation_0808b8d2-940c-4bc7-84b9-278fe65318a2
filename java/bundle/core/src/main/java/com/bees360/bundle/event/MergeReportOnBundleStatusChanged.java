package com.bees360.bundle.event;

import com.bees360.bundle.BundleManager;
import com.bees360.bundle.Message;
import com.bees360.bundle.report.BundleReportProcessor;
import com.bees360.customer.CustomerProvider;
import com.bees360.event.registry.BundleStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.Report;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
public class MergeReportOnBundleStatusChanged
        extends AbstractNamedEventListener<BundleStatusChanged> {

    private final CustomerProvider customerProvider;
    private final BundleManager bundleManager;
    private final ProjectReportProvider projectReportProvider;
    private final BundleReportProcessor bundleReportProcessor;
    private final List<Message.BundleStatus> triggerStatuses;
    private final String systemUserId;

    public MergeReportOnBundleStatusChanged(
            BundleManager bundleManager,
            BundleReportProcessor bundleReportProcessor,
            CustomerProvider customerProvider,
            ProjectReportProvider projectReportProvider,
            List<Message.BundleStatus> triggerStatuses,
            String systemUserId) {
        this.customerProvider = customerProvider;
        this.bundleManager = bundleManager;
        this.projectReportProvider = projectReportProvider;
        this.bundleReportProcessor = bundleReportProcessor;
        this.triggerStatuses = triggerStatuses;
        this.systemUserId = systemUserId;
        log.info(
                "Created {}(customerProvider={}, bundleReportProcessor={}).",
                this,
                this.customerProvider,
                this.bundleReportProcessor);
    }

    @Override
    public void handle(BundleStatusChanged event) throws IOException {
        String bundleId = event.getBundleId();
        var newStatus = Message.BundleStatus.valueOf(event.getStatus());

        if (!triggerStatuses.contains(newStatus)) {
            log.debug(
                    "Bundle status {} changed to {} will not trigger report merge.",
                    bundleId,
                    newStatus);
            return;
        }

        var bundle = bundleManager.findById(bundleId);
        var bundleProjectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        var insuredById = bundle.getContract().getInsuredBy().getId();
        var insuredBy = customerProvider.findById(insuredById);
        var bundleAttributes = insuredBy.toMessage().getAttributes().getBundle();
        var reportTypeCodes = bundleAttributes.getAutoMergeReport().getReportTypeList();

        if (CollectionUtils.isEmpty(reportTypeCodes)) {
            log.debug("Bundle status {} changed to {} will not merge report.", bundleId, newStatus);
            return;
        }

        var bundleProjectReports = projectReportProvider.findByProjectIds(bundleProjectIds, true);
        var bundleProjectReportsMap =
                bundleProjectReports.values().stream()
                        .flatMap(List::stream)
                        .filter(report -> reportTypeCodes.contains(report.getType()))
                        .collect(Collectors.groupingBy(Report::getType));
        for (String code : reportTypeCodes) {
            var projectReportIds =
                    bundleProjectReportsMap.get(code).stream().map(Report::getId).toList();
            log.info(
                    "Trigger merge {} report for bundle {} by {}, reportIds: {}.",
                    code,
                    bundleId,
                    systemUserId,
                    projectReportIds);
            bundleReportProcessor.mergeReport(bundleId, code, projectReportIds, systemUserId);
        }
    }
}
