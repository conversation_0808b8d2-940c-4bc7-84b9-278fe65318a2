package com.bees360.bundle;

import static com.bees360.bundle.JooqBundleManager.BUNDLE_PROJECT_GROUP_TYPE;
import static com.bees360.bundle.Message.BundleProjectSyncFieldType.CONTACT_AGENT;
import static com.bees360.bundle.Message.BundleProjectSyncFieldType.CONTACT_INSURED;
import static com.bees360.bundle.Message.BundleProjectSyncFieldType.CONTACT_PROPERTY_ACCESS_CONTACT;
import static com.bees360.bundle.Message.BundleStatus.CANCELLED;
import static com.bees360.bundle.Message.BundleStatus.CUSTOMER_CONTACTED;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.activity.Message.ActivityMessage.ActionType;
import com.bees360.activity.Message.ScopeType;
import com.bees360.address.AddressProvider;
import com.bees360.api.ApiStatus;
import com.bees360.bundle.Message.SyncBundleProjectResponse.SyncProjectResponse;
import com.bees360.contact.PrimaryContactRecord;
import com.bees360.contact.PrimaryContactRecordProvider;
import com.bees360.contact.PrimaryContactRecordQuery;
import com.bees360.contract.ContractRepository;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ContactQuery;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.group.ProjectGroup;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.util.DateTimes;
import com.bees360.util.Functions;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Log4j2
public class AggregateBundleManager implements BundleManager {

    private static final String WEB_ACTIVITY_SOURCE = "WEB";
    private final BundleManager bundleManager;
    private final Predicate<Message.CreateBundleRequest> bundleCreationValidator;
    private final Function<ApiStatus, String> creationExceptionMessageResolver;
    private final ProjectGroupManager projectGroupManager;
    private final AddressProvider addressProvider;
    private final ProjectIIRepository projectIIRepository;
    private final ContractRepository contractRepository;
    private final BundleProjectManager bundleProjectManager;
    private final CommentManager commentManager;
    private final BiPredicate<Message.BundleStatus, Message.BundleStatus>
            bundleStatusTransitionValidator;
    private final BundleTagManager bundleTagManager;

    /** The bean is used to query project primary contact. */
    private final ContactManager contactManager;

    /**
     * The bean is used to query project contact protocol (data of contact project primary contact).
     */
    private final PrimaryContactRecordProvider primaryContactRecordProvider;

    private final ActivityManager activityManager;

    public AggregateBundleManager(
            BundleManager bundleManager,
            Predicate<Message.CreateBundleRequest> bundleCreationValidator,
            Function<ApiStatus, String> bundleCreationExceptionMessageResolver,
            ProjectGroupManager projectGroupManager,
            AddressProvider addressProvider,
            ProjectIIRepository projectIIRepository,
            ContractRepository contractRepository,
            BundleProjectManager bundleProjectManager,
            CommentManager commentManager,
            BiPredicate<Message.BundleStatus, Message.BundleStatus> bundleStatusTransitionValidator,
            BundleTagManager bundleTagManager,
            ContactManager contactManager,
            PrimaryContactRecordProvider primaryContactRecordProvider,
            ActivityManager activityManager) {
        this.bundleManager = bundleManager;
        this.bundleCreationValidator = bundleCreationValidator;
        this.creationExceptionMessageResolver = bundleCreationExceptionMessageResolver;
        this.projectGroupManager = projectGroupManager;
        this.addressProvider = addressProvider;
        this.projectIIRepository = projectIIRepository;
        this.contractRepository = contractRepository;
        this.bundleProjectManager = bundleProjectManager;
        this.commentManager = commentManager;
        this.bundleStatusTransitionValidator = bundleStatusTransitionValidator;
        this.bundleTagManager = bundleTagManager;
        this.contactManager = contactManager;
        this.primaryContactRecordProvider = primaryContactRecordProvider;
        this.activityManager = activityManager;
        log.info(
                "Created {}(bundleManager={}, bundleCreationValidator={}, projectGroupManager={},"
                        + " addressProvider={}, projectIIRepository={},contractRepository={},"
                        + " bundleProjectManager={},"
                        + " commentManager={},bundleStatusTransitionValidator={},"
                        + " bundleTagManager={}, contactManager={},"
                        + " primaryContactRecordProvider={}).",
                this,
                this.bundleManager,
                this.bundleCreationValidator,
                this.projectGroupManager,
                this.addressProvider,
                this.projectIIRepository,
                this.contractRepository,
                this.bundleProjectManager,
                this.commentManager,
                this.bundleStatusTransitionValidator,
                this.bundleTagManager,
                this.contactManager,
                this.primaryContactRecordProvider);
    }

    @Override
    public Message.CreateBundleResponse createBundle(Message.CreateBundleRequest request) {
        log.debug("Create bundle from request: {}", request);
        bundleCreationValidator.test(request);
        var responseBuilder = bundleManager.createBundle(request).toBuilder();
        // add bundle attachment comment if bundle created
        if (responseBuilder.hasId()
                && request.hasAttachment()
                && CollectionUtils.isNotEmpty(request.getAttachment().getAttachmentList())) {
            var bundleId = responseBuilder.getId().getValue();
            commentManager.addComment(
                    mapToComment(
                            bundleId, request.getAttachment(), request.getCreateBy().getValue()));
        }
        // translate bundle creation exception
        var results =
                responseBuilder.getResultList().stream()
                        .map(
                                result -> {
                                    var builder = result.getStatus().toBuilder();
                                    builder.setDescription(
                                            creationExceptionMessageResolver.apply(
                                                    ApiStatus.fromMessage(result.getStatus())));
                                    return result.toBuilder().setStatus(builder).build();
                                })
                        .toList();
        log.debug(
                "Translate bundle creation results from {} to {}",
                responseBuilder.getResultList(),
                results);
        responseBuilder.clearResult().addAllResult(results);
        var result = responseBuilder.build();
        submitBundleProjectCreatedActivity(request.getCreateBy().getValue(), result);
        return result;
    }

    @Override
    public Message.BundleMessage findById(String id) {
        log.debug("Find bundle by id: {}", id);
        var bundle = bundleManager.findById(id);
        if (bundle == null) {
            return null;
        }
        var bundleBuilder = bundle.toBuilder();
        var address = addressProvider.findById(bundle.getAddress().getId());
        bundleBuilder.setAddress(address.toMessage());
        var contract = contractRepository.findById(bundle.getContract().getId());
        bundleBuilder.setContract(contract.toMessage());
        var projectGroup = projectGroupManager.findByGroupKey(id, BUNDLE_PROJECT_GROUP_TYPE);
        var bundleProjectIds = Iterables.toList(projectGroup.getProjectIds());
        var bundleProjects =
                Iterables.toStream(projectIIRepository.findAllById(projectGroup.getProjectIds()))
                        .map(ProjectII::toMessage)
                        .toList();
        bundleBuilder.addAllProject(bundleProjects);
        bundleBuilder.setContactProtocol(getBundleContactProtocol(bundleProjectIds));
        var totalLivingArea =
                bundleProjects.stream()
                        .mapToInt(p -> p.getPolicy().getBuilding().getLivingArea())
                        .sum();
        bundleBuilder.setLivingArea(Int32Value.of(totalLivingArea));
        return bundleBuilder.build();
    }

    @Override
    public List<Message.BundleMessage> findByQuery(Message.BundleRequestQuery query) {
        log.debug("Find bundle by query: {}", query);
        return bundleManager.findByQuery(query);
    }

    private Message.BundleContactProtocolMessage getBundleContactProtocol(
            List<String> bundleProjectIds) {
        var totalFullAttemptTimes = 0;
        var isSuccess = false;
        // Find all bundle project's primary contact.
        var primaryContacts = contactManager.findByQuery(new ContactQuery(bundleProjectIds, true));
        // Find all bundle project's primary contact record.
        var primaryContactRecords =
                primaryContactRecordProvider.findByQuery(
                        PrimaryContactRecordQuery.builder().projectId(bundleProjectIds).build());
        var primaryContactRecordsByProject =
                primaryContactRecords.stream()
                        .collect(Collectors.groupingBy(PrimaryContactRecord::getProjectId));
        for (Contact primaryContact : primaryContacts) {
            var projectId = primaryContact.getProjectId();
            var primaryContactRole =
                    Optional.ofNullable(ContactRoleEnum.getByName(primaryContact.getRole()))
                            .map(Enum::name)
                            .orElse("");
            var projectPrimaryContactRecords =
                    primaryContactRecordsByProject
                            .getOrDefault(projectId, Collections.emptyList())
                            .stream()
                            .filter(e -> primaryContactRole.equals(e.getToRole()))
                            .toList();
            totalFullAttemptTimes =
                    totalFullAttemptTimes
                            + projectPrimaryContactRecords.stream()
                                    .mapToInt(
                                            e ->
                                                    Optional.ofNullable(e.getFullAttemptTimes())
                                                            .orElse(0))
                                    .sum();
            isSuccess =
                    isSuccess
                            || projectPrimaryContactRecords.stream()
                                    .anyMatch(
                                            e -> Optional.ofNullable(e.isSuccess()).orElse(false));
        }
        return Message.BundleContactProtocolMessage.newBuilder()
                .setTimes(Int32Value.of(totalFullAttemptTimes))
                .setIsSuccess(BoolValue.of(isSuccess))
                .build();
    }

    @Override
    public boolean updateBundle(Message.UpdateBundleRequest request, String requestBy) {
        log.debug(
                "Update bundle field for {} by {}, with request {}.",
                request.getBundleIdList(),
                requestBy,
                request);
        var bundleIds = request.getBundleIdList();
        var query =
                Message.BundleRequestQuery.newBuilder()
                        .addAllBundleId(bundleIds)
                        .setLimit(bundleIds.size())
                        .build();
        var oldBundles =
                bundleManager.findByQuery(query).stream()
                        .collect(
                                Collectors.toMap(
                                        b -> b.getId().getValue(), b -> b, (b1, b2) -> b2));

        var result = bundleManager.updateBundle(request, requestBy);
        bundleUpdatePostProcess(request, oldBundles);
        return result;
    }

    private void bundleUpdatePostProcess(
            Message.UpdateBundleRequest request, Map<String, Message.BundleMessage> oldBundles) {
        // post process after update bundle
        var requestBy = request.getRequestBy().getValue();
        for (var bundleId : request.getBundleIdList()) {
            switch (request.getUpdateFieldCase()) {
                case SERVICE_TYPE -> {
                    var updateRequest = request.getServiceType();
                    var oldBundle = oldBundles.get(bundleId);
                    var oldServiceTypeEnum =
                            ServiceTypeEnum.valueOf(oldBundle.getServiceType().getNumber());
                    var serviceTypeEnum =
                            ServiceTypeEnum.valueOf(updateRequest.getValue().getNumber());
                    var updatedAt = Instant.now();
                    var comment =
                            BundleActivities.buildComment(
                                    bundleId,
                                    updateRequest.getReason().getValue(),
                                    requestBy,
                                    updatedAt,
                                    BundleActivities.ACTIVITY_SOURCE_WEB);
                    var activity =
                            BundleActivities.buildFieldChangedActivity(
                                    bundleId,
                                    BundleActivities.ACTIVITY_FIELD_NAME_SERVICE_TYPE,
                                    null,
                                    com.bees360.activity.Message.ActivityMessage.FieldType.STRING,
                                    oldServiceTypeEnum,
                                    serviceTypeEnum,
                                    ServiceTypeEnum::getName,
                                    requestBy,
                                    updatedAt,
                                    BundleActivities.ACTIVITY_SOURCE_WEB,
                                    comment.toMessage());
                    submitBundleActivity(activity);
                }
                default -> {
                    // do nothing here
                }
            }
        }
    }

    @Override
    public boolean updateBundlePolicy(Message.UpdateBundlePolicyRequest request, String requestBy) {
        log.debug(
                "Update bundle policy for {} by {}, with request {}.",
                request.getBundleIdList(),
                requestBy,
                request);
        return bundleManager.updateBundlePolicy(request, requestBy);
    }

    @Override
    public boolean saveBundleContact(Message.UpdateBundleContactRequest request, String requestBy) {
        log.debug(
                "Save bundle contact for {} by {}, with request {}.",
                request.getBundleIdList(),
                requestBy,
                request);
        return bundleManager.saveBundleContact(request, requestBy);
    }

    @Override
    public Message.SyncBundleProjectResponse syncBundleProject(
            Message.SyncBundleProjectRequest request, String requestBy) {
        log.debug(
                "Sync bundle project for {} by {}, with request {}.",
                request.getBundleId(),
                requestBy,
                request);
        var bundleId = request.getBundleId().getValue();
        var bundle = bundleManager.findById(bundleId);
        var projectResponseList = new ArrayList<SyncProjectResponse>();
        switch (request.getSyncField()) {
            case POLICY_NO -> {
                var policyNo = bundle.getPolicyNo().getValue();
                projectResponseList.addAll(
                        bundleProjectManager.updatePolicyNumber(bundleId, policyNo, requestBy));
            }
            case INSPECTION_NO -> {
                var inspectionNo = bundle.getInspectionNo().getValue();
                projectResponseList.addAll(
                        bundleProjectManager.updateInspectionNumber(
                                bundleId, inspectionNo, requestBy));
            }
            case SERVICE_TYPE -> {
                var serviceType = ServiceTypeEnum.valueOf(bundle.getServiceType().getNumber());
                projectResponseList.addAll(
                        bundleProjectManager.updateServiceType(bundleId, serviceType, requestBy));
            }
            case YEAR_BUILT -> {
                var yearBuilt = bundle.getMetadata().getYearBuilt().getValue();
                projectResponseList.addAll(
                        bundleProjectManager.updateYearBuilt(bundleId, yearBuilt, requestBy));
            }
            case POLICY_AND_PROPERTY_TYPE -> {
                var policyType = bundle.getMetadata().getPolicy().getType().getValue();
                var propertyType = bundle.getMetadata().getPolicy().getTypeOfProperty().getNumber();
                projectResponseList.addAll(
                        bundleProjectManager.updatePolicyTypeAndPropertyType(
                                bundleId, policyType, propertyType, requestBy));
            }
            case POLICY_RENEWAL -> {
                var policyRenewal = bundle.getMetadata().getPolicy().getIsRenewal().getValue();
                projectResponseList.addAll(
                        bundleProjectManager.updatePolicyRenewal(
                                bundleId, policyRenewal, requestBy));
            }
            case POLICY_EFFECTIVE_DATE -> {
                var effectiveDate =
                        DateTimes.toLocalDate(
                                bundle.getMetadata().getPolicy().getPolicyEffectiveDate());
                projectResponseList.addAll(
                        bundleProjectManager.updatePolicyEffectiveDate(
                                bundleId, effectiveDate, requestBy));
            }
            case UNDERWRITER -> {
                var bundleUnderwriter =
                        bundle.getMemberList().stream()
                                .filter(m -> RoleEnum.UNDERWRITER.getValue().equals(m.getRole()))
                                .findFirst()
                                .orElse(null);
                projectResponseList.addAll(
                        bundleProjectManager.updateProjectMember(
                                bundleId,
                                RoleEnum.UNDERWRITER,
                                bundleUnderwriter == null
                                        ? null
                                        : bundleUnderwriter.getUser().getId(),
                                requestBy));
            }
            case CONTACT_INSURED -> syncBundleContact(
                    CONTACT_INSURED, bundle, requestBy, projectResponseList);
            case CONTACT_AGENT -> syncBundleContact(
                    CONTACT_AGENT, bundle, requestBy, projectResponseList);
            case CONTACT_PROPERTY_ACCESS_CONTACT -> syncBundleContact(
                    CONTACT_PROPERTY_ACCESS_CONTACT, bundle, requestBy, projectResponseList);
            default -> throw new IllegalArgumentException(
                    "Unsupported sync bundle field %s.".formatted(request.getSyncField()));
        }
        return Message.SyncBundleProjectResponse.getDefaultInstance().toBuilder()
                .addAllResult(projectResponseList)
                .build();
    }

    private void syncBundleContact(
            Message.BundleProjectSyncFieldType fieldType,
            Message.BundleMessage bundle,
            String requestBy,
            List<SyncProjectResponse> projectResponseList) {
        log.debug(
                "Sync bundle contact for {} by {}, with field type {}.",
                bundle,
                requestBy,
                fieldType);
        var fieldTypeRoleMap =
                Map.of(
                        CONTACT_AGENT,
                        ContactRoleEnum.AGENT.getName(),
                        CONTACT_INSURED,
                        ContactRoleEnum.INSURED.getName(),
                        CONTACT_PROPERTY_ACCESS_CONTACT,
                        ContactRoleEnum.PROPERTY_ACCESS_CONTACT.getName());
        if (!fieldTypeRoleMap.containsKey(fieldType)) {
            return;
        }
        bundle.getContactList().stream()
                .filter(c -> c.getRole().getValue().equals(fieldTypeRoleMap.get(fieldType)))
                .findFirst()
                .ifPresent(
                        bundleAgent ->
                                projectResponseList.addAll(
                                        bundleProjectManager.updateProjectContact(
                                                bundle.getId().getValue(),
                                                bundleAgent,
                                                requestBy)));
    }

    @Override
    public Message.CreateBundleResponse createBundleProject(
            String bundleId,
            List<Message.BundleAddressMessage> buildingAddresses,
            String creationChannel,
            String requestBy) {
        var responseBuilder =
                bundleManager
                        .createBundleProject(
                                bundleId, buildingAddresses, creationChannel, requestBy)
                        .toBuilder();
        var results =
                responseBuilder.getResultList().stream()
                        .map(
                                result -> {
                                    var builder = result.getStatus().toBuilder();
                                    builder.setDescription(
                                            creationExceptionMessageResolver.apply(
                                                    ApiStatus.fromMessage(result.getStatus())));
                                    return result.toBuilder().setStatus(builder).build();
                                })
                        .toList();
        responseBuilder.clearResult().addAllResult(results);
        var result = responseBuilder.build();
        submitBundleProjectCreatedActivity(requestBy, result);
        return result;
    }

    @Override
    public boolean addBundleProject(String bundleId, List<String> projectIds, String requestBy) {
        var bundle = bundleManager.findById(bundleId);
        var contractMismatchProject =
                Iterables.toStream(projectIIRepository.findAllById(projectIds))
                        .filter(
                                project ->
                                        !StringUtils.equals(
                                                bundle.getContract().getId(),
                                                project.getContract().getId()))
                        .map(ProjectII::getId)
                        .toList();
        Preconditions.checkArgument(
                CollectionUtils.isEmpty(contractMismatchProject),
                "Add bundle project failed: following projects %s do not match the bundle"
                        + " contract.",
                StringUtils.joinWith(",", contractMismatchProject.toArray()));

        var associatedProject =
                projectIds.stream()
                        .map(
                                id -> {
                                    var group =
                                            projectGroupManager.findByProjectId(
                                                    id, BUNDLE_PROJECT_GROUP_TYPE);
                                    var groupKey =
                                            Optional.ofNullable(group)
                                                    .map(ProjectGroup::getKey)
                                                    .orElse("");
                                    return StringUtils.isBlank(groupKey)
                                                    || StringUtils.equals(groupKey, bundleId)
                                            ? null
                                            : id;
                                })
                        .filter(Objects::nonNull)
                        .toList();
        Preconditions.checkArgument(
                CollectionUtils.isEmpty(associatedProject),
                "Project %s is already associated with another bundle."
                        .formatted(StringUtils.joinWith(", ", associatedProject.toArray())));
        var result = bundleManager.addBundleProject(bundleId, projectIds, requestBy);

        if (result) {
            submitBundleProjectChangedActivity(bundleId, projectIds, requestBy, ActionType.CREATE);
        }

        return result;
    }

    @Override
    public boolean removeBundleProject(String bundleId, List<String> projectIds, String requestBy) {
        var result = bundleManager.removeBundleProject(bundleId, projectIds, requestBy);
        if (result) {
            submitBundleProjectChangedActivity(bundleId, projectIds, requestBy, ActionType.DELETE);
        }
        return result;
    }

    private Comment mapToComment(
            String bundleId, Message.BundleAttachmentMessage attachment, String requestBy) {
        var commentBuilder = com.bees360.activity.Message.CommentMessage.newBuilder();
        var attachments =
                attachment.getAttachmentList().stream()
                        .map(
                                a -> {
                                    var builder =
                                            com.bees360.activity.Message.CommentMessage.Attachment
                                                    .newBuilder();
                                    Functions.acceptIfNotNull(
                                            builder::setUrl, a.getUrl(), StringValue::getValue);
                                    Functions.acceptIfNotNull(
                                            builder::setMetadata, a.getMetadata());
                                    Functions.acceptIfNotNull(
                                            builder::setFilename,
                                            a.getFilename(),
                                            StringValue::getValue);
                                    return builder.build();
                                })
                        .toList();
        commentBuilder.addAllAttachment(attachments);
        commentBuilder.setBundleId(Long.parseLong(bundleId));
        commentBuilder.setSource(WEB_ACTIVITY_SOURCE);
        commentBuilder.setContent(attachment.getDescription().getValue());
        commentBuilder.setScopeType(ScopeType.BUNDLE);
        return Comment.from(commentBuilder.build());
    }

    @Override
    public boolean updateStatus(
            String bundleId, Message.BundleStatus status, String updatedBy, Instant updatedAt) {
        log.debug("Updating bundle {} status: {} by {}.", bundleId, status, updatedBy);
        var currentBundle = bundleManager.findById(bundleId);

        var currentStatus = currentBundle.getStatus();
        if (status.equals(CANCELLED)) {
            return bundleManager.updateStatus(bundleId, status, updatedBy, updatedAt);
        }
        if (!bundleStatusTransitionValidator.test(currentStatus, status)) {
            throw new IllegalArgumentException(
                    String.format(
                            "Bundle status transition is not allowed from %s to %s for bundle ID:"
                                    + " %s",
                            currentStatus, status, bundleId));
        }
        var bundleProjects = getBundleProjects(bundleId);
        if (bundleProjects.isEmpty()) {
            throw new IllegalArgumentException(
                    String.format(
                            "Bundle %s cannot be updated without projects. Please add projects to"
                                    + " the bundle.",
                            bundleId));
        }
        var projectStatusMap = getBundleProjectCurrentStatus(bundleProjects);
        // if status is CUSTOMER_CONTACTED, check if any of the projects contains the status
        if (status == CUSTOMER_CONTACTED) {
            // if any of the projects contains the status, update the bundle status and project
            // status
            boolean hasMatched =
                    projectStatusMap.entrySet().stream()
                            .anyMatch(
                                    entry ->
                                            entry.getValue()
                                                    .equals(
                                                            com.bees360.project.Message
                                                                    .ProjectStatus.forNumber(
                                                                    status.getNumber())));

            if (hasMatched) {
                var result =
                        bundleManager.updateStatus(
                                bundleId, CUSTOMER_CONTACTED, updatedBy, updatedAt);
                // update project status as well
                bundleProjectManager.updateProjectStatus(
                        bundleId,
                        com.bees360.project.Message.ProjectStatus.CUSTOMER_CONTACTED,
                        projectStatusMap,
                        updatedBy,
                        updatedAt);
                return result;
            } else {
                throw new IllegalArgumentException(
                        String.format("None of the bundle project contains status %s", status));
            }
        } else {
            // if not customer_contacted, check if all the projects contains the status
            var nonMatchingProjects =
                    projectStatusMap.entrySet().stream()
                            .filter(
                                    entry ->
                                            !entry.getValue()
                                                    .equals(
                                                            com.bees360.project.Message
                                                                    .ProjectStatus.forNumber(
                                                                    status.getNumber())))
                            .map(Map.Entry::getKey)
                            .collect(Collectors.toSet());
            if (!nonMatchingProjects.isEmpty()) {
                throw new IllegalArgumentException(
                        String.format(
                                "Cannot change bundle %s status, projects %s do not match the"
                                        + " status.",
                                bundleId, nonMatchingProjects));
            } else {
                return bundleManager.updateStatus(bundleId, status, updatedBy, updatedAt);
            }
        }
    }

    @Override
    public boolean deleteById(String bundleId, String deletedBy) {
        return bundleManager.deleteById(bundleId, deletedBy);
    }

    private void submitBundleProjectCreatedActivity(
            String requestBy, Message.CreateBundleResponse result) {
        var bundleId = result.getId().getValue();
        var projectIds =
                result.getResultList().stream()
                        .map(
                                r -> {
                                    var status = ApiStatus.fromMessage(r.getStatus());
                                    return status.isOk() ? r.getProjectId() : null;
                                })
                        .filter(Objects::nonNull)
                        .map(StringValue::getValue)
                        .toList();
        if (result.hasId() && CollectionUtils.isNotEmpty(projectIds)) {
            submitBundleProjectChangedActivity(bundleId, projectIds, requestBy, ActionType.CREATE);
        }
    }

    private void submitBundleProjectChangedActivity(
            String bundleId, List<String> projectIds, String requestBy, ActionType actionType) {
        submitBundleActivity(
                BundleActivities.buildBundleProjectChangedActivity(
                        bundleId,
                        projectIds,
                        requestBy,
                        Instant.now(),
                        actionType,
                        BundleActivities.ACTIVITY_SOURCE_WEB));
    }

    private boolean submitBundleActivity(Activity activity) {
        // not throw exception if activity submission fails
        try {
            activityManager.submitActivity(activity);
            return true;
        } catch (RuntimeException e) {
            log.warn("Fail to submit bundle activity {}.", activity, e);
            return false;
        }
    }

    List<com.bees360.project.Message.ProjectMessage> getBundleProjects(String bundleId) {
        var projectGroup = projectGroupManager.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE);
        if (projectGroup == null) {
            return Collections.emptyList();
        }
        return Iterables.toStream(projectIIRepository.findAllById(projectGroup.getProjectIds()))
                .map(ProjectII::toMessage)
                .toList();
    }

    static Map<String, com.bees360.project.Message.ProjectStatus> getBundleProjectCurrentStatus(
            List<com.bees360.project.Message.ProjectMessage> projectMessages) {
        return projectMessages.stream()
                .filter(p -> !p.getIsCanceled())
                .collect(
                        Collectors.toMap(
                                com.bees360.project.Message.ProjectMessage::getId,
                                com.bees360.project.Message.ProjectMessage::getLatestStatus));
    }
}
