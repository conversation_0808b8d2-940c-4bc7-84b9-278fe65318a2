package com.bees360.bundle.event;

import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_DISPLAY_NAME_PROPERTY_TYPE;
import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_PROPERTY_TYPE;
import static com.bees360.bundle.BundleActivities.ACTIVITY_SOURCE_WEB;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/** 监听BundleChanged事件，当属性类型发生变化时提交对应的活动记录 */
@Log4j2
public class SubmitPropertyTypeChangedActivityOnBundleChanged
        extends AbstractNamedEventListener<BundleChanged> {

    private final ActivityManager activityManager;

    public SubmitPropertyTypeChangedActivityOnBundleChanged(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={}).", this, this.activityManager);
    }

    @Override
    public void handle(BundleChanged event) throws IOException {
        log.debug("Received bundle changed event {}.", event);
        var oldBundleMeta = event.getOldValue().getMetadata();
        var oldPropertyType = oldBundleMeta.getPolicy().getTypeOfProperty();
        var newBundle = event.getNewValue();
        var newBundleMeta = event.getNewValue().getMetadata();
        var newPropertyType = newBundleMeta.getPolicy().getTypeOfProperty();

        if (oldPropertyType.getNumber() == newPropertyType.getNumber()) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        newBundle.getId(),
                        ACTIVITY_FIELD_NAME_PROPERTY_TYPE,
                        ACTIVITY_FIELD_DISPLAY_NAME_PROPERTY_TYPE,
                        Message.ActivityMessage.FieldType.INTEGER,
                        oldPropertyType.getNumber(),
                        newPropertyType.getNumber(),
                        String::valueOf,
                        newBundle.getUpdatedBy(),
                        newBundle.getUpdatedAt(),
                        ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle property type changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
