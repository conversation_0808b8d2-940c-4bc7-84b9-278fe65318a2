package com.bees360.bundle.event;

import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_DISPLAY_NAME_YEAR_BUILT;
import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_YEAR_BUILT;
import static com.bees360.bundle.BundleActivities.ACTIVITY_SOURCE_WEB;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Objects;

/** 监听BundleChanged事件，当检测到建筑年份变更时提交对应的活动记录 */
@Log4j2
public class SubmitYearBuiltChangedActivityOnBundleChanged
        extends AbstractNamedEventListener<BundleChanged> {

    private final ActivityManager activityManager;

    public SubmitYearBuiltChangedActivityOnBundleChanged(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={}).", this, this.activityManager);
    }

    @Override
    public void handle(BundleChanged event) throws IOException {
        log.debug("Received bundle changed event {}.", event);
        var oldBundleMeta = event.getOldValue().getMetadata();
        var oldYearBuilt =
                oldBundleMeta.hasYearBuilt() ? oldBundleMeta.getYearBuilt().getValue() : null;
        var newBundle = event.getNewValue();
        var newBundleMeta = event.getNewValue().getMetadata();
        var newYearBuilt =
                newBundleMeta.hasYearBuilt() ? newBundleMeta.getYearBuilt().getValue() : null;

        if (Objects.equals(oldYearBuilt, newYearBuilt)) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        event.getNewValue().getId(),
                        ACTIVITY_FIELD_NAME_YEAR_BUILT,
                        ACTIVITY_FIELD_DISPLAY_NAME_YEAR_BUILT,
                        Message.ActivityMessage.FieldType.INTEGER,
                        oldYearBuilt,
                        newYearBuilt,
                        String::valueOf,
                        newBundle.getUpdatedBy(),
                        newBundle.getUpdatedAt(),
                        ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle year built changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
