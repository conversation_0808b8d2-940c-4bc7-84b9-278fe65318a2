package com.bees360.bundle.event;

import com.bees360.beespilot.BeespilotBundleManager;
import com.bees360.bundle.BundleManager;
import com.bees360.event.registry.BundleChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class UpdateBeespilotBundleOnBundleUpdated
        extends AbstractNamedEventListener<BundleChanged> {
    private final BeespilotBundleManager beespilotBundleManager;
    private final BundleManager bundleManager;

    public UpdateBeespilotBundleOnBundleUpdated(
            BeespilotBundleManager beespilotBundleManager, BundleManager bundleManager) {
        this.beespilotBundleManager = beespilotBundleManager;
        this.bundleManager = bundleManager;
        log.info(
                "Created {}(beespilotBundleManager={}, bundleManager={}).",
                this,
                this.beespilotBundleManager,
                this.bundleManager);
    }

    @Override
    public void handle(BundleChanged event) throws IOException {
        log.debug("Received bundle changed event: {}, ", event);
        var bundleId = bundleManager.findById(event.getBundleId());
        beespilotBundleManager.updateBundle(bundleId);
    }
}
