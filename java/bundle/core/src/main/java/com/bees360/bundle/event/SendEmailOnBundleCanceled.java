package com.bees360.bundle.event;

import com.bees360.bundle.BundleProvider;
import com.bees360.bundle.Message;
import com.bees360.bundle.notification.BundleEmailContext;
import com.bees360.bundle.notification.CustomerBundleEmailProperties;
import com.bees360.bundle.notification.SendCustomerBundleEmailJobProvider;
import com.bees360.customer.Customer;
import com.bees360.event.registry.BundleStateChanged;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.util.EventTriggeredJob;
import com.bees360.util.retry.RetryProperties;

import lombok.extern.log4j.Log4j2;

import java.util.Objects;
import java.util.function.BiFunction;

@Log4j2
public class SendEmailOnBundleCanceled extends EventTriggeredJob<BundleStateChanged> {
    private final ThreadLocal<Message.BundleMessage> bundleCache = new ThreadLocal<>();
    private final BundleProvider bundleProvider;
    private final BiFunction<String, String, CustomerBundleEmailProperties>
            customerBundleEmailsPropertiesProvider;
    private final SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider;

    private final RetryProperties jobRetryProperties;

    private final BundleEmailContext bundleEmailContext;
    private final String BUNDLE_CANCELED = "bundle_canceled";

    public SendEmailOnBundleCanceled(
            JobScheduler jobScheduler,
            BundleProvider bundleProvider,
            BiFunction<String, String, CustomerBundleEmailProperties>
                    customerBundleEmailsPropertiesProvider,
            SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider,
            RetryProperties jobRetryProperties,
            BundleEmailContext bundleEmailContext) {
        super(jobScheduler);
        this.bundleProvider = bundleProvider;
        this.customerBundleEmailsPropertiesProvider = customerBundleEmailsPropertiesProvider;
        this.sendCustomerBundleEmailJobProvider = sendCustomerBundleEmailJobProvider;
        this.jobRetryProperties = jobRetryProperties;
        this.bundleEmailContext = bundleEmailContext;
    }

    @Override
    protected boolean filter(BundleStateChanged bundleStateChanged) {
        var state = Message.BundleState.valueOf(bundleStateChanged.getState());
        if (!Objects.equals(Message.BundleState.CLOSE, state)) {
            return false;
        }
        var bundle = getBundleById(bundleStateChanged.getBundleId());
        if (!bundle.getIsCanceled().getValue()) {
            return false;
        }
        var customer = Customer.of(bundle.getContract().getInsuredBy());
        var config =
                customerBundleEmailsPropertiesProvider.apply(
                        customer.getCompanyKey(), BUNDLE_CANCELED);
        return config.isSubscribed();
    }

    @Override
    protected Job convert(BundleStateChanged bundleStatusChanged) {
        var bundle = getBundleById(bundleStatusChanged.getBundleId());
        var customer = Customer.of(bundle.getContract().getInsuredBy());
        var job =
                sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        bundle, customer, BUNDLE_CANCELED, bundleEmailContext);
        return RetryableJob.of(
                Job.ofPayload(job),
                jobRetryProperties.getRetryCount(),
                jobRetryProperties.getRetryDelay(),
                jobRetryProperties.getRetryDelayIncreaseFactor());
    }

    @Override
    public void handle(BundleStateChanged event) {
        log.info("Send email on bundle state changed by event {}.", event);
        try {
            super.handle(event);
        } finally {
            clearCache();
        }
    }

    private Message.BundleMessage getBundleById(String bundleId) {
        Message.BundleMessage result = bundleCache.get();
        if (result == null) {
            result = bundleProvider.findById(bundleId);
            bundleCache.set(result);
        }
        return result;
    }

    private void clearCache() {
        bundleCache.remove();
    }
}
