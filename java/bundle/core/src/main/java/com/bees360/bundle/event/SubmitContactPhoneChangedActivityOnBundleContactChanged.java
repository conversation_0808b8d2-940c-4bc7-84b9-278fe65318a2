package com.bees360.bundle.event;

import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_DISPLAY_NAME_PATTERN_CONTACT_PHONE;
import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_CONTACT;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleContactChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.function.Function;

/** 监听联系人电话变更事件并提交活动记录 */
@Log4j2
public class SubmitContactPhoneChangedActivityOnBundleContactChanged
        extends AbstractNamedEventListener<BundleContactChanged> {

    private final ActivityManager activityManager;

    public SubmitContactPhoneChangedActivityOnBundleContactChanged(
            ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={})", this, this.activityManager);
    }

    @Override
    public void handle(BundleContactChanged event) throws IOException {
        log.debug("Received event {}", event);
        var bundleId = event.getNewValue().getBundleId();
        var role = event.getNewValue().getRole();

        var oldContactPhone = event.getOldValue().getPhone();
        var newContactPhone = event.getNewValue().getPhone();

        if (StringUtils.equals(oldContactPhone, newContactPhone)) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        bundleId,
                        ACTIVITY_FIELD_NAME_CONTACT,
                        ACTIVITY_FIELD_DISPLAY_NAME_PATTERN_CONTACT_PHONE.formatted(role),
                        Message.ActivityMessage.FieldType.STRING,
                        oldContactPhone,
                        newContactPhone,
                        Function.identity(),
                        event.getNewValue().getUpdatedBy(),
                        event.getNewValue().getUpdatedAt(),
                        BundleActivities.ACTIVITY_SOURCE_WEB);
        log.debug(
                "Submit bundle {} contact phone changed activity {}.", role, activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
