package com.bees360.bundle.validator;

import com.bees360.bundle.Message;
import com.bees360.project.ContactRoleEnum;
import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Predicate;

@Log4j2
public class BundleCreationContactValidator implements Predicate<Message.CreateBundleRequest> {

    @Override
    public boolean test(Message.CreateBundleRequest request) {
        log.debug("Validating contacts for bundle creation request: {}", request);
        var contacts = request.getContactList();

        boolean insuredExists = false;
        var primaryContactCount = 0;

        for (var contact : contacts) {
            Preconditions.checkArgument(
                    StringUtils.isNotBlank(contact.getFullName().getValue()),
                    "Create bundle failed: Contact name can not be empty.");
            Preconditions.checkArgument(
                    StringUtils.isNotBlank(contact.getEmail().getValue())
                            || StringUtils.isNotBlank(contact.getPhone().getValue()),
                    "Create bundle failed: Contact %s must provide either phone number or email"
                            + " address",
                    contact.getFullName().getValue());

            if (StringUtils.equalsIgnoreCase(
                    ContactRoleEnum.INSURED.getName(), contact.getRole().getValue())) {
                insuredExists = true;
            }
            primaryContactCount += contact.getIsPrimary().getValue() ? 1 : 0;
        }

        Preconditions.checkArgument(
                insuredExists, "Create bundle failed: Insured can not be null.");

        if (primaryContactCount < 1) {
            throw new IllegalArgumentException(
                    "Create bundle failed: At least one contact must be marked as primary.");
        } else if (primaryContactCount > 1) {
            throw new IllegalArgumentException(
                    "Create bundle failed: Only one primary contact can be marked as primary.");
        }
        return true;
    }
}
