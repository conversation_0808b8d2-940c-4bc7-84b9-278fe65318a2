package com.bees360.bundle.event;

import com.bees360.bundle.BundleProvider;
import com.bees360.bundle.Message;
import com.bees360.bundle.notification.BundleEmailContext;
import com.bees360.bundle.notification.CustomerBundleEmailProperties;
import com.bees360.bundle.notification.SendCustomerBundleEmailJobProvider;
import com.bees360.customer.Customer;
import com.bees360.event.registry.BundleCreated;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.util.EventTriggeredJob;
import com.bees360.util.retry.RetryProperties;

import lombok.extern.log4j.Log4j2;

import java.util.function.BiFunction;

@Log4j2
public class SendEmailOnBundleCreated extends EventTriggeredJob<BundleCreated> {

    private final ThreadLocal<Message.BundleMessage> bundleCache = new ThreadLocal<>();

    private final BundleProvider bundleProvider;
    private final BiFunction<String, String, CustomerBundleEmailProperties>
            customerBundleEmailsPropertiesProvider;
    private final BundleEmailContext bundleEmailContext;
    private final RetryProperties jobRetryProperties;
    private final String BUNDLE_CREATED = "bundle_created";
    private final SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider;

    public SendEmailOnBundleCreated(
            JobScheduler jobScheduler,
            BundleProvider bundleProvider,
            BiFunction<String, String, CustomerBundleEmailProperties>
                    customerBundleEmailsPropertiesProvider,
            RetryProperties jobRetryProperties,
            SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider,
            BundleEmailContext bundleEmailContext) {
        super(jobScheduler);
        this.bundleProvider = bundleProvider;
        this.customerBundleEmailsPropertiesProvider = customerBundleEmailsPropertiesProvider;
        this.jobRetryProperties = jobRetryProperties;
        this.sendCustomerBundleEmailJobProvider = sendCustomerBundleEmailJobProvider;
        this.bundleEmailContext = bundleEmailContext;
    }

    @Override
    protected boolean filter(BundleCreated bundleCreatedEvent) {

        var bundleId = bundleCreatedEvent.getId();
        var bundle = getBundleById(bundleId);
        var customer = Customer.of(bundle.getContract().getInsuredBy());
        var config =
                customerBundleEmailsPropertiesProvider.apply(
                        customer.getCompanyKey(), BUNDLE_CREATED);
        log.info("Got config: {}", config);
        return config.isSubscribed();
    }

    @Override
    protected Job convert(BundleCreated bundleCreatedEvent) {

        var bundleId = bundleCreatedEvent.getId();
        var bundle = getBundleById(bundleId);
        var customer = Customer.of(bundle.getContract().getInsuredBy());

        var job =
                sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        bundle, customer, BUNDLE_CREATED, bundleEmailContext);
        log.info("Created bundle created job: {}", job);
        return RetryableJob.of(
                Job.ofPayload(job),
                jobRetryProperties.getRetryCount(),
                jobRetryProperties.getRetryDelay(),
                jobRetryProperties.getRetryDelayIncreaseFactor());
    }

    @Override
    public void handle(BundleCreated event) {
        log.info("Send email on bundle created by event {}.", event);
        try {
            super.handle(event);
        } finally {
            clearCache();
        }
    }

    private Message.BundleMessage getBundleById(String bundleId) {
        Message.BundleMessage result = bundleCache.get();
        if (result == null) {
            result = bundleProvider.findById(bundleId);
            bundleCache.set(result);
        }
        return result;
    }

    private void clearCache() {
        bundleCache.remove();
    }
}
