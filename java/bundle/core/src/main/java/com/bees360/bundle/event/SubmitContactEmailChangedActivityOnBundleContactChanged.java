package com.bees360.bundle.event;

import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_DISPLAY_NAME_PATTERN_CONTACT_EMAIL;
import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_CONTACT;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleContactChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.function.Function;

/** 监听BundleContactChanged事件，当联系人邮箱变更时提交活动记录 */
@Log4j2
public class SubmitContactEmailChangedActivityOnBundleContactChanged
        extends AbstractNamedEventListener<BundleContactChanged> {

    private final ActivityManager activityManager;

    public SubmitContactEmailChangedActivityOnBundleContactChanged(
            ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={})", this, this.activityManager);
    }

    @Override
    public void handle(BundleContactChanged event) throws IOException {
        log.debug("Received event {}", event);
        var bundleId = event.getNewValue().getBundleId();
        var role = event.getNewValue().getRole();

        var oldContactEmail = event.getOldValue().getEmail();
        var newContactEmail = event.getNewValue().getEmail();

        if (StringUtils.equals(oldContactEmail, newContactEmail)) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        bundleId,
                        ACTIVITY_FIELD_NAME_CONTACT,
                        ACTIVITY_FIELD_DISPLAY_NAME_PATTERN_CONTACT_EMAIL.formatted(role),
                        Message.ActivityMessage.FieldType.STRING,
                        oldContactEmail,
                        newContactEmail,
                        Function.identity(),
                        event.getNewValue().getUpdatedBy(),
                        event.getNewValue().getUpdatedAt(),
                        BundleActivities.ACTIVITY_SOURCE_WEB);
        log.debug(
                "Submit bundle {} contact email changed activity {}.", role, activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
