package com.bees360.bundle.event;

import com.bees360.beespilot.BeespilotBundleManager;
import com.bees360.event.registry.ProjectGroupAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.group.ProjectGroupManager;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Log4j2
public class AddBeespilotBundleProjectOnProjectGroupAdded
        extends AbstractNamedEventListener<ProjectGroupAdded> {
    private final ProjectGroupManager projectGroupManager;
    private final BeespilotBundleManager beespilotBundleManager;

    public AddBeespilotBundleProjectOnProjectGroupAdded(
            ProjectGroupManager projectGroupManager,
            BeespilotBundleManager beespilotBundleManager) {
        this.projectGroupManager = projectGroupManager;
        this.beespilotBundleManager = beespilotBundleManager;
        log.info(
                "Created {}(projectGroupManager={}, beespilotBundleManager={}).",
                this,
                this.projectGroupManager,
                this.beespilotBundleManager);
    }

    @Override
    public void handle(ProjectGroupAdded event) throws IOException {
        log.debug(
                "Received project group added event: {}, start to add beespilot bundle project.",
                event);
        if (!Objects.equals(event.getGroupType(), "BUNDLE_PROJECT")) {
            return;
        }
        var group = projectGroupManager.findByProjectId(event.getProjectId(), event.getGroupType());
        // If group is null, it means the project is not added to the bundle.
        if (group == null || !Objects.equals(group.getKey(), event.getGroupKey())) {
            log.error(
                    "Cannot find bundle {} for project {}",
                    event.getGroupKey(),
                    event.getProjectId());
            return;
        }
        beespilotBundleManager.addBundleProject(event.getGroupKey(), List.of(event.getProjectId()));
    }
}
