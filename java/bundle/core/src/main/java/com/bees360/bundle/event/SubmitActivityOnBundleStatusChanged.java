package com.bees360.bundle.event;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.bundle.BundleActivities;
import com.bees360.event.registry.BundleStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.util.function.Function;

/** 监听Bundle状态变更事件并提交相应的活动记录到ActivityManager */
@Log4j2
public class SubmitActivityOnBundleStatusChanged
        extends AbstractNamedEventListener<BundleStatusChanged> {
    private final ActivityManager activityManager;

    public SubmitActivityOnBundleStatusChanged(ActivityManager activityManager) {
        this.activityManager = activityManager;
        log.info("Created {}(activityManager={})", this, this.activityManager);
    }

    @Override
    public void handle(BundleStatusChanged event) throws IOException {
        log.debug("Received {}.", event);
        var bundleId = event.getBundleId();
        var oldStatus = event.getOldStatus();
        var newStatus = event.getStatus();
        var updatedBy = event.getUpdatedBy();
        var updatedAt = Instant.now();

        if (StringUtils.equals(oldStatus, newStatus)) {
            return;
        }

        var activity =
                BundleActivities.buildFieldChangedActivity(
                        bundleId,
                        Message.ActivityMessage.FieldName.STATUS.name(),
                        null,
                        Message.ActivityMessage.FieldType.STRING,
                        null,
                        newStatus,
                        Function.identity(),
                        updatedBy,
                        updatedAt,
                        BundleActivities.ACTIVITY_SOURCE_WEB);
        log.debug("Submit bundle status changed activity {}.", activity.toMessage());
        activityManager.submitActivity(activity);
    }
}
