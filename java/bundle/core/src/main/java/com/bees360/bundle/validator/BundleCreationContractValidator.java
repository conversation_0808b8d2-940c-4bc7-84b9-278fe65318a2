package com.bees360.bundle.validator;

import com.bees360.bundle.Message;
import com.bees360.contract.ContractRepository;
import com.bees360.customer.CustomerProvider;
import com.google.common.base.Preconditions;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Predicate;

@Log4j2
public class BundleCreationContractValidator implements Predicate<Message.CreateBundleRequest> {
    private final CustomerProvider customerProvider;
    private final ContractRepository contractRepository;

    public BundleCreationContractValidator(
            CustomerProvider customerProvider, ContractRepository contractRepository) {
        this.customerProvider = customerProvider;
        this.contractRepository = contractRepository;
        log.info(
                "Created {}(customerProvider={}, contractRepository={}).",
                this,
                this.customerProvider,
                this.contractRepository);
    }

    @Override
    public boolean test(Message.CreateBundleRequest request) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(request.getInsuredBy().getValue()),
                "Create bundle failed: Insurance Company can not be null.");
        Preconditions.checkArgument(
                StringUtils.isNotBlank(request.getProcessedBy().getValue()),
                "Create bundle failed: Process Company can not be null.");

        var insuredBy = customerProvider.findById(request.getInsuredBy().getValue());
        var processedBy = customerProvider.findById(request.getProcessedBy().getValue());

        Preconditions.checkArgument(
                insuredBy != null,
                "Create bundle failed: Insurance Company %s does not exist.",
                request.getInsuredBy().getValue());
        Preconditions.checkArgument(
                processedBy != null,
                "Create bundle failed: Process Company %s does not exist.",
                request.getProcessedBy().getValue());

        var contract = contractRepository.findByCompanyId(insuredBy.getId(), processedBy.getId());
        Preconditions.checkArgument(
                contract != null,
                "Create bundle failed: Contract for insurance company %s and process company %s"
                        + " does not exists.",
                insuredBy.getName(),
                processedBy.getName());

        return true;
    }
}
