package com.bees360.bundle.event;

import com.bees360.beespilot.BeespilotBundleManager;
import com.bees360.bundle.BundleManager;
import com.bees360.event.registry.BundleCreated;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.Message;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class CreateBeespilotBundleOnBundleCreated
        extends AbstractNamedEventListener<BundleCreated> {
    private final BeespilotBundleManager beespilotBundleManager;
    private final BundleManager bundleManager;

    public CreateBeespilotBundleOnBundleCreated(
            BeespilotBundleManager beespilotBundleManager, BundleManager bundleManager) {
        this.beespilotBundleManager = beespilotBundleManager;
        this.bundleManager = bundleManager;
        log.info(
                "Created {}(beespilotBundleManager={}, bundleManager={}).",
                this,
                this.beespilotBundleManager,
                this.bundleManager);
    }

    @Override
    public void handle(BundleCreated event) throws IOException {
        log.debug("Received bundle created event: {}, ", event);
        var bundle = bundleManager.findById(event.getId());
        beespilotBundleManager.createBundle(bundle);
        beespilotBundleManager.addBundleProject(
                event.getId(),
                bundle.getProjectList().stream().map(Message.ProjectMessage::getId).toList());
    }
}
