package com.bees360.bundle;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.bundle.Message.BundleMessage;
import com.bees360.bundle.Message.BundleState;
import com.bees360.project.Message.ProjectStatus;
import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.state.StateChangeReasonGroupProvider;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

class AggregateBundleStateManagerTest {

    @Mock private BundleStateManager bundleStateManager;

    @Mock private StateChangeReasonGroupProvider stateChangeReasonGroupProvider;

    @Mock private BundleManager bundleManager;

    @InjectMocks private AggregateBundleStateManager aggregateBundleStateManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testChangeState_BundleWithoutProjects_ThrowsException() {
        // Arrange
        String bundleId = "bundle1";
        BundleMessage bundleMessage = BundleMessage.newBuilder().build();
        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);

        // Act & Assert
        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                aggregateBundleStateManager.changeState(
                                        bundleId, BundleState.CLOSE, "reason", "user"));
        assertTrue(exception.getMessage().contains("cannot be updated without projects"));
    }

    @Test
    void testChangeState_CloseWithCanceledReason_CallsBundleStateManager() {
        // Arrange
        String bundleId = "bundle1";
        String changeReason = "cancelReason";
        var project = mock(com.bees360.project.Message.ProjectMessage.class);
        when(project.getId()).thenReturn("10001");
        BundleMessage bundleMessage = BundleMessage.newBuilder().addProject(project).build();
        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);

        ProjectStateChangeReason reason = mock(ProjectStateChangeReason.class);
        when(reason.getId()).thenReturn(changeReason);
        when(stateChangeReasonGroupProvider.findByGroupAndType(anyString(), anyString(), any()))
                .thenAnswer(e -> Collections.singletonList(reason));

        when(bundleStateManager.changeState(bundleId, BundleState.CLOSE, changeReason, "user"))
                .thenReturn(true);

        // Act
        boolean result =
                aggregateBundleStateManager.changeState(
                        bundleId, BundleState.CLOSE, changeReason, "user");

        // Assert
        assertTrue(result);
        verify(bundleStateManager).changeState(bundleId, BundleState.CLOSE, changeReason, "user");
    }

    @Test
    void testChangeState_CloseWithValidReasonAndAllProjectsCompleted_CallsBundleStateManager() {
        // Arrange
        String bundleId = "bundle1";
        String changeReason = "validReason";
        var project = mock(com.bees360.project.Message.ProjectMessage.class);
        when(project.getId()).thenReturn("10001");
        BundleMessage bundleMessage = BundleMessage.newBuilder().addProject(project).build();
        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);

        when(stateChangeReasonGroupProvider.findByGroupAndType(anyString(), anyString(), any()))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<AggregateBundleManager> mockedStatic =
                mockStatic(AggregateBundleManager.class)) {
            Map<String, ProjectStatus> projectStatusMap = new HashMap<>();
            projectStatusMap.put("10001", ProjectStatus.RETURNED_TO_CLIENT);
            mockedStatic
                    .when(() -> AggregateBundleManager.getBundleProjectCurrentStatus(anyList()))
                    .thenReturn(projectStatusMap);

            when(bundleStateManager.changeState(bundleId, BundleState.CLOSE, changeReason, "user"))
                    .thenReturn(true);

            // Act
            boolean result =
                    aggregateBundleStateManager.changeState(
                            bundleId, BundleState.CLOSE, changeReason, "user");

            // Assert
            assertTrue(result);
            verify(bundleStateManager)
                    .changeState(bundleId, BundleState.CLOSE, changeReason, "user");
        }
    }

    @Test
    void testChangeState_CloseWithValidReasonAndSomeProjectsNotCompleted_ThrowsException() {
        // Arrange
        String bundleId = "bundle1";
        String changeReason = "validReason";
        var project = mock(com.bees360.project.Message.ProjectMessage.class);
        when(project.getId()).thenReturn("10001");
        BundleMessage bundleMessage = BundleMessage.newBuilder().addProject(project).build();
        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);

        when(stateChangeReasonGroupProvider.findByGroupAndType(anyString(), anyString(), any()))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<AggregateBundleManager> mockedStatic =
                mockStatic(AggregateBundleManager.class)) {
            Map<String, ProjectStatus> projectStatusMap = new HashMap<>();
            projectStatusMap.put("10001", ProjectStatus.PROJECT_CREATED);
            mockedStatic
                    .when(() -> AggregateBundleManager.getBundleProjectCurrentStatus(anyList()))
                    .thenReturn(projectStatusMap);

            // Act & Assert
            IllegalArgumentException exception =
                    assertThrows(
                            IllegalArgumentException.class,
                            () ->
                                    aggregateBundleStateManager.changeState(
                                            bundleId, BundleState.CLOSE, changeReason, "user"));
            assertTrue(exception.getMessage().contains("Failed to Close"));
        }
    }

    @Test
    void testChangeState_NonCloseState_CallsBundleStateManager() {
        // Arrange
        String bundleId = "bundle1";
        var project = mock(com.bees360.project.Message.ProjectMessage.class);
        when(project.getId()).thenReturn("10001");
        BundleMessage bundleMessage = BundleMessage.newBuilder().addProject(project).build();
        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);

        when(bundleStateManager.changeState(bundleId, BundleState.OPEN, "reason", "user"))
                .thenReturn(true);

        // Act
        boolean result =
                aggregateBundleStateManager.changeState(
                        bundleId, BundleState.OPEN, "reason", "user");

        // Assert
        assertTrue(result);
        verify(bundleStateManager).changeState(bundleId, BundleState.OPEN, "reason", "user");
    }
}
