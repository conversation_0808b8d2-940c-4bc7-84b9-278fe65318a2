package com.bees360.bundle;

import static com.bees360.bundle.BundleActivities.ACTIVITY_ENTITY_TYPE_BUNDLE;
import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_BUNDLE_PROJECT;
import static com.bees360.bundle.BundleActivities.ACTIVITY_FIELD_NAME_SERVICE_TYPE;
import static com.bees360.project.Message.ServiceType.FOUR_POINT_UNDERWRITING;
import static com.google.protobuf.StringValue.of;

import static org.apache.commons.lang3.RandomUtils.nextInt;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.CommentManager;
import com.bees360.activity.Message.ActivityMessage.ActionType;
import com.bees360.address.Address;
import com.bees360.address.AddressProvider;
import com.bees360.api.ApiStatus;
import com.bees360.api.UnknownException;
import com.bees360.bundle.config.BundleProperties;
import com.bees360.bundle.config.BundleServiceConfig;
import com.bees360.bundle.config.BundleServiceMockConfig;
import com.bees360.contact.PrimaryContactRecord;
import com.bees360.contact.PrimaryContactRecordProvider;
import com.bees360.contract.ContractRepository;
import com.bees360.customer.Customer;
import com.bees360.customer.CustomerProvider;
import com.bees360.customer.Message;
import com.bees360.project.CheckDuplicationProjectCreator;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.state.ProjectStateChangeReasonProvider;
import com.bees360.util.Iterables;
import com.google.protobuf.BoolValue;
import com.google.protobuf.StringValue;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.function.TriFunction;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@SpringBootTest(
        classes = AggregateBundleManagerTest.Config.class,
        properties = {
            "grpc.server.in-process-name=AggregateBundleManagerTest",
        })
@DirtiesContext
@ImportAutoConfiguration({
    RefreshAutoConfiguration.class,
})
public class AggregateBundleManagerTest extends AbstractBundleManagerTest {

    @Import({
        BundleServiceConfig.class,
        BundleServiceMockConfig.class,
    })
    @Configuration
    static class Config {}

    @Autowired AddressProvider addressProvider;

    @Autowired ContractRepository contractRepository;

    @Autowired BatchProjectCreator batchProjectCreator;

    @Autowired ExternalIntegrationProvider externalIntegrationProvider;

    @Autowired CustomerProvider customerProvider;

    @Autowired CommentManager commentManager;

    @Autowired
    @Qualifier("policyTypePropertyTypeValidator")
    TriFunction<String, String, Integer, Integer> validator;

    @Autowired ContactManager contactManager;

    @Autowired PrimaryContactRecordProvider primaryContactRecordProvider;

    @Autowired ActivityManager activityManager;

    private final BundleProperties bundleProperties;

    public AggregateBundleManagerTest(
            @Autowired @Qualifier("grpcBundleManagerClient") BundleManager bundleManager,
            @Autowired AddressProvider mockAddressProvider,
            @Autowired ContractRepository mockContractRepository,
            @Autowired BatchProjectCreator mockBatchProjectCreator,
            @Autowired ExternalIntegrationProvider mockExternalIntegrationProvider,
            @Autowired ProjectGroupManager mockProjectGroupManager,
            @Autowired ProjectIIManager mockProjectIIManager,
            @Autowired BundleProperties bundleProperties,
            @Autowired ProjectStateChangeReasonProvider projectStateChangeReasonProvider,
            @Autowired BundleStateManager bundleStateManager) {
        super(
                bundleManager,
                mockAddressProvider,
                mockContractRepository,
                mockBatchProjectCreator,
                mockExternalIntegrationProvider,
                mockProjectGroupManager,
                mockProjectIIManager,
                projectStateChangeReasonProvider,
                bundleStateManager);
        this.bundleProperties = bundleProperties;
    }

    @BeforeEach
    public void setup() {
        Mockito.doAnswer(
                        args -> {
                            var customerId = args.getArgument(0, String.class);
                            return Customer.of(
                                    Message.CustomerMessage.newBuilder()
                                            .setId(customerId)
                                            .setKey(customerId)
                                            .build());
                        })
                .when(customerProvider)
                .findById(Mockito.anyString());
        Mockito.doAnswer(
                        args -> {
                            return args.getArgument(2, Integer.class);
                        })
                .when(validator)
                .apply(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt());
        Mockito.clearInvocations(activityManager);
    }

    @Test
    public void testCreateBundleWithNoFailure() {
        super.testCreateBundleWithNoFailure();
        var activityCaptor = ArgumentCaptor.forClass(Activity.class);
        Mockito.verify(activityManager, Mockito.atLeastOnce())
                .submitActivity(activityCaptor.capture());
        var actualActivity =
                activityCaptor.getAllValues().stream()
                        .filter(
                                a ->
                                        StringUtils.equals(
                                                a.getFiledName(),
                                                ACTIVITY_FIELD_NAME_BUNDLE_PROJECT))
                        .findFirst()
                        .orElse(null);
        Assertions.assertEquals(ActionType.CREATE.name(), actualActivity.getAction());
        Assertions.assertEquals(ACTIVITY_ENTITY_TYPE_BUNDLE, actualActivity.getEntityType());
        Assertions.assertTrue(StringUtils.isNotEmpty(actualActivity.getValue()));
    }

    @Test
    public void testCreateBundleWithIllegalPolicyNo() {
        var creationRequest = randomCreateBundleRequest().toBuilder().setPolicyNo(of("")).build();

        assertThrows(
                IllegalArgumentException.class, () -> bundleManager.createBundle(creationRequest));
    }

    @Test
    public void testCreateBundleWithNoProjectCreated() {
        var creationRequest = randomCreateBundleRequest();
        var insuredBy = creationRequest.getInsuredBy().getValue();
        var processedBy = creationRequest.getProcessedBy().getValue();
        var contract = randomContract(insuredBy, processedBy);
        var mainBundleAddress = creationRequest.getBuildingAddressList().get(0);
        var address = randomAddress(mainBundleAddress);
        var mockProjectCreationResult = new HashMap<Integer, ApiStatus>();
        for (var idx = 0; idx < creationRequest.getNumberOfBuildings().getValue(); idx++) {
            // Simulate a duplication exception for each project
            var exception =
                    idx % 2 == 0
                            ? new CheckDuplicationProjectCreator.ProjectDuplicationException(
                                    new String[] {String.valueOf(idx)}, "duplicated project")
                            : new UnknownException("unknown internal exception");
            mockProjectCreationResult.put(idx, ApiStatus.fromThrowable(exception));
        }

        Mockito.doReturn(address).when(addressProvider).normalize(Mockito.any(Address.class));
        Mockito.doReturn(contract)
                .when(contractRepository)
                .findByCompanyId(Mockito.eq(insuredBy), Mockito.eq(processedBy));
        Mockito.doReturn(mockProjectCreationResult)
                .when(batchProjectCreator)
                .createBatch(
                        Mockito.any(),
                        Mockito.eq(creationRequest.getCreateBy().getValue()),
                        Mockito.any(),
                        Mockito.eq(false),
                        Mockito.eq("WEB"));
        Mockito.doReturn(List.of())
                .when(externalIntegrationProvider)
                .findAllByReference(Mockito.eq("BATCH"), Mockito.any());
        doMockCreatedReason();
        var response = assertDoesNotThrow(() -> bundleManager.createBundle(creationRequest));
        assertFalse(response.hasId());
        response.getResultList()
                .forEach(
                        result -> {
                            assertFalse(result.hasProjectId());
                            assertNotNull(result.getStatus());
                            assertNotNull(result.getStatus().getDescription());
                            var expectedDescription =
                                    bundleProperties
                                            .getCreation()
                                            .getErrorCodeMessageMapping()
                                            .getOrDefault(
                                                    result.getStatus().getCode().getNumber(),
                                                    bundleProperties
                                                            .getCreation()
                                                            .getDefaultErrorMessage());
                            assertEquals(expectedDescription, result.getStatus().getDescription());
                        });
    }

    @Test
    public void testCreateBundleWithoutInsuredContact() {
        var contacts = List.of(randomBundleContact("Agent", true));
        var creationRequest =
                randomCreateBundleRequest().toBuilder().addAllContact(contacts).build();
        var insuredBy = creationRequest.getInsuredBy().getValue();
        var processedBy = creationRequest.getProcessedBy().getValue();
        var contract = randomContract(insuredBy, processedBy);

        Mockito.doReturn(contract)
                .when(contractRepository)
                .findByCompanyId(Mockito.eq(insuredBy), Mockito.eq(processedBy));

        assertThrows(
                IllegalArgumentException.class, () -> bundleManager.createBundle(creationRequest));
    }

    @Test
    public void testCreateBundleWithContactNoPhoneAndNoEmail() {
        var illegalContact =
                randomBundleContact("Insured", true).toBuilder()
                        .setPhone(of(""))
                        .setEmail(of(""))
                        .build();
        var creationRequest =
                randomCreateBundleRequest().toBuilder()
                        .clearContact()
                        .addContact(illegalContact)
                        .build();
        var insuredBy = creationRequest.getInsuredBy().getValue();
        var processedBy = creationRequest.getProcessedBy().getValue();
        var contract = randomContract(insuredBy, processedBy);

        Mockito.doReturn(contract)
                .when(contractRepository)
                .findByCompanyId(Mockito.eq(insuredBy), Mockito.eq(processedBy));

        assertThrows(
                IllegalArgumentException.class, () -> bundleManager.createBundle(creationRequest));
    }

    @Test
    public void testCreateBundleWithoutPrimaryContact() {
        var creationRequest = randomCreateBundleRequest();
        var illegalContact =
                creationRequest.getContactList().stream()
                        .map(com.bees360.bundle.Message.BundleContactMessage::toBuilder)
                        .map(
                                com.bees360.bundle.Message.BundleContactMessage.Builder
                                        ::clearIsPrimary)
                        .map(com.bees360.bundle.Message.BundleContactMessage.Builder::build)
                        .toList();
        var illegalCreationRequest =
                creationRequest.toBuilder().clearContact().addAllContact(illegalContact).build();

        assertThrows(
                IllegalArgumentException.class,
                () -> bundleManager.createBundle(illegalCreationRequest));
    }

    @Test
    public void testCreateBundleWithMultiplePrimaryContacts() {
        var contacts =
                List.of(randomBundleContact("Insured", true), randomBundleContact("Agent", true));
        var creationRequest =
                randomCreateBundleRequest().toBuilder()
                        .clearContact()
                        .addAllContact(contacts)
                        .build();
        var insuredBy = creationRequest.getInsuredBy().getValue();
        var processedBy = creationRequest.getProcessedBy().getValue();
        var contract = randomContract(insuredBy, processedBy);

        Mockito.doReturn(contract)
                .when(contractRepository)
                .findByCompanyId(Mockito.eq(insuredBy), Mockito.eq(processedBy));

        assertThrows(
                IllegalArgumentException.class, () -> bundleManager.createBundle(creationRequest));
    }

    @Test
    public void testCreateBundleWithNoMainAddress() {
        var addresses = List.of(randomAddress(false), randomAddress(false));
        var creationRequest =
                randomCreateBundleRequest().toBuilder()
                        .clearBuildingAddress()
                        .addAllBuildingAddress(addresses)
                        .build();
        var insuredBy = creationRequest.getInsuredBy().getValue();
        var processedBy = creationRequest.getProcessedBy().getValue();
        var contract = randomContract(insuredBy, processedBy);

        Mockito.doReturn(contract)
                .when(contractRepository)
                .findByCompanyId(Mockito.eq(insuredBy), Mockito.eq(processedBy));

        assertThrows(
                IllegalArgumentException.class, () -> bundleManager.createBundle(creationRequest));
    }

    @Test
    public void testCreateBundleWithMultipleMainAddresses() {
        var addresses = List.of(randomAddress(true), randomAddress(true));
        var creationRequest =
                randomCreateBundleRequest().toBuilder()
                        .clearBuildingAddress()
                        .addAllBuildingAddress(addresses)
                        .build();
        var insuredBy = creationRequest.getInsuredBy().getValue();
        var processedBy = creationRequest.getProcessedBy().getValue();
        var contract = randomContract(insuredBy, processedBy);

        Mockito.doReturn(contract)
                .when(contractRepository)
                .findByCompanyId(Mockito.eq(insuredBy), Mockito.eq(processedBy));

        assertThrows(
                IllegalArgumentException.class, () -> bundleManager.createBundle(creationRequest));
    }

    @Test
    public void testFindBundleByIdWithContactProtocolShouldSucceed() {
        var bundleResponse = createRandomBundle();
        var bundleId = bundleResponse.getId().getValue();
        var createdProjectIds =
                bundleResponse.getResultList().stream()
                        .map(
                                com.bees360.bundle.Message.CreateBundleResponse
                                                .CreateBundleProjectResponse
                                        ::getProjectId)
                        .map(StringValue::getValue)
                        .toList();

        var mockedContacts = new ArrayList<Contact>();
        var mockedPrimaryContactRecords = new ArrayList<PrimaryContactRecord>();
        var exceptedAttemptTimes = 0;
        var exceptedSuccessStatus = false;
        for (String projectId : createdProjectIds) {
            var primaryContactRole = randomPrimaryContactRole(List.of());
            var contact =
                    Contact.ContactBuilder.newBuilder()
                            .setProjectId(projectId)
                            .setFullName(secureRandomStringUtils.nextAlphabetic(12))
                            .setRole(primaryContactRole.getName())
                            .setPrimaryEmail(
                                    secureRandomStringUtils.nextAlphabetic(6) + "@bees360.com")
                            .setPrimaryPhone("1" + secureRandomStringUtils.nextNumeric(10))
                            .setIsPrimary(true)
                            .build();
            mockedContacts.add(contact);

            // Pre-primary contact's contact record
            var pre =
                    randomPrimaryContactRecord(
                            projectId,
                            randomPrimaryContactRole(List.of(primaryContactRole)).name());
            // Current-primary contact's contact record
            var current = randomPrimaryContactRecord(projectId, primaryContactRole.name());
            mockedPrimaryContactRecords.add(pre);
            mockedPrimaryContactRecords.add(current);
            exceptedAttemptTimes = exceptedAttemptTimes + current.getFullAttemptTimes();
            exceptedSuccessStatus = exceptedSuccessStatus || current.isSuccess();
        }

        Mockito.doReturn(mockedContacts).when(contactManager).findByQuery(Mockito.any());
        Mockito.doReturn(mockedPrimaryContactRecords)
                .when(primaryContactRecordProvider)
                .findByQuery(Mockito.any());

        var bundle = bundleManager.findById(bundleId);
        var contactProtocol = bundle.getContactProtocol();

        assertEquals(exceptedAttemptTimes, contactProtocol.getTimes().getValue());
        assertEquals(exceptedSuccessStatus, contactProtocol.getIsSuccess().getValue());
    }

    private PrimaryContactRecord randomPrimaryContactRecord(String projectId, String toRole) {
        var fullAttemptTimes = nextInt(1, 4);
        return PrimaryContactRecord.from(
                com.bees360.contact.Message.PrimaryContactRecordMessage.newBuilder()
                        .setProjectId(projectId)
                        .setFromRole(ContactRoleEnum.PILOT.name())
                        .setToRole(toRole)
                        .setFullAttemptTimes(fullAttemptTimes)
                        .setIsSuccess(BoolValue.of(fullAttemptTimes > 2))
                        .build());
    }

    private ContactRoleEnum randomPrimaryContactRole(List<ContactRoleEnum> excludeRoles) {
        var primaryContactRoles =
                Iterables.toStream(ContactRoleEnum.primaryContactRoles())
                        .filter(role -> !excludeRoles.contains(role))
                        .toList();
        return primaryContactRoles.get(nextInt(0, primaryContactRoles.size()));
    }

    @Test
    public void testFindBundleByIdShouldSucceed() {
        super.testFindByIdShouldSucceed();
    }

    @Test
    public void testUpdateServiceTypeShouldSubmitActivity() {
        var bundleId = createRandomBundle().getId().getValue();
        var bundle = bundleManager.findById(bundleId);
        var activityCaptor = ArgumentCaptor.forClass(Activity.class);
        var originalBundle = bundleManager.findById(bundleId);
        assertTrue(
                bundleManager.updateBundle(
                        com.bees360.bundle.Message.UpdateBundleRequest.newBuilder()
                                .addAllBundleId(List.of(bundleId))
                                .setServiceType(
                                        com.bees360.bundle.Message.UpdateBundleRequest
                                                .UpdateServiceTypeRequest.newBuilder()
                                                .setValue(FOUR_POINT_UNDERWRITING)
                                                .setReason(of("unit test"))
                                                .build())
                                .build(),
                        "11000"));
        var updatedBundle = bundleManager.findById(bundleId);
        assertNotEquals(originalBundle.getServiceType(), updatedBundle.getServiceType());
        assertEquals(FOUR_POINT_UNDERWRITING, updatedBundle.getServiceType());
        Mockito.verify(activityManager, Mockito.atLeastOnce())
                .submitActivity(activityCaptor.capture());
        var activity =
                activityCaptor.getAllValues().stream()
                        .filter(
                                a ->
                                        StringUtils.equals(
                                                a.getFiledName(), ACTIVITY_FIELD_NAME_SERVICE_TYPE))
                        .findFirst()
                        .orElse(null);
        Assertions.assertNotNull(activity);
        Assertions.assertEquals(
                ServiceTypeEnum.valueOf(bundle.getServiceType().getNumber()).getName(),
                activity.getOldValue());
        Assertions.assertEquals(
                ServiceTypeEnum.valueOf(FOUR_POINT_UNDERWRITING.getNumber()).getName(),
                activity.getValue());
        Assertions.assertEquals(
                BundleActivities.ACTIVITY_FIELD_NAME_SERVICE_TYPE, activity.getFiledName());
        Assertions.assertEquals("unit test", activity.getComment().getContent());
    }

    @Test
    public void testUpdateYearBuilt() {
        super.testUpdateYearBuilt();
    }

    @Test
    public void testUpdateNumberOfBuildings() {
        super.testUpdateNumberOfBuildings();
    }

    @Test
    public void testUpdatePolicyNumber() {
        super.testUpdatePolicyNumber();
    }

    @Test
    public void testUpdatePolicyEffectiveDate() {
        super.testUpdatePolicyEffectiveDate();
    }

    @Test
    public void testUpdatePolicyRenewal() {
        super.testUpdatePolicyRenewal();
    }

    @Test
    public void testUpdatePolicyTypeAndPropertyType() {
        super.testUpdatePolicyTypeAndPropertyType();
    }

    @Test
    public void testUpdateInspectionNumber() {
        super.testUpdateInspectionNumber();
    }

    @Test
    public void testUpdateAddress() {
        super.testUpdateAddress();
    }

    @Test
    public void testUpdateContact() {
        super.testUpdateContact();
    }

    @Test
    public void testCreateBundleProject() {
        super.testCreateBundleProject();
        var activityCaptor = ArgumentCaptor.forClass(Activity.class);
        Mockito.verify(activityManager, Mockito.atLeastOnce())
                .submitActivity(activityCaptor.capture());
        var actualActivity =
                activityCaptor.getAllValues().stream()
                        .filter(
                                a ->
                                        StringUtils.equals(
                                                a.getFiledName(),
                                                ACTIVITY_FIELD_NAME_BUNDLE_PROJECT))
                        .findFirst()
                        .orElse(null);
        Assertions.assertEquals(ActionType.CREATE.name(), actualActivity.getAction());
        Assertions.assertEquals(ACTIVITY_ENTITY_TYPE_BUNDLE, actualActivity.getEntityType());
        Assertions.assertTrue(StringUtils.isNotEmpty(actualActivity.getValue()));
    }

    @Test
    public void testAddBundleProject() {
        super.testAddBundleProject();
        var activityCaptor = ArgumentCaptor.forClass(Activity.class);
        Mockito.verify(activityManager, Mockito.atLeastOnce())
                .submitActivity(activityCaptor.capture());
        var actualActivity =
                activityCaptor.getAllValues().stream()
                        .filter(
                                a ->
                                        StringUtils.equals(
                                                a.getFiledName(),
                                                ACTIVITY_FIELD_NAME_BUNDLE_PROJECT))
                        .findFirst()
                        .orElse(null);
        Assertions.assertEquals(ActionType.CREATE.name(), actualActivity.getAction());
        Assertions.assertEquals(ACTIVITY_ENTITY_TYPE_BUNDLE, actualActivity.getEntityType());
        Assertions.assertTrue(StringUtils.isNotEmpty(actualActivity.getValue()));
    }

    @Test
    public void testAddBundleProjectWithMismatchContract() {
        super.testAddBundleProjectWithMismatchContract();
    }

    @Test
    public void testRemoveBundleProject() {
        super.testRemoveBundleProject();
        var activityCaptor = ArgumentCaptor.forClass(Activity.class);
        Mockito.verify(activityManager, Mockito.atLeastOnce())
                .submitActivity(activityCaptor.capture());
        var actualActivity =
                activityCaptor.getAllValues().stream()
                        .filter(
                                a ->
                                        StringUtils.equals(
                                                a.getFiledName(),
                                                ACTIVITY_FIELD_NAME_BUNDLE_PROJECT))
                        .filter(a -> StringUtils.equals(a.getAction(), ActionType.DELETE.name()))
                        .findFirst()
                        .orElse(null);
        Assertions.assertEquals(ACTIVITY_ENTITY_TYPE_BUNDLE, actualActivity.getEntityType());
        Assertions.assertTrue(StringUtils.isNotEmpty(actualActivity.getValue()));
    }

    @Test
    public void testRemoveAllBundleProjectShouldFail() {
        super.testRemoveAllBundleProjectShouldFail();
    }
}
