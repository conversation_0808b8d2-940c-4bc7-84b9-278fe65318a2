package com.bees360.bundle.job;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.job.registry.SendCustomerBundleEmailJob;
import com.bees360.mail.MailMessage;
import com.bees360.mail.MailSender;
import com.bees360.mail.MailSenderProvider;
import com.bees360.mail.util.MailMessageFactory;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class SendCustomerBundleEmailJobExecutorTest {

    @Mock private MailSenderProvider mailSenderProvider;

    @Mock private MailMessageFactory mailMessageFactory;

    @Mock private MailSender mailSender;

    @Mock private MailMessage mailMessage;

    @InjectMocks private SendCustomerBundleEmailJobExecutor executor;

    @Test
    public void testHandle_ValidJobParameters() {
        // Arrange
        Collection<String> toRecipients = List.of("<EMAIL>");
        Collection<String> ccRecipients = List.of("<EMAIL>");
        Collection<String> bccRecipients = List.of("<EMAIL>");
        String mailSenderName = "testSender";
        String templateKey = "testTemplate";
        String variablesJson = "{\"key\":\"value\"}";

        SendCustomerBundleEmailJob job =
                new SendCustomerBundleEmailJob()
                        .setToRecipients(toRecipients)
                        .setCcRecipients(ccRecipients)
                        .setBccRecipients(bccRecipients)
                        .setMailSender(mailSenderName)
                        .setTemplateKey(templateKey)
                        .setVariablesJson(variablesJson);

        // Mock behavior for MailSenderProvider
        when(mailSenderProvider.get(mailSenderName)).thenReturn(mailSender);

        // Mock behavior for MailMessageFactory
        when(mailMessageFactory.create(
                        eq(toRecipients),
                        eq(ccRecipients),
                        eq(bccRecipients),
                        eq(templateKey),
                        eq(variablesJson),
                        eq(Map.of())))
                .thenReturn(mailMessage);

        // Act
        executor.handle(job);

        // Verify interactions
        verify(mailSenderProvider, times(1)).get(mailSenderName);
        verify(mailMessageFactory, times(1))
                .create(
                        toRecipients,
                        ccRecipients,
                        bccRecipients,
                        templateKey,
                        variablesJson,
                        Map.of());
        verify(mailSender, times(1)).send(mailMessage);
    }
}
