package com.bees360.bundle.event;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.bees360.activity.ActivityManager;
import com.bees360.event.registry.BundleStateChanged;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;

class SubmitActivityOnBundleStateChangedTest {

    private ActivityManager activityManager;

    private SubmitActivityOnBundleStateChanged listener;

    @BeforeEach
    void setUp() {
        activityManager = mock(ActivityManager.class);
        listener = new SubmitActivityOnBundleStateChanged(activityManager);
    }

    @Test
    void testSubmitActivity_WhenStateChanged() throws IOException {
        BundleStateChanged event = new BundleStateChanged();
        event.setBundleId(randomId());
        event.setOldState("OPEN");
        event.setState("CLOSE");
        event.setUpdatedBy(randomId());
        event.setUpdatedAt(Instant.now());

        listener.handle(event);

        verify(activityManager, times(1)).submitActivity(any());
    }

    @Test
    void testNotSubmitActivityWhenStateNotChanged() throws IOException {
        BundleStateChanged event = new BundleStateChanged();
        event.setBundleId(randomId());
        event.setOldState("OPEN");
        event.setState("OPEN");
        event.setUpdatedBy(randomId());
        event.setUpdatedAt(Instant.now());

        listener.handle(event);

        verify(activityManager, never()).submitActivity(any());
    }

    private String randomId() {
        return "1" + RandomStringUtils.secure().nextNumeric(10);
    }
}
