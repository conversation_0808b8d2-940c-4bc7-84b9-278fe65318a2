package com.bees360.bundle.event;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.bundle.BundleProvider;
import com.bees360.bundle.Message;
import com.bees360.bundle.notification.BundleEmailContext;
import com.bees360.bundle.notification.CustomerBundleEmailProperties;
import com.bees360.bundle.notification.SendCustomerBundleEmailJobProvider;
import com.bees360.customer.Customer;
import com.bees360.event.registry.BundleStatusChanged;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.SendCustomerBundleEmailJob;
import com.bees360.util.retry.RetryProperties;
import com.google.protobuf.BoolValue;
import com.google.protobuf.StringValue;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.time.Instant;
import java.util.function.BiFunction;

@ExtendWith(MockitoExtension.class)
class SendEmailOnBundleCompletedTest {

    @Mock private JobScheduler jobScheduler;
    @Mock private BundleProvider bundleProvider;

    @Mock
    private BiFunction<String, String, CustomerBundleEmailProperties>
            customerBundleEmailsPropertiesProvider;

    @Mock private RetryProperties jobRetryProperties;
    @Mock private SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider;
    @Mock private BundleEmailContext bundleEmailContext;
    @Mock private CustomerBundleEmailProperties emailProperties;
    @Mock private SendCustomerBundleEmailJob emailJob;

    private SendEmailOnBundleCompleted sendEmailOnBundleCompleted;

    @BeforeEach
    void setUp() {
        sendEmailOnBundleCompleted =
                new SendEmailOnBundleCompleted(
                        jobScheduler,
                        bundleProvider,
                        customerBundleEmailsPropertiesProvider,
                        jobRetryProperties,
                        sendCustomerBundleEmailJobProvider,
                        bundleEmailContext);
    }

    @Test
    void testFilter_ShouldReturnTrue_WhenBundleIsReturnedToClientAndNotCanceledAndSubscribed() {
        // Arrange
        String bundleId = randomId();
        String companyKey = "Test Insurance Company";
        BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, "RETURNED_TO_CLIENT");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, false);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(true);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_completed"))
                .thenReturn(emailProperties);

        // Act
        boolean result = sendEmailOnBundleCompleted.filter(event);

        // Assert
        assertTrue(result);
        verify(bundleProvider).findById(bundleId);
        verify(customerBundleEmailsPropertiesProvider).apply(companyKey, "bundle_completed");
    }

    @Test
    void testFilter_ShouldReturnFalse_WhenStatusIsNotReturnedToClient() {
        // Arrange
        String bundleId = randomId();
        BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, "CUSTOMER_CONTACTED");

        // Act
        boolean result = sendEmailOnBundleCompleted.filter(event);

        // Assert
        assertFalse(result);
        verify(bundleProvider, never()).findById(any());
    }

    @Test
    void testFilter_ShouldReturnFalse_WhenBundleIsCanceled() {
        // Arrange
        String bundleId = randomId();
        String companyKey = "Test Insurance Company";
        BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, "RETURNED_TO_CLIENT");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, true); // canceled

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);

        // Act
        boolean result = sendEmailOnBundleCompleted.filter(event);

        // Assert
        assertFalse(result);
        verify(bundleProvider).findById(bundleId);
        verify(customerBundleEmailsPropertiesProvider, never()).apply(any(), any());
    }

    @Test
    void testFilter_ShouldReturnFalse_WhenCustomerIsNotSubscribed() {
        // Arrange
        String bundleId = randomId();
        String companyKey = "Test Insurance Company";
        BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, "RETURNED_TO_CLIENT");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, false);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(false);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_completed"))
                .thenReturn(emailProperties);

        // Act
        boolean result = sendEmailOnBundleCompleted.filter(event);

        // Assert
        assertFalse(result);
    }

    @Test
    void testConvert_ShouldCreateRetryableEmailJob() {
        // Arrange
        String bundleId = randomId();
        String companyKey = "Test Insurance Company";
        BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, "RETURNED_TO_CLIENT");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, false);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_completed"),
                        eq(bundleEmailContext)))
                .thenReturn(emailJob);
        // Setup default retry properties
        when(jobRetryProperties.getRetryCount()).thenReturn(3);
        when(jobRetryProperties.getRetryDelay()).thenReturn(Duration.ofMinutes(1));
        when(jobRetryProperties.getRetryDelayIncreaseFactor()).thenReturn(1.5f);
        // Act
        Job result = sendEmailOnBundleCompleted.convert(event);

        // Assert
        assertNotNull(result);
        verify(bundleProvider).findById(bundleId);
        verify(sendCustomerBundleEmailJobProvider)
                .bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_completed"),
                        eq(bundleEmailContext));
    }

    @Test
    void testHandle_ShouldClearCacheAfterProcessing() {
        // Arrange
        String bundleId = randomId();
        String companyKey = "Test Insurance Company";
        BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, "RETURNED_TO_CLIENT");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, false);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(true);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_completed"))
                .thenReturn(emailProperties);
        when(sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_completed"),
                        eq(bundleEmailContext)))
                .thenReturn(emailJob);
        // Setup default retry properties
        when(jobRetryProperties.getRetryCount()).thenReturn(3);
        when(jobRetryProperties.getRetryDelay()).thenReturn(Duration.ofMinutes(1));
        when(jobRetryProperties.getRetryDelayIncreaseFactor()).thenReturn(1.5f);
        // Act
        sendEmailOnBundleCompleted.handle(event);

        // Assert
        verify(jobScheduler).schedule(any(Job.class));
    }

    @Test
    void testHandle_ShouldClearCacheEvenWhenExceptionOccurs() {
        // Arrange
        String bundleId = randomId();
        BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, "RETURNED_TO_CLIENT");

        when(bundleProvider.findById(bundleId)).thenThrow(new RuntimeException("Test exception"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> sendEmailOnBundleCompleted.handle(event));

        // Verify that cache is cleared even when exception occurs
        // This is tested by ensuring the method completes without hanging
    }

    private BundleStatusChanged createBundleStatusChangedEvent(String bundleId, String status) {
        BundleStatusChanged event = new BundleStatusChanged();
        event.setBundleId(bundleId);
        event.setStatus(status);
        event.setOldStatus("CUSTOMER_CONTACTED");
        event.setCreatedBy(randomId());
        event.setCreatedAt(Instant.now());
        event.setUpdatedBy(randomId());
        event.setUpdatedAt(Instant.now());
        return event;
    }

    private Message.BundleMessage createMockBundle(
            String bundleId, String companyKey, boolean isCanceled) {
        // Create mock customer
        com.bees360.customer.Message.CustomerMessage customerMessage =
                com.bees360.customer.Message.CustomerMessage.newBuilder()
                        .setId(randomId())
                        .setName("Test Insurance Company")
                        .setKey(companyKey)
                        .build();

        // Create mock contract
        com.bees360.contract.Message.ContractMessage contractMessage =
                com.bees360.contract.Message.ContractMessage.newBuilder()
                        .setId(randomId())
                        .setInsuredBy(customerMessage)
                        .build();

        // Create mock bundle
        return Message.BundleMessage.newBuilder()
                .setId(StringValue.newBuilder().setValue(bundleId).build())
                .setContract(contractMessage)
                .setIsCanceled(BoolValue.newBuilder().setValue(isCanceled).build())
                .build();
    }

    @Test
    void testBundleCaching_ShouldUseCachedBundleOnSecondCall() {
        // Arrange
        String bundleId = randomId();
        String companyKey = "Test Insurance Company";
        BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, "RETURNED_TO_CLIENT");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, false);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(true);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_completed"))
                .thenReturn(emailProperties);
        when(sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_completed"),
                        eq(bundleEmailContext)))
                .thenReturn(emailJob);
        // Setup default retry properties
        when(jobRetryProperties.getRetryCount()).thenReturn(3);
        when(jobRetryProperties.getRetryDelay()).thenReturn(Duration.ofMinutes(1));
        when(jobRetryProperties.getRetryDelayIncreaseFactor()).thenReturn(1.5f);
        // Act - call filter and convert which both call getBundleById
        sendEmailOnBundleCompleted.filter(event);
        sendEmailOnBundleCompleted.convert(event);

        // Assert - bundleProvider.findById should only be called once due to caching
        verify(bundleProvider, times(1)).findById(bundleId);
    }

    @Test
    void testMultipleEvents_ShouldClearCacheBetweenEvents() {
        // Arrange
        String bundleId1 = randomId();
        String bundleId2 = randomId();
        String companyKey = "Test Insurance Company";

        BundleStatusChanged event1 =
                createBundleStatusChangedEvent(bundleId1, "RETURNED_TO_CLIENT");
        BundleStatusChanged event2 =
                createBundleStatusChangedEvent(bundleId2, "RETURNED_TO_CLIENT");

        Message.BundleMessage bundle1 = createMockBundle(bundleId1, companyKey, false);
        Message.BundleMessage bundle2 = createMockBundle(bundleId2, companyKey, false);

        when(bundleProvider.findById(bundleId1)).thenReturn(bundle1);
        when(bundleProvider.findById(bundleId2)).thenReturn(bundle2);
        when(emailProperties.isSubscribed()).thenReturn(true);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_completed"))
                .thenReturn(emailProperties);
        when(sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        any(Message.BundleMessage.class),
                        any(Customer.class),
                        eq("bundle_completed"),
                        eq(bundleEmailContext)))
                .thenReturn(emailJob);
        // Setup default retry properties
        when(jobRetryProperties.getRetryCount()).thenReturn(3);
        when(jobRetryProperties.getRetryDelay()).thenReturn(Duration.ofMinutes(1));
        when(jobRetryProperties.getRetryDelayIncreaseFactor()).thenReturn(1.5f);
        // Act
        sendEmailOnBundleCompleted.handle(event1);
        sendEmailOnBundleCompleted.handle(event2);

        // Assert
        verify(bundleProvider).findById(bundleId1);
        verify(bundleProvider).findById(bundleId2);
        verify(jobScheduler, times(2)).schedule(any(Job.class));
    }

    @Test
    void testConvert_ShouldPassCorrectParametersToEmailJobProvider() {
        // Arrange
        String bundleId = randomId();
        String companyKey = "Test Insurance Company";
        BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, "RETURNED_TO_CLIENT");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, false);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_completed"),
                        eq(bundleEmailContext)))
                .thenReturn(emailJob);
        // Setup default retry properties
        when(jobRetryProperties.getRetryCount()).thenReturn(3);
        when(jobRetryProperties.getRetryDelay()).thenReturn(Duration.ofMinutes(1));
        when(jobRetryProperties.getRetryDelayIncreaseFactor()).thenReturn(1.5f);
        // Act
        sendEmailOnBundleCompleted.convert(event);

        // Assert
        verify(sendCustomerBundleEmailJobProvider)
                .bundleEmailJob(
                        eq(bundle),
                        argThat(customer -> customer.getCompanyKey().equals(companyKey)),
                        eq("bundle_completed"),
                        eq(bundleEmailContext));
    }

    @Test
    void testFilter_WithDifferentBundleStatuses() {
        // Test various bundle statuses
        String bundleId = randomId();

        // Test cases: status -> expected result
        String[][] testCases = {
            {Message.BundleStatus.RETURNED_TO_CLIENT.name(), "true"},
            {Message.BundleStatus.CUSTOMER_CONTACTED.name(), "false"},
            {Message.BundleStatus.CREATED.name(), "false"},
            {Message.BundleStatus.SITE_INSPECTED.name(), "false"},
            {Message.BundleStatus.CANCELLED.name(), "false"}
        };

        for (String[] testCase : testCases) {
            String status = testCase[0];
            boolean expectedResult = Boolean.parseBoolean(testCase[1]);

            BundleStatusChanged event = createBundleStatusChangedEvent(bundleId, status);

            if (expectedResult) {
                // Setup mocks for positive case
                String companyKey = "Test Insurance Company";
                Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, false);
                when(bundleProvider.findById(bundleId)).thenReturn(bundle);
                when(emailProperties.isSubscribed()).thenReturn(true);
                when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_completed"))
                        .thenReturn(emailProperties);
            }

            boolean result = sendEmailOnBundleCompleted.filter(event);
            assertEquals(
                    expectedResult,
                    result,
                    "Status " + status + " should return " + expectedResult);

            // Reset mocks for next iteration
            reset(bundleProvider, customerBundleEmailsPropertiesProvider);
        }
    }

    private String randomId() {
        return "1" + RandomStringUtils.secure().nextNumeric(10);
    }
}
