package com.bees360.bundle;

import com.bees360.address.AddressProvider;
import com.bees360.api.ApiStatus;
import com.bees360.bundle.Message.BundleProjectSyncFieldType;
import com.bees360.bundle.config.BundleServiceConfig;
import com.bees360.bundle.config.BundleServiceMockConfig;
import com.bees360.contract.ContractRepository;
import com.bees360.customer.Customer;
import com.bees360.customer.CustomerProvider;
import com.bees360.customer.Message;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectPolicyManager;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.member.MemberManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.state.ProjectStateChangeReasonProvider;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.user.UserProvider;
import com.google.protobuf.StringValue;

import org.apache.commons.lang3.function.TriFunction;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(
        classes = AggregateBundleManagerSyncProjectTest.Config.class,
        properties = {"grpc.server.in-process-name=AggregateBundleManagerSyncProjectTest"})
@DirtiesContext
@ImportAutoConfiguration({
    RefreshAutoConfiguration.class,
})
public class AggregateBundleManagerSyncProjectTest extends AbstractBundleManagerTest {

    @Import({
        BundleServiceConfig.class,
        BundleServiceMockConfig.class,
    })
    @Configuration
    static class Config {}

    @Autowired CustomerProvider customerProvider;

    @Autowired ProjectPolicyManager projectPolicyManager;

    @Autowired
    @Qualifier("policyTypePropertyTypeValidator")
    TriFunction<String, String, Integer, Integer> validator;

    @MockitoBean ProjectStateChangeReasonProvider projectStateChangeReasonProvider;

    @MockitoBean ProjectStatusManager projectStatusManager;

    @MockitoBean MemberManager memberManager;

    @MockitoBean UserProvider userProvider;

    @Autowired JooqBundleMemberManager jooqBundleMemberManager;

    public AggregateBundleManagerSyncProjectTest(
            @Autowired @Qualifier("grpcBundleManagerClient") BundleManager bundleManager,
            @Autowired AddressProvider mockAddressProvider,
            @Autowired ContractRepository mockContractRepository,
            @Autowired BatchProjectCreator mockBatchProjectCreator,
            @Autowired ExternalIntegrationProvider mockExternalIntegrationProvider,
            @Autowired ProjectGroupManager mockProjectGroupManager,
            @Autowired ProjectIIManager mockProjectIIManager,
            @Autowired ProjectStateChangeReasonProvider projectStateChangeReasonProvider) {
        super(
                bundleManager,
                mockAddressProvider,
                mockContractRepository,
                mockBatchProjectCreator,
                mockExternalIntegrationProvider,
                mockProjectGroupManager,
                mockProjectIIManager,
                projectStateChangeReasonProvider,
                null);
    }

    @BeforeEach
    public void setup() {
        Mockito.doAnswer(
                        args -> {
                            var customerId = args.getArgument(0, String.class);
                            return Customer.of(
                                    Message.CustomerMessage.newBuilder()
                                            .setId(customerId)
                                            .setKey(customerId)
                                            .build());
                        })
                .when(customerProvider)
                .findById(Mockito.anyString());
        Mockito.doAnswer(
                        args -> {
                            return args.getArgument(2, Integer.class);
                        })
                .when(validator)
                .apply(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt());
    }

    @Test
    public void testSyncBundlePolicyNo() {
        var bundleId = createRandomBundle().getId().getValue();
        var requestBy = "10000";
        var bundle = bundleManager.findById(bundleId);
        var policyNo = bundle.getPolicyNo().getValue();
        var projectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(true)
                            .when(projectPolicyManager)
                            .updatePolicyNumber(projectId, policyNo, requestBy);
                });

        var request =
                com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setSyncField(BundleProjectSyncFieldType.POLICY_NO)
                        .build();
        var updateResponse = bundleManager.syncBundleProject(request, requestBy);
        updateResponse
                .getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            Assertions.assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        projectIds.forEach(
                projectId -> {
                    Mockito.verify(projectPolicyManager)
                            .updatePolicyNumber(projectId, policyNo, requestBy);
                });
    }

    @Test
    public void testSyncBundlePolicyNoWithPartialSuccess() {
        var bundleId = createRandomBundle().getId().getValue();
        var requestBy = "10000";
        var bundle = bundleManager.findById(bundleId);
        var policyNo = bundle.getPolicyNo().getValue();
        var projectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(true)
                            .when(projectPolicyManager)
                            .updatePolicyNumber(projectId, policyNo, requestBy);
                });
        Mockito.doThrow(new IllegalArgumentException("fail to update"))
                .when(projectPolicyManager)
                .updatePolicyNumber(projectIds.get(0), policyNo, requestBy);

        var request =
                com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setSyncField(BundleProjectSyncFieldType.POLICY_NO)
                        .build();
        var updateResponse = bundleManager.syncBundleProject(request, requestBy);
        updateResponse
                .getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            if (result.getProjectId().equals(projectIds.get(0))) {
                                Assertions.assertNotEquals(
                                        ApiStatus.OK.toMessage(), result.getStatus());
                                Assertions.assertNotNull(result.getStatus().getDescription());
                            }
                        });
    }

    @Test
    public void testSyncBundleInspectionNo() {
        var bundleId = createRandomBundle().getId().getValue();
        var requestBy = "10000";
        var bundle = bundleManager.findById(bundleId);
        var inspectionNo = bundle.getInspectionNo().getValue();
        var projectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(true)
                            .when(projectIIManager)
                            .updateInspectionNumber(projectId, inspectionNo, requestBy);
                });

        var request =
                com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setSyncField(BundleProjectSyncFieldType.INSPECTION_NO)
                        .build();
        var updateResponse = bundleManager.syncBundleProject(request, requestBy);
        updateResponse
                .getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            Assertions.assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        projectIds.forEach(
                projectId -> {
                    Mockito.verify(projectIIManager)
                            .updateInspectionNumber(projectId, inspectionNo, requestBy);
                });
    }

    @Test
    public void testSyncBundleServiceType() {
        var bundleId = createRandomBundle().getId().getValue();
        var requestBy = "10000";
        var bundle = bundleManager.findById(bundleId);
        var serviceType =
                com.bees360.project.ServiceTypeEnum.valueOf(bundle.getServiceType().getNumber());
        var projectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(true)
                            .when(projectIIManager)
                            .updateServiceTypeById(projectId, serviceType, requestBy);
                });

        var request =
                com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setSyncField(BundleProjectSyncFieldType.SERVICE_TYPE)
                        .build();
        var updateResponse = bundleManager.syncBundleProject(request, requestBy);
        updateResponse
                .getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            Assertions.assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        projectIds.forEach(
                projectId -> {
                    Mockito.verify(projectIIManager)
                            .updateServiceTypeById(projectId, serviceType, requestBy);
                });
    }

    @Test
    public void testSyncBundleYearBuilt() {
        var bundleId = createRandomBundle().getId().getValue();
        var requestBy = "10000";
        var bundle = bundleManager.findById(bundleId);
        var yearBuilt = bundle.getMetadata().getYearBuilt().getValue();
        var projectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(true)
                            .when(projectPolicyManager)
                            .updateYearBuilt(projectId, yearBuilt, requestBy);
                });

        var request =
                com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setSyncField(BundleProjectSyncFieldType.YEAR_BUILT)
                        .build();
        var updateResponse = bundleManager.syncBundleProject(request, requestBy);
        updateResponse
                .getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            Assertions.assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        projectIds.forEach(
                projectId -> {
                    Mockito.verify(projectPolicyManager)
                            .updateYearBuilt(projectId, yearBuilt, requestBy);
                });
    }

    @Test
    public void testSyncBundlePolicyAndPropertyType() {
        var bundleId = createRandomBundle().getId().getValue();
        var requestBy = "10000";
        var bundle = bundleManager.findById(bundleId);
        var policyType = bundle.getMetadata().getPolicy().getType().getValue();
        var propertyType = bundle.getMetadata().getPolicy().getTypeOfProperty().getNumber();
        var projectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(true)
                            .when(projectPolicyManager)
                            .updatePolicyAndPropertyType(
                                    projectId, policyType, propertyType, requestBy);
                });

        var request =
                com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setSyncField(BundleProjectSyncFieldType.POLICY_AND_PROPERTY_TYPE)
                        .build();
        var updateResponse = bundleManager.syncBundleProject(request, requestBy);
        updateResponse
                .getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            Assertions.assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        projectIds.forEach(
                projectId -> {
                    Mockito.verify(projectPolicyManager)
                            .updatePolicyAndPropertyType(
                                    projectId, policyType, propertyType, requestBy);
                });
    }

    @Test
    public void testSyncBundlePolicyRenewal() {
        var bundleId = createRandomBundle().getId().getValue();
        var requestBy = "10000";
        var bundle = bundleManager.findById(bundleId);
        var policyRenewal = bundle.getMetadata().getPolicy().getIsRenewal().getValue();
        var projectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(true)
                            .when(projectIIManager)
                            .updateProjectPolicyRenewal(projectId, policyRenewal);
                });

        var request =
                com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setSyncField(BundleProjectSyncFieldType.POLICY_RENEWAL)
                        .build();
        var updateResponse = bundleManager.syncBundleProject(request, requestBy);
        updateResponse
                .getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            Assertions.assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        projectIds.forEach(
                projectId -> {
                    Mockito.verify(projectIIManager)
                            .updateProjectPolicyRenewal(projectId, policyRenewal);
                });
    }

    @Test
    public void testSyncBundlePolicyEffectiveDate() {
        var bundleId = createRandomBundle().getId().getValue();
        var requestBy = "10000";
        var bundle = bundleManager.findById(bundleId);
        var effectiveDate =
                com.bees360.util.DateTimes.toLocalDate(
                        bundle.getMetadata().getPolicy().getPolicyEffectiveDate());
        var projectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(true)
                            .when(projectPolicyManager)
                            .updatePolicyEffectiveDate(projectId, effectiveDate, requestBy);
                });

        var request =
                com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setSyncField(BundleProjectSyncFieldType.POLICY_EFFECTIVE_DATE)
                        .build();
        var updateResponse = bundleManager.syncBundleProject(request, requestBy);
        updateResponse
                .getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            Assertions.assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        projectIds.forEach(
                projectId -> {
                    Mockito.verify(projectPolicyManager)
                            .updatePolicyEffectiveDate(projectId, effectiveDate, requestBy);
                });
    }

    @Test
    public void testSyncBundleUnderwriter() {
        var bundleId = createRandomBundle().getId().getValue();
        var requestBy = "10000";
        var underwriter = "10001";
        var bundle = bundleManager.findById(bundleId);
        jooqBundleMemberManager.setMember(bundleId, RoleEnum.UNDERWRITER, underwriter, requestBy);
        var request =
                com.bees360.bundle.Message.SyncBundleProjectRequest.newBuilder()
                        .setBundleId(StringValue.of(bundleId))
                        .setSyncField(BundleProjectSyncFieldType.UNDERWRITER)
                        .build();
        var projectIds = bundle.getProjectList().stream().map(ProjectMessage::getId).toList();
        projectIds.forEach(
                projectId -> {
                    Mockito.doReturn(true)
                            .when(memberManager)
                            .setMember(projectId, underwriter, RoleEnum.UNDERWRITER, requestBy);
                });
        var updateResponse = bundleManager.syncBundleProject(request, requestBy);
        updateResponse
                .getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            Assertions.assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        projectIds.forEach(
                projectId -> {
                    Mockito.verify(memberManager)
                            .setMember(projectId, underwriter, RoleEnum.UNDERWRITER, requestBy);
                });
    }
}
