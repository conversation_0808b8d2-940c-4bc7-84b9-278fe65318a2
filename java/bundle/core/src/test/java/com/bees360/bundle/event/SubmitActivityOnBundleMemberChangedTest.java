package com.bees360.bundle.event;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.bees360.activity.ActivityManager;
import com.bees360.event.registry.BundleMemberChanged;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;

class SubmitActivityOnBundleMemberChangedTest {

    private ActivityManager activityManager;

    private SubmitActivityOnBundleMemberChanged listener;

    @BeforeEach
    void setUp() {
        activityManager = mock(ActivityManager.class);
        listener = new SubmitActivityOnBundleMemberChanged(activityManager);
    }

    @Test
    void testSubmitActivityWhenMemberChanged() throws IOException {
        BundleMemberChanged.BundleMember oldValue = new BundleMemberChanged.BundleMember();
        oldValue.setUserId(randomId());
        oldValue.setRole("AGENT");

        BundleMemberChanged.BundleMember newValue = new BundleMemberChanged.BundleMember();
        newValue.setUserId(randomId());
        newValue.setRole("AGENT");
        newValue.setUpdatedBy(randomId());
        newValue.setUpdatedAt(Instant.now());

        BundleMemberChanged event = new BundleMemberChanged();
        event.setBundleId(randomId());
        event.setOldValue(oldValue);
        event.setNewValue(newValue);

        listener.handle(event);

        verify(activityManager, times(1)).submitActivity(any());
    }

    @Test
    void testNotSubmitActivityWhenMemberNotChanged() throws IOException {
        BundleMemberChanged.BundleMember oldValue = new BundleMemberChanged.BundleMember();
        oldValue.setUserId(randomId());
        oldValue.setRole("AGENT");

        BundleMemberChanged.BundleMember newValue = new BundleMemberChanged.BundleMember();
        newValue.setUserId(oldValue.getUserId());
        newValue.setRole("AGENT");
        newValue.setUpdatedBy(randomId());
        newValue.setUpdatedAt(Instant.now());

        BundleMemberChanged event = new BundleMemberChanged();
        event.setBundleId(randomId());
        event.setOldValue(oldValue);
        event.setNewValue(newValue);

        listener.handle(event);

        verify(activityManager, never()).submitActivity(any());
    }

    @Test
    void testSubmitActivityWhenAddingNewMember() throws IOException {
        BundleMemberChanged.BundleMember newValue = new BundleMemberChanged.BundleMember();
        newValue.setUserId(randomId());
        newValue.setRole("AGENT");
        newValue.setUpdatedBy(randomId());
        newValue.setUpdatedAt(Instant.now());

        BundleMemberChanged event = new BundleMemberChanged();
        event.setBundleId(randomId());
        event.setNewValue(newValue);

        listener.handle(event);

        verify(activityManager, times(1)).submitActivity(any());
    }

    private String randomId() {
        return "1" + RandomStringUtils.secure().nextNumeric(10);
    }
}
