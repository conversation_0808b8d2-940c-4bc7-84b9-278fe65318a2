package com.bees360.bundle.config;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.CommentManager;
import com.bees360.address.AddressProvider;
import com.bees360.contact.PrimaryContactRecordProvider;
import com.bees360.contract.ContractRepository;
import com.bees360.customer.CustomerProvider;
import com.bees360.policy.PolicyManager;
import com.bees360.project.BuildingManager;
import com.bees360.project.ContactManager;
import com.bees360.project.ExternalIntegrationProvider;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectPolicyManager;
import com.bees360.project.creator.BatchProjectCreator;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.member.MemberManager;
import com.bees360.project.state.ProjectStateChangeReasonProvider;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.state.StateChangeReasonGroupProvider;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.project.tag.ProjectTagManager;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportProcessor;
import com.bees360.user.UserProvider;

import net.devh.boot.grpc.client.autoconfigure.GrpcClientAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration;

import org.apache.commons.lang3.function.TriFunction;
import org.mockito.Mockito;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@ImportAutoConfiguration({
    GrpcServerAutoConfiguration.class,
    GrpcServerFactoryAutoConfiguration.class,
    GrpcClientAutoConfiguration.class,
})
@Import({
    BundleServiceConfig.class,
    GrpcBundleManagerConfig.class,
})
@Configuration
public class BundleServiceMockConfig {
    @Bean
    AddressProvider addressProvider() {
        return Mockito.mock(AddressProvider.class);
    }

    @Bean
    ContractRepository contractRepository() {
        return Mockito.mock(ContractRepository.class);
    }

    @Bean
    BatchProjectCreator batchProjectCreator() {
        return Mockito.mock(BatchProjectCreator.class);
    }

    @Bean
    ExternalIntegrationProvider externalIntegrationProvider() {
        return Mockito.mock(ExternalIntegrationProvider.class);
    }

    @Bean
    ProjectGroupManager projectGroupManager() {
        return Mockito.mock(ProjectGroupManager.class);
    }

    @Bean
    CustomerProvider customerProvider() {
        return Mockito.mock(CustomerProvider.class);
    }

    @Bean
    ProjectIIManager projectIIRepository() {
        return Mockito.mock(ProjectIIManager.class);
    }

    @Bean
    PolicyManager policyManager() {
        return Mockito.mock(PolicyManager.class);
    }

    @Bean
    BuildingManager buildingManager() {
        return Mockito.mock(BuildingManager.class);
    }

    @Bean
    ProjectPolicyManager projectPolicyManager() {
        return Mockito.mock(ProjectPolicyManager.class);
    }

    @Bean
    CommentManager commentManager() {
        return Mockito.mock(CommentManager.class);
    }

    @Bean("policyTypePropertyTypeValidator")
    TriFunction<String, String, Integer, Integer> validator() {
        return Mockito.mock(TriFunction.class);
    }

    @Bean
    ProjectStateChangeReasonProvider projectStateChangeReasonProvider() {
        return Mockito.mock(ProjectStateChangeReasonProvider.class);
    }

    @Bean
    ProjectStatusManager projectStatusManager() {
        return Mockito.mock(ProjectStatusManager.class);
    }

    @Bean
    ProjectTagManager projectTagManager() {
        return Mockito.mock(ProjectTagManager.class);
    }

    @Bean
    ContactManager contactManager() {
        return Mockito.mock(ContactManager.class);
    }

    @Bean
    PrimaryContactRecordProvider primaryContactRecordProvider() {
        return Mockito.mock(PrimaryContactRecordProvider.class);
    }

    @Bean
    MemberManager memberManager() {
        return Mockito.mock(MemberManager.class);
    }

    @Bean
    UserProvider userProvider() {
        return Mockito.mock(UserProvider.class);
    }

    @Bean
    ActivityManager activityManager() {
        return Mockito.mock(ActivityManager.class);
    }

    @Bean
    ReportProcessor reportProcessor() {
        return Mockito.mock(ReportProcessor.class);
    }

    @Bean
    ReportManager reportManager() {
        return Mockito.mock(ReportManager.class);
    }

    @Bean
    ProjectStateManager projectStateManager() {
        return Mockito.mock(ProjectStateManager.class);
    }

    @Bean
    StateChangeReasonGroupProvider stateChangeReasonGroupProvider() {
        return Mockito.mock(StateChangeReasonGroupProvider.class);
    }
}
