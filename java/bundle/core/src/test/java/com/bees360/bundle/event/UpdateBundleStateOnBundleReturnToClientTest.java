package com.bees360.bundle.event;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import com.bees360.bundle.BundleStateManager;
import com.bees360.bundle.Message;
import com.bees360.event.registry.BundleStatusChanged;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;

class UpdateBundleStateOnBundleReturnToClientTest {

    private UpdateBundleStateOnBundleReturnToClient handler;
    private BundleStateManager bundleStateManager;

    @BeforeEach
    void setUp() {
        bundleStateManager = mock(BundleStateManager.class);
        handler = new UpdateBundleStateOnBundleReturnToClient(bundleStateManager);
    }

    /**
     * Test case: Event status is not RETURNED_TO_CLIENT Expected: No call to
     * bundleStateManager.changeState(...)
     */
    @Test
    void handle_whenStatusIsNotReturnedToClient_shouldNotCallChangeState() throws IOException {
        BundleStatusChanged event = createEvent("OPEN");

        handler.handle(event);

        verifyNoInteractions(bundleStateManager);
    }

    /**
     * Test case: Event status is RETURNED_TO_CLIENT Expected: bundleStateManager.changeState(...)
     * is called with correct parameters
     */
    @Test
    void handle_whenStatusIsReturnedToClient_shouldCallChangeStateWithCorrectParams()
            throws IOException {
        BundleStatusChanged event = createEvent("RETURNED_TO_CLIENT");

        handler.handle(event);

        verify(bundleStateManager)
                .changeState(
                        eq("test-bundle-id"),
                        eq(Message.BundleState.CLOSE),
                        eq("COMPLETED"),
                        eq("test-updated-by"));
    }

    private BundleStatusChanged createEvent(String status) {
        BundleStatusChanged event = new BundleStatusChanged();
        event.setBundleId("test-bundle-id");
        event.setCreatedAt(Instant.now());
        event.setCreatedBy("test-created-by");
        event.setOldStatus("PREVIOUS_STATUS");
        event.setStatus(status);
        event.setUpdatedAt(Instant.now());
        event.setUpdatedBy("test-updated-by");
        return event;
    }
}
