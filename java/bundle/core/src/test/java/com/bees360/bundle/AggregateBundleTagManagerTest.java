package com.bees360.bundle;

import static com.bees360.bundle.JooqBundleManager.BUNDLE_PROJECT_GROUP_TYPE;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.bees360.api.ApiStatus;
import com.bees360.api.Entity;
import com.bees360.bundle.config.BundleServiceConfig;
import com.bees360.bundle.config.BundleServiceMockConfig;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.group.Message.ProjectGroupMessage;
import com.bees360.project.group.ProjectGroup;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.tag.Message;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagManager;
import com.bees360.project.tag.ProjectTagRepository;
import com.bees360.util.Iterables;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.stream.IntStreams;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@SpringBootTest(
        classes = AggregateBundleTagManagerTest.Config.class,
        properties = {
            "grpc.server.in-process-name=AggregateBundleTagManagerTest",
        })
@DirtiesContext
@Transactional
@ImportAutoConfiguration({
    RefreshAutoConfiguration.class,
})
public class AggregateBundleTagManagerTest extends AbstractBundleTagManagerTest {

    @Import({
        BundleServiceConfig.class,
        BundleServiceMockConfig.class,
    })
    @Configuration
    static class Config {}

    @Autowired ProjectTagManager projectTagManager;

    @Autowired ProjectIIManager projectIIManager;

    @Autowired ProjectGroupManager projectGroupManager;

    public AggregateBundleTagManagerTest(
            @Autowired BundleTagManager bundleTagManager,
            @Autowired ProjectTagRepository projectTagRepository) {
        super(bundleTagManager, projectTagRepository);
    }

    @Test
    void testSyncBundleTag() {
        var bundleId = randomId();
        var requestBy = "19999";

        // prepare data
        var companyId = randomId();
        var mockProjectGroup =
                ProjectGroupMessage.newBuilder()
                        .addProjectId(randomId())
                        .addProjectId(randomId())
                        .setKey(bundleId)
                        .setType(BUNDLE_PROJECT_GROUP_TYPE);
        var projectIds = mockProjectGroup.getProjectIdList();
        var uwTags =
                IntStreams.range(3)
                        .mapToObj(
                                id ->
                                        randomProjectTag(
                                                companyId, Message.ProjectTagType.UNDERWRITING))
                        .toList();
        var unknownTags =
                IntStreams.range(3)
                        .mapToObj(id -> randomProjectTag(companyId, Message.ProjectTagType.UNKNOWN))
                        .toList();
        var tagMap =
                CollectionUtils.union(uwTags, unknownTags).stream()
                        .collect(Collectors.toMap(Entity::getId, Function.identity()));
        var uwTagIds = uwTags.stream().map(Entity::getId).toList();
        var unknownTagIds = unknownTags.stream().map(Entity::getId).toList();
        var updatedBy = "10000";

        Mockito.doReturn(List.of()).when(projectTagRepository).findAllById(Mockito.any());
        // add UW tags
        bundleTagManager.updateBundleTag(
                bundleId, uwTagIds, Message.ProjectTagType.UNDERWRITING, updatedBy, "AI");
        bundleTagManager.updateBundleTag(
                bundleId, unknownTagIds, Message.ProjectTagType.UNKNOWN, updatedBy, "WEB");

        Mockito.doAnswer(
                        args -> {
                            Iterable<String> tagIds = args.getArgument(0);
                            return Iterables.toStream(tagIds).map(tagMap::get).toList();
                        })
                .when(projectTagRepository)
                .findAllById(Mockito.any());
        Mockito.doReturn(ProjectGroup.from(mockProjectGroup.build()))
                .when(projectGroupManager)
                .findByGroupKey(eq(mockProjectGroup.getKey()), eq(mockProjectGroup.getType()));
        when(projectIIManager.findAllById(projectIds))
                .thenAnswer(
                        e ->
                                projectIds.stream()
                                        .map(
                                                id ->
                                                        ProjectII.from(
                                                                com.bees360.project.Message
                                                                        .ProjectMessage.newBuilder()
                                                                        .setId(id)
                                                                        .build()))
                                        .toList());

        var response = bundleTagManager.syncBundleTag(bundleId, requestBy);

        response.getResultList()
                .forEach(
                        result -> {
                            Assertions.assertTrue(projectIds.contains(result.getProjectId()));
                            Assertions.assertEquals(ApiStatus.OK.toMessage(), result.getStatus());
                        });
        projectIds.forEach(
                id -> {
                    ArgumentCaptor<List<ProjectTag>> uwTagCaptor =
                            ArgumentCaptor.forClass(List.class);
                    ArgumentCaptor<List<ProjectTag>> unknownCaptor =
                            ArgumentCaptor.forClass(List.class);
                    Mockito.verify(projectTagManager, Mockito.times(1))
                            .updateProjectTag(
                                    eq(id),
                                    unknownCaptor.capture(),
                                    eq(Message.ProjectTagType.UNKNOWN),
                                    eq(requestBy),
                                    eq("WEB"));
                    Mockito.verify(projectTagManager, Mockito.times(1))
                            .updateProjectTag(
                                    eq(id),
                                    uwTagCaptor.capture(),
                                    eq(Message.ProjectTagType.UNDERWRITING),
                                    eq(requestBy),
                                    eq("AI"));

                    uwTagCaptor
                            .getValue()
                            .forEach(
                                    tag -> {
                                        var expectedTag = tagMap.get(tag.getId());
                                        assertNotNull(expectedTag);
                                        assertEquals(
                                                expectedTag.getCompanyId(), tag.getCompanyId());
                                        assertEquals(expectedTag.getType(), tag.getType());
                                    });
                    unknownCaptor
                            .getValue()
                            .forEach(
                                    tag -> {
                                        var expectedTag = tagMap.get(tag.getId());
                                        assertNotNull(expectedTag);
                                        assertEquals(
                                                expectedTag.getCompanyId(), tag.getCompanyId());
                                        assertEquals(expectedTag.getType(), tag.getType());
                                    });
                });
    }

    @Test
    public void testFindBundleTag() {
        super.testFindBundleTag();
    }

    @Test
    public void testUpdateBundleTag() {
        super.testUpdateBundleTag();
    }
}
