package com.bees360.bundle.event;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.bundle.BundleProvider;
import com.bees360.bundle.Message;
import com.bees360.bundle.notification.BundleEmailContext;
import com.bees360.bundle.notification.CustomerBundleEmailProperties;
import com.bees360.bundle.notification.SendCustomerBundleEmailJobProvider;
import com.bees360.customer.Customer;
import com.bees360.event.registry.BundleStateChanged;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.SendCustomerBundleEmailJob;
import com.bees360.util.retry.RetryProperties;
import com.google.protobuf.BoolValue;
import com.google.protobuf.StringValue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.time.Instant;
import java.util.function.BiFunction;

@ExtendWith(MockitoExtension.class)
class SendEmailOnBundleCanceledTest {

    @Mock private JobScheduler jobScheduler;
    @Mock private BundleProvider bundleProvider;

    @Mock
    private BiFunction<String, String, CustomerBundleEmailProperties>
            customerBundleEmailsPropertiesProvider;

    @Mock private SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider;
    @Mock private BundleEmailContext bundleEmailContext;
    @Mock private CustomerBundleEmailProperties emailProperties;
    @Mock private SendCustomerBundleEmailJob emailJob;

    private RetryProperties retryProperties;
    private SendEmailOnBundleCanceled sendEmailOnBundleCanceled;

    @BeforeEach
    void setUp() {
        retryProperties = new RetryProperties();
        retryProperties.setRetryCount(3);
        retryProperties.setRetryDelay(Duration.ofMinutes(1));
        retryProperties.setRetryDelayIncreaseFactor(1.5f);

        sendEmailOnBundleCanceled =
                new SendEmailOnBundleCanceled(
                        jobScheduler,
                        bundleProvider,
                        customerBundleEmailsPropertiesProvider,
                        sendCustomerBundleEmailJobProvider,
                        retryProperties,
                        bundleEmailContext);
    }

    @Test
    void testFilter_ShouldReturnTrue_WhenBundleIsClosedAndCanceledAndSubscribed() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleStateChanged event = createBundleStateChangedEvent(bundleId, "CLOSE");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, true); // canceled

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(true);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_canceled"))
                .thenReturn(emailProperties);

        // Act
        boolean result = sendEmailOnBundleCanceled.filter(event);

        // Assert
        assertTrue(result);
        verify(bundleProvider).findById(bundleId);
        verify(customerBundleEmailsPropertiesProvider).apply(companyKey, "bundle_canceled");
    }

    @Test
    void testFilter_ShouldReturnFalse_WhenBundleStateIsNotClose() {
        // Arrange
        String bundleId = "12345";
        BundleStateChanged event = createBundleStateChangedEvent(bundleId, "OPEN");

        // Act
        boolean result = sendEmailOnBundleCanceled.filter(event);

        // Assert
        assertFalse(result);
        verify(bundleProvider, never()).findById(any());
    }

    @Test
    void testFilter_ShouldReturnFalse_WhenBundleIsNotCanceled() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleStateChanged event = createBundleStateChangedEvent(bundleId, "CLOSE");
        Message.BundleMessage bundle =
                createMockBundle(bundleId, companyKey, false); // not canceled

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);

        // Act
        boolean result = sendEmailOnBundleCanceled.filter(event);

        // Assert
        assertFalse(result);
        verify(bundleProvider).findById(bundleId);
        verify(customerBundleEmailsPropertiesProvider, never()).apply(any(), any());
    }

    @Test
    void testFilter_ShouldReturnFalse_WhenCustomerIsNotSubscribed() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleStateChanged event = createBundleStateChangedEvent(bundleId, "CLOSE");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, true); // canceled

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(false);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_canceled"))
                .thenReturn(emailProperties);

        // Act
        boolean result = sendEmailOnBundleCanceled.filter(event);

        // Assert
        assertFalse(result);
    }

    @Test
    void testConvert_ShouldCreateRetryableEmailJob() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleStateChanged event = createBundleStateChangedEvent(bundleId, "CLOSE");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, true);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_canceled"),
                        eq(bundleEmailContext)))
                .thenReturn(emailJob);

        // Act
        Job result = sendEmailOnBundleCanceled.convert(event);

        // Assert
        assertNotNull(result);
        verify(bundleProvider).findById(bundleId);
        verify(sendCustomerBundleEmailJobProvider)
                .bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_canceled"),
                        eq(bundleEmailContext));
    }

    @Test
    void testHandle_ShouldScheduleJobWhenFilterPasses() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleStateChanged event = createBundleStateChangedEvent(bundleId, "CLOSE");
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey, true);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(true);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_canceled"))
                .thenReturn(emailProperties);
        when(sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_canceled"),
                        eq(bundleEmailContext)))
                .thenReturn(emailJob);

        // Act
        sendEmailOnBundleCanceled.handle(event);

        // Assert
        verify(jobScheduler).schedule(any(Job.class));
    }

    @Test
    void testHandle_ShouldNotScheduleJobWhenFilterFails() {
        // Arrange
        String bundleId = "12345";
        BundleStateChanged event = createBundleStateChangedEvent(bundleId, "OPEN"); // not CLOSE

        // Act
        sendEmailOnBundleCanceled.handle(event);

        // Assert
        verify(jobScheduler, never()).schedule(any());
    }

    private BundleStateChanged createBundleStateChangedEvent(String bundleId, String state) {
        BundleStateChanged event = new BundleStateChanged();
        event.setBundleId(bundleId);
        event.setState(state);
        event.setOldState("OPEN");
        event.setCreatedBy("test-user");
        event.setCreatedAt(Instant.now());
        return event;
    }

    private Message.BundleMessage createMockBundle(
            String bundleId, String companyKey, boolean isCanceled) {
        // Create mock customer
        com.bees360.customer.Message.CustomerMessage customerMessage =
                com.bees360.customer.Message.CustomerMessage.newBuilder()
                        .setId("customer-123")
                        .setName("Test Insurance Company")
                        .setKey(companyKey)
                        .build();

        // Create mock contract
        com.bees360.contract.Message.ContractMessage contractMessage =
                com.bees360.contract.Message.ContractMessage.newBuilder()
                        .setId("contract-123")
                        .setInsuredBy(customerMessage)
                        .build();

        // Create mock bundle
        return Message.BundleMessage.newBuilder()
                .setId(StringValue.newBuilder().setValue(bundleId).build())
                .setContract(contractMessage)
                .setIsCanceled(BoolValue.newBuilder().setValue(isCanceled).build())
                .build();
    }
}
