package com.bees360.bundle.event;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.activity.ActivityManager;
import com.bees360.event.registry.BundleTagAdded;
import com.bees360.event.registry.BundleTagRemoved;
import com.bees360.project.tag.Message;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagRepository;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

class SubmitActivityOnBundleTagRemovedTest {

    private ActivityManager activityManager;

    private ProjectTagRepository projectTagRepository;

    private SubmitActivityOnBundleTagRemoved listener;

    @BeforeEach
    void setUp() {
        activityManager = mock(ActivityManager.class);
        projectTagRepository = mock(ProjectTagRepository.class);
        listener = new SubmitActivityOnBundleTagRemoved(activityManager, projectTagRepository);
    }

    @Test
    void testSubmitActivityWhenTagsRemoved() throws IOException {
        var bundleId = randomId();
        var updatedBy = randomId();
        BundleTagAdded.BundleTagChangedObject tag1 = new BundleTagAdded.BundleTagChangedObject();
        tag1.setId(randomId());
        tag1.setBundleId(bundleId);
        tag1.setUpdatedBy(updatedBy);
        tag1.setUpdatedAt(Instant.now());
        tag1.setUpdatedVia("WEB");
        var projectTag1 =
                ProjectTag.of(
                        Message.ProjectTagMessage.newBuilder()
                                .setId(tag1.getId())
                                .setTitle(RandomStringUtils.secure().nextAlphanumeric(12))
                                .build());

        BundleTagAdded.BundleTagChangedObject tag2 = new BundleTagAdded.BundleTagChangedObject();
        tag2.setId(randomId());
        tag2.setBundleId(bundleId);
        tag2.setUpdatedBy(updatedBy);
        tag2.setUpdatedAt(Instant.now());
        tag2.setUpdatedVia("WEB");
        var projectTag2 =
                ProjectTag.of(
                        Message.ProjectTagMessage.newBuilder()
                                .setId(tag2.getId())
                                .setTitle(RandomStringUtils.secure().nextAlphanumeric(12))
                                .build());

        List<BundleTagAdded.BundleTagChangedObject> tagList = new ArrayList<>();
        tagList.add(tag1);
        tagList.add(tag2);

        when(projectTagRepository.findAllById(anyList()))
                .thenReturn(List.of(projectTag1, projectTag2));

        BundleTagRemoved event = new BundleTagRemoved();
        event.setList(tagList);

        listener.handle(event);

        verify(activityManager, times(1)).submitActivity(any());
    }

    @Test
    void testNotSubmitActivityWhenNoTagsRemoved() throws IOException {
        BundleTagRemoved event = new BundleTagRemoved();
        event.setList(new ArrayList<>()); // Empty list

        listener.handle(event);

        verify(activityManager, never()).submitActivity(any());
    }

    private String randomId() {
        return "1" + RandomStringUtils.secure().nextNumeric(10);
    }
}
