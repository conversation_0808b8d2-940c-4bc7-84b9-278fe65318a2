package com.bees360.bundle;

import static com.bees360.bundle.JooqBundleManager.BUNDLE_PROJECT_GROUP_TYPE;
import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE;
import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN;
import static com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED;
import static com.bees360.project.Message.ProjectStatus.PROJECT_CREATED;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.same;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.api.ApiStatus;
import com.bees360.policy.PolicyManager;
import com.bees360.project.BuildingManager;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectPolicyManager;
import com.bees360.project.group.ProjectGroup;
import com.bees360.project.group.ProjectGroupProvider;
import com.bees360.project.state.ProjectState;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.status.ProjectStatusManager;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

class BundleProjectManagerTest {

    @InjectMocks private BundleProjectManager bundleProjectManager;

    @Mock private ProjectGroupProvider projectGroupProvider;

    @Mock private ProjectIIManager projectIIManager;

    @Mock private PolicyManager policyManager;

    @Mock private BuildingManager buildingManager;

    @Mock private ProjectPolicyManager projectPolicyManager;

    @Mock private ProjectStatusManager projectStatusManager;

    @Captor private ArgumentCaptor<ApiStatus> apiStatusCaptor;

    @Mock private ProjectStateManager projectStateManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testUpdateProjectStatusSuccess() throws Exception {
        // Given
        String bundleId = "bundle-1";
        com.bees360.project.Message.ProjectStatus newStatus =
                com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED;
        Instant updatedAt = Instant.now();
        String updatedBy = "test-user";

        // Current status map with lower status
        Map<String, com.bees360.project.Message.ProjectStatus> currentStatusMap = new HashMap<>();
        currentStatusMap.put("100001", PROJECT_CREATED);
        currentStatusMap.put("100002", PROJECT_CREATED);

        // Mock project group with project IDs
        ProjectGroup projectGroup = mock(ProjectGroup.class);
        when(projectGroup.getProjectIds()).thenReturn(Arrays.asList("100001", "100002"));
        when(projectGroupProvider.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE))
                .thenReturn(projectGroup);

        // Mock project info
        ProjectII project = mock(ProjectII.class);

        var project1 = mock(ProjectII.class);
        var project2 = mock(ProjectII.class);
        when(projectIIManager.findAllById(List.of("100001", "100002")))
                .thenAnswer(e -> List.of(project1, project2));
        when(project1.isCanceled()).thenReturn(false);
        when(project2.isCanceled()).thenReturn(false);
        when(project1.getId()).thenReturn("100001");
        when(project2.getId()).thenReturn("100002");
        // Mock successful status update
        when(projectStatusManager.updateStatus(
                        anyString(), same(newStatus), eq(updatedBy), eq(updatedAt)))
                .thenReturn(true);

        // When
        List<Message.SyncBundleProjectResponse.SyncProjectResponse> responses =
                bundleProjectManager.updateProjectStatus(
                        bundleId, newStatus, currentStatusMap, updatedBy, updatedAt);

        // Then
        assertNotNull(responses);
        assertEquals(2, responses.size());

        responses.forEach(
                response -> {
                    assertNotNull(response.getStatus());
                    assertEquals(ApiStatus.OK.getCode(), response.getStatus().getCode());
                });

        verify(projectStatusManager, times(2))
                .updateStatus(anyString(), same(newStatus), eq(updatedBy), eq(updatedAt));
    }

    @Test
    void testUpdateProjectStatusCurrentStatusHigher() {
        // Given
        String bundleId = "bundle-1";
        com.bees360.project.Message.ProjectStatus newStatus =
                com.bees360.project.Message.ProjectStatus.ASSIGNED_TO_PILOT;

        Map<String, com.bees360.project.Message.ProjectStatus> currentStatusMap = new HashMap<>();
        currentStatusMap.put("100001", com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED);

        ProjectGroup projectGroup = mock(ProjectGroup.class);
        when(projectGroup.getProjectIds()).thenReturn(Collections.singletonList("100001"));
        when(projectGroupProvider.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE))
                .thenReturn(projectGroup);
        var project = mock(ProjectII.class);
        when(projectIIManager.findAllById(List.of("100001")))
                .thenAnswer(e -> Collections.singletonList(project));
        when(project.isCanceled()).thenReturn(false);
        when(project.getId()).thenReturn("100001");

        // When
        List<Message.SyncBundleProjectResponse.SyncProjectResponse> responses =
                bundleProjectManager.updateProjectStatus(
                        bundleId, newStatus, currentStatusMap, "user", Instant.now());

        // Then
        assertNotNull(responses);
        assertEquals(1, responses.size());
        assertEquals(ApiStatus.OK.getCode(), responses.get(0).getStatus().getCode());

        verify(projectStatusManager, never())
                .updateStatus(
                        anyString(), any(com.bees360.project.Message.ProjectStatus.class),
                        anyString(), any(Instant.class));
    }

    @Test
    void testUpdateProjectStatusExceptionHandling() throws Exception {
        // Given
        String bundleId = "bundle-1";
        com.bees360.project.Message.ProjectStatus newStatus =
                com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED;
        Instant updatedAt = Instant.now();
        String updatedBy = "test-user";

        Map<String, com.bees360.project.Message.ProjectStatus> currentStatusMap = new HashMap<>();
        currentStatusMap.put("100001", PROJECT_CREATED);

        ProjectGroup projectGroup = mock(ProjectGroup.class);
        when(projectGroup.getProjectIds()).thenReturn(Collections.singletonList("100001"));
        when(projectGroupProvider.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE))
                .thenReturn(projectGroup);

        var project = mock(ProjectII.class);
        when(projectIIManager.findAllById(List.of("100001")))
                .thenAnswer(e -> Collections.singletonList(project));
        when(project.isCanceled()).thenReturn(false);
        when(project.getId()).thenReturn("100001");

        // Mock exception
        when(projectStatusManager.updateStatus(
                        anyString(), same(newStatus), eq(updatedBy), eq(updatedAt)))
                .thenThrow(new IllegalArgumentException("Test exception."));

        // When
        List<Message.SyncBundleProjectResponse.SyncProjectResponse> responses =
                bundleProjectManager.updateProjectStatus(
                        bundleId, newStatus, currentStatusMap, updatedBy, updatedAt);

        // Then
        assertNotNull(responses);
        assertEquals(1, responses.size());
        assertEquals(
                com.bees360.status.Message.StatusMessage.Code.INVALID_ARGUMENT,
                responses.get(0).getStatus().getCode());

        verify(projectStatusManager, times(1))
                .updateStatus(anyString(), same(newStatus), eq(updatedBy), eq(updatedAt));
    }

    @Test
    void testUpdateProjectStatusEmptyBundle() {
        // Given
        String bundleId = "bundle-1";
        com.bees360.project.Message.ProjectStatus newStatus =
                com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED;

        // Mock empty project group
        ProjectGroup projectGroup = mock(ProjectGroup.class);
        when(projectGroup.getProjectIds()).thenReturn(Collections.emptyList());
        when(projectGroupProvider.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE))
                .thenReturn(projectGroup);

        // When
        List<Message.SyncBundleProjectResponse.SyncProjectResponse> responses =
                bundleProjectManager.updateProjectStatus(
                        bundleId, newStatus, Collections.emptyMap(), "user", Instant.now());

        // Then
        assertNotNull(responses);
        assertTrue(responses.isEmpty());
        verify(projectStatusManager, never())
                .updateStatus(
                        anyString(), any(com.bees360.project.Message.ProjectStatus.class),
                        anyString(), any(Instant.class));
    }

    @Test
    void testUpdateProjectStatusNoUpdateNeeded() {
        // Arrange
        String bundleId = "123";
        Map<String, com.bees360.project.Message.ProjectStatus> currentProjectStatusMap =
                new HashMap<>();
        currentProjectStatusMap.put("10001", IMAGE_UPLOADED); // Higher status, no update needed
        String updatedBy = "user1";
        Instant updatedAt = Instant.now();

        ProjectGroup projectGroup = mock(ProjectGroup.class);
        when(projectGroup.getProjectIds()).thenAnswer(e -> List.of("10001"));
        when(projectGroupProvider.findByGroupKey(
                        bundleId, JooqBundleManager.BUNDLE_PROJECT_GROUP_TYPE))
                .thenReturn(projectGroup);

        ProjectII project = mock(ProjectII.class);
        when(project.getId()).thenReturn("10001");
        when(projectIIManager.findAllById(List.of("10001"))).thenAnswer(e -> List.of(project));

        // Act
        List<Message.SyncBundleProjectResponse.SyncProjectResponse> responses =
                bundleProjectManager.updateProjectStatus(
                        bundleId, IMAGE_UPLOADED, currentProjectStatusMap, updatedBy, updatedAt);

        // Assert
        assertEquals(1, responses.size());
        Message.SyncBundleProjectResponse.SyncProjectResponse response = responses.get(0);
        assertEquals("10001", response.getProjectId());
        assertTrue(ApiStatus.fromMessage(response.getStatus()).isOk());
        verify(projectStatusManager, never())
                .updateStatus(
                        anyString(),
                        any(com.bees360.project.Message.ProjectStatus.class),
                        anyString(),
                        any(Instant.class));
    }

    @Test
    void testUpdateProjectStatusSuccessfulUpdate() {
        // Arrange
        String bundleId = "123";
        Map<String, com.bees360.project.Message.ProjectStatus> currentProjectStatusMap =
                new HashMap<>();
        currentProjectStatusMap.put("10001", IMAGE_UPLOADED); // Higher status, no update needed
        String updatedBy = "user1";
        Instant updatedAt = Instant.now();

        ProjectGroup projectGroup = mock(ProjectGroup.class);
        when(projectGroup.getProjectIds()).thenAnswer(e -> List.of("10001"));
        when(projectGroupProvider.findByGroupKey(
                        bundleId, JooqBundleManager.BUNDLE_PROJECT_GROUP_TYPE))
                .thenReturn(projectGroup);

        ProjectII project = mock(ProjectII.class);
        when(project.getId()).thenReturn("10001");
        when(projectIIManager.findAllById(List.of("10001"))).thenAnswer(e -> List.of(project));

        // Act
        List<Message.SyncBundleProjectResponse.SyncProjectResponse> responses =
                bundleProjectManager.updateProjectStatus(
                        bundleId, IMAGE_UPLOADED, currentProjectStatusMap, updatedBy, updatedAt);

        // Assert
        assertEquals(1, responses.size());
        Message.SyncBundleProjectResponse.SyncProjectResponse response = responses.get(0);
        assertEquals("10001", response.getProjectId());
        assertTrue(ApiStatus.fromMessage(response.getStatus()).isOk());
    }

    @Test
    void testUpdateProjectStateSuccess() {
        // Arrange
        String bundleId = "bundle123";
        var state = PROJECT_OPEN;
        String changeReason = "Test reason";
        String updatedBy = "user123";

        ProjectGroup projectGroup = mock(ProjectGroup.class);
        when(projectGroup.getProjectIds()).thenAnswer(e -> List.of("project1", "project2"));

        ProjectII project1 = mock(ProjectII.class);
        ProjectII project2 = mock(ProjectII.class);
        when(project1.getId()).thenReturn("project1");
        when(project2.getId()).thenReturn("project2");
        var openState = mock(ProjectState.class);
        when(openState.getState()).thenReturn(PROJECT_OPEN);
        when(project1.getCurrentState()).thenReturn(openState);
        when(project2.getCurrentState()).thenReturn(openState);

        when(projectGroupProvider.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE))
                .thenReturn(projectGroup);
        when(projectIIManager.findAllById(anyList())).thenAnswer(e -> List.of(project1, project2));
        doNothing()
                .when(projectStateManager)
                .changeProjectState(anyString(), any(), anyString(), anyString());

        // Act
        List<Message.SyncBundleProjectResponse.SyncProjectResponse> responses =
                bundleProjectManager.updateProjectState(bundleId, state, changeReason, updatedBy);

        // Assert
        assertEquals(2, responses.size());
        assertTrue(
                responses.stream()
                        .allMatch(
                                response ->
                                        response.getStatus()
                                                .getCode()
                                                .equals(
                                                        com.bees360.status.Message.StatusMessage
                                                                .Code.OK)));
        verify(projectStateManager, times(2))
                .changeProjectState(anyString(), any(), anyString(), anyString());
    }

    @Test
    void testUpdateProjectStatePredicateMismatch() {
        // Arrange
        String bundleId = "bundle123";
        var state = PROJECT_OPEN;
        String changeReason = "Test reason";
        String updatedBy = "user123";

        ProjectGroup projectGroup = mock(ProjectGroup.class);
        when(projectGroup.getProjectIds()).thenAnswer(e -> List.of("project1"));

        ProjectII project1 = mock(ProjectII.class);
        var closeState = mock(ProjectState.class);
        when(closeState.getState()).thenReturn(PROJECT_CLOSE);
        when(project1.getId()).thenReturn("project1");
        when(project1.getCurrentState()).thenReturn(closeState);

        when(projectGroupProvider.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE))
                .thenReturn(projectGroup);
        when(projectIIManager.findAllById(anyList())).thenAnswer(e -> List.of(project1));

        // Act
        List<Message.SyncBundleProjectResponse.SyncProjectResponse> responses =
                bundleProjectManager.updateProjectState(bundleId, state, changeReason, updatedBy);

        // Assert
        assertEquals(0, responses.size()); // No updates should occur
        verify(projectStateManager, never())
                .changeProjectState(anyString(), any(), anyString(), anyString());
    }

    @Test
    void testUpdateProjectStateExceptionHandling() {
        // Arrange
        String bundleId = "bundle123";
        var state = PROJECT_OPEN;
        String changeReason = "Test reason";
        String updatedBy = "user123";

        ProjectGroup projectGroup = mock(ProjectGroup.class);
        when(projectGroup.getProjectIds()).thenAnswer(e -> List.of("project1"));

        ProjectII project1 = mock(ProjectII.class);
        when(project1.getId()).thenReturn("project1");
        var openState = mock(ProjectState.class);
        when(openState.getState()).thenReturn(PROJECT_OPEN);
        when(project1.getId()).thenReturn("project1");
        when(project1.getCurrentState()).thenReturn(openState);

        when(projectGroupProvider.findByGroupKey(bundleId, BUNDLE_PROJECT_GROUP_TYPE))
                .thenReturn(projectGroup);
        when(projectIIManager.findAllById(anyList())).thenAnswer(e -> List.of(project1));
        doThrow(new IllegalArgumentException("Test exception"))
                .when(projectStateManager)
                .changeProjectState(anyString(), any(), anyString(), anyString());

        // Act
        List<Message.SyncBundleProjectResponse.SyncProjectResponse> responses =
                bundleProjectManager.updateProjectState(bundleId, state, changeReason, updatedBy);

        // Assert
        assertEquals(1, responses.size());
        assertNotEquals(
                com.bees360.status.Message.StatusMessage.Code.OK,
                responses.get(0).getStatus().getCode());
    }
}
