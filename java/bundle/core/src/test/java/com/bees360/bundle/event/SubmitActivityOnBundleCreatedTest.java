package com.bees360.bundle.event;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.CommentManager;
import com.bees360.bundle.Message;
import com.bees360.event.registry.BundleCreated;
import com.google.protobuf.StringValue;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;

class SubmitActivityOnBundleCreatedTest {

    private ActivityManager activityManager;

    private CommentManager commentManager;

    private SubmitActivityOnBundleCreated listener;

    @BeforeEach
    void setUp() {
        activityManager = mock(ActivityManager.class);
        commentManager = mock(CommentManager.class);
        listener = new SubmitActivityOnBundleCreated(activityManager, commentManager);
    }

    @Test
    void testHandleEvent() throws IOException {
        BundleCreated event = new BundleCreated();
        event.setId("1" + RandomStringUtils.secure().nextNumeric(10));
        event.setCreatedBy("1" + RandomStringUtils.secure().nextNumeric(10));
        event.setCreatedAt(Instant.now());
        event.setMetadata(
                Message.BundleMessage.Metadata.newBuilder()
                        .setNote(StringValue.of("unit test"))
                        .build());

        listener.handle(event);

        verify(activityManager, times(1)).submitActivity(any());

        verify(commentManager, times(1)).addComment(any());
    }

    @Test
    void testHandleEventWithoutNote() throws IOException {
        BundleCreated event = new BundleCreated();
        event.setId("1" + RandomStringUtils.secure().nextNumeric(10));
        event.setCreatedBy("1" + RandomStringUtils.secure().nextNumeric(10));
        event.setCreatedAt(Instant.now());

        listener.handle(event);

        verify(activityManager, times(1)).submitActivity(any());

        verify(commentManager, never()).addComment(any());
    }
}
