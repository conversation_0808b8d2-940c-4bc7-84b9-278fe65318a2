package com.bees360.bundle.event;

import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.bundle.BundleManager;
import com.bees360.bundle.Message;
import com.bees360.bundle.report.BundleReportProcessor;
import com.bees360.customer.Customer;
import com.bees360.customer.CustomerProvider;
import com.bees360.event.registry.BundleStatusChanged;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.Report;
import com.bees360.report.ReportTypeEnum;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.Futures;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class MergeReportOnBundleStatusChangedTest {

    private BundleManager bundleManager;

    private BundleReportProcessor bundleReportProcessor;

    private CustomerProvider customerProvider;

    private ProjectReportProvider projectReportProvider;

    private MergeReportOnBundleStatusChanged listener;

    private final List<Message.BundleStatus> triggerStatuses =
            List.of(Message.BundleStatus.RETURNED_TO_CLIENT);

    private final String systemUserId = "10000";

    @BeforeEach
    void setUp() {
        bundleManager = mock(BundleManager.class);
        bundleReportProcessor = mock(BundleReportProcessor.class);
        customerProvider = mock(CustomerProvider.class);
        projectReportProvider = mock(ProjectReportProvider.class);
        listener =
                new MergeReportOnBundleStatusChanged(
                        bundleManager,
                        bundleReportProcessor,
                        customerProvider,
                        projectReportProvider,
                        triggerStatuses,
                        systemUserId);
    }

    @Test
    void handleShouldNotTriggerWhenStatusNotInTriggerList() throws IOException {
        // Given
        BundleStatusChanged event = new BundleStatusChanged();
        var bundleId = randomId();
        event.setBundleId(bundleId);
        event.setStatus(Message.BundleStatus.CREATED.name());

        listener.handle(event);

        verify(bundleManager, never()).findById(bundleId);
    }

    @Test
    void handleShouldTriggerReportMergeWhenConditionsAreMet() throws IOException {
        // Given
        BundleStatusChanged event = new BundleStatusChanged();
        var bundleId = randomId();
        event.setBundleId(bundleId);
        event.setStatus(Message.BundleStatus.RETURNED_TO_CLIENT.name());

        // Setup mocks for successful execution path
        var bundleProjects =
                List.of(
                        com.bees360.project.Message.ProjectMessage.newBuilder()
                                .setId(randomId())
                                .build(),
                        com.bees360.project.Message.ProjectMessage.newBuilder()
                                .setId(randomId())
                                .build());
        var reportType = String.valueOf(ReportTypeEnum.FUR.getCode());
        var insuredBy =
                com.bees360.customer.Message.CustomerMessage.newBuilder()
                        .setId(randomId())
                        .setAttributes(
                                com.bees360.customer.Message.CustomerAttributes.newBuilder()
                                        .setBundle(
                                                com.bees360.customer.Message.CustomerAttributes
                                                        .BundleAttributes.newBuilder()
                                                        .setAutoMergeReport(
                                                                com.bees360.customer.Message
                                                                        .CustomerAttributes
                                                                        .BundleAttributes
                                                                        .AutoMergeReportAttribute
                                                                        .newBuilder()
                                                                        .addAllReportType(
                                                                                List.of(reportType))
                                                                        .build())
                                                        .build())
                                        .build())
                        .build();
        var bundleContract =
                com.bees360.contract.Message.ContractMessage.newBuilder()
                        .setInsuredBy(insuredBy)
                        .build();
        var bundle =
                Message.BundleMessage.newBuilder()
                        .addAllProject(bundleProjects)
                        .setContract(bundleContract)
                        .build();
        when(bundleManager.findById(bundleId)).thenReturn(bundle);
        when(customerProvider.findById(insuredBy.getId())).thenReturn(Customer.of(insuredBy));

        Report report1 = mock(Report.class);
        when(report1.getId()).thenReturn("report1");
        when(report1.getType()).thenReturn(reportType);

        Report report2 = mock(Report.class);
        when(report2.getId()).thenReturn("report2");
        when(report2.getType()).thenReturn(reportType);

        when(projectReportProvider.findByProjectIds(anyList(), anyBoolean()))
                .thenReturn(
                        Map.of(
                                bundleProjects.get(0).getId(),
                                List.of(report1),
                                bundleProjects.get(1).getId(),
                                List.of(report2)));

        when(bundleReportProcessor.mergeReport(anyString(), anyString(), anyList(), anyString()))
                .thenReturn(Futures.immediateFuture(null));

        // When
        listener.handle(event);

        // Then
        verify(bundleReportProcessor, atLeastOnce())
                .mergeReport(
                        eq(bundleId),
                        eq(reportType),
                        argThat(
                                l -> {
                                    return Iterables.contains(l, "report1")
                                            && Iterables.contains(l, "report2");
                                }),
                        eq(systemUserId));
    }

    private String randomId() {
        return "1" + RandomStringUtils.secure().nextNumeric(10);
    }
}
