package com.bees360.bundle.event;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.bundle.BundleProvider;
import com.bees360.bundle.Message;
import com.bees360.bundle.notification.BundleEmailContext;
import com.bees360.bundle.notification.CustomerBundleEmailProperties;
import com.bees360.bundle.notification.SendCustomerBundleEmailJobProvider;
import com.bees360.customer.Customer;
import com.bees360.event.registry.BundleCreated;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.SendCustomerBundleEmailJob;
import com.bees360.util.retry.RetryProperties;
import com.google.protobuf.StringValue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.time.Instant;
import java.util.function.BiFunction;

@ExtendWith(MockitoExtension.class)
class SendEmailOnBundleCreatedTest {

    @Mock private JobScheduler jobScheduler;
    @Mock private BundleProvider bundleProvider;

    @Mock
    private BiFunction<String, String, CustomerBundleEmailProperties>
            customerBundleEmailsPropertiesProvider;

    @Mock private SendCustomerBundleEmailJobProvider sendCustomerBundleEmailJobProvider;
    @Mock private BundleEmailContext bundleEmailContext;
    @Mock private CustomerBundleEmailProperties emailProperties;
    @Mock private SendCustomerBundleEmailJob emailJob;

    private RetryProperties retryProperties;
    private SendEmailOnBundleCreated sendEmailOnBundleCreated;

    @BeforeEach
    void setUp() {
        retryProperties = new RetryProperties();
        retryProperties.setRetryCount(3);
        retryProperties.setRetryDelay(Duration.ofMinutes(1));
        retryProperties.setRetryDelayIncreaseFactor(1.5f);

        sendEmailOnBundleCreated =
                new SendEmailOnBundleCreated(
                        jobScheduler,
                        bundleProvider,
                        customerBundleEmailsPropertiesProvider,
                        retryProperties,
                        sendCustomerBundleEmailJobProvider,
                        bundleEmailContext);
    }

    @Test
    void testFilter_ShouldReturnTrue_WhenCustomerIsSubscribed() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleCreated event = createBundleCreatedEvent(bundleId);
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(true);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_created"))
                .thenReturn(emailProperties);

        // Act
        boolean result = sendEmailOnBundleCreated.filter(event);

        // Assert
        assertTrue(result);
        verify(bundleProvider).findById(bundleId);
        verify(customerBundleEmailsPropertiesProvider).apply(companyKey, "bundle_created");
    }

    @Test
    void testFilter_ShouldReturnFalse_WhenCustomerIsNotSubscribed() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleCreated event = createBundleCreatedEvent(bundleId);
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(false);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_created"))
                .thenReturn(emailProperties);

        // Act
        boolean result = sendEmailOnBundleCreated.filter(event);

        // Assert
        assertFalse(result);
    }

    @Test
    void testConvert_ShouldCreateRetryableEmailJob() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleCreated event = createBundleCreatedEvent(bundleId);
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_created"),
                        eq(bundleEmailContext)))
                .thenReturn(emailJob);

        // Act
        Job result = sendEmailOnBundleCreated.convert(event);

        // Assert
        assertNotNull(result);
        verify(bundleProvider).findById(bundleId);
        verify(sendCustomerBundleEmailJobProvider)
                .bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_created"),
                        eq(bundleEmailContext));
    }

    @Test
    void testHandle_ShouldScheduleJobWhenFilterPasses() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleCreated event = createBundleCreatedEvent(bundleId);
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(true);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_created"))
                .thenReturn(emailProperties);
        when(sendCustomerBundleEmailJobProvider.bundleEmailJob(
                        eq(bundle),
                        any(Customer.class),
                        eq("bundle_created"),
                        eq(bundleEmailContext)))
                .thenReturn(emailJob);

        // Act
        sendEmailOnBundleCreated.handle(event);

        // Assert
        verify(jobScheduler).schedule(any(Job.class));
    }

    @Test
    void testHandle_ShouldNotScheduleJobWhenFilterFails() {
        // Arrange
        String bundleId = "12345";
        String companyKey = "Test Insurance Company";
        BundleCreated event = createBundleCreatedEvent(bundleId);
        Message.BundleMessage bundle = createMockBundle(bundleId, companyKey);

        when(bundleProvider.findById(bundleId)).thenReturn(bundle);
        when(emailProperties.isSubscribed()).thenReturn(false);
        when(customerBundleEmailsPropertiesProvider.apply(companyKey, "bundle_created"))
                .thenReturn(emailProperties);

        // Act
        sendEmailOnBundleCreated.handle(event);

        // Assert
        verify(jobScheduler, never()).schedule(any());
    }

    private BundleCreated createBundleCreatedEvent(String bundleId) {
        BundleCreated event = new BundleCreated();
        event.setId(bundleId);
        event.setCreatedBy("test-user");
        event.setCreatedAt(Instant.now());
        return event;
    }

    private Message.BundleMessage createMockBundle(String bundleId, String companyKey) {
        // Create mock customer
        com.bees360.customer.Message.CustomerMessage customerMessage =
                com.bees360.customer.Message.CustomerMessage.newBuilder()
                        .setId("customer-123")
                        .setName("Test Insurance Company")
                        .setKey(companyKey)
                        .build();

        // Create mock contract
        com.bees360.contract.Message.ContractMessage contractMessage =
                com.bees360.contract.Message.ContractMessage.newBuilder()
                        .setId("contract-123")
                        .setInsuredBy(customerMessage)
                        .build();

        // Create mock bundle
        return Message.BundleMessage.newBuilder()
                .setId(StringValue.newBuilder().setValue(bundleId).build())
                .setContract(contractMessage)
                .build();
    }
}
