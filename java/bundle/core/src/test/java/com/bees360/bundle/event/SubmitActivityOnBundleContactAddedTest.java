package com.bees360.bundle.event;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.bees360.activity.ActivityManager;
import com.bees360.event.registry.BundleContactAdded;
import com.bees360.event.registry.BundleContactChanged;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;

class SubmitActivityOnBundleContactAddedTest {

    private ActivityManager activityManager;
    private SubmitActivityOnBundleContactAdded listener;

    @BeforeEach
    void setUp() {
        activityManager = mock(ActivityManager.class);
        listener = new SubmitActivityOnBundleContactAdded(activityManager);
    }

    @Test
    void testHandleEvent() throws IOException {
        BundleContactAdded event = createBundleContactAddedEvent();

        listener.handle(event);

        verify(activityManager, times(1)).submitActivity(any());
    }

    private BundleContactAdded createBundleContactAddedEvent() {
        BundleContactAdded event = new BundleContactAdded();

        BundleContactChanged.BundleContact contact = new BundleContactChanged.BundleContact();
        contact.setBundleId("12345");
        contact.setRole("INSURER");
        contact.setFullName("John Doe");
        contact.setCreatedBy("user123");
        contact.setCreatedAt(Instant.now());

        event.setNewValue(contact);
        event.setBundleId("12345");

        return event;
    }
}
