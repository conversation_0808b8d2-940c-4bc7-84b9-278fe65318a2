package com.bees360.bundle.event;

import static org.mockito.Mockito.*;

import com.bees360.bundle.BundleManager;
import com.bees360.bundle.BundleProjectManager;
import com.bees360.bundle.Message.BundleMessage;
import com.bees360.event.registry.BundleStateChanged;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.ProjectIIManager;
import com.google.protobuf.BoolValue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.List;

class UpdateBundleProjectOnBundleClosedTest {

    @InjectMocks private UpdateBundleProjectOnBundleClosed handler;

    @Mock private BundleManager bundleManager;

    @Mock private BundleProjectManager bundleProjectManager;

    @Mock private ProjectIIManager projectIIManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testHandleIgnoreEventWhenBundleIsNotCanceled() throws IOException {
        // Arrange
        BundleStateChanged event = mock(BundleStateChanged.class);
        BundleStateChanged.StateChangeReason reason =
                mock(BundleStateChanged.StateChangeReason.class);
        when(reason.key()).thenReturn("OTHER_REASON");
        when(event.getStateChangeReason()).thenReturn(reason);
        when(event.getBundleId()).thenReturn("123");
        BundleMessage bundle = mock(BundleMessage.class);
        when(bundleManager.findById("123")).thenReturn(bundle);
        when(bundle.getIsCanceled()).thenReturn(BoolValue.of(false));

        // Act
        handler.handle(event);

        // Assert
        verifyNoInteractions(bundleProjectManager);
    }

    @Test
    void testHandleUpdateProjectStateWhenReasonIsDuplicateInspection() throws IOException {
        // Arrange
        BundleStateChanged event = mock(BundleStateChanged.class);
        BundleStateChanged.StateChangeReason reason =
                mock(BundleStateChanged.StateChangeReason.class);
        when(reason.key()).thenReturn("DUPLICATE INSPECTION");
        when(reason.id()).thenReturn("123123");
        when(event.getStateChangeReason()).thenReturn(reason);
        when(event.getBundleId()).thenReturn("123");
        when(event.getUpdatedBy()).thenReturn("user1");

        BundleMessage bundle = mock(BundleMessage.class);
        ProjectMessage project1 = mock(ProjectMessage.class);
        ProjectMessage project2 = mock(ProjectMessage.class);

        when(project1.getId()).thenReturn("100001");
        when(project1.getIsCanceled()).thenReturn(false);
        when(project1.getLatestStatus())
                .thenReturn(mock(com.bees360.project.Message.ProjectStatus.class));
        var state = mock(ProjectMessage.ProjectState.class);
        when(state.getState())
                .thenReturn(ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN);
        when(project1.getCurrentState()).thenReturn(state);

        when(project2.getId()).thenReturn("100002");
        when(project2.getIsCanceled()).thenReturn(true);
        when(project2.getCurrentState()).thenReturn(state);
        when(bundle.getProjectList()).thenReturn(List.of(project1, project2));
        when(bundle.getIsCanceled()).thenReturn(BoolValue.of(true));
        when(bundleManager.findById("123")).thenReturn(bundle);

        // Act
        handler.handle(event);

        // Assert
        verify(bundleProjectManager)
                .updateProjectState(
                        "123",
                        com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum
                                .PROJECT_CLOSE,
                        "123123",
                        "user1");
        verifyNoMoreInteractions(projectIIManager);
    }
}
