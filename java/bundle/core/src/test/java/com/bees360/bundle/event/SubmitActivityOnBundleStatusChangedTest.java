package com.bees360.bundle.event;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.bees360.activity.ActivityManager;
import com.bees360.event.registry.BundleStatusChanged;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;

class SubmitActivityOnBundleStatusChangedTest {

    private ActivityManager activityManager;

    private SubmitActivityOnBundleStatusChanged listener;

    @BeforeEach
    void setUp() {
        activityManager = mock(ActivityManager.class);
        listener = new SubmitActivityOnBundleStatusChanged(activityManager);
    }

    @Test
    void testSubmitActivityWhenStatusChanged() throws IOException {
        BundleStatusChanged event = new BundleStatusChanged();
        event.setBundleId(randomId());
        event.setOldStatus("Created");
        event.setStatus("Customer Contacted");
        event.setUpdatedBy(randomId());
        event.setUpdatedAt(Instant.now());

        listener.handle(event);

        verify(activityManager, times(1)).submitActivity(any());
    }

    @Test
    void testNotSubmitActivityWhenStatusNotChanged() throws IOException {
        BundleStatusChanged event = new BundleStatusChanged();
        event.setBundleId(randomId());
        event.setOldStatus("Created");
        event.setStatus("Created");
        event.setUpdatedBy(randomId());
        event.setUpdatedAt(Instant.now());

        listener.handle(event);

        verify(activityManager, never()).submitActivity(any());
    }

    private String randomId() {
        return "1" + RandomStringUtils.secure().nextNumeric(10);
    }
}
