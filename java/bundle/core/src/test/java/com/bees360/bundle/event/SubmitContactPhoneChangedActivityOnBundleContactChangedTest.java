package com.bees360.bundle.event;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.bees360.activity.ActivityManager;
import com.bees360.event.registry.BundleContactChanged;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Instant;

class SubmitContactPhoneChangedActivityOnBundleContactChangedTest {

    private ActivityManager activityManager;

    private SubmitContactPhoneChangedActivityOnBundleContactChanged listener;

    @BeforeEach
    void setUp() {
        activityManager = mock(ActivityManager.class);
        listener = new SubmitContactPhoneChangedActivityOnBundleContactChanged(activityManager);
    }

    @Test
    void testSubmitActivityWhenContactPhoneChanged() throws IOException {
        BundleContactChanged.BundleContact oldContact = new BundleContactChanged.BundleContact();
        oldContact.setPhone("************");

        BundleContactChanged.BundleContact newContact = new BundleContactChanged.BundleContact();
        newContact.setBundleId(randomId());
        newContact.setRole("AGENT");
        newContact.setPhone("************");
        newContact.setUpdatedBy(randomId());
        newContact.setUpdatedAt(Instant.now());

        BundleContactChanged event = new BundleContactChanged();
        event.setOldValue(oldContact);
        event.setNewValue(newContact);

        listener.handle(event);

        verify(activityManager, times(1)).submitActivity(any());
    }

    @Test
    void testNotSubmitActivityWhenContactPhoneNotChanged() throws IOException {
        BundleContactChanged.BundleContact oldContact = new BundleContactChanged.BundleContact();
        oldContact.setPhone("************");

        BundleContactChanged.BundleContact newContact = new BundleContactChanged.BundleContact();
        newContact.setBundleId(randomId());
        newContact.setRole("AGENT");
        newContact.setPhone("************");
        newContact.setUpdatedBy(randomId());
        newContact.setUpdatedAt(Instant.now());

        BundleContactChanged event = new BundleContactChanged();
        event.setOldValue(oldContact);
        event.setNewValue(newContact);

        listener.handle(event);

        verify(activityManager, never()).submitActivity(any());
    }

    private String randomId() {
        return "1" + RandomStringUtils.secure().nextNumeric(10);
    }
}
