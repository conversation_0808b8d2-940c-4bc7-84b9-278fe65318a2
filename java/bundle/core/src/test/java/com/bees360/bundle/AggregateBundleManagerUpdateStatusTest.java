package com.bees360.bundle;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.bundle.Message.BundleMessage;
import com.bees360.bundle.Message.BundleStatus;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.Message.ProjectStatus;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.group.ProjectGroup;
import com.bees360.project.group.ProjectGroupManager;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.BiPredicate;

class AggregateBundleManagerUpdateStatusTest {

    @InjectMocks private AggregateBundleManager aggregateBundleManager;

    @Mock private BundleManager bundleManager;

    @Mock private ProjectGroupManager projectGroupManager;

    @Mock private ProjectIIRepository projectIIRepository;

    @Mock private BundleProjectManager bundleProjectManager;

    @Mock private BiPredicate<BundleStatus, BundleStatus> bundleStatusTransitionValidator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testUpdateStatus_NoProjects_ThrowsException() {
        String bundleId = "bundle1";
        BundleStatus status = BundleStatus.CREATED;
        String updatedBy = "user1";
        Instant updatedAt = Instant.now();

        when(bundleManager.findById(bundleId)).thenReturn(BundleMessage.newBuilder().build());
        when(projectGroupManager.findByGroupKey(bundleId, "BUNDLE_PROJECT")).thenReturn(null);
        when(bundleStatusTransitionValidator.test(
                        BundleStatus.STATUS_UNRECOGNIZED, BundleStatus.CREATED))
                .thenReturn(true);
        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () -> {
                            aggregateBundleManager.updateStatus(
                                    bundleId, status, updatedBy, updatedAt);
                        });

        assertEquals(
                "Bundle bundle1 cannot be updated without projects. Please add projects to the"
                        + " bundle.",
                exception.getMessage());
    }

    @Test
    void testUpdateStatus_InvalidTransition_ThrowsException() {
        String bundleId = "bundle1";
        BundleStatus status = BundleStatus.IMAGE_UPLOADED;
        String updatedBy = "user1";
        Instant updatedAt = Instant.now();

        BundleMessage bundleMessage =
                BundleMessage.newBuilder().setStatus(BundleStatus.CREATED).build();
        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);
        when(projectGroupManager.findByGroupKey(bundleId, "BUNDLE_PROJECT"))
                .thenReturn(mock(ProjectGroup.class));
        when(bundleStatusTransitionValidator.test(
                        BundleStatus.CREATED, BundleStatus.IMAGE_UPLOADED))
                .thenReturn(false);

        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () -> {
                            aggregateBundleManager.updateStatus(
                                    bundleId, status, updatedBy, updatedAt);
                        });

        assertTrue(exception.getMessage().contains("Bundle status transition is not allowed"));
    }

    @Test
    void testUpdateStatus_CustomerContacted_MatchesProject() {
        String bundleId = "bundle1";
        BundleStatus status = BundleStatus.CUSTOMER_CONTACTED;
        String updatedBy = "user1";
        Instant updatedAt = Instant.now();

        BundleMessage bundleMessage =
                BundleMessage.newBuilder().setStatus(BundleStatus.CREATED).build();
        ProjectGroup projectGroup = mock(ProjectGroup.class);
        ProjectII projectII = mock(ProjectII.class);
        ProjectMessage projectMessage =
                ProjectMessage.newBuilder()
                        .setId("10001")
                        .setLatestStatus(ProjectStatus.CUSTOMER_CONTACTED)
                        .build();

        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);
        when(projectGroupManager.findByGroupKey(bundleId, "BUNDLE_PROJECT"))
                .thenReturn(projectGroup);
        when(projectGroup.getProjectIds()).thenReturn(Collections.singletonList("10001"));
        when(projectIIRepository.findAllById(Collections.singletonList("10001")))
                .thenAnswer(e -> Collections.singletonList(projectII));
        when(projectII.toMessage()).thenReturn(projectMessage);
        when(bundleStatusTransitionValidator.test(
                        BundleStatus.CREATED, BundleStatus.CUSTOMER_CONTACTED))
                .thenReturn(true);
        when(bundleManager.updateStatus(
                        bundleId, BundleStatus.CUSTOMER_CONTACTED, updatedBy, updatedAt))
                .thenReturn(true);
        when(bundleProjectManager.updateProjectStatus(
                        eq(bundleId), any(), any(), eq(updatedBy), eq(updatedAt)))
                .thenReturn(List.of());

        boolean result =
                aggregateBundleManager.updateStatus(bundleId, status, updatedBy, updatedAt);

        assertTrue(result);
        verify(bundleManager, times(1))
                .updateStatus(bundleId, BundleStatus.CUSTOMER_CONTACTED, updatedBy, updatedAt);
        verify(bundleProjectManager, times(1))
                .updateProjectStatus(eq(bundleId), any(), any(), eq(updatedBy), eq(updatedAt));
    }

    @Test
    void testUpdateStatus_CustomerContacted_NoMatch_ThrowsException() {
        String bundleId = "bundle1";
        BundleStatus status = BundleStatus.CUSTOMER_CONTACTED;
        String updatedBy = "user1";
        Instant updatedAt = Instant.now();

        BundleMessage bundleMessage =
                BundleMessage.newBuilder().setStatus(BundleStatus.CREATED).build();
        ProjectGroup projectGroup = mock(ProjectGroup.class);
        ProjectII projectII = mock(ProjectII.class);
        ProjectMessage projectMessage =
                ProjectMessage.newBuilder()
                        .setId("10001")
                        .setLatestStatus(ProjectStatus.PROJECT_CREATED)
                        .build();

        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);
        when(projectGroupManager.findByGroupKey(bundleId, "BUNDLE_PROJECT"))
                .thenReturn(projectGroup);
        when(projectGroup.getProjectIds()).thenReturn(Collections.singletonList("10001"));
        when(projectIIRepository.findAllById(Collections.singletonList("10001")))
                .thenAnswer(e -> Collections.singletonList(projectII));
        when(projectII.toMessage()).thenReturn(projectMessage);
        when(bundleStatusTransitionValidator.test(
                        BundleStatus.CREATED, BundleStatus.CUSTOMER_CONTACTED))
                .thenReturn(true);

        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () -> {
                            aggregateBundleManager.updateStatus(
                                    bundleId, status, updatedBy, updatedAt);
                        });

        assertEquals(
                "None of the bundle project contains status CUSTOMER_CONTACTED",
                exception.getMessage());
    }

    @Test
    void testUpdateStatus_OtherStatus_AllMatch() {
        String bundleId = "bundle1";
        BundleStatus status = BundleStatus.IMAGE_UPLOADED;
        String updatedBy = "user1";
        Instant updatedAt = Instant.now();

        BundleMessage bundleMessage =
                BundleMessage.newBuilder().setStatus(BundleStatus.CREATED).build();
        ProjectGroup projectGroup = mock(ProjectGroup.class);
        ProjectII projectII = mock(ProjectII.class);
        ProjectMessage projectMessage =
                ProjectMessage.newBuilder()
                        .setId("10001")
                        .setLatestStatus(ProjectStatus.IMAGE_UPLOADED)
                        .build();

        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);
        when(projectGroupManager.findByGroupKey(bundleId, "BUNDLE_PROJECT"))
                .thenReturn(projectGroup);
        when(projectGroup.getProjectIds()).thenReturn(Collections.singletonList("10001"));
        when(projectIIRepository.findAllById(Collections.singletonList("10001")))
                .thenAnswer(e -> Collections.singletonList(projectII));
        when(projectII.toMessage()).thenReturn(projectMessage);
        when(bundleStatusTransitionValidator.test(
                        BundleStatus.CREATED, BundleStatus.IMAGE_UPLOADED))
                .thenReturn(true);
        when(bundleManager.updateStatus(
                        bundleId, BundleStatus.IMAGE_UPLOADED, updatedBy, updatedAt))
                .thenReturn(true);

        boolean result =
                aggregateBundleManager.updateStatus(bundleId, status, updatedBy, updatedAt);

        assertTrue(result);
        verify(bundleManager, times(1))
                .updateStatus(bundleId, BundleStatus.IMAGE_UPLOADED, updatedBy, updatedAt);
    }

    @Test
    void testUpdateStatus_OtherStatus_NotAllMatch_ThrowsException() {
        String bundleId = "bundle1";
        BundleStatus status = BundleStatus.IMAGE_UPLOADED;
        String updatedBy = "user1";
        Instant updatedAt = Instant.now();

        BundleMessage bundleMessage =
                BundleMessage.newBuilder().setStatus(BundleStatus.CREATED).build();
        ProjectGroup projectGroup = mock(ProjectGroup.class);
        ProjectII projectII1 = mock(ProjectII.class);
        ProjectII projectII2 = mock(ProjectII.class);
        ProjectMessage projectMessage1 =
                ProjectMessage.newBuilder()
                        .setId("10001")
                        .setLatestStatus(ProjectStatus.IMAGE_UPLOADED)
                        .build();
        ProjectMessage projectMessage2 =
                ProjectMessage.newBuilder()
                        .setId("10002")
                        .setLatestStatus(ProjectStatus.PROJECT_CREATED)
                        .build();

        when(bundleManager.findById(bundleId)).thenReturn(bundleMessage);
        when(projectGroupManager.findByGroupKey(bundleId, "BUNDLE_PROJECT"))
                .thenReturn(projectGroup);
        when(projectGroup.getProjectIds()).thenReturn(Arrays.asList("10001", "10002"));
        when(projectIIRepository.findAllById(Arrays.asList("10001", "10002")))
                .thenAnswer(e -> Arrays.asList(projectII1, projectII2));
        when(projectII1.toMessage()).thenReturn(projectMessage1);
        when(projectII2.toMessage()).thenReturn(projectMessage2);
        when(bundleStatusTransitionValidator.test(
                        BundleStatus.CREATED, BundleStatus.IMAGE_UPLOADED))
                .thenReturn(true);

        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () -> {
                            aggregateBundleManager.updateStatus(
                                    bundleId, status, updatedBy, updatedAt);
                        });

        assertTrue(
                exception
                        .getMessage()
                        .contains(
                                "Cannot change bundle bundle1 status, projects [10002] do not"
                                        + " match the status."));
    }
}
