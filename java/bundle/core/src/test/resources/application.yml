spring:
  datasource:
    url: ****************************************
    username: db_user
    password: db_password
    platform: postgres
  jooq:
    sql-dialect: POSTGRES

bundle:
  creation:
    error-code-message-mapping:
      # already exists exception
      12: >-
        Duplicate Projects Detected. If these projects aren't actual duplicates,
        create them via the Bundle General page.
  status:
    status-transition-mapping:
      CREATED: [ "ASSIGNED_TO_PILOT", "CANCELLED" ]
      ASSIGNED_TO_PILOT: [ "CUSTOMER_CONTACTED", "CANCELLED" ]
      CUSTOMER_CONTACTED: [ "SITE_INSPECTED", "IMAGE_UPLOADED", "CANCELLED" ]
      SITE_INSPECTED: [ "IMAGE_UPLOADED", "CANC<PERSON>LED" ]
      IMAGE_UPLOADED: [ "RETURNED_TO_CLIENT", "CANCELLED" ]
      RETURNED_TO_CLIENT: [ "CA<PERSON><PERSON>LED", "RECEIVED_ERROR" ]
      RECEIVED_ERROR: [ "CANCELLED" ]
      CANCELLED: [ ]

grpc:
  server:
    port: -1
  client:
    bundleManager:
      address: "in-process:${grpc.server.in-process-name}"
      negotiation-type: plaintext
