package com.bees360.hazardhub;

import com.bees360.repository.Repository;
import com.bees360.util.Iterables;

import java.util.Objects;

public interface AddressHazardHubRepository
        extends AddressHazardHubProvider, Repository<AddressHazardHub> {

    @Override
    default AddressHazardHub findById(String id) {
        return findLatestByAddressId(id);
    }

    @Override
    default Iterable<? extends AddressHazardHub> findAllById(Iterable<String> ids) {
        return Iterables.from(Iterables.toStream(ids).map(this::findById).filter(Objects::nonNull));
    }
}
