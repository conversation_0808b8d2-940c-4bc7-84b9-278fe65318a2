package com.bees360.hazardhub;

import com.google.common.base.Preconditions;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

@Log4j2
public class LinkedAddressHazardHubProvider implements AddressHazardHubProvider {
    private final AddressHazardHubProvider primaryAddressHazardHubProvider;
    private final AddressHazardHubProvider secondaryAddressHazardHubProvider;

    public LinkedAddressHazardHubProvider(
            @NonNull AddressHazardHubProvider primaryAddressHazardHubProvider,
            @NonNull AddressHazardHubProvider secondaryAddressHazardHubProvider) {
        this.primaryAddressHazardHubProvider = primaryAddressHazardHubProvider;
        this.secondaryAddressHazardHubProvider = secondaryAddressHazardHubProvider;
        log.info(
                "Create {}(primaryAddressHazardHubProvider={},"
                        + " secondaryAddressHazardHubProvider={})",
                this,
                this.primaryAddressHazardHubProvider,
                this.secondaryAddressHazardHubProvider);
    }

    @Override
    public AddressHazardHub findLatestByAddressId(String addressId) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(addressId), "Address id can not be null");
        AddressHazardHub addressHazardHub;
        addressHazardHub = primaryAddressHazardHubProvider.findLatestByAddressId(addressId);
        if (isNeedToGetFromSecondary(addressHazardHub)) {
            addressHazardHub = secondaryAddressHazardHubProvider.findLatestByAddressId(addressId);
        }

        return addressHazardHub;
    }

    private boolean isNeedToGetFromSecondary(AddressHazardHub addressHazardHub) {
        return addressHazardHub == null
                || ArrayUtils.contains(
                        new Object[] {
                            addressHazardHub.getRisksJson(),
                            addressHazardHub.getEnhancedPropertyJson(),
                            addressHazardHub.getReplacementCostsJson()
                        },
                        null);
    }
}
