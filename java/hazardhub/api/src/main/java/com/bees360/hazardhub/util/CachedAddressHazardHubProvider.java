package com.bees360.hazardhub.util;

import com.bees360.hazardhub.AddressHazardHub;
import com.bees360.hazardhub.AddressHazardHubProvider;
import com.bees360.hazardhub.AddressHazardHubRepository;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.Set;

@Log4j2
public class CachedAddressHazardHubProvider implements AddressHazardHubProvider {
    private final AddressHazardHubRepository cacheAddressHazardHubRepository;
    private final AddressHazardHubProvider addressHazardHubProvider;
    private final Duration updateInterval;

    private static final Set<String> EMPTY_JSON_STRINGS = Sets.newHashSet("{}", "", null);

    public CachedAddressHazardHubProvider(
            @NonNull AddressHazardHubRepository cacheAddressHazardHubRepository,
            @NonNull AddressHazardHubProvider addressHazardHubProvider) {
        this(cacheAddressHazardHubRepository, addressHazardHubProvider, Duration.ofDays(1));
    }

    public CachedAddressHazardHubProvider(
            @NonNull AddressHazardHubRepository cacheAddressHazardHubRepository,
            @NonNull AddressHazardHubProvider addressHazardHubProvider,
            @NonNull Duration updateInterval) {
        this.cacheAddressHazardHubRepository = cacheAddressHazardHubRepository;
        this.addressHazardHubProvider = addressHazardHubProvider;
        this.updateInterval = updateInterval;

        log.info(
                "Create {}(cacheAddressHazardHubRepository={}, addressHazardHubProvider={})",
                this,
                this.cacheAddressHazardHubRepository,
                this.addressHazardHubProvider);
    }

    @Override
    public AddressHazardHub findLatestByAddressId(String addressId) {
        Preconditions.checkArgument(
                StringUtils.isNotBlank(addressId), "Address id can not be null");
        // Get address hazard hub data from cache repository
        AddressHazardHub addressHazardHub = cacheAddressHazardHubRepository.findById(addressId);
        // If not found in cache, then get by addressHazardHubProvider
        if (needUpdate(addressHazardHub)) {
            log.info(
                    "Can't find the latest address risks data (address_id={}) correctly from"
                            + " cache.",
                    addressId);
            // Get by addressHazardHubProvider
            addressHazardHub = addressHazardHubProvider.findLatestByAddressId(addressId);
            // Save into cache repository
            cacheAddressHazardHubRepository.save(addressHazardHub);
            addressHazardHub = cacheAddressHazardHubRepository.findById(addressId);
        }
        return addressHazardHub;
    }

    private boolean needUpdate(AddressHazardHub addressHazardHub) {
        if (addressHazardHub == null) {
            return true;
        }
        var isAllFieldsEmpty =
                EMPTY_JSON_STRINGS.contains(addressHazardHub.getRisksJson())
                        && EMPTY_JSON_STRINGS.contains(addressHazardHub.getEnhancedPropertyJson())
                        && EMPTY_JSON_STRINGS.contains(addressHazardHub.getReplacementCostsJson());

        var updateIntervalMatch =
                addressHazardHub.getUpdatedAt() != null
                        && addressHazardHub
                                        .getUpdatedAt()
                                        .plus(updateInterval)
                                        .compareTo(Instant.now())
                                < 0;
        return isAllFieldsEmpty && updateIntervalMatch;
    }
}
