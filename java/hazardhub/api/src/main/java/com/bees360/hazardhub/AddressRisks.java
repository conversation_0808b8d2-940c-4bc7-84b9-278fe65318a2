package com.bees360.hazardhub;

import com.bees360.address.Message.AddressRisksMessage;
import com.bees360.api.Entity;
import com.bees360.api.Proto;

import java.time.Instant;
import java.util.Optional;

public interface AddressRisks extends Proto<AddressRisksMessage>, Entity {

    @Override
    default String getNamespace() {
        return "address/risks";
    }

    @Override
    String getId();

    String getRisksJson();

    String getProtectionCode();

    Instant getCreatedAt();

    Instant getUpdatedAt();

    class AddressRisksBuilder {

        @lombok.Builder(
                builderClassName = "Builder",
                builderMethodName = "newBuilder",
                setterPrefix = "set")
        static AddressRisks of(
                String addressId,
                String risksJson,
                String protectionCode,
                Instant createdAt,
                Instant updatedAt) {

            return AddressRisks.of(addressId, risksJson, protectionCode, createdAt, updatedAt);
        }
    }

    static AddressRisks of(
            String addressId,
            String risksJson,
            String protectionCode,
            Instant createdAt,
            Instant updatedAt) {
        return new AddressRisks() {

            @Override
            public String getId() {
                return addressId;
            }

            @Override
            public String getRisksJson() {
                return risksJson;
            }

            @Override
            public String getProtectionCode() {
                return protectionCode;
            }

            @Override
            public Instant getCreatedAt() {
                return createdAt;
            }

            @Override
            public Instant getUpdatedAt() {
                return updatedAt;
            }
        };
    }

    static AddressRisks from(final AddressRisksMessage addressRisks) {
        if (addressRisks.equals(AddressRisksMessage.getDefaultInstance())) {
            return null;
        }

        return new AddressRisks() {

            @Override
            public String getId() {
                return addressRisks.getAddressId();
            }

            @Override
            public String getRisksJson() {
                return addressRisks.getRisksJson();
            }

            @Override
            public String getProtectionCode() {
                return addressRisks.getProtectionCode();
            }

            @Override
            public Instant getCreatedAt() {
                return Instant.ofEpochSecond(
                        addressRisks.getCreatedAt().getSeconds(),
                        addressRisks.getCreatedAt().getNanos());
            }

            @Override
            public Instant getUpdatedAt() {
                return Instant.ofEpochSecond(
                        addressRisks.getUpdatedAt().getSeconds(),
                        addressRisks.getUpdatedAt().getNanos());
            }
        };
    }

    @Override
    default AddressRisksMessage toMessage() {
        return AddressRisksMessage.newBuilder()
                .setAddressId(Optional.ofNullable(getId()).orElse(""))
                .setRisksJson(Optional.ofNullable(getRisksJson()).orElse(""))
                .setProtectionCode(Optional.ofNullable(getProtectionCode()).orElse(""))
                .build();
    }
}
