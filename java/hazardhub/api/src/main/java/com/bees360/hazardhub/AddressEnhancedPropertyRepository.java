package com.bees360.hazardhub;

import com.bees360.repository.Repository;
import com.bees360.util.Iterables;

import java.util.Objects;

public interface AddressEnhancedPropertyRepository
        extends AddressEnhancedPropertyProvider, Repository<AddressEnhancedProperty> {

    @Override
    default AddressEnhancedProperty findById(String id) {
        return findLatestByAddressId(id);
    }

    @Override
    default Iterable<? extends AddressEnhancedProperty> findAllById(Iterable<String> ids) {
        return Iterables.from(Iterables.toStream(ids).map(this::findById).filter(Objects::nonNull));
    }
}
