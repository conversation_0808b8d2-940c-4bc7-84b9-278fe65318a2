package com.bees360.hazardhub;

import com.bees360.address.Message.AddressEnhancedPropertyMessage;
import com.bees360.api.Entity;
import com.bees360.api.Proto;

import java.time.Instant;
import java.util.Optional;

public interface AddressEnhancedProperty extends Proto<AddressEnhancedPropertyMessage>, Entity {

    @Override
    default String getNamespace() {
        return "address/enhanced-property";
    }

    @Override
    String getId();

    String getEnhancedPropertyJson();

    Instant getCreatedAt();

    Instant getUpdatedAt();

    class AddressEnhancedPropertyBuilder {

        @lombok.Builder(
                builderClassName = "Builder",
                builderMethodName = "newBuilder",
                setterPrefix = "set")
        static AddressEnhancedProperty of(
                String addressId,
                String enhancedPropertyJson,
                Instant createdAt,
                Instant updatedAt) {

            return AddressEnhancedProperty.of(
                    addressId, enhancedPropertyJson, createdAt, updatedAt);
        }
    }

    static AddressEnhancedProperty of(
            String addressId, String enhancedPropertyJson, Instant createdAt, Instant updatedAt) {
        return new AddressEnhancedProperty() {

            @Override
            public String getId() {
                return addressId;
            }

            @Override
            public String getEnhancedPropertyJson() {
                return enhancedPropertyJson;
            }

            @Override
            public Instant getCreatedAt() {
                return createdAt;
            }

            @Override
            public Instant getUpdatedAt() {
                return updatedAt;
            }
        };
    }

    static AddressEnhancedProperty from(
            final AddressEnhancedPropertyMessage addressEnhancedProperty) {
        if (addressEnhancedProperty.equals(AddressEnhancedPropertyMessage.getDefaultInstance())) {
            return null;
        }

        return new AddressEnhancedProperty() {

            @Override
            public String getId() {
                return addressEnhancedProperty.getId();
            }

            @Override
            public String getEnhancedPropertyJson() {
                return addressEnhancedProperty.getEnhancedPropertyJson();
            }

            @Override
            public Instant getCreatedAt() {
                return Instant.ofEpochSecond(
                        addressEnhancedProperty.getCreatedAt().getSeconds(),
                        addressEnhancedProperty.getCreatedAt().getNanos());
            }

            @Override
            public Instant getUpdatedAt() {
                return Instant.ofEpochSecond(
                        addressEnhancedProperty.getUpdatedAt().getSeconds(),
                        addressEnhancedProperty.getUpdatedAt().getNanos());
            }
        };
    }

    @Override
    default AddressEnhancedPropertyMessage toMessage() {
        return AddressEnhancedPropertyMessage.newBuilder()
                .setAddressId(Optional.ofNullable(getId()).orElse(""))
                .setEnhancedPropertyJson(Optional.ofNullable(getEnhancedPropertyJson()).orElse(""))
                .build();
    }
}
