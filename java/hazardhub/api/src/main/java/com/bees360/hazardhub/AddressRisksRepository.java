package com.bees360.hazardhub;

import com.bees360.repository.Repository;
import com.bees360.util.Iterables;

import java.util.Objects;

public interface AddressRisksRepository extends AddressRisksProvider, Repository<AddressRisks> {

    @Override
    default AddressRisks findById(String id) {
        return findLatestByAddressId(id);
    }

    @Override
    default Iterable<? extends AddressRisks> findAllById(Iterable<String> ids) {
        return Iterables.from(Iterables.toStream(ids).map(this::findById).filter(Objects::nonNull));
    }
}
