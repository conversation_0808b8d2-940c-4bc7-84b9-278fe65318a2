package com.bees360.hazardhub;

import com.bees360.repository.Repository;
import com.bees360.util.Iterables;

import java.util.Objects;

public interface AddressReplacementCostsRepository
        extends AddressReplacementCostsProvider, Repository<AddressReplacementCosts> {

    @Override
    default AddressReplacementCosts findById(String id) {
        return findLatestByAddressId(id);
    }

    @Override
    default Iterable<? extends AddressReplacementCosts> findAllById(Iterable<String> ids) {
        return Iterables.from(Iterables.toStream(ids).map(this::findById).filter(Objects::nonNull));
    }
}
