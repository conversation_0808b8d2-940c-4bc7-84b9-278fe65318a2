package com.bees360.hazardhub;

import com.bees360.address.Message.AddressReplacementCostsMessage;
import com.bees360.api.Entity;
import com.bees360.api.Proto;

import java.time.Instant;
import java.util.Optional;

public interface AddressReplacementCosts extends Proto<AddressReplacementCostsMessage>, Entity {

    @Override
    default String getNamespace() {
        return "address/replacement-costs";
    }

    @Override
    String getId();

    String getReplacementCostsJson();

    Instant getCreatedAt();

    Instant getUpdatedAt();

    class AddressReplacementCostsBuilder {

        @lombok.Builder(
                builderClassName = "Builder",
                builderMethodName = "newBuilder",
                setterPrefix = "set")
        static AddressReplacementCosts of(
                String addressId,
                String replacementCostsJson,
                Instant createdAt,
                Instant updatedAt) {

            return AddressReplacementCosts.of(
                    addressId, replacementCostsJson, createdAt, updatedAt);
        }
    }

    static AddressReplacementCosts of(
            String addressId, String replacementCostsJson, Instant createdAt, Instant updatedAt) {
        return new AddressReplacementCosts() {

            @Override
            public String getId() {
                return addressId;
            }

            @Override
            public String getReplacementCostsJson() {
                return replacementCostsJson;
            }

            @Override
            public Instant getCreatedAt() {
                return createdAt;
            }

            @Override
            public Instant getUpdatedAt() {
                return updatedAt;
            }
        };
    }

    static AddressReplacementCosts from(
            final AddressReplacementCostsMessage addressReplacementCosts) {
        if (addressReplacementCosts.equals(AddressReplacementCostsMessage.getDefaultInstance())) {
            return null;
        }

        return new AddressReplacementCosts() {

            @Override
            public String getId() {
                return addressReplacementCosts.getId();
            }

            @Override
            public String getReplacementCostsJson() {
                return addressReplacementCosts.getReplacementCostsJson();
            }

            @Override
            public Instant getCreatedAt() {
                return Instant.ofEpochSecond(
                        addressReplacementCosts.getCreatedAt().getSeconds(),
                        addressReplacementCosts.getCreatedAt().getNanos());
            }

            @Override
            public Instant getUpdatedAt() {
                return Instant.ofEpochSecond(
                        addressReplacementCosts.getUpdatedAt().getSeconds(),
                        addressReplacementCosts.getUpdatedAt().getNanos());
            }
        };
    }

    @Override
    default AddressReplacementCostsMessage toMessage() {
        return AddressReplacementCostsMessage.newBuilder()
                .setAddressId(Optional.ofNullable(getId()).orElse(""))
                .setReplacementCostsJson(Optional.ofNullable(getReplacementCostsJson()).orElse(""))
                .build();
    }
}
