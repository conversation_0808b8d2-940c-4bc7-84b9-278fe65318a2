package com.bees360.hazardhub;

import com.bees360.api.Entity;
import com.google.protobuf.ByteString;

import jakarta.annotation.Nullable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.Instant;
import java.util.Optional;

@Builder
@Getter
@AllArgsConstructor
@EqualsAndHashCode
public class AddressHazardHub implements Entity {

    private String addressId;
    private String risksJson;
    private String protectionCode;
    private String enhancedPropertyJson;
    private String replacementCostsJson;
    private Instant createdAt;
    private Instant updatedAt;

    public AddressHazardHub() {}

    static AddressHazardHub of(
            String addressId,
            String risksJson,
            String protectionCode,
            String enhancedPropertyJson,
            String replacementCostsJson,
            Instant createdAt,
            Instant updatedAt) {
        return new AddressHazardHub(
                addressId,
                risksJson,
                protectionCode,
                enhancedPropertyJson,
                replacementCostsJson,
                createdAt,
                updatedAt) {

            @Override
            public ByteString toByteString() {
                return null;
            }

            @Override
            public String getAddressId() {
                return addressId;
            }

            @Override
            public String getRisksJson() {
                return risksJson;
            }

            @Override
            public String getProtectionCode() {
                return protectionCode;
            }

            @Override
            public String getEnhancedPropertyJson() {
                return enhancedPropertyJson;
            }

            @Override
            public String getReplacementCostsJson() {
                return replacementCostsJson;
            }

            @Override
            public Instant getCreatedAt() {
                return createdAt;
            }

            @Override
            public Instant getUpdatedAt() {
                return updatedAt;
            }
        };
    }

    static AddressHazardHub from(final AddressHazardHub addressHazardhub) {

        return new AddressHazardHub() {
            @Override
            public String getId() {
                return addressHazardhub.getId();
            }

            @Override
            public String getRisksJson() {
                return addressHazardhub.getRisksJson();
            }

            @Override
            public String getProtectionCode() {
                return addressHazardhub.getProtectionCode();
            }

            @Override
            public String getEnhancedPropertyJson() {
                return addressHazardhub.getEnhancedPropertyJson();
            }

            @Override
            public String getReplacementCostsJson() {
                return addressHazardhub.getReplacementCostsJson();
            }

            @Override
            public Instant getCreatedAt() {
                return addressHazardhub.getCreatedAt();
            }

            @Override
            public Instant getUpdatedAt() {
                return addressHazardhub.getUpdatedAt();
            }
        };
    }

    @Override
    public String getNamespace() {
        return "address/hazardhub";
    }

    @Nullable
    @Override
    public String getId() {
        return null;
    }

    public AddressHazardHub toMessage() {
        return AddressHazardHub.builder()
                .addressId(Optional.ofNullable(getAddressId()).orElse(""))
                .risksJson(Optional.ofNullable(getRisksJson()).orElse(""))
                .protectionCode(Optional.ofNullable(getProtectionCode()).orElse(""))
                .enhancedPropertyJson(Optional.ofNullable(getEnhancedPropertyJson()).orElse(""))
                .replacementCostsJson(Optional.ofNullable(getReplacementCostsJson()).orElse(""))
                .build();
    }

    @Override
    public ByteString toByteString() {
        return null;
    }
}
