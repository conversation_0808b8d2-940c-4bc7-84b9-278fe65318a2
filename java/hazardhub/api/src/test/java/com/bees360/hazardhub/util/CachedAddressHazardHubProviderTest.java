package com.bees360.hazardhub.util;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.bees360.hazardhub.AddressHazardHub;
import com.bees360.hazardhub.AddressHazardHubProvider;
import com.bees360.hazardhub.AddressHazardHubRepository;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CachedAddressHazardHubProviderTest {
    @Mock private AddressHazardHubRepository cacheAddressHazardHubRepository;
    @Mock private AddressHazardHubProvider addressHazardHubProvider;
    private CachedAddressHazardHubProvider cachedAddressHazardHubProvider;
    private AddressHazardHub addressHazardHub;

    @BeforeEach
    void init() {
        cachedAddressHazardHubProvider =
                new CachedAddressHazardHubProvider(
                        cacheAddressHazardHubRepository, addressHazardHubProvider);
        addressHazardHub =
                AddressHazardHub.builder()
                        .addressId(getRandomString())
                        .risksJson(getRandomString())
                        .protectionCode(getRandomString())
                        .enhancedPropertyJson(getRandomString())
                        .replacementCostsJson(getRandomString())
                        .build();
    }

    @Test
    void testFindLatestByAddressId() {
        when(cacheAddressHazardHubRepository.findById(any())).thenReturn(addressHazardHub);
        AddressHazardHub actual =
                cachedAddressHazardHubProvider.findLatestByAddressId(getRandomString());
        Assertions.assertEquals(addressHazardHub, actual);
    }

    private String getRandomString() {
        return RandomStringUtils.randomAlphabetic(10);
    }
}
