package com.bees360.activity.listener;

import static org.apache.commons.lang3.RandomStringUtils.randomAlphabetic;
import static org.apache.commons.lang3.RandomStringUtils.randomAlphanumeric;
import static org.apache.commons.lang3.RandomStringUtils.randomNumeric;
import static org.apache.commons.lang3.RandomUtils.nextInt;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.config.ActivityEventConfig;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.CommonActivityEvent;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.MoreExecutors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.net.URL;
import java.util.ArrayList;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;

@SpringBootTest
public class AddActivityOnCommonActivityEventTest {
    @Configuration
    @Import({
        ActivityEventConfig.class,
        InMemoryEventPublisher.class,
        AutoRegisterEventListenerConfig.class
    })
    static class Config {
        @MockBean ActivityManager activityManager;

        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired private ActivityManager activityManager;

    @Autowired private EventPublisher eventPublisher;

    @Test
    void testAddActivityOnActivityEvent() {
        var event = new CommonActivityEvent();
        event.setProjectId("1" + randomNumeric(8));
        event.setAction("CHANGE");
        event.setCreatedAt(System.currentTimeMillis());
        event.setSource("WEB");
        event.setLevel("INFO");
        event.setVisibility(Set.of("IO", "AI"));

        var createdByUser = new CommonActivityEvent.User();
        createdByUser.setId(randomNumeric(32));
        createdByUser.setName(randomAlphabetic(12));
        createdByUser.setEmail(String.format("%<EMAIL>", randomAlphabetic(6)));
        createdByUser.setPhone(randomNumeric(11));
        createdByUser.setPhoto("https://default-photo");
        event.setCreatedByUser(createdByUser);

        var entity = new CommonActivityEvent.Entity();
        entity.setId(randomAlphanumeric(8));
        entity.setType("IMAGE");
        entity.setCount(nextInt());
        entity.setOperationsManager(randomAlphabetic(32));
        entity.setPilotId(randomAlphabetic(32));

        var field = new CommonActivityEvent.Field();
        field.setName(randomAlphabetic(8));
        field.setType("STRING");
        field.setValue("NEW");
        field.setOldValue("OLD");
        field.setDisplayName(randomAlphabetic(8));

        var comment = new CommonActivityEvent.Comment();
        comment.setContent(randomAlphanumeric(16));
        var attachments = new ArrayList<CommonActivityEvent.Attachment>();
        for (int i = nextInt(0, 5); i > 0; i--) {
            var attachment = new CommonActivityEvent.Attachment();
            attachment.setFilename(randomAlphanumeric(16));
            attachment.setUrl(randomAlphanumeric(32));
            attachments.add(attachment);
        }
        comment.setAttachment(attachments);

        event.setEntity(entity);
        event.setField(field);
        event.setComment(comment);

        eventPublisher.publish(event);

        ArgumentCaptor<Activity> activityCaptor = ArgumentCaptor.forClass(Activity.class);
        Mockito.verify(activityManager).submitActivity(activityCaptor.capture());

        var activity = activityCaptor.getValue();
        Assertions.assertEquals(event.getProjectId(), String.valueOf(activity.getProjectId()));
        Assertions.assertEquals(event.getCreatedAt(), activity.getCreatedAt().toEpochMilli());
        Assertions.assertEquals(createdByUser.getId(), activity.getCreatedBy());
        Assertions.assertEquals(createdByUser.getName(), activity.getCreatedByUser().getName());
        Assertions.assertEquals(createdByUser.getEmail(), activity.getCreatedByUser().getEmail());
        Assertions.assertEquals(createdByUser.getPhone(), activity.getCreatedByUser().getPhone());
        Assertions.assertEquals(
                createdByUser.getPhoto(),
                Optional.ofNullable(activity.getCreatedByUser().getPhoto())
                        .map(URL::toString)
                        .orElse(null));
        Assertions.assertEquals(entity.getId(), activity.getEntityId());
        Assertions.assertEquals(entity.getType(), activity.getEntityType());
        Assertions.assertEquals(entity.getCount(), activity.getEntityCount());
        Assertions.assertEquals(entity.getPilotId(), activity.getPilot().getPilotUser().getId());
        Assertions.assertEquals(
                entity.getOperationsManager(), activity.getPilot().getOperationsManager().getId());

        Assertions.assertEquals(field.getName(), activity.getFiledName());
        Assertions.assertEquals(field.getType(), activity.getFiledType());
        Assertions.assertEquals(field.getValue(), activity.getValue());
        Assertions.assertEquals(field.getOldValue(), activity.getOldValue());
        Assertions.assertEquals(field.getDisplayName(), activity.getFieldDisplayName());

        var activityComment = activity.getComment();
        Assertions.assertNotNull(activityComment);
        Assertions.assertEquals(
                event.getProjectId(), String.valueOf(activityComment.getProjectId()));
        Assertions.assertEquals(createdByUser.getId(), activityComment.getCreatedBy());
        Assertions.assertEquals(
                event.getCreatedAt(), activityComment.getCreatedAt().toEpochMilli());
        Assertions.assertEquals(event.getSource(), activityComment.getSource());
        Assertions.assertEquals(comment.getContent(), activityComment.getContent());
        var commentAttachment = Iterables.toList(activityComment.getAttachment());
        Assertions.assertEquals(attachments.size(), commentAttachment.size());
    }
}
