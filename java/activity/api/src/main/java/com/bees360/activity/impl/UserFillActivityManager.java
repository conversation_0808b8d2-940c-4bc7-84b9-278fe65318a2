package com.bees360.activity.impl;

import static com.bees360.activity.Activity.getUserIdFromEntity;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.Message;
import com.bees360.user.User;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;

import jakarta.annotation.Nullable;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Log4j2
public class UserFillActivityManager extends ForwardingActivityManager {
    private final ActivityManager activityManager;
    private final UserKeyProvider userKeyProvider;
    private final UserProvider userProvider;
    private final String defaultSource;

    public UserFillActivityManager(
            ActivityManager activityManager,
            UserKeyProvider userKeyProvider,
            UserProvider userProvider,
            @Nullable String defaultSource) {
        this.activityManager = activityManager;
        this.userKeyProvider = userKeyProvider;
        this.userProvider = userProvider;
        this.defaultSource = defaultSource;
        log.info(
                "Created :{} with activityManager :{} and userKeyProvider :{} and userProvider :{}"
                        + " and defaultSource :{}",
                this,
                activityManager,
                userKeyProvider,
                userProvider,
                defaultSource);
    }

    public UserFillActivityManager(
            ActivityManager activityManager,
            UserKeyProvider userKeyProvider,
            UserProvider userProvider) {
        this(activityManager, userKeyProvider, userProvider, null);
    }

    @Override
    protected ActivityManager delegate() {
        return activityManager;
    }

    @Override
    public List<? extends Activity> getActivities(ActivityQuery query) {
        List<? extends Activity> activities = super.getActivities(query);
        Set<String> userIdSet = new HashSet<>(activities.size() * 2);
        log.info("get activity user id set :{}", userIdSet);
        activities.forEach(activity -> putUserIdToSet(activity, userIdSet));
        var userMap = userKeyProvider.findUsersByKeys(userIdSet, null);
        var userKey = userMap.keySet();
        userIdSet.removeAll(userKey);
        var webUserMap =
                Iterables.toStream(userProvider.findUserById(userIdSet))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(User::getId, user -> user));
        log.info("Find user map :{} and web user map :{}", userMap, webUserMap);

        return activities.stream()
                .map(activity -> fillUser(activity, userMap, webUserMap))
                .collect(Collectors.toList());
    }

    private void putUserIdToSet(Activity activity, Set<String> userIdSet) {
        getUserIdFromEntity(activity).ifPresent(userIdSet::add);
        userIdSet.add(activity.getCreatedBy());
    }

    private Activity fillUser(
            Activity activity,
            Map<String, ? extends User> userMap,
            Map<String, ? extends User> webUserMap) {
        if (activity == null || StringUtils.isBlank(activity.getCreatedBy())) {
            return activity;
        }
        Message.ActivityMessage.Builder message = activity.toMessage().toBuilder();
        var creatorId = activity.getCreatedBy();
        var entityId = activity.getEntityId();
        Optional.ofNullable(webUserMap.get(creatorId))
                .ifPresent(user -> message.setCreatedBy(user.toMessage()));
        Optional.ofNullable(userMap.get(creatorId))
                .ifPresent(user -> message.setCreatedBy(user.toMessage()));

        // fill entity user
        getUserIdFromEntity(activity)
                .flatMap(
                        memberUserId -> {
                            if (userMap.get(activity.getEntityId()) == null) {
                                return Optional.ofNullable(webUserMap.get(entityId));
                            }
                            return Optional.ofNullable(userMap.get(entityId));
                        })
                .ifPresent(
                        memberUser -> {
                            Message.ActivityMessage.Entity.Builder entityBuilder =
                                    activity.toMessage().getEntity().toBuilder();
                            entityBuilder.setUser(memberUser.toMessage());
                            message.setEntity(entityBuilder.build());
                        });
        return Activity.of(message.build());
    }

    @Override
    public Iterable<String> submitActivities(Iterable<? extends Activity> activities) {
        return super.submitActivities(
                Iterables.toStream(activities)
                        .map(this::buildActivityWithDefaultSource)
                        .collect(Collectors.toList()));
    }

    @Override
    public String submitActivity(Activity originActivity) {
        // Set default source and project type
        var activity = buildActivityWithDefaultSource(originActivity);
        return super.submitActivity(activity);
    }

    private Activity buildActivityWithDefaultSource(Activity activity) {
        Message.ActivityMessage activityMessage = activity.toMessage();
        // 默认后端生成的activity的来源为WEB, 如果是前端创建的activity应该主动设置该值，否则该值会被自动设置为WEB
        if (StringUtils.isBlank(activity.getSource()) && defaultSource != null) {
            activityMessage = activityMessage.toBuilder().setSource(defaultSource).build();
        }
        return Activity.of(activityMessage);
    }
}
