package com.bees360.activity.impl;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivitySyncLog;
import com.bees360.activity.ActivitySyncManager;
import com.bees360.activity.Message;
import com.google.common.base.Preconditions;

/** 实现了sync操作的activityManager */
public abstract class AbstractActivityManager implements ActivityManager {

    private ActivitySyncManager activitySyncManager;

    public AbstractActivityManager(ActivitySyncManager activitySyncManager) {
        this.activitySyncManager = activitySyncManager;
    }

    @Override
    public ActivitySyncLog sync(String activityId, String syncTo, String createdByUserId) {
        var activity = this.getActivity(activityId);
        Preconditions.checkArgument(activity != null);
        var log = activity.getSyncLog(syncTo);
        if (log != null
                && log.getStatus().equals(Message.ActivitySyncLogMessage.SyncLogStatus.SUCCESS)) {
            return log;
        }
        return activitySyncManager.getOrCreate(activity, syncTo, createdByUserId);
    }
}
