package com.bees360.activity;

import jakarta.annotation.Nullable;

import java.util.List;
import java.util.NoSuchElementException;

public interface ActivityManager {

    @Nullable
    Activity getActivity(String id);

    /**
     * 提交多个活动
     *
     * @param activities 活动对象
     * @return 成功提交的活动的主键ID
     */
    Iterable<String> submitActivities(Iterable<? extends Activity> activities);

    /**
     * 提交一个活动
     *
     * @param activity 活动对象
     * @return 成功提交的活动的主键ID
     * @throws IllegalArgumentException 提交的activity数据不符合要求
     */
    String submitActivity(Activity activity);

    /**
     * 查询活动列表: 分页参数说明
     *
     * <p>1. {@link ActivityQuery#getLimit()} ()} 限制单次查询的出的 activity 数量, 与 limit 结合使用, 具有最大值限制
     * (不传时取最大值), 小于 0 抛异常, 超过限制会抛出异常
     *
     * <p>2. {@link ActivityQuery#getOffset()}} 偏移量, 默认为 0, 与 limit 结合使用, 如果查询数量大于 limit, 设置 offset
     * 以查出符合条件的全部 activity
     *
     * @param query 活动查询参数
     * @return 返回符合查询条件的活动列表
     * @throws IllegalArgumentException limit 超过限制或者小于 0 时, 抛出非法参数异常; offset 小于 0 时抛出非法参数异常
     */
    List<? extends Activity> getActivities(ActivityQuery query);

    /**
     * 执行sync操作
     *
     * @throws NoSuchElementException 找不到activity
     */
    ActivitySyncLog sync(String activityId, @Nullable String syncTo, String createdByUserId);

    /**
     * 增加该activity的可见平台
     *
     * @param activityId,platform 活动id,平台
     */
    void addVisibility(String activityId, String platform);

    /**
     * 删除该activity的可见平台
     *
     * @param activityId,platform 活动id,平台
     */
    void removeVisibility(String activityId, String platform);

    /**
     * 通过主键ID删除活动
     *
     * @param id activity主键ID
     */
    void deleteById(String id);
}
