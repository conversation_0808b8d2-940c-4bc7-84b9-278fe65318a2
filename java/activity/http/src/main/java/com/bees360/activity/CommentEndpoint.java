package com.bees360.activity;

import com.bees360.activity.Message.CommentMessage;

import lombok.extern.log4j.Log4j2;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.NoSuchElementException;

/** CommentEndpoint */
@Log4j2
@RestController
public class CommentEndpoint {
    private final CommentManager commentManager;
    private final ActivityManager activityManager;

    public CommentEndpoint(
            final CommentManager endpointCommentManager,
            final ActivityManager endpointActivityManager) {
        this.commentManager = endpointCommentManager;
        this.activityManager = endpointActivityManager;
        log.info(
                "Created '{} (commentManager={},activityManager={})'",
                this,
                commentManager,
                activityManager);
    }

    /**
     * 提交评论信息
     *
     * @param commentMessage 评论信息
     * @return 评论信息
     */
    @PostMapping(value = "comment")
    public Comment addComment(@RequestBody CommentMessage commentMessage) {
        Comment comment = Comment.from(commentMessage);
        String currUserId = CurUserHolder.getUserId();
        if (currUserId != null) {
            comment = comment.withCreatedBy(currUserId);
        }
        return Comment.from(
                CommentMessage.newBuilder().setId(commentManager.addComment(comment)).build());
    }

    /**
     * 更新评论信息
     *
     * @param commentMessage 评论信息
     * @param commentId 评论ID
     * @return 评论信息
     */
    @PutMapping("comment/{commentId}")
    public Comment updateMessage(
            @RequestBody CommentMessage commentMessage, @PathVariable String commentId) {
        Comment comment = Comment.from(commentMessage).withId(commentId);
        String currUserId = CurUserHolder.getUserId();
        if (currUserId != null) {
            comment = comment.withCreatedBy(currUserId);
        }
        commentManager.updateComment(comment);
        return comment;
    }

    /**
     * 删除评论信息
     *
     * @param commentId 评论ID
     */
    @DeleteMapping("comment/{commentId}")
    public void deleteMessage(@PathVariable String commentId) {
        commentManager.deleteById(commentId);
    }

    /**
     * 查询评论信息
     *
     * @param commentId 评论ID
     * @return 评论
     */
    @GetMapping("comment/{commentId}")
    public Comment getComment(@PathVariable String commentId) {
        return commentManager.findById(commentId);
    }

    /**
     * 查询评论信息列表
     *
     * @param query 查询条件
     * @return 评论列表
     */
    @GetMapping("comment")
    public Iterable<? extends Comment> getComments(CommentQuery query) {
        return commentManager.getComments(query);
    }

    /**
     * 同步评论到指定目标
     *
     * @param commentId 评论ID
     * @param syncTo 同步目的地
     * @return 同步日志
     */
    @PostMapping("comment/{commentId}/sync-log/{syncTo}")
    public ActivitySyncLog syncComment(
            @PathVariable String commentId, @PathVariable String syncTo) {
        var query =
                ActivityQuery.builder()
                        .entityType(Message.ActivityMessage.EntityType.COMMENT.name())
                        .entityId(commentId)
                        .build();
        var activity = activityManager.getActivities(query).stream().findFirst().orElse(null);
        if (activity == null) {
            var message = String.format("The comment %s not found", commentId);
            throw new NoSuchElementException(message);
        }
        var createdByUserId = CurUserHolder.getUserId();
        return activityManager.sync(activity.getId(), syncTo, createdByUserId);
    }
}
