package com.bees360.activity;

import com.bees360.activity.impl.AbstractActivityManager;
import com.bees360.activity.impl.ActivityPlatformConfigProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Log4j2
public class MongoActivityManager extends AbstractActivityManager {

    private ActivityRepository activityRepository;
    private ActivityPlatformConfigProvider activityPlatformConfigProvider;

    public MongoActivityManager(
            ActivityRepository activityRepository,
            ActivitySyncManager activitySyncManager,
            ActivityPlatformConfigProvider activityPlatformConfigProvider) {
        super(activitySyncManager);
        this.activityRepository = activityRepository;
        this.activityPlatformConfigProvider = activityPlatformConfigProvider;
        log.info(
                "{} created: {activitySyncManager={}, activityRepository={},"
                        + " activityPlatformConfigProvider={}}",
                this,
                activitySyncManager,
                activityRepository,
                activityPlatformConfigProvider);
    }

    @Override
    public ActivityEntity getActivity(String id) {
        ActivityQuery query = new ActivityQuery();
        query.setId(id);
        return getActivities(query).stream().findFirst().orElse(null);
    }

    @Override
    public List<ActivityEntity> getActivities(ActivityQuery query) {
        return activityRepository.getActivities(query);
    }

    @Override
    public void addVisibility(String activityId, String platform) {
        activityRepository.addVisibility(activityId, platform);
    }

    @Override
    public void removeVisibility(String activityId, String platform) {
        activityRepository.removeVisibility(activityId, platform);
    }

    @Override
    public void deleteById(String id) {
        activityRepository.deleteById(id);
    }

    @Override
    public String submitActivity(Activity activity) {
        ActivityEntity activityEntity = ActivityEntitys.create(activity);
        if (!StringUtils.isBlank(activity.getId())) {
            throw new IllegalArgumentException("Update activity is not support");
        }
        if (StringUtils.isBlank(activity.getSource())) {
            log.error(
                    "Update activity is not supported, projectId:{}, activityId:{}.",
                    activity.getProjectId(),
                    activity.getId(),
                    new IllegalArgumentException("Blank source may cause invisibility."));
        }
        return activityRepository.save(fillExtraAndBlockedVisibility(activityEntity));
    }

    @Override
    public Iterable<String> submitActivities(Iterable<? extends Activity> activities) {
        return activityRepository.saveAll(
                Iterables.toStream(activities)
                        .map(ActivityEntitys::create)
                        .map(this::fillExtraAndBlockedVisibility)
                        .collect(Collectors.toList()));
    }

    /**
     * 将activity中传入的visibility与defaultVisibility对比后，初始化extraVisiblePlatform和blockedPlatform的值，再存入数据库。
     *
     * @param activity 活动对象
     * @return 初始化extraVisiblePlatform和blockedPlatform值后的活动对象
     */
    private ActivityEntity fillExtraAndBlockedVisibility(ActivityEntity activity) {
        if (activity.getVisibility().size() == 0) {
            return activity;
        }
        var defaultVisibility =
                activityPlatformConfigProvider.getPermittedPlatformsBySource(activity.getSource());
        var visibility = activity.getVisibility();
        Set<String> extraVisiblePlatform =
                visibility.stream()
                        .filter(e -> !defaultVisibility.contains(e))
                        .collect(Collectors.toSet());
        Set<String> blockedPlatform =
                defaultVisibility.stream()
                        .filter(e -> !visibility.contains(e))
                        .collect(Collectors.toSet());
        activity.setExtraVisiblePlatform(extraVisiblePlatform);
        activity.setBlockedPlatform(blockedPlatform);
        return activity;
    }
}
