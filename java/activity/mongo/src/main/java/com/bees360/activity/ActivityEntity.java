package com.bees360.activity;

import static com.bees360.project.base.Message.ProjectType.UNKNOWN_PROJECT_TYPE;

import com.bees360.activity.impl.AbstractActivity;
import com.bees360.project.base.Message.ProjectType;
import com.bees360.user.Pilot;
import com.bees360.user.util.AbstractUser;
import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.net.MalformedURLException;
import java.net.URL;
import java.time.Instant;
import java.util.Collections;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
@Document(value = "activity")
public class ActivityEntity extends AbstractActivity {
    @Override
    public String toString() {
        return getClass().getSimpleName() + "[ " + toMessage().toString() + " ]";
    }

    /** id */
    @Id private String id;

    /** projectId */
    @Indexed private long projectId;

    /** action */
    private String action;

    /** createdAt */
    private Instant createdAt;

    /** createdBy */
    @Indexed private String createdBy;

    private NestedUser createdByUser;

    /** entity */
    @Indexed private String entityId;

    private String entityType;
    private String entityName;
    private int entityCount;

    /** filed */
    private String filedName;

    private String fieldDisplayName;

    private String filedType;
    private String value;
    private String oldValue;

    private Comment comment;

    // comment in the comment collection
    private Comment oldComment;

    @Indexed private String source;

    private String level;

    private String projectType;

    private NestedPilot pilot;

    private Iterable<ActivitySyncLogEntity> allSyncLog = Lists.newArrayList();

    private Set<String> extraVisiblePlatform = new HashSet<>();

    private Set<String> blockedPlatform = new HashSet<>();

    private Set<String> visibility = new HashSet<>();

    private boolean deleted;

    @Override
    public ProjectType getProjectType() {
        if (Strings.isBlank(projectType)) {
            return UNKNOWN_PROJECT_TYPE;
        }
        return ProjectType.valueOf(projectType);
    }

    public Set<String> getExtraVisiblePlatform() {
        return extraVisiblePlatform;
    }

    public Set<String> getBlockedPlatform() {
        return blockedPlatform;
    }

    @Override
    public Set<String> getVisibility() {
        return visibility;
    }

    @Getter
    @Builder(builderClassName = "Builder", builderMethodName = "newBuilder", setterPrefix = "set")
    static class NestedUser extends AbstractUser {
        // avoid treat id as _id in mongodb because _id must be hex string
        @Id private final String _id;

        @Indexed(background = true)
        private final String id;

        private final String name;
        private final String email;
        private final String phone;
        private final URL photo;
        private final long uid;
        private final Set<String> allAuthority;

        static NestedUser from(com.bees360.user.Message.UserMessage message) {
            URL photo;
            try {
                photo = message.getPhoto().isEmpty() ? null : new URL(message.getPhoto());
            } catch (MalformedURLException e) {
                throw new IllegalArgumentException(e);
            }
            return message == com.bees360.user.Message.UserMessage.getDefaultInstance()
                    ? null
                    : NestedUser.newBuilder()
                            .setId(message.getId())
                            .setEmail(message.getEmail())
                            .setName(message.getName())
                            .setAllAuthority(
                                    message.getAuthorityList().asByteStringList().stream()
                                            .map(ByteString::toStringUtf8)
                                            .collect(Collectors.toSet()))
                            .setPhone(message.getPhone())
                            .setPhoto(photo)
                            .setUid(message.getUid())
                            .build();
        }

        @Override
        public String getEmail() {
            return StringUtils.isBlank(email) ? null : email;
        }

        @Override
        public String getPhone() {
            return StringUtils.isBlank(phone) ? null : phone;
        }

        @Override
        public com.bees360.user.Message.UserMessage toMessage() {
            return com.bees360.user.Message.UserMessage.newBuilder()
                    .setId(Optional.ofNullable(getId()).orElse(Strings.EMPTY))
                    .setName(Optional.ofNullable(getName()).orElse(Strings.EMPTY))
                    .setEmail(Optional.ofNullable(getEmail()).orElse(Strings.EMPTY))
                    .setPhone(Optional.ofNullable(getPhone()).orElse(Strings.EMPTY))
                    .setPhoto(
                            Optional.ofNullable(getPhoto())
                                    .map(URL::toString)
                                    .orElse(Strings.EMPTY))
                    .setUid(uid)
                    .addAllAuthority(
                            Optional.ofNullable(getAllAuthority()).orElse(Collections.emptySet()))
                    .build();
        }
    }

    @Getter
    @Builder(builderClassName = "Builder", builderMethodName = "newBuilder", setterPrefix = "set")
    static class NestedPilot implements Pilot {
        private final NestedUser operationsManager;
        private final NestedUser pilotUser;

        static NestedPilot from(com.bees360.user.Message.PilotMessage message) {
            return message == com.bees360.user.Message.PilotMessage.getDefaultInstance()
                    ? null
                    : NestedPilot.newBuilder()
                            .setOperationsManager(NestedUser.from(message.getOperationsManager()))
                            .setPilotUser(NestedUser.from(message.getPilotUser()))
                            .build();
        }

        @Override
        public com.bees360.user.Message.PilotMessage toMessage() {
            com.bees360.user.Message.PilotMessage.Builder builder =
                    com.bees360.user.Message.PilotMessage.newBuilder();
            Optional.ofNullable(operationsManager)
                    .ifPresent(o -> builder.setOperationsManager(o.toMessage()));
            Optional.ofNullable(pilotUser).ifPresent(o -> builder.setPilotUser(o.toMessage()));
            return builder.build();
        }
    }

    public Comment getComment() {
        return Optional.ofNullable(comment).orElse(oldComment);
    }
}
