package com.bees360.sse;

import com.bees360.estintel.Message;
import com.bees360.user.User;

import lombok.extern.log4j.Log4j2;

import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Objects;

/** SseEmitterServerEndpoint */
@Log4j2
@RestController
@RequestMapping("${http.sse.endpoint:sse}")
public class SseEmitterServerEndpoint {

    private final SseEmitterServer sseEmitterServer;
    private final SseEmitterServer lambdaStatusSseEmitterServer;

    public SseEmitterServerEndpoint(
            SseEmitterServer inMemorySseServer, SseEmitterServer lambdaStatusSseEmitterServer) {
        this.sseEmitterServer = inMemorySseServer;
        this.lambdaStatusSseEmitterServer = lambdaStatusSseEmitterServer;
        log.info("Created '{} (sseEmitterServer={})'", this, this.sseEmitterServer);
    }

    /**
     * Creates a sse connection with the user.
     *
     * @param user the user makes the connection
     * @return SseEmitter connection
     * @throws IOException network io exception
     */
    @GetMapping("")
    public SseEmitter connect(@AuthenticationPrincipal User user) throws IOException {
        return sseEmitterServer.connect(user.getId());
    }

    /**
     * Creates a sse connection using in checking estintel lambda status.
     *
     * @param lambdaFilter the filter for lambda the emitter will follow
     * @return SseEmitter connection
     * @throws IOException network io exception
     */
    @GetMapping("/lambda/status")
    public SseEmitter connectLambdaStatus(LambdaRunStatusEmitterParam lambdaFilter)
            throws IOException {
        if (!Objects.equals(
                lambdaFilter.toMessage().toBuilder().clearParams().build(),
                Message.LambdaRunStatusQueryRequest.getDefaultInstance())) {
            throw new UnsupportedOperationException(
                    String.format(
                            "Cannot register lambda status emitter with request '%s': temporarily"
                                    + " support params only.",
                            lambdaFilter));
        }

        var subscriber = lambdaFilter.getParams();
        return lambdaStatusSseEmitterServer.connect(subscriber);
    }
}
