spring:
  profiles:
    active: ${ENV}
    include: actuator

server:
  port: 8080
  servlet:
    context-path: /sse

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}
    property:
      prefix: ENC(
      suffix: )

es:
  host: elasticsearch
  port: 9200

http:
  sse:
    endpoint: "/sse"
  client:
    apache:
      maxConnPerRoute: 16
      maxConnTotal: 64
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT30S
        socketTimeout: PT15S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: true
  cors:
    '[/**]':
      allowed_origins: "*"
      allowed_headers: "*"
      allowed_methods: GET,HEAD,PUT,OPTIONS,POST,DELETE,PATCH
      allow_credentials: true
      exposed_headers: Authorization,Access-Control-Allow-Origin,Access-Control-Allow-Credentials,X-Protobuf-Message
      max_age: 86400
rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password
auth:
  jwt:
    verifierKey: |
      -----BEGIN CERTIFICATE-----
      MIIC/DCCAeSgAwIBAgIIOcybo4ESpmMwDQYJKoZIhvcNAQEFBQAwIDEeMBwGA1UE
      AxMVMTAzNzQxMjAwMzE3OTIwODg2ODQ1MCAXDTIxMDcyNzAzNDgxM1oYDzk5OTkx
      MjMxMjM1OTU5WjAgMR4wHAYDVQQDExUxMDM3NDEyMDAzMTc5MjA4ODY4NDUwggEi
      MA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDnpfrDqFClLoVqAg9GcM0sEmIR
      i7BP+gC8KautIhPyrScuujFHZf8MHP1AezjVd2gcWCi1pGTTNUbeZH2xet5qqP/X
      Gc2OsLFh9vAGOXQz1WtJWENv4ncazZu8uBeurVyJrNIaY0G3v3JYAUHKi/Wu0GCy
      t0bSwVYVlzH8IOwlZezuUmsoWMX56eI0CFrfA2WuGwvMJQzB7daN6XB68hGt+WFE
      18xXeJCKmyjhaFoY/KpWO1Z0g8/gLYLkPrC/oa6tNigENaK7aQmaPCJ3phwiDz2a
      WPNJ8evXMGVFIE5qUHG3aNEpA2Rru4pFA9+g3ZfjIu8VnQh1kLmL5dtw0x4pAgMB
      AAGjODA2MAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMBYGA1UdJQEB/wQM
      MAoGCCsGAQUFBwMCMA0GCSqGSIb3DQEBBQUAA4IBAQCTlQLQOUEr5PHvG1Y9z7Hc
      +6MXFb7WxiRM7IcSEPA0BwkZM94BF5fojlHZ2ox335669wrGqGpkNLBqIMLuxw1b
      4N5wxeeS2M8fZeUjM7kaHT8ZE+27FzOY1NNbFn+dyUqonGM/1GlGL3hgOV4hkyzk
      3vderqQakqR7bYyUmjCgBcAvQahOpkJxX4/XgqzaDQBDGmhG1VOvyTJEyZtBX1GN
      nmsz9CzqClBgydf6bnjgm4Gh4CHI4YKL/7yGXAkk50JqYbhkAmlNcIbILryilTRu
      MZGX7oskns0vBqMtLmIPG5eH/KL3KqUS1KTxW33Ucca+WnmKQjNys2QKBVLR1Y+F
      -----END CERTIFICATE-----
sse:
  expiration: PT5M
  todo:
    channel: todo
  app:
    estintel-lambda-run-status:
      lambdaStatus:
        - FAILED
        - COMPLETED
