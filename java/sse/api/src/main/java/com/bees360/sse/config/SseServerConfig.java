package com.bees360.sse.config;

import com.bees360.sse.InMemorySseEmitterServer;
import com.bees360.sse.SseEmitterServer;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

@EnableConfigurationProperties
@Configuration
public class SseServerConfig {
    @Data
    public static class Properties {
        private Duration expiration = Duration.ZERO;
    }

    @ConfigurationProperties("sse")
    @Bean
    public Properties todoProperties() {
        return new Properties();
    }

    @Bean
    public ScheduledExecutorService heartBeatExecutor() {
        var builder = new ThreadFactoryBuilder().setNameFormat("executor-heart-beat-%s");
        return Executors.newSingleThreadScheduledExecutor(builder.build());
    }

    @Bean
    public SseEmitterServer inMemorySseServer(
            Properties todoProperties, ScheduledExecutorService heartBeatExecutor) {
        return new InMemorySseEmitterServer(todoProperties.getExpiration(), heartBeatExecutor);
    }
}
