<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License").
  ~ You may not use this file except in compliance with the License.
  ~ A copy of the License is located at
  ~
  ~  http://aws.amazon.com/apache2.0
  ~
  ~ or in the "license" file accompanying this file. This file is distributed
  ~ on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
  ~ express or implied. See the License for the specific language governing
  ~ permissions and limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>core</artifactId>
        <version>2.29.16</version>
    </parent>

    <artifactId>checksums-spi</artifactId>
    <name>AWS Java SDK :: Checksums SPI </name>
    <description>
        The AWS SDK for Java - Checksums SPI module contains checksum interfaces that are used by other modules
            in the library.
    </description>
    <url>https://aws.amazon.com/sdkforjava</url>

    <dependencies>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>annotations</artifactId>
            <version>${awsjavasdk.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Automatic-Module-Name>software.amazon.awssdk.checksums.spi</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
