<?xml version="1.0" encoding="UTF-8"?>

<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 2017-2018 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    https://oss.oracle.com/licenses/CDDL+GPL-1.1
    or LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jaxb-api-parent</artifactId>
        <groupId>javax.xml.bind</groupId>
        <version>2.3.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jaxb-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>javax.activation-api</artifactId>
        </dependency>
    </dependencies>

    <build>

        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.build</groupId>
                    <artifactId>gfnexus-maven-plugin</artifactId>
                    <version>0.20</version>
                    <configuration>
                        <stagingRepos>
                            <stagingRepo>
                                <ref>javax.xml.bind:jaxb-api:${project.version}:jar</ref>
                                <profile>javax.xml.bind</profile>
                            </stagingRepo>
                        </stagingRepos>
                        <promotionProfile>metro</promotionProfile>
                        <message>JAXB_API-${project.version}</message>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <configuration>
                        <rules>
                            <requireJavaVersion>
                                <version>[1.8,)</version>
                            </requireJavaVersion>
                            <requireMavenVersion>
                                <version>[3.0.3,)</version>
                            </requireMavenVersion>
                            <DependencyConvergence />
                        </rules>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.7</version>
                    <configuration>
                        <formats>
                            <format>xml</format>
                        </formats>
                        <check>
                            <totalLineRate>45</totalLineRate>
                            <packageLineRate>45</packageLineRate>
                            <haltOnFailure>true</haltOnFailure>
                        </check>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.49</version>
                    <configuration>
                        <templateFile>${project.basedir}/copyright.txt</templateFile>
                        <excludeFile>${project.basedir}/copyright-exclude</excludeFile>
                        <!-- skip files not under SCM-->
                        <scmOnly>true</scmOnly>
                        <!-- turn off warnings -->
                        <warn>true</warn>
                        <!-- for use with repair -->
                        <update>false</update>
                        <!-- check that year is correct -->
                        <ignoreYear>false</ignoreYear>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.1.0</version>
                    <configuration>
                        <archive>
                            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                            <manifestEntries>
                                <Extension-Name>javax.xml.bind</Extension-Name>
                                <Implementation-Build-Id>${scmBranch}-${buildNumber}, ${timestamp}</Implementation-Build-Id>
                                <Multi-Release>true</Multi-Release>
                            </manifestEntries>
                            <manifest>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>1.4</version>
                    <configuration>
                        <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                        <timestampFormat>{0,date,yyyy-MM-dd'T'HH:mm:ssZ}</timestampFormat>
                        <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                        <revisionOnScmFailure>false</revisionOnScmFailure>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <extensions>true</extensions>
                    <version>3.5.1</version>
                    <configuration>
                        <archive>
                            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        </archive>
                        <instructions>
                            <Require-Capability>osgi.ee;filter:="(&amp;(osgi.ee=JavaSE)(version>=1.8))"</Require-Capability>
                            <Bundle-Version>${project.version}</Bundle-Version>  <!-- 2.2.99.bnull -->
                            <Extension-Name>${extension.name}</Extension-Name>
                            <Implementation-Version>${spec.version}.${impl.version}</Implementation-Version>
                            <Specification-Version>${project.version}</Specification-Version>
                            <Export-Package>${extension.name}.*; version=${spec.version}</Export-Package>
                            <Import-Package>
                                javax.activation;version=!,
                                javax.xml.bind;version="[${spec.version},3)",
                                javax.xml.bind.annotation;version="[${spec.version},3)",
                                javax.xml.bind.annotation.adapters;version="[${spec.version},3)",
                                javax.xml.bind.attachment;version="[${spec.version},3)",
                                javax.xml.bind.helpers;version="[${spec.version},3)",
                                javax.xml.bind.util;version="[${spec.version},3)",
                                javax.xml.datatype,
                                javax.xml.namespace,
                                javax.xml.parsers,
                                javax.xml.stream,
                                javax.xml.transform,
                                javax.xml.transform.dom,
                                javax.xml.transform.sax,
                                javax.xml.transform.stream,
                                javax.xml.validation,
                                org.w3c.dom,
                                org.xml.sax,
                                org.xml.sax.ext,
                                org.xml.sax.helpers
                            </Import-Package>
                            <Bundle-SymbolicName>jaxb-api</Bundle-SymbolicName>
                            <DynamicImport-Package>org.glassfish.hk2.osgiresourcelocator</DynamicImport-Package>
                            <Specification-Vendor>Oracle Corporation</Specification-Vendor>
                            <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                            <Implementation-Vendor-Id>org.glassfish</Implementation-Vendor-Id>
                        </instructions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>3.0.5</version>
                    <configuration>
                        <skip>${findbugs.skip}</skip>
                        <threshold>${findbugs.threshold}</threshold>
                        <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                        <excludeFilterFile>
                            ${findbugs.exclude}
                        </excludeFilterFile>
                        <fork>true</fork>
                        <jvmArgs>-Xms64m -Xmx256m</jvmArgs>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.glassfish.findbugs</groupId>
                            <artifactId>findbugs</artifactId>
                            <version>1.0</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <configuration>
                        <nodeprecated>false</nodeprecated>
                        <use>false</use>
                        <author>true</author>
                        <version>true</version>
                        <doctitle><![CDATA[<br>
JAXB ${project.version} Runtime Library</h2>
${project.name} specification, ${release.spec.date}<br>
Comments to: <i><a href='mailto:${release.spec.feedback}'>${release.spec.feedback}</a></i><br>
More information at: <i><a target='_top'
href='http://jaxb.java.net'>http://jaxb.java.net</a></i><br>
&nbsp;<br>&nbsp;<br><hr width='65%'><h1>${project.name}</h1><hr width='75%'>
<br>&nbsp;<br>]]>
                        </doctitle>
                        <header><![CDATA[JAXB<br>v${project.version}]]>
                        </header>
                        <bottom><![CDATA[<font size=-1>
<br>Comments to: <a href='mailto:${release.spec.feedback}'><i>${release.spec.feedback}</i></a>
<br>More information at: <a target='_top'
href='http://jaxb.java.net'><i>http://jaxb.java.net</i></a>
<p>Copyright &copy; 2004-2017 Oracle </font>]]>
                        </bottom>
                        <detectJavaApiLink>false</detectJavaApiLink>
                        <offlineLinks>
                            <offlineLink>
                                <url>http://download.oracle.com/javase/8/docs/api/</url>
                                <location>${basedir}/offline-javadoc</location>
                            </offlineLink>
                        </offlineLinks>
                        <tags>
                            <tag>
                                <name>apiNote</name>
                                <!-- todo tag for all places -->
                                <placement>a</placement>
                                <head>API Note:</head>
                            </tag>
                            <tag>
                                <name>implSpec</name>
                                <!-- todo tag for all places -->
                                <placement>a</placement>
                                <head>Implementation Requirements:</head>
                            </tag>
                            <tag>
                                <name>implNote</name>
                                <!-- todo tag for all places -->
                                <placement>a</placement>
                                <head>Implementation Note:</head>
                            </tag>
                        </tags>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-release-plugin</artifactId>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>default-compile</id>
                            <configuration>
                                <release>8</release>
                                <excludes>
                                    <exclude>module-info.java</exclude>
                                </excludes>
                            </configuration>
                        </execution>
                        <execution>
                            <id>module-info-compile</id>
                            <phase>test-compile</phase><!--Avoid JavaSE9 capability added by bundle-plugin-->
                            <goals>
                                <goal>compile</goal>
                            </goals>
                            <configuration>
                                <release>9</release>
                                <includes>
                                    <include>module-info.java</include>
                                </includes>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/mr-jar/META-INF/versions/9</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${basedir}/src/main/mr-jar</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>initialize</id>
                        <goals>
                            <goal>properties</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>compile-java9</id>
                        <phase>compile</phase>
                        <configuration>
                            <tasks>
                                <mkdir dir="${project.build.outputDirectory}/META-INF/versions/9" />
                                <javac srcdir="${mrjar.sourceDirectory}" destdir="${project.build.outputDirectory}/META-INF/versions/9" classpath="${project.build.outputDirectory}" includeantruntime="false" source="9" target="9" />
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>update-source-jar</id>
                        <phase>verify</phase>
                        <configuration>
                            <tasks>
                                <jar destfile="${project.build.directory}/jaxb-api-${project.version}-sources.jar" update="true">
                                    <fileset dir="${project.build.directory}/mr-jar/" />
                                </jar>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>



</project>
