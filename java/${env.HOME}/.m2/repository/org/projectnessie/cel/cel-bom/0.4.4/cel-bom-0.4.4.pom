<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>rad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.projectnessie.cel</groupId>
  <artifactId>cel-bom</artifactId>
  <version>0.4.4</version>
  <packaging>pom</packaging>
  <name>cel-bom</name>
  <description>cel-bom</description>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.projectnessie.cel</groupId>
        <artifactId>cel-core</artifactId>
        <version>0.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.projectnessie.cel</groupId>
        <artifactId>cel-generated-antlr</artifactId>
        <version>0.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.projectnessie.cel</groupId>
        <artifactId>cel-generated-pb</artifactId>
        <version>0.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.projectnessie.cel</groupId>
        <artifactId>cel-conformance</artifactId>
        <version>0.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.projectnessie.cel</groupId>
        <artifactId>cel-jackson</artifactId>
        <version>0.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.projectnessie.cel</groupId>
        <artifactId>cel-tools</artifactId>
        <version>0.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.projectnessie.cel</groupId>
        <artifactId>cel-standalone</artifactId>
        <version>0.4.4</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <parent>
    <groupId>org.projectnessie.cel</groupId>
    <artifactId>cel-parent</artifactId>
    <version>0.4.4</version>
  </parent>
</project>
