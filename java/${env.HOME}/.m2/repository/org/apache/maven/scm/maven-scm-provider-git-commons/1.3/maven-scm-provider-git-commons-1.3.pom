<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one
  ~ or more contributor license agreements.  See the NOTICE file
  ~ distributed with this work for additional information
  ~ regarding copyright ownership.  The ASF licenses this file
  ~ to you under the Apache License, Version 2.0 (the
  ~ "License"); you may not use this file except in compliance
  ~ with the License.  You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing,
  ~ software distributed under the License is distributed on an
  ~ "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  ~ KIND, either express or implied.  See the License for the
  ~ specific language governing permissions and limitations
  ~ under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.scm</groupId>
    <artifactId>maven-scm-providers-git</artifactId>
    <version>1.3</version>
  </parent>

  <artifactId>maven-scm-provider-git-commons</artifactId>

  <name>Maven SCM Git Provider - Common library</name>
  <description>Common library for SCM Git Provider.</description>

  <dependencies>
    <dependency>
      <groupId>regexp</groupId>
      <artifactId>regexp</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.modello</groupId>
        <artifactId>modello-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>java</goal>
              <goal>xpp3-reader</goal>
              <goal>xsd</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <version>1.0.0</version>
          <models>
            <model>src/main/mdo/git-settings.mdo</model>
          </models>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
