<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one
  ~ or more contributor license agreements.  See the NOTICE file
  ~ distributed with this work for additional information
  ~ regarding copyright ownership.  The ASF licenses this file
  ~ to you under the Apache License, Version 2.0 (the
  ~ "License"); you may not use this file except in compliance
  ~ with the License.  You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing,
  ~ software distributed under the License is distributed on an
  ~ "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  ~ KIND, either express or implied.  See the License for the
  ~ specific language governing permissions and limitations
  ~ under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.scm</groupId>
    <artifactId>maven-scm-providers-git</artifactId>
    <version>1.13.0</version>
  </parent>

  <artifactId>maven-scm-provider-gitexe</artifactId>

  <name>Maven SCM Git Provider - Git Executable Impl.</name>
  <description>Executable implementation for SCM Git Provider.</description>

  <dependencies>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
    </dependency>

    <!-- Test -->
    <dependency>
      <groupId>org.apache.maven.scm</groupId>
      <artifactId>maven-scm-provider-git-commons</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.scm</groupId>
      <artifactId>maven-scm-provider-gittest</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <executions>
          <execution>
            <id>create-component-descriptor</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>        
        </executions>        
      </plugin>
    </plugins>
  </build>
</project>
