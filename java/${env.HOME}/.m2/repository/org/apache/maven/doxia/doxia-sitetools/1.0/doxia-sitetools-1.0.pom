<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven-parent</artifactId>
    <version>11</version>
    <relativePath>../../pom/maven/pom.xml</relativePath>
  </parent>

  <groupId>org.apache.maven.doxia</groupId>
  <artifactId>doxia-sitetools</artifactId>
  <version>1.0</version>
  <packaging>pom</packaging>

  <name>Doxia Sitetools</name>
  <description>Doxia Sitetools generates sites, consisting of static and dynamic content that was generated by Doxia.</description>
  <url>http://maven.apache.org/doxia/doxia-sitetools</url>
  <inceptionYear>2005</inceptionYear>

  <mailingLists>
    <mailingList>
      <name>Doxia Developer List</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-doxia-dev/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://www.nabble.com/Doxia---dev-f11816.html</otherArchive>
        <otherArchive>http://maven.doxia.dev.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Doxia User List</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-doxia-users/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://www.nabble.com/Doxia---Users-f14483.html</otherArchive>
        <otherArchive>http://maven.doxia.users.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Doxia Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-doxia-commits/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://maven.doxia.commits.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Issues List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-issues/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://www.nabble.com/Maven---Issues-f15573.html</otherArchive>
        <otherArchive>http://maven.issues.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>

  <modules>
    <module>doxia-decoration-model</module>
    <module>doxia-doc-renderer</module>
    <module>doxia-site-renderer</module>
  </modules>

  <scm>
    <connection>scm:svn:https://svn.apache.org/repos/asf/maven/doxia/doxia-sitetools/tags/doxia-sitetools-1.0</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/doxia/doxia-sitetools/tags/doxia-sitetools-1.0</developerConnection>
    <url>https://svn.apache.org/viewvc/maven/doxia/doxia-sitetools/tags/doxia-sitetools-1.0</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/DOXIASITETOOLS</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scp://people.apache.org/www/maven.apache.org/doxia/doxia-sitetools-1.0.x</url>
    </site>
  </distributionManagement>

  <properties>
    <projectVersion>${project.version}</projectVersion>
    <doxiaVersion>1.0</doxiaVersion>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-sink-api</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-core</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-apt</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-fml</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-xdoc</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-xhtml</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-decoration-model</artifactId>
        <version>${projectVersion}</version>
      </dependency>
      <!-- Plexus -->
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-container-default</artifactId>
        <version>1.0-alpha-30</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-i18n</artifactId>
        <version>1.0-beta-7</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-component-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>1.5.7</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <tagBase>https://svn.apache.org/repos/asf/maven/doxia/doxia-sitetools/tags</tagBase>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.modello</groupId>
          <artifactId>modello-maven-plugin</artifactId>
          <version>1.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-maven-plugin</artifactId>
          <version>1.3.5</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <source>1.4</source>
              <aggregate>true</aggregate>
              <links>
                <link>http://java.sun.com/j2se/1.4.2/docs/api/</link>
                <link>http://plexus.codehaus.org/plexus-utils/apidocs/</link>
                <link>http://junit.sourceforge.net/javadoc/</link>
              </links>
              <tagletArtifacts>
                <tagletArtifact>
                  <groupId>org.codehaus.plexus</groupId>
                  <artifactId>plexus-javadoc</artifactId>
                  <version>1.0</version>
                </tagletArtifact>
              </tagletArtifacts>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <configuration>
              <aggregate>true</aggregate>
            </configuration>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
</project>
