<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>maven-parent</artifactId>
    <groupId>org.apache.maven</groupId>
    <version>26</version>
    <relativePath>../pom/maven/pom.xml</relativePath>
  </parent>

  <groupId>org.apache.maven.wagon</groupId>
  <artifactId>wagon</artifactId>
  <version>2.10</version>
  <packaging>pom</packaging>

  <name>Apache Maven Wagon</name>
  <description>Tools to manage artifacts and deployment</description>
  <url>http://maven.apache.org/wagon</url>
  <inceptionYear>2003</inceptionYear>

  <properties>
    <maven.test.redirectTestOutputToFile>true</maven.test.redirectTestOutputToFile>
    <slf4jVersion>1.7.7</slf4jVersion>
    <maven.site.path>wagon-archives/wagon-LATEST</maven.site.path>
  </properties>

  <contributors>
    <contributor>
      <name>James William Dumay</name>
    </contributor>
    <contributor>
      <name>Nathan Beyer</name>
    </contributor>
    <contributor>
      <name>Gregory Block</name>
    </contributor>
    <contributor>
      <name>Thomas Recloux</name>
    </contributor>
    <contributor>
      <name>Trustin Lee</name>
    </contributor>
    <contributor>
      <name>John Wells</name>
    </contributor>
    <contributor>
      <name>Marcel Schutte</name>
    </contributor>
    <contributor>
      <name>David Hawkins</name>
    </contributor>
    <contributor>
      <name>Juan F. Codagnone</name>
    </contributor>
    <contributor>
      <name>ysoonleo</name>
    </contributor>
    <contributor>
      <name>Thomas Champagne</name>
    </contributor>
    <contributor>
      <name>M. van der Plas</name>
    </contributor>
    <contributor>
      <name>Jason Dillon</name>
    </contributor>
    <contributor>
      <name>Jochen Wiedmann</name>
    </contributor>
    <contributor>
      <name>Gilles Scokart</name>
    </contributor>
    <contributor>
      <name>Wolfgang Glas</name>
    </contributor>
    <contributor>
      <name>Kohsuke Kawaguchi</name>
    </contributor>
    <contributor>
      <name>Antti Virtanen</name>
    </contributor>
    <contributor>
      <name>Thorsten Heit</name>
    </contributor>
    <contributor>
      <name>Michal Maczka</name>
      <email><EMAIL></email>
      <organization>Codehaus</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </contributor>
    <contributor>
      <name>Adrián Boimvaser</name>
      <organization>Application Security, Inc.</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </contributor>
    <contributor>
      <name>Oleg Kalnichevski</name>
    </contributor>
    <contributor>
      <name>William Bernardet</name>
    </contributor>
    <contributor>
      <name>Michael Neale</name>
    </contributor>
    <contributor>
      <name>Grzegorz Grzybek</name>
    </contributor>
  </contributors>

  <mailingLists>
    <mailingList>
      <name>Maven Developer List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-dev</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://maven.40175.n5.nabble.com/Maven-Developers-f142166.html</otherArchive>
        <otherArchive>http://maven-dev.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-users</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://maven.40175.n5.nabble.com/Maven-Users-f40176.html</otherArchive>
        <otherArchive>http://maven-users.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>

    <mailingList>
      <name>LEGACY Wagon User List (deprecated)</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-wagon-users/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://maven.40175.n5.nabble.com/Wagon-Users-f326332.html</otherArchive>
        <otherArchive>http://maven-wagon-users.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>LEGACY Wagon Developer List (deprecated)</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-wagon-dev/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://maven.40175.n5.nabble.com/Wagon-Dev-f326406.html</otherArchive>
        <otherArchive>http://maven-wagon-dev.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Wagon Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-wagon-commits/</archive>
      <otherArchives>
        <otherArchive>http://maven-wagon-commits.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:https://git-wip-us.apache.org/repos/asf/maven-wagon.git</connection>
    <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/maven-wagon.git</developerConnection>
    <url>https://github.com/apache/maven-wagon/tree/${project.scm.tag}</url>
    <tag>wagon-2.10</tag>
  </scm>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/WAGON</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://builds.apache.org/job/maven-wagon/</url>
  </ciManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <modules>
    <module>wagon-provider-api</module>
    <module>wagon-providers</module>
    <module>wagon-provider-test</module>
    <module>wagon-tcks</module>
  </modules>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-provider-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-provider-test</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh-common-test</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh-common</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-interactivity-api</artifactId>
        <version>1.0-alpha-6</version>
        <exclusions>
          <exclusion>
            <groupId>plexus</groupId>
            <artifactId>plexus-utils</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-container-default</artifactId>
          </exclusion>
          <exclusion>
            <groupId>classworlds</groupId>
            <artifactId>classworlds</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-container-default</artifactId>
        <version>1.5.5</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>3.0.15</version>
      </dependency>

      <!-- for slf4j -->
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4jVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>${slf4jVersion}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>${slf4jVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jcl-over-slf4j</artifactId>
        <version>${slf4jVersion}</version>
      </dependency>

      <dependency>
        <groupId>commons-lang</groupId>
        <artifactId>commons-lang</artifactId>
        <version>2.6</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>org.easymock</groupId>
        <artifactId>easymock</artifactId>
        <version>3.2</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.mortbay.jetty</groupId>
        <artifactId>jetty</artifactId>
        <version>6.1.26</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <forkedProcessTimeoutInSeconds>400</forkedProcessTimeoutInSeconds>
            <systemPropertyVariables>
              <java.io.tmpdir>${project.build.directory}</java.io.tmpdir>
            </systemPropertyVariables>
          </configuration>
        </plugin>
        <plugin><!-- TODO remove when upgrading maven-parent to 25 -->
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <configuration>
            <topSiteURL>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/components/${maven.site.path}</topSiteURL>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <configuration>
            <excludes combine.children="append">
              <exclude>**/*.odg</exclude>
              <exclude>src/test/resources/**</exclude>
              <exclude>src/main/resources/default-server-root/**</exclude>
              <exclude>src/main/resources/ssh-keys/**</exclude>
              <exclude>src/test/ssh-keys/**</exclude>
              <exclude>.repository/**</exclude><!-- for CI -->
            </excludes>
          </configuration>
        </plugin>
        <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.rat</groupId>
                    <artifactId>apache-rat-plugin</artifactId>
                    <versionRange>[0.11,)</versionRange>
                    <goals>
                      <goal>check</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <executions>
          <execution>
            <id>generate</id>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.9</version>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java15</artifactId>
            <version>1.0</version>
          </signature>
        </configuration>
        <executions>
          <execution>
            <id>check-java-1.5-compat</id>
            <phase>process-classes</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <version>1.6.0</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>

    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <linksource>true</linksource>
              <links>
                <link>http://java.sun.com/j2ee/1.4/docs/api</link>
                <link>http://commons.apache.org/collections/apidocs-COLLECTIONS_3_0/</link>
                <link>http://commons.apache.org/logging/apidocs/</link>
                <link>http://commons.apache.org/pool/apidocs/</link>
                <link>http://junit.sourceforge.net/javadoc/</link>
                <link>http://logging.apache.org/log4j/1.2/apidocs/</link>
                <link>http://jakarta.apache.org/regexp/apidocs/</link>
                <link>http://velocity.apache.org/engine/releases/velocity-1.5/apidocs/</link>
                <link>http://maven.apache.org/ref/current/maven-artifact/apidocs/</link>
                <link>http://maven.apache.org/ref/current/maven-artifact-manager/apidocs/</link>
                <link>http://maven.apache.org/ref/current/maven-model/apidocs/</link>
                <link>http://maven.apache.org/ref/current/maven-plugin-api/apidocs/</link>
                <link>http://maven.apache.org/ref/current/maven-project/apidocs/</link>
                <link>http://maven.apache.org/ref/current/maven-reporting/maven-reporting-api/apidocs/</link>
                <link>http://maven.apache.org/ref/current/maven-settings/apidocs/</link>
              </links>
              <groups>
                <group>
                  <title>API + Test</title>
                  <packages>org.apache.maven.wagon*</packages>
                </group>
                <group>
                  <title>File Provider</title>
                  <packages>org.apache.maven.wagon.providers.file*</packages>
                </group>
                <group>
                  <title>FTP Provider</title>
                  <packages>org.apache.maven.wagon.providers.ftp*</packages>
                </group>
                <group>
                  <title>HTTP Providers</title>
                  <packages>org.apache.maven.wagon.providers.http*:org.apache.maven.wagon.shared.http*</packages>
                </group>
                <group>
                  <title>SCM Provider</title>
                  <packages>org.apache.maven.wagon.providers.scm*</packages>
                </group>
                <group>
                  <title>SSH Providers</title>
                  <packages>org.apache.maven.wagon.providers.ssh*</packages>
                </group>
                <group>
                  <title>Webdav Provider</title>
                  <packages>org.apache.maven.wagon.providers.webdav*:org.apache.jackrabbit.webdav*</packages>
                </group>
                <group>
                  <title>HTTP TCK</title>
                  <packages>org.apache.maven.wagon.tck.http*</packages>
                </group>
              </groups>
            </configuration>
            <reportSets>
              <reportSet>
                <id>non-aggregate</id>
                <reports>
                  <report>javadoc</report>
                  <report>test-javadoc</report>
                </reports>
              </reportSet>
              <reportSet>
                <id>aggregate</id>
                <inherited>false</inherited>
                <reports>
                  <report>aggregate</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <reportSets>
              <reportSet>
                <id>non-aggregate</id>
                <reports>
                  <report>jxr</report>
                  <report>test-jxr</report>
                </reports>
              </reportSet>
              <reportSet>
                <id>aggregate</id>
                <inherited>false</inherited>
                <reports>
                  <report>aggregate</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <reportSets>
              <reportSet>
                <id>non-aggregate</id>
                <reports>
                  <report>checkstyle</report>
                </reports>
              </reportSet>
              <reportSet>
                <id>aggregate</id>
                <inherited>false</inherited>
                <reports>
                  <report>checkstyle-aggregate</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
  
</project>
