<?xml version='1.0' encoding='UTF-8'?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements. See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership. The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License. You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied. See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.shared</groupId>
    <artifactId>maven-shared-components</artifactId>
    <version>36</version>
    <relativePath />
  </parent>

  <artifactId>file-management</artifactId>
  <version>3.1.0</version>

  <name>Apache Maven File Management API</name>
  <description>API to collect files from a given directory using several include/exclude rules.</description>

  <contributors>
    <contributor>
      <name>Joakim Erdfelt</name>
      <email><EMAIL></email>
    </contributor>
  </contributors>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-file-management.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-file-management.git</developerConnection>
    <url>https://github.com/apache/maven-file-management/tree/${project.scm.tag}</url>
    <tag>file-management-3.1.0</tag>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/MSHARED/</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>
  <properties>
    <javaVersion>8</javaVersion>
    <slf4jVersion>1.7.36</slf4jVersion>
    <project.build.outputTimestamp>2022-06-29T11:19:57Z</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4jVersion}</version>
    </dependency>

    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.4.2</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version>
    </dependency>

    <!-- Test -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>${slf4jVersion}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.modello</groupId>
        <artifactId>modello-maven-plugin</artifactId>
        <configuration>
          <version>1.1.0</version>
          <models>
            <model>src/main/mdo/fileset.mdo</model>
          </models>
        </configuration>
        <executions>
          <execution>
            <id>fileset</id>
            <goals>
              <goal>xpp3-reader</goal>
              <goal>xpp3-writer</goal>
              <goal>java</goal>
            </goals>
          </execution>
          <execution>
            <id>site-docs</id>
            <phase>pre-site</phase>
            <goals>
              <goal>xdoc</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes combine.children="append">
            <exclude>src/test/resources/test*/**/*.txt</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
