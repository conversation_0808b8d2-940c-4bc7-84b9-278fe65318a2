<?xml version="1.0"?>
<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->

<project>
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.apache.xmlbeans</groupId>
    <artifactId>xmlbeans</artifactId>
    <version>5.3.0</version>

    <name>XmlBeans</name>
    <description>XmlBeans main jar</description>
    <url>https://xmlbeans.apache.org/</url>

    <issueManagement>
        <system>jira</system>
        <url>https://issues.apache.org/jira/browse/XMLBEANS</url>
    </issueManagement>

    <mailingLists>
        <mailingList>
            <name>POI Users List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <archive>https://lists.apache.org/list.html?<EMAIL></archive>
        </mailingList>
        <mailingList>
            <name>POI Developer List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <archive>https://lists.apache.org/list.html?<EMAIL></archive>
        </mailingList>
    </mailingLists>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:svn:https://svn.apache.org/repos/asf/xmlbeans/</connection>
        <developerConnection>scm:svn:https://${maven.username}@svn.apache.org/repos/asf/xmlbeans/</developerConnection>
        <url>https://svn.apache.org/repos/asf/xmlbeans/</url>
    </scm>

    <organization>
        <name>XmlBeans</name>
        <url>https://xmlbeans.apache.org/</url>
    </organization>

    <developers>
        <developer>
            <name>POI Team</name>
            <id>poi</id>
            <email><EMAIL></email>
            <organization>Apache POI</organization>
        </developer>
    </developers>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>2.24.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.saxon</groupId>
            <artifactId>Saxon-HE</artifactId>
            <version>12.5</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.javaparser</groupId>
            <artifactId>javaparser-core</artifactId>
            <version>3.26.2</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.javaparser</groupId>
            <artifactId>javaparser-symbol-solver-core</artifactId>
            <version>3.26.2</version>
            <scope>provided</scope>
        </dependency>

        <!-- dependencies for xmlbeans maven plugin -->
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-plugin-api</artifactId>
            <version>3.9.9</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-model</artifactId>
            <version>3.9.9</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven.plugin-tools</groupId>
            <artifactId>maven-plugin-annotations</artifactId>
            <version>3.15.1</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
