<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-parent</artifactId>
        <version>3.19.18</version>
    </parent>

    <artifactId>jooq</artifactId>
    <name>jOOQ</name>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.jooq.org/inc/LICENSE.txt</url>
            <distribution>repo</distribution>
        </license>







    </licenses>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <extensions>true</extensions>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <supportedProjectTypes>
                        <supportedProjectType>jar</supportedProjectType>
                    </supportedProjectTypes>
                    <instructions>
                        <Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
                        <Export-Package>*</Export-Package>
                        
                        <!-- [#12408] Some of these are resolved by introspecting reflective usage -->
                        <Import-Package>
                            jakarta.persistence;resolution:=optional,
                            org.slf4j;resolution:=optional;version="[1.7,2)",
                            com.fasterxml.jackson.databind;resolution:=optional,
                            com.fasterxml.jackson.module.kotlin;resolution:=optional,
                            com.google.gson;resolution:=optional,
                            *
                        </Import-Package>
                        <_versionpolicy>[$(version;==;$(@)),$(version;+;$(@)))</_versionpolicy>
                    </instructions>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifestEntries>
                            <Automatic-Module-Name>org.jooq</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
        
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <configuration>
    
                        <!-- [#13703] Validate at least HTML correctness -->
                        <additionalparam>-Xdoclint:html</additionalparam>
                        <additionalOptions>-Xdoclint:html</additionalOptions>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <dependencies>
        <dependency>
            <groupId>io.r2dbc</groupId>
            <artifactId>r2dbc-spi</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.persistence</groupId>
            <artifactId>jakarta.persistence-api</artifactId>
        </dependency>

        <!-- From JDK 9 onwards, the JAXB dependency needs to be made explicit
        
             [#7649] The dependency can cause trouble in older JDKs, so it needs to be
             excluded from pre-java-9 builds: https://github.com/jOOQ/jOOQ/issues/7649
             [#13047] The dependency is now optional. Users can opt in to using this
             dependency if they need, by making it explicit on their end. 
          -->
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <optional>true</optional>
        </dependency>
        














    </dependencies>
</project>
