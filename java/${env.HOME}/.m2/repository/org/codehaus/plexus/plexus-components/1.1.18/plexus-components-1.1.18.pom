<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>plexus</artifactId>
    <groupId>org.codehaus.plexus</groupId>
    <version>2.0.7</version>
    <relativePath>../pom/pom.xml</relativePath>
  </parent>

  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus-components</artifactId>
  <version>1.1.18</version>
  <packaging>pom</packaging>

  <name>Plexus Components</name>
  <url>http://plexus.codehaus.org/plexus-components</url>


  <modules>
    <module>plexus-archiver</module>
    <module>plexus-cli</module>
    <module>plexus-compiler</module>
    <module>plexus-digest</module>
    <module>plexus-i18n</module>
    <module>plexus-interactivity</module>
    <module>plexus-interpolation</module>
    <module>plexus-io</module>
    <module>plexus-resources</module>
    <!--
    <module>plexus-swizzle</module>
    -->
    <module>plexus-velocity</module>
  </modules>

  <scm>
    <connection>scm:svn:http://svn.codehaus.org/plexus/plexus-components/tags/plexus-components-1.1.18</connection>
    <developerConnection>scm:svn:https://svn.codehaus.org/plexus/plexus-components/tags/plexus-components-1.1.18</developerConnection>
    <url>http://fisheye.codehaus.org/browse/plexus/plexus-components/tags/plexus-components-1.1.18</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>http://jira.codehaus.org/browse/PLXCOMP</url>
  </issueManagement>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-container-default</artifactId>
        <version>1.0-alpha-9-stable-1</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>1.5.5</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>3.8.2</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.4</source>
          <target>1.4</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <version>1.5.4</version>
        <executions>
          <execution>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  
  <profiles>
    <profile>
      <id>parent-release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
              <arguments>-N -Pplexus-release</arguments>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>maven-3</id>
      <activation>
        <file>
          <!-- This employs that the basedir expression is only recognized by Maven 3.x (see MNG-2363) -->
          <exists>${basedir}</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-site-plugin</artifactId>
            <version>2.1</version>
            <inherited>false</inherited>
            <executions>
              <execution>
                <id>attach-descriptor</id>
                <goals>
                  <goal>attach-descriptor</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>  
    <profile>
      <id>m2e</id>
      <activation>
        <property>
          <name>m2e.version</name>
        </property>
      </activation>
      <properties>
        <m2BuildDirectory>target</m2BuildDirectory>
      </properties>
      <build>
        <directory>${m2BuildDirectory}</directory>
        <plugins>
          <plugin>
            <groupId>org.maven.ide.eclipse</groupId>
            <artifactId>lifecycle-mapping</artifactId>
            <version>0.10.0</version>
            <configuration>
              <mappingId>customizable</mappingId>
              <configurators>
                <configurator id="org.maven.ide.eclipse.jdt.javaConfigurator" />
                <configurator id="org.maven.ide.eclipse.plexus.annotations.plexusConfigurator" />
                <configurator id="org.maven.ide.eclipse.modello.modelloConfigurator" />
              </configurators>
              <mojoExecutions>
                <mojoExecution>org.apache.maven.plugins:maven-resources-plugin::</mojoExecution>
              </mojoExecutions>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>  
  </profiles>
  
</project>
