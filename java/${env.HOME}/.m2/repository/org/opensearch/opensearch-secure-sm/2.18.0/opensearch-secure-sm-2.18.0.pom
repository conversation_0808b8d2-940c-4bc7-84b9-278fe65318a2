<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Grad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.opensearch</groupId>
  <artifactId>opensearch-secure-sm</artifactId>
  <version>2.18.0</version>
  <name>opensearch-secure-sm</name>
  <description>OpenSearch subproject :libs:opensearch-secure-sm</description>
  <inceptionYear>2021</inceptionYear>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>OpenSearch</name>
      <url>https://github.com/opensearch-project/OpenSearch</url>
    </developer>
  </developers>
  <url>https://github.com/opensearch-project/OpenSearch.git</url>
  <scm>
    <url>https://github.com/opensearch-project/OpenSearch.git</url>
  </scm>
</project>
