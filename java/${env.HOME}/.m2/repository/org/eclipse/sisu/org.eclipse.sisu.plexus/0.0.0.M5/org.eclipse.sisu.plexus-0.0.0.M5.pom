<?xml version="1.0" encoding="UTF-8"?>

<!--
 ~ Copyright (c) 2010, 2013 Sonatype, Inc.
 ~ All rights reserved. This program and the accompanying materials
 ~ are made available under the terms of the Eclipse Public License v1.0
 ~ which accompanies this distribution, and is available at
 ~ http://www.eclipse.org/legal/epl-v10.html
 ~
 ~ Contributors:
 ~    <PERSON> (Sonatype, Inc.) - initial API and implementation
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.eclipse.sisu</groupId>
    <artifactId>sisu-plexus</artifactId>
    <version>0.0.0.M5</version>
  </parent>

  <artifactId>org.eclipse.sisu.plexus</artifactId>
  <packaging>eclipse-plugin</packaging>

  <!--
   | Minimum requirements
  -->
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.6.4</version>
      <optional>true</optional>
    </dependency>
    <!--
     | CDI's @Typed helps Plexus->JSR330 migration
    -->
    <dependency>
      <groupId>javax.enterprise</groupId>
      <artifactId>cdi-api</artifactId>
      <version>1.0</version>
      <exclusions>
        <exclusion>
          <groupId>javax.el</groupId>
          <artifactId>el-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jboss.ejb3</groupId>
          <artifactId>jboss-ejb3-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jboss.interceptor</groupId>
          <artifactId>jboss-interceptor-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--
     | Swap in sisu-guice for ProvisionListener API
    -->
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>10.0.1</version>
    </dependency>
    <dependency>
      <groupId>org.sonatype.sisu</groupId>
      <artifactId>sisu-guice</artifactId>
      <version>3.1.0</version>
      <classifier>no_aop</classifier>
      <exclusions>
        <exclusion>
          <groupId>org.sonatype.sisu</groupId>
          <artifactId>sisu-guava</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.sisu</groupId>
      <artifactId>org.eclipse.sisu.inject</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.google.inject</groupId>
          <artifactId>guice</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--
     | Plexus classloading/utilities
    -->
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
      <version>1.5.5</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-classworlds</artifactId>
      <version>2.4</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>2.1</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.10</version>
      <optional>true</optional>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>src</sourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>1.8</version>
        <executions>
          <execution>
            <id>attach-build-target</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>attach-artifact</goal>
            </goals>
            <configuration>
              <artifacts>
                <artifact>
                  <file>build.target</file>
                  <classifier>build</classifier>
                  <type>target</type>
                </artifact>
              </artifacts>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.eclipse.tycho</groupId>
        <artifactId>target-platform-configuration</artifactId>
      </plugin>
      <plugin>
        <groupId>org.eclipse.tycho</groupId>
        <artifactId>tycho-maven-plugin</artifactId>
        <extensions>true</extensions>
      </plugin>
      <plugin>
        <groupId>org.eclipse.tycho</groupId>
        <artifactId>tycho-source-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>
