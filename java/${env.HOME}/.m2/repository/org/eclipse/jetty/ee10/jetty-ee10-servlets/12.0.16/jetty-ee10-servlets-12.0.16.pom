<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.eclipse.jetty.ee10</groupId>
    <artifactId>jetty-ee10</artifactId>
    <version>12.0.16</version>
  </parent>
  <artifactId>jetty-ee10-servlets</artifactId>
  <name>EE10 :: Utility Servlets and Filters</name>
  <description>Utility Servlets from Jetty</description>

  <properties>
    <bundle-symbolic-name>${project.groupId}.servlets</bundle-symbolic-name>
    <spotbugs.onlyAnalyze>org.eclipse.jetty.ee10.servlets.*</spotbugs.onlyAnalyze>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-http</artifactId>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-io</artifactId>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-util</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty.ee10</groupId>
      <artifactId>jetty-ee10-webapp</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-http-tools</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-jmx</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-slf4j-impl</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty.toolchain</groupId>
      <artifactId>jetty-test-helper</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <argLine>@{argLine} ${jetty.surefire.argLine}
            --add-modules jakarta.servlet 
            --add-modules org.eclipse.jetty.util
            --add-modules org.eclipse.jetty.io
            --add-modules org.eclipse.jetty.http
            --add-modules org.eclipse.jetty.server
            --add-modules org.eclipse.jetty.jmx</argLine>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
