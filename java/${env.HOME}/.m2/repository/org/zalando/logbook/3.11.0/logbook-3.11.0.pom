<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.zalando</groupId>
    <artifactId>logbook</artifactId>
    <version>3.11.0</version>
    <packaging>pom</packaging>
    <name>Logbook</name>
    <description>HTTP request and response logging</description>
    <url>https://github.com/zalando/logbook</url>
    <inceptionYear>2015</inceptionYear>
    <organization>
        <name>Zalando SE</name>
    </organization>
    <licenses>
        <license>
            <name>MIT License</name>
            <url>https://opensource.org/licenses/MIT</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <developers>
        <developer>
            <name><PERSON><PERSON></name>
            <email><EMAIL></email>
            <organization>Zalando SE</organization>
            <organizationUrl>https://tech.zalando.com/</organizationUrl>
        </developer>
        <developer>
            <name>Willi Schönborn</name>
            <email><EMAIL></email>
            <organization>Zalando SE</organization>
            <organizationUrl>https://tech.zalando.com/</organizationUrl>
        </developer>
    </developers>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <kotlin.compiler.incremental>true</kotlin.compiler.incremental>
    </properties>
    <modules>
        <module>logbook-parent</module>
        <module>logbook-api</module>
        <module>logbook-bom</module>
        <module>logbook-common</module>
        <module>logbook-core</module>
        <module>logbook-httpclient</module>
        <module>logbook-httpclient5</module>
        <module>logbook-jaxrs</module>
        <module>logbook-jdkserver</module>
        <module>logbook-json</module>
        <module>logbook-logstash</module>
        <module>logbook-netty</module>
        <module>logbook-okhttp</module>
        <module>logbook-okhttp2</module>
        <module>logbook-openfeign</module>
        <module>logbook-servlet</module>
        <module>logbook-spring</module>
        <module>logbook-spring-webflux</module>
        <module>logbook-ktor</module>
        <module>logbook-ktor-common</module>
        <module>logbook-ktor-client</module>
        <module>logbook-ktor-server</module>
        <module>logbook-spring-boot-autoconfigure</module>
        <module>logbook-spring-boot-webflux-autoconfigure</module>
        <module>logbook-spring-boot-starter</module>
        <module>logbook-test</module>
    </modules>
    <scm>
        <url>https://github.com/zalando/logbook</url>
        <connection>scm:git:**************:zalando/logbook.git</connection>
        <developerConnection>scm:git:**************:zalando/logbook.git</developerConnection>
    </scm>
    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.2.7</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>1.7.0</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                            <stagingProgressTimeoutMinutes>10</stagingProgressTimeoutMinutes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>coverage</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.eluder.coveralls</groupId>
                        <artifactId>coveralls-maven-plugin</artifactId>
                        <version>4.3.0</version>
                        <dependencies>
                            <dependency>
                                <groupId>javax.xml.bind</groupId>
                                <artifactId>jaxb-api</artifactId>
                                <version>2.3.1</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
