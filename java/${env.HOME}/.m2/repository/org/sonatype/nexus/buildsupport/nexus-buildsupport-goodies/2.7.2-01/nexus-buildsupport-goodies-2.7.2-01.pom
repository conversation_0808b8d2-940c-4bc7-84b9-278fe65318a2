<!--

    Sonatype Nexus (TM) Open Source Version
    Copyright (c) 2007-2013 Sonatype, Inc.
    All rights reserved. Includes the third-party code listed at http://links.sonatype.com/products/nexus/oss/attributions.

    This program and the accompanying materials are made available under the terms of the Eclipse Public License Version 1.0,
    which accompanies this distribution and is available at http://www.eclipse.org/legal/epl-v10.html.

    Sonatype Nexus (TM) Professional Version is available from Sonatype, Inc. "Sonatype" and "Sonatype Nexus" are trademarks
    of Sonatype, Inc. Apache Maven is a trademark of the Apache Software Foundation. M2eclipse is a trademark of the
    Eclipse Foundation. All other trademarks are the property of their respective owners.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.nexus.buildsupport</groupId>
    <artifactId>nexus-buildsupport</artifactId>
    <version>2.7.2-01</version>
  </parent>

  <artifactId>nexus-buildsupport-goodies</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>
  <packaging>pom</packaging>

  <properties>
    <goodies.version>1.7.4</goodies.version>
  </properties>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-common</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-i18n</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-marshal</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-eventbus</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-lifecycle</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-thread</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-crypto</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-ssl</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-prefs</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-template</artifactId>
        <version>${goodies.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.goodies</groupId>
        <artifactId>goodies-inject</artifactId>
        <version>${goodies.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

</project>
