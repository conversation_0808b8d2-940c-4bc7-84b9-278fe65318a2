<!--

    Sonatype Nexus (TM) Open Source Version
    Copyright (c) 2007-2013 Sonatype, Inc.
    All rights reserved. Includes the third-party code listed at http://links.sonatype.com/products/nexus/oss/attributions.

    This program and the accompanying materials are made available under the terms of the Eclipse Public License Version 1.0,
    which accompanies this distribution and is available at http://www.eclipse.org/legal/epl-v10.html.

    Sonatype Nexus (TM) Professional Version is available from Sonatype, Inc. "Sonatype" and "Sonatype Nexus" are trademarks
    of Sonatype, Inc. Apache Maven is a trademark of the Apache Software Foundation. M2eclipse is a trademark of the
    Eclipse Foundation. All other trademarks are the property of their respective owners.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.nexus.buildsupport</groupId>
    <artifactId>nexus-buildsupport</artifactId>
    <version>2.7.2-01</version>
  </parent>

  <artifactId>nexus-buildsupport-rest</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>
  <packaging>pom</packaging>

  <properties>
    <jackson.version>1.9.12</jackson.version>
    <siesta.version>1.5.2</siesta.version>
  </properties>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>1.7.2</version>
      </dependency>

      <dependency>
        <groupId>org.json</groupId>
        <artifactId>org.json</artifactId>
        <version>2.0-NEXUS-3758</version>
      </dependency>

      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-core-asl</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-mapper-asl</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.thoughtworks.xstream</groupId>
        <artifactId>xstream</artifactId>
        <version>1.4.5</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.nexus.xstream</groupId>
        <artifactId>xstream</artifactId>
        <version>1.4.6-SONATYPE-02</version>
      </dependency>

      <dependency>
        <groupId>xpp3</groupId>
        <artifactId>xpp3_min</artifactId>
        <version>1.1.4c</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.siesta</groupId>
        <artifactId>siesta-common</artifactId>
        <version>${siesta.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.siesta</groupId>
        <artifactId>siesta-server</artifactId>
        <version>${siesta.version}</version>
        <exclusions>
          <exclusion>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.siesta</groupId>
        <artifactId>siesta-client</artifactId>
        <version>${siesta.version}</version>
        <exclusions>
          <exclusion>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.siesta</groupId>
        <artifactId>siesta-jackson</artifactId>
        <version>${siesta.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.jacksbee</groupId>
        <artifactId>jacksbee-runtime</artifactId>
        <version>1.2</version>
        <exclusions>
          <exclusion>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.jacksbee</groupId>
        <artifactId>jacksbee-xjc</artifactId>
        <version>1.2</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

</project>
