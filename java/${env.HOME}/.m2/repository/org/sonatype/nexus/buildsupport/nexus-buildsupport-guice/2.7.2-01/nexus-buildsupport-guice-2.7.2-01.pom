<!--

    Sonatype Nexus (TM) Open Source Version
    Copyright (c) 2007-2013 Sonatype, Inc.
    All rights reserved. Includes the third-party code listed at http://links.sonatype.com/products/nexus/oss/attributions.

    This program and the accompanying materials are made available under the terms of the Eclipse Public License Version 1.0,
    which accompanies this distribution and is available at http://www.eclipse.org/legal/epl-v10.html.

    Sonatype Nexus (TM) Professional Version is available from Sonatype, Inc. "Sonatype" and "Sonatype Nexus" are trademarks
    of Sonatype, Inc. Apache Maven is a trademark of the Apache Software Foundation. M2eclipse is a trademark of the
    Eclipse Foundation. All other trademarks are the property of their respective owners.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.nexus.buildsupport</groupId>
    <artifactId>nexus-buildsupport</artifactId>
    <version>2.7.2-01</version>
  </parent>

  <artifactId>nexus-buildsupport-guice</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>
  <packaging>pom</packaging>

  <properties>
    <sisu-inject.version>2.3.4</sisu-inject.version>
    <sisu-guice.version>3.1.4</sisu-guice.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>javax.inject</groupId>
        <artifactId>javax.inject</artifactId>
        <version>1</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-guice</artifactId>
        <version>${sisu-guice.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-servlet</artifactId>
        <version>${sisu-guice.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-multibindings</artifactId>
        <version>${sisu-guice.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-assistedinject</artifactId>
        <version>${sisu-guice.version}</version>
      </dependency>

      <dependency>
        <groupId>aopalliance</groupId>
        <artifactId>aopalliance</artifactId>
        <version>1.0</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-inject-bean</artifactId>
        <version>${sisu-inject.version}</version>
        <exclusions>
          <!--
          FIXME: This causes problems for module which only depend on this and not sisu-guice as well.
          sisu-inject-bean depends on no_aop guice, strip it out so we don't get aop and no_aop variants.
          -->
          <exclusion>
            <groupId>org.sonatype.sisu</groupId>
            <artifactId>sisu-guice</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-inject-plexus</artifactId>
        <version>${sisu-inject.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

</project>
