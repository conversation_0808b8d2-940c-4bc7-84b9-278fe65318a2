<!--

    Sonatype Nexus (TM) Open Source Version
    Copyright (c) 2007-2014 Sonatype, Inc.
    All rights reserved. Includes the third-party code listed at http://links.sonatype.com/products/nexus/oss/attributions.

    This program and the accompanying materials are made available under the terms of the Eclipse Public License Version 1.0,
    which accompanies this distribution and is available at http://www.eclipse.org/legal/epl-v10.html.

    Sonatype Nexus (TM) Professional Version is available from Sonatype, Inc. "Sonatype" and "Sonatype Nexus" are trademarks
    of Sonatype, Inc. Apache Maven is a trademark of the Apache Software Foundation. M2eclipse is a trademark of the
    Eclipse Foundation. All other trademarks are the property of their respective owners.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.nexus.buildsupport</groupId>
    <artifactId>nexus-buildsupport</artifactId>
    <version>2.9.1-02</version>
  </parent>

  <artifactId>nexus-buildsupport-testing</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>
  <packaging>pom</packaging>

  <properties>
    <selenium.version>2.26.0</selenium.version>
  </properties>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-bundle-launcher</artifactId>
        <version>1.8</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.plexus</groupId>
        <artifactId>plexus-jetty-testsuite</artifactId>
        <version>2.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-container-default</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.sonatype.http-testing-harness</groupId>
        <artifactId>server-provider</artifactId>
        <version>0.8</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.http-testing-harness</groupId>
        <artifactId>junit-runner</artifactId>
        <version>0.8</version>
      </dependency>

      <dependency>
        <groupId>httpunit</groupId>
        <artifactId>httpunit</artifactId>
        <version>1.6.2</version>
      </dependency>

      <dependency>
        <groupId>org.databene</groupId>
        <artifactId>contiperf</artifactId>
        <version>2.2.0</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu.litmus</groupId>
        <artifactId>litmus-testsupport</artifactId>
        <version>1.9</version>
        <exclusions>
          <exclusion>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.icegreen</groupId>
        <artifactId>greenmail</artifactId>
        <version>1.3</version>
        <exclusions>
          <exclusion>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-firefox-driver</artifactId>
        <version>${selenium.version}</version>
      </dependency>

      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-chrome-driver</artifactId>
        <version>${selenium.version}</version>
      </dependency>

      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-htmlunit-driver</artifactId>
        <version>${selenium.version}</version>
      </dependency>

      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-support</artifactId>
        <version>${selenium.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>com.saucelabs</groupId>
        <artifactId>sauce-connect</artifactId>
        <version>3.0.18</version>
      </dependency>

      <dependency>
        <groupId>com.saucelabs</groupId>
        <artifactId>saucerest</artifactId>
        <version>1.0.2</version>
      </dependency>

      <dependency>
        <groupId>xmlunit</groupId>
        <artifactId>xmlunit</artifactId>
        <version>1.3</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

</project>
