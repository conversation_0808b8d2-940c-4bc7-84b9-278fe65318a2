<!--

    Sonatype Nexus (TM) Open Source Version
    Copyright (c) 2007-2013 Sonatype, Inc.
    All rights reserved. Includes the third-party code listed at http://links.sonatype.com/products/nexus/oss/attributions.

    This program and the accompanying materials are made available under the terms of the Eclipse Public License Version 1.0,
    which accompanies this distribution and is available at http://www.eclipse.org/legal/epl-v10.html.

    Sonatype Nexus (TM) Professional Version is available from Sonatype, Inc. "Sonatype" and "Sonatype Nexus" are trademarks
    of Sonatype, Inc. Apache Maven is a trademark of the Apache Software Foundation. M2eclipse is a trademark of the
    Eclipse Foundation. All other trademarks are the property of their respective owners.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.nexus.buildsupport</groupId>
    <artifactId>nexus-buildsupport</artifactId>
    <version>2.7.2-01</version>
  </parent>

  <artifactId>nexus-buildsupport-plexus</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>
  <packaging>pom</packaging>

  <dependencyManagement>
    <dependencies>
      <!--
      FIXME: Deprecated, to be removed in future release.
      -->
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>1.5.5</version>
      </dependency>

      <!--
      FIXME: This is used by a few tests in nexus-core (org.codehaus.plexus.digest.{Md5|Sha1}Digester).
      -->
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-digest</artifactId>
        <version>1.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-container-default</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-component-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-interpolation</artifactId>
        <version>1.16</version>
      </dependency>

      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-classworlds</artifactId>
        <version>2.4</version>
      </dependency>

      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>3.0.10</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.plexus</groupId>
        <artifactId>plexus-cipher</artifactId>
        <version>1.7</version>
      </dependency>

      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-archiver</artifactId>
        <version>2.4.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-container-default</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-component-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.sonatype.plexus</groupId>
        <artifactId>plexus-ldap-testsuite</artifactId>
        <version>1.5</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-container-default</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>
  </dependencyManagement>

</project>
