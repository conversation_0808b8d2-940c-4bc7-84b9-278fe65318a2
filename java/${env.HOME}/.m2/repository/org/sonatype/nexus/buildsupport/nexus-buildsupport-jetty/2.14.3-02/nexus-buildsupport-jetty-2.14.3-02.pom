<!--

    Sonatype Nexus (TM) Open Source Version
    Copyright (c) 2008-present Sonatype, Inc.
    All rights reserved. Includes the third-party code listed at http://links.sonatype.com/products/nexus/oss/attributions.

    This program and the accompanying materials are made available under the terms of the Eclipse Public License Version 1.0,
    which accompanies this distribution and is available at http://www.eclipse.org/legal/epl-v10.html.

    Sonatype Nexus (TM) Professional Version is available from Sonatype, Inc. "Sonatype" and "Sonatype Nexus" are trademarks
    of Sonatype, Inc. Apache Maven is a trademark of the Apache Software Foundation. M2eclipse is a trademark of the
    Eclipse Foundation. All other trademarks are the property of their respective owners.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.nexus.buildsupport</groupId>
    <artifactId>nexus-buildsupport</artifactId>
    <version>2.14.3-02</version>
  </parent>

  <artifactId>nexus-buildsupport-jetty</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>
  <packaging>pom</packaging>

  <properties>
    <eclipse-jetty.version>8.1.16.v20140903</eclipse-jetty.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.eclipse.jetty.orbit</groupId>
        <artifactId>javax.servlet</artifactId>
        <version>3.0.0.v201112011016</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-webapp</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-continuation</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-security</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-deploy</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-client</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlets</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-io</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-xml</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jmx</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-rewrite</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-ajp</artifactId>
        <version>${eclipse-jetty.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

</project>
