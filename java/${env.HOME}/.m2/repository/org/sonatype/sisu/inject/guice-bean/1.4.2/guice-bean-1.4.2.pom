<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.sisu</groupId>
    <artifactId>sisu-inject</artifactId>
    <version>1.4.2</version>
  </parent>

  <packaging>pom</packaging>

  <groupId>org.sonatype.sisu.inject</groupId>
  <artifactId>guice-bean</artifactId>

  <name>Guice - Bean</name>

  <modules>
    <module>guice-bean-reflect</module>
    <module>guice-bean-inject</module>
    <module>guice-bean-scanners</module>
    <module>guice-bean-converters</module>
    <module>guice-bean-locators</module>
    <module>guice-bean-binders</module>
    <module>guice-bean-containers</module>
    <module>sisu-inject-bean</module>
  </modules>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-bean-reflect</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-bean-inject</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-bean-scanners</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-bean-converters</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-bean-locators</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-bean-binders</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu.inject</groupId>
        <artifactId>guice-bean-containers</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-inject-bean</artifactId>
        <version>${project.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

</project>
