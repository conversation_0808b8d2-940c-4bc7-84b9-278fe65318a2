<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.sisu.inject</groupId>
    <artifactId>guice-plexus</artifactId>
    <version>1.4.2</version>
  </parent>

  <packaging>bundle</packaging>

  <groupId>org.sonatype.sisu</groupId>
  <artifactId>sisu-inject-plexus</artifactId>

  <name>Sisu - Inject (Plexus bean support)</name>

  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-classworlds</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
    </dependency>
    <dependency>
      <groupId>org.sonatype.sisu</groupId>
      <artifactId>sisu-inject-bean</artifactId>
    </dependency>
    <dependency>
      <groupId>org.sonatype.sisu.inject</groupId>
      <artifactId>guice-plexus-shim</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
          <excludeDependencies>
            <!-- fragment host and external dependencies -->
            sisu-inject-bean,*;groupId=org.codehaus.plexus
          </excludeDependencies>
          <instructions>
            <Bundle-SymbolicName>
              org.sonatype.inject.plexus
            </Bundle-SymbolicName>
            <Fragment-Host>
              org.sonatype.inject
            </Fragment-Host>
            <Export-Package>
              org.codehaus.plexus.*;-noimport:=true;-split-package:=merge-first
            </Export-Package>
            <Private-Package>
              META-INF.plexus,org.sonatype.guice.*,org.objectweb.asm
            </Private-Package>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <includes>
                  <include>${project.groupId}:${project.artifactId}</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>org.objectweb</pattern>
                  <shadedPattern>org.sonatype.guice</shadedPattern>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <includes>
                    <include>META-INF/**</include>
                    <include>org/codehaus/plexus/**</include>
                    <include>org/sonatype/guice/plexus/**</include>
                    <include>org/objectweb/asm/*Writer*</include>
                  </includes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <execution>
                <id>unpack-source</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <classifier>sources</classifier>
                  <failOnMissingClassifierArtifact>false</failOnMissingClassifierArtifact>
                  <outputDirectory>${project.build.directory}/sources</outputDirectory>
                  <excludeGroupIds>org.codehaus.plexus</excludeGroupIds>
                  <includes>
                    org/codehaus/plexus/**,org/sonatype/guice/plexus/**
                  </includes>
                  <silent>true</silent>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-source</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>add-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>${project.build.directory}/sources</source>
                  </sources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
