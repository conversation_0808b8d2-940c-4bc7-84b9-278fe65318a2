<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>co.realms9</groupId>
    <artifactId>realms9-estintel</artifactId>
    <version>2.0.3-RELEASE</version>
  </parent>
  <groupId>co.realms9</groupId>
  <artifactId>realms9-estintel-grpc</artifactId>
  <version>2.0.3-RELEASE</version>
  <dependencies>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-grpc-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-boot</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-api</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-grpc-client</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-buf-build-grpc-client</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-jooq</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-lambda</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-lambda</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-airflow</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-airflow</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-estintel-buf-build</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
