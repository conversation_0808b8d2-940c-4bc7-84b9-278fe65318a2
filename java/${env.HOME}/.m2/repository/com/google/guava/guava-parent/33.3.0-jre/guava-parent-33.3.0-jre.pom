<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.guava</groupId>
  <artifactId>guava-parent</artifactId>
  <version>33.3.0-jre</version>
  <packaging>pom</packaging>
  <name>Guava Maven Parent</name>
  <description>Parent for guava artifacts</description>
  <url>https://github.com/google/guava</url>
  <properties>
    <!--
    We could override this to change which version we run tests under.
    However, I think that our -Djava.security.manager=allow profile is based on the *Maven* JDK.
    If we want to use overrides here, we should change that profile to also be based on surefire.toolchain.version.
    -->
    <surefire.toolchain.version>${java.specification.version}</surefire.toolchain.version>
    <!-- Override this with -Dtest.include="**/SomeTest.java" on the CLI -->
    <test.include>%regex[.*.class]</test.include>
    <truth.version>1.4.4</truth.version>
    <jsr305.version>3.0.2</jsr305.version>
    <checker.version>3.43.0</checker.version>
    <errorprone.version>2.28.0</errorprone.version>
    <j2objc.version>3.0.0</j2objc.version>
    <!-- Empty for all JDKs but 9-12 -->
    <maven-javadoc-plugin.additionalJOptions></maven-javadoc-plugin.additionalJOptions>
    <project.build.outputTimestamp>2024-08-16T22:42:58Z</project.build.outputTimestamp>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <test.add.opens></test.add.opens>
    <test.add.args></test.add.args>
    <module.status>release</module.status>
    <variant.jvmEnvironment>standard-jvm</variant.jvmEnvironment>
    <variant.jvmEnvironmentVariantName>jre</variant.jvmEnvironmentVariantName>
    <otherVariant.version>33.3.0-android</otherVariant.version>
    <otherVariant.jvmEnvironment>android</otherVariant.jvmEnvironment>
    <otherVariant.jvmEnvironmentVariantName>android</otherVariant.jvmEnvironmentVariantName>
  </properties>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/google/guava/issues</url>
  </issueManagement>
  <inceptionYear>2010</inceptionYear>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/google/guava.git</connection>
    <developerConnection>scm:git:**************:google/guava.git</developerConnection>
    <url>https://github.com/google/guava</url>
  </scm>
  <developers>
    <developer>
      <id>kevinb9n</id>
      <name>Kevin Bourrillion</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <organizationUrl>http://www.google.com</organizationUrl>
      <roles>
        <role>owner</role>
        <role>developer</role>
      </roles>
      <timezone>-8</timezone>
    </developer>
  </developers>
  <ciManagement>
    <system>GitHub Actions</system>
    <url>https://github.com/google/guava/actions</url>
  </ciManagement>
  <modules>
    <module>guava</module>
    <module>guava-bom</module>
    <module>guava-gwt</module>
    <module>guava-testlib</module>
    <module>guava-tests</module>
  </modules>
  <build>
    <!-- Handle where Guava deviates from Maven defaults -->
    <sourceDirectory>src</sourceDirectory>
    <testSourceDirectory>test</testSourceDirectory>
    <resources>
      <resource>
        <directory>..</directory>
        <includes>
          <include>LICENSE</include>
        </includes>
        <targetPath>META-INF</targetPath>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>test</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </testResource>
    </testResources>
    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-versions</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.0.5</version>
                </requireMavenVersion>
                <requireJavaVersion>
                  <version>1.8.0</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.13.0</version>
          <configuration>
            <source>1.8</source>
            <target>1.8</target>
            <encoding>UTF-8</encoding>
            <parameters>true</parameters>
            <compilerArgs>
              <!--
                   Make includes/excludes fully work:
                   https://issues.apache.org/jira/browse/MCOMPILER-174

                   (Compare what guava-gwt has to do for maven-javadoc-plugin.)
              -->
              <arg>-sourcepath</arg>
              <arg>doesnotexist</arg>
              <!-- https://errorprone.info/docs/installation#maven -->
              <arg>-XDcompilePolicy=simple</arg>

              <!-- https://errorprone.info/docs/installation#maven -->
              <!-- TODO(cpovirk): Enable NullArgumentForNonNullParameter for
                   prod code. It's disabled automatically for "test code"
                   (which is good: our tests have intentional violations), but
                   Error Prone doesn't know it's building test code unless we
                   pass -XepCompilingTestOnlyCode, and that argument needs to
                   be passed as part of the same <arg> as -Xplugin:ErrorProne,
                   and I gave up trying to figure out how to do that for test
                   compilation only. -->
              <arg>-Xplugin:ErrorProne -Xep:NullArgumentForNonNullParameter:OFF -Xep:Java8ApiChecker:ERROR</arg>
              <!-- https://github.com/google/error-prone/blob/f8e33bc460be82ab22256a7ef8b979d7a2cacaba/docs/installation.md#jdk-16 -->
              <!-- TODO(cpovirk): Use .mvn/jvm.config instead (per
                   https://errorprone.info/docs/installation#maven) if it can
                   be made not to interfere with JDK8 or if we stop building
                   with JDK8. -->
              <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED</arg>
              <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED</arg>
              <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED</arg>
              <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED</arg>
              <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED</arg>
              <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>
              <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED</arg>
              <arg>-J--add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>
              <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>
              <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED</arg>
            </compilerArgs>
            <annotationProcessorPaths>
              <path>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_core</artifactId>
                <version>2.23.0</version>
              </path>
            </annotationProcessorPaths>
            <!-- Fork:

                 - for JDK8 because we use a javac9 bootclasspath

                 - for JDK9+ because we need args like add-exports
                 -->
            <fork>true</fork>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.mvnsearch</groupId>
          <artifactId>toolchains-maven-plugin</artifactId>
          <version>4.5.0</version>
          <executions>
            <!--
            We can apparently have only one <jdk> per execution: Others are silently ignored :(
            To properly test this, you need to remove existing toolchains:
            rm -rf ~/.m2/jdks/ ~/.m2/toolchains.xml
            (But don't run that if you have put something into ~/.m2/toolchains.xml yourself.)
            -->
            <execution>
              <id>download-11</id>
              <goals>
                <goal>toolchain</goal>
              </goals>
              <configuration>
                <toolchains>
                  <jdk>
                    <version>11</version>
                    <vendor>zulu</vendor>
                  </jdk>
                </toolchains>
              </configuration>
            </execution>
            <execution>
              <id>download-22-and-surefire-version</id>
              <goals>
                <goal>toolchain</goal>
              </goals>
              <configuration>
                <toolchains>
                  <jdk>
                    <version>22</version>
                    <vendor>zulu</vendor>
                  </jdk>
                  <testJdk>
                    <version>${surefire.toolchain.version}</version>
                    <vendor>zulu</vendor>
                  </testJdk>
                </toolchains>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-toolchains-plugin</artifactId>
          <version>3.2.0</version>
          <executions>
            <execution>
              <goals>
                <goal>toolchain</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <toolchains>
              <jdk>
                <version>22</version>
                <vendor>zulu</vendor>
              </jdk>
            </toolchains>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.3.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
          <dependencies>
            <dependency>
              <groupId>org.codehaus.plexus</groupId>
              <artifactId>plexus-io</artifactId>
              <!-- DO NOT UPGRADE this past 3.4.1 until https://github.com/codehaus-plexus/plexus-io/issues/109 is fixed (probably in 3.5.1). -->
              <version>3.4.1</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.23</version>
          <dependencies>
            <dependency>
              <groupId>org.ow2.asm</groupId>
              <artifactId>asm</artifactId>
              <version>9.6</version>
            </dependency>
          </dependencies>
          <configuration>
            <annotations>com.google.common.base.IgnoreJRERequirement,com.google.common.cache.IgnoreJRERequirement,com.google.common.collect.IgnoreJRERequirement,com.google.common.hash.IgnoreJRERequirement,com.google.common.io.IgnoreJRERequirement,com.google.common.reflect.IgnoreJRERequirement,com.google.common.testing.IgnoreJRERequirement</annotations>
            <checkTestClasses>true</checkTestClasses>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java18</artifactId>
              <version>1.0</version>
            </signature>
          </configuration>
          <executions>
            <execution>
              <id>check-java-version-compatibility</id>
              <phase>test</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.8.0</version>
          <configuration>
            <!-- TODO: b/286965322 - Use a newer version (probably the default toolchain we set up?) if it doesn't break Javadoc (including causing trouble for our later run of JDiff, which we do outside Maven, during snapshots/releases). -->
            <jdkToolchain>
              <version>11</version>
              <vendor>zulu</vendor>
            </jdkToolchain>
            <quiet>true</quiet>
            <notimestamp>true</notimestamp>
            <encoding>UTF-8</encoding>
            <docencoding>UTF-8</docencoding>
            <charset>UTF-8</charset>
            <additionalOptions>
              <additionalOption>-XDignore.symbol.file</additionalOption>
              <additionalOption>-Xdoclint:-html</additionalOption>
            </additionalOptions>
            <linksource>true</linksource>
            <source>8</source>
            <additionalJOption>${maven-javadoc-plugin.additionalJOptions}</additionalJOption>
          </configuration>
          <executions>
            <execution>
              <id>attach-docs</id>
              <goals><goal>jar</goal></goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.1.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.6</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.3.1</version>
          <configuration>
            <jdkToolchain>
              <version>${surefire.toolchain.version}</version>
              <vendor>zulu</vendor>
            </jdkToolchain>
            <includes>
              <include>${test.include}</include>
            </includes>
            <!-- By having our own entries here, we also override the default exclusion filter, which excludes all nested classes. -->
            <excludes>
              <!-- https://github.com/google/guava/issues/2840 -->
              <exclude>%regex[.*PackageSanityTests.*.class]</exclude>
              <!-- FeatureUtilTest.*ExampleDerivedInterfaceTester, com.google.common.io.*Tester, incidentally FeatureSpecificTestSuiteBuilderTest.MyAbstractTester (but we don't care either way because it's not meant to run on its own but works OK if it does)... but not NullPointerTesterTest, etc. -->
              <exclude>%regex[.*Tester.class]</exclude>
              <!-- Anonymous TestCase subclasses in GeneratedMonitorTest -->
              <exclude>%regex[.*[$]\d+.class]</exclude>
            </excludes>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
            <runOrder>alphabetical</runOrder>
            <!-- Set max heap for tests. -->
            <!-- Catch dependencies on the default locale by setting it to hi-IN. -->
            <argLine>-Xmx1536M -Duser.language=hi -Duser.country=IN ${test.add.args} ${test.add.opens}</argLine>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.0.0-M3</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>3.4.0</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <name>Nexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <site>
      <id>guava-site</id>
      <name>Guava Documentation Site</name>
      <url>scp://dummy.server/dontinstall/usestaging</url>
    </site>
  </distributionManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>${jsr305.version}</version>
      </dependency>
      <dependency>
        <groupId>org.checkerframework</groupId>
        <artifactId>checker-qual</artifactId>
        <version>${checker.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.errorprone</groupId>
        <artifactId>error_prone_annotations</artifactId>
        <version>${errorprone.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.j2objc</groupId>
        <artifactId>j2objc-annotations</artifactId>
        <version>${j2objc.version}</version>
      </dependency>
      <!--
      We moved away from using dependencyManagement for test-only deps because of https://github.com/google/guava/issues/6654.
      We could probably have resumed it after https://github.com/google/guava/pull/6664.
      But it's always weird that published poms reference test-only libraries at all, so I'm not in any rush to do so.
      -->
    </dependencies>
  </dependencyManagement>
  <profiles>
    <profile>
        <id>sonatype-oss-release</id>
        <build>
          <plugins>
            <plugin>
              <artifactId>maven-gpg-plugin</artifactId>
              <version>3.0.1</version>
              <executions>
                <execution>
                  <id>sign-artifacts</id>
                  <phase>verify</phase>
                  <goals>
                    <goal>sign</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
          </plugins>
      </build>
    </profile>
    <profile>
      <!--
          Passes JDK 11-12-specific `no-module-directories` flag to Javadoc tool,
          which is required to make symbol search work correctly in the generated
          pages.

          This flag does not exist on 9-10 and 13+ (https://bugs.openjdk.java.net/browse/JDK-8215582).

          Consider removing it once our release and test scripts are migrated to a recent JDK (17+).
       -->
      <id>javadocs-jdk11-12</id>
      <activation>
        <jdk>[11,13)</jdk>
      </activation>
      <properties>
        <maven-javadoc-plugin.additionalJOptions>--no-module-directories</maven-javadoc-plugin.additionalJOptions>
      </properties>
    </profile>
    <profile>
      <id>open-jre-modules</id>
      <activation>
        <jdk>[9,]</jdk>
      </activation>
      <properties>
        <!--
            Some tests need reflective access to the internals of these packages. It is only the
            tests themselves and not the code being tested that needs that access, though there's no
            obvious way to ensure that.

            We could consider arranging things so that only the tests we know need this would get
            the add-opens. Right now that doesn't seem worth the effort, though.
        -->
        <test.add.opens>
          --add-opens java.base/java.lang=ALL-UNNAMED
          --add-opens java.base/java.util=ALL-UNNAMED
          --add-opens java.base/sun.security.jca=ALL-UNNAMED
        </test.add.opens>
      </properties>
    </profile>
    <profile>
      <id>javac-for-jvm18plus</id>
      <activation>
        <!--
            In order to build and run the tests against JDK 18+, we need to pass java.security.manager=allow, to make
            the deprecated 'java.lang.SecurityManager' available for use.
         -->
        <jdk>[18,]</jdk>
      </activation>
      <properties>
        <test.add.args>-Djava.security.manager=allow</test.add.args>
      </properties>
    </profile>
  </profiles>
</project>
