<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <artifactId>gax-grpc</artifactId>
  <version>2.58.0</version><!-- {x-version-update:gax-grpc:current} -->
  <packaging>jar</packaging>
  <name>GAX (Google Api eXtensions) for Java (gRPC)</name>
  <description>Google Api eXtensions for Java (gRPC)</description>

  <parent>
    <groupId>com.google.api</groupId>
    <artifactId>gax-parent</artifactId>
    <version>2.58.0</version><!-- {x-version-update:gax:current} -->
  </parent>

  <dependencies>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>api-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-common-protos</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-credentials</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-api</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-inprocess</artifactId>
    </dependency>
    <dependency>
      <groupId>org.threeten</groupId>
      <artifactId>threetenbp</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-oauth2-http</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-alts</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-auth</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-s2a</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-stub</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-netty-shaded</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-googleapis</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.graalvm.sdk</groupId>
      <artifactId>graal-sdk</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.google.auto.value</groupId>
      <artifactId>auto-value-annotations</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- test dependencies -->
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>grpc-google-common-protos</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
      <classifier>testlib</classifier>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <skip>false</skip>
              <classifier>testlib</classifier>
              <includes>
                <include>com/google/api/gax/grpc/testing/**</include>
              </includes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!-- These tests require an Env Var to be set. Use -PenvVarTest to ONLY run these tests -->
          <test>!InstantiatingGrpcChannelProviderTest#testLogDirectPathMisconfig_AttemptDirectPathNotSetAndAttemptDirectPathXdsSetViaEnv_warns</test>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>envVarTest</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <test>InstantiatingGrpcChannelProviderTest#testLogDirectPathMisconfig_AttemptDirectPathNotSetAndAttemptDirectPathXdsSetViaEnv_warns</test>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>