<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>native-image-shared-config</artifactId>
  <packaging>pom</packaging>
  <version>1.12.0</version><!-- {x-version-update:google-cloud-shared-config:current} -->
  <name>Native Image Shared Config</name>
  <url>https://github.com/googleapis/java-shared-config</url>
  <description>
    Native Image build configuration for Google Cloud Java libraries.
  </description>
  <developers>
    <developer>
      <id>chingor13</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <organization>
    <name>Google LLC</name>
  </organization>
  <scm>
    <connection>scm:git:**************:googleapis/java-shared-config.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-shared-config.git
    </developerConnection>
    <url>https://github.com/googleapis/java-shared-config</url>
    <tag>HEAD</tag>
  </scm>

  <issueManagement>
    <url>https://github.com/googleapis/java-shared-config/issues</url>
    <system>GitHub Issues</system>
  </issueManagement>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <properties>
    <graal-sdk.version>24.1.1</graal-sdk.version>
    <surefire.version>3.5.2</surefire.version>
    <graal-sdk-nativeimage.version>24.1.1</graal-sdk-nativeimage.version>
    <native-maven-plugin.version>0.10.3</native-maven-plugin.version>
    <junit-vintage-engine.version>5.11.3</junit-vintage-engine.version>
    <opentest4j.version>1.3.0</opentest4j.version>
    <ignoredDependencies>org.graalvm.sdk:nativeimage</ignoredDependencies>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.graalvm.sdk</groupId>
        <artifactId>graal-sdk</artifactId>
        <version>${graal-sdk.version}</version>
      </dependency>
      <dependency>
        <groupId>org.graalvm.sdk</groupId>
        <artifactId>nativeimage</artifactId>
        <version>${graal-sdk-nativeimage.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.7.0</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>false</autoReleaseAfterClose>
            <stagingProgressTimeoutMinutes>15</stagingProgressTimeoutMinutes>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>release</id>
      <activation>
        <property>
          <name>performRelease</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.3.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.11.1</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <doclint>none</doclint>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.2.7</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <!-- This profile is used to enable GraalVM native image testing -->
      <id>native</id>
      <dependencies>
        <!-- Add this dependency to resolve class org.opentest4j.TestAbortedException -->
        <dependency>
          <groupId>org.opentest4j</groupId>
          <artifactId>opentest4j</artifactId>
          <version>${opentest4j.version}</version>
        </dependency>
        <dependency>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
          <version>${junit-vintage-engine.version}</version>
          <scope>test</scope>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>${surefire.version}</version>
            <dependencies>
              <dependency>
                <groupId>org.junit.vintage</groupId>
                <artifactId>junit-vintage-engine</artifactId>
                <version>${junit-vintage-engine.version}</version>
              </dependency>
            </dependencies>
            <configuration>
              <!-- Include all tests during native image testing. -->
              <excludes combine.self="override" />
              <includes>
                <include>**/IT*.java</include>
                <!-- Enable unit tests in generated libraries for native image testing. -->
                <include>**/*ClientTest.java</include>
              </includes>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.graalvm.buildtools</groupId>
            <artifactId>native-maven-plugin</artifactId>
            <version>${native-maven-plugin.version}</version>
            <extensions>true</extensions>
            <executions>
              <execution>
                <id>test-native</id>
                <goals>
                  <goal>test</goal>
                </goals>
                <phase>test</phase>
              </execution>
            </executions>
            <configuration>
              <buildArgs>
                <buildArg>--no-fallback</buildArg>
                <buildArg>--no-server</buildArg>
              </buildArgs>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
