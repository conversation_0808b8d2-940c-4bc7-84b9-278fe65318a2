<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-datastore-bom</artifactId>
  <version>2.22.0</version><!-- {x-version-update:google-cloud-datastore-bom:current} -->
  <packaging>pom</packaging>
  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>sdk-platform-java-config</artifactId>
    <version>3.36.1</version>
  </parent>

  <name>Google Cloud datastore BOM</name>
  <url>https://github.com/googleapis/java-datastore</url>
  <description>
    BOM for Google Cloud datastore
  </description>

  <organization>
    <name>Google LLC</name>
  </organization>

  <developers>
    <developer>
      <id>chingor13</id>
      <name>Jeff Ching</name>
      <email><EMAIL></email>
      <organization>Google LLC</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:https://github.com/googleapis/java-datastore.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-datastore.git</developerConnection>
    <url>https://github.com/googleapis/java-datastore</url>
  </scm>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datastore</artifactId>
        <version>2.22.0</version><!-- {x-version-update:google-cloud-datastore:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-datastore-admin-v1</artifactId>
        <version>2.22.0</version><!-- {x-version-update:grpc-google-cloud-datastore-admin-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-datastore-v1</artifactId>
        <version>0.113.0</version><!-- {x-version-update:proto-google-cloud-datastore-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-datastore-admin-v1</artifactId>
        <version>2.22.0</version><!-- {x-version-update:proto-google-cloud-datastore-admin-v1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
