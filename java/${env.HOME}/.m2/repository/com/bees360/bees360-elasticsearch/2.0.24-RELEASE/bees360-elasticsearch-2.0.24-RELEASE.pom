<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bees360</groupId>
    <artifactId>bees360-utility-kit-parent</artifactId>
    <version>2.0.24-RELEASE</version>
  </parent>
  <groupId>com.bees360</groupId>
  <artifactId>bees360-elasticsearch</artifactId>
  <version>2.0.24-RELEASE</version>
  <dependencies>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>org.opensearch.client</groupId>
      <artifactId>opensearch-rest-high-level-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-repository-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-boot</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
