<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bees360</groupId>
    <artifactId>bees360-delivery</artifactId>
    <version>${revision}${changelist}</version>
  </parent>

  <artifactId>bees360-delivery-app</artifactId>
  <packaging>jar</packaging>

  <name>bees360-delivery-app</name>

  <dependencies>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-delivery-job</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-delivery-jooq</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-delivery-lc360</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-delivery-mysql</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-boot</artifactId>
    </dependency>
    <dependency>
      <groupId>co.realms9</groupId>
      <artifactId>realms9-secretsmanger-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-event-rabbit</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-event-config</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-job-rabbit</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-job-config</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-project-grpc-client</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-project-event</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-integration-grpc-client</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-image-grpc-client</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-report-grpc-client</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-pipeline-grpc-client</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-grpc-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-user-grpc-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-lc360-config</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-jooq</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-slf4j-impl</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-log</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-monitor-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.ulisesbocchio</groupId>
      <artifactId>jasypt-spring-boot</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-job</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-sftp</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-event</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-delivery-rct</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-map-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-openapi-resource</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-openapi-grpc-client</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-openapi-http</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-http-client</artifactId>
    </dependency>
    <!-- testing -->
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-image-grpc</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-image-jooq</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-project-grpc</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-project-jooq</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-pipeline-grpc</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-pipeline-jooq</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-integration-grpc</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-integration-jooq</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-event-jooq</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-grpc</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-user-grpc</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-event-api</artifactId>
      <scope>test</scope>
      <type>test-jar</type>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-project-api</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
      <type>test-jar</type>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-openapi-grpc</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.jayway.jsonpath</groupId>
      <artifactId>json-path</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>com.google.cloud.tools</groupId>
        <artifactId>jib-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <skip>false</skip>
          <mainClass>com.bees360.delivery.ProjectDeliveryApp</mainClass>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
