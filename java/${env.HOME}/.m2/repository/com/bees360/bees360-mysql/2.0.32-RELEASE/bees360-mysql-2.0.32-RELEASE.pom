<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bees360</groupId>
    <artifactId>bees360-utility-kit-parent</artifactId>
    <version>2.0.32-RELEASE</version>
  </parent>
  <groupId>com.bees360</groupId>
  <artifactId>bees360-mysql</artifactId>
  <version>2.0.32-RELEASE</version>
  <dependencies>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-jooq</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.mariadb.jdbc</groupId>
      <artifactId>mariadb-java-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.jetbrains</groupId>
      <artifactId>annotations</artifactId>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-codegen-maven</artifactId>
        <executions>
          <execution>
            <id>convergence-ai</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>generate</goal>
            </goals>
            <configuration>
              <jdbc>
                <driver>org.mariadb.jdbc.Driver</driver>
                <url>********************************************************************************************************************************************************************************************</url>
                <user>root</user>
                <password>123456</password>
              </jdbc>
              <generator>
                <name>org.jooq.codegen.JavaGenerator</name>
                <database>
                  <name>org.jooq.meta.mysql.MySQLDatabase</name>
                  <includes>.*</includes>
                  <inputSchema>Bees360Report</inputSchema>
                  <forcedTypes>
                    <forcedType>
                      <userType>java.lang.String</userType>
                      <converter>com.bees360.jooq.IdConverter</converter>
                      <includeExpression>id</includeExpression>
                      <includeTypes>bigint</includeTypes>
                    </forcedType>
                    <forcedType>
                      <userType>com.bees360.api.common.Gps</userType>
                      <converter>com.bees360.jooq.GPSConverter</converter>
                      <includeExpression>gps_location</includeExpression>
                      <includeTypes>point</includeTypes>
                    </forcedType>
                  </forcedTypes>
                </database>
                <target>
                  <packageName>com.bees360.jooq.mysql</packageName>
                  <directory>target/generated-sources/mysql</directory>
                </target>
                <generate>
                  <spatialTypes>false</spatialTypes>
                </generate>
              </generator>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
