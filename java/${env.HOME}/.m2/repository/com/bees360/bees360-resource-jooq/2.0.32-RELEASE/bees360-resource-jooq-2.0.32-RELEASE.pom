<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bees360</groupId>
    <artifactId>bees360-utility-kit-resource</artifactId>
    <version>2.0.32-RELEASE</version>
  </parent>
  <groupId>com.bees360</groupId>
  <artifactId>bees360-resource-jooq</artifactId>
  <version>2.0.32-RELEASE</version>
  <properties>
    <jooq-tables>resource_alias | resource_key_alias</jooq-tables>
    <jooq-package>com.bees360.jooq.persistent.resource</jooq-package>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-jooq</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-map-api</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-event-jooq</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-api</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-boot</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-event-rabbit</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-event-api</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-codegen-maven</artifactId>
        <executions>
          <execution>
            <id>convergence</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>generate</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <jdbc>
            <driver>org.postgresql.Driver</driver>
            <url>*******************************************</url>
            <user>db_user</user>
            <password>db_password</password>
          </jdbc>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
