<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bees360-pipeline</artifactId>
        <groupId>com.bees360</groupId>
        <version>${revision}${changelist}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bees360-pipeline-event</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-api</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jmock</groupId>
            <artifactId>jmock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-rabbit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-rabbit</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-jooq</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-job-event</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-boot</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-api</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
