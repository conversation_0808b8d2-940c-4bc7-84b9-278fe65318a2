<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bees360</groupId>
    <artifactId>bees360-utility-kit-parent</artifactId>
    <version>2.0.32-RELEASE</version>
  </parent>
  <groupId>com.bees360</groupId>
  <artifactId>bees360-s3</artifactId>
  <version>2.0.32-RELEASE</version>
  <properties>
    <ion-java.version>1.11.9</ion-java.version>
    <aws-java-sdk.version>1.12.779</aws-java-sdk.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.amazon.ion</groupId>
        <artifactId>ion-java</artifactId>
        <version>${ion-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-core</artifactId>
        <version>${aws-java-sdk.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.amazon.ion</groupId>
            <artifactId>ion-java</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-bom</artifactId>
        <version>${aws-java-sdk.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>software.amazon.awssdk</groupId>
      <artifactId>s3</artifactId>
    </dependency>
    <dependency>
      <groupId>com.amazon.ion</groupId>
      <artifactId>ion-java</artifactId>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-cloudfront</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-http-client</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
    </dependency>
    <dependency>
      <groupId>software.amazon.awssdk</groupId>
      <artifactId>apache-client</artifactId>
    </dependency>
    <dependency>
      <groupId>software.amazon.awssdk</groupId>
      <artifactId>netty-nio-client</artifactId>
    </dependency>
  </dependencies>
</project>
