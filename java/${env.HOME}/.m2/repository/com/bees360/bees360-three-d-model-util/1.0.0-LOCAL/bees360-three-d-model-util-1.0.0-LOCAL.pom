<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bees360</groupId>
        <artifactId>bees360-three-d-model</artifactId>
        <version>${revision}${changelist}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>bees360-three-d-model-util</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-resource-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-three-d-model-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-three-d-model-api</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
