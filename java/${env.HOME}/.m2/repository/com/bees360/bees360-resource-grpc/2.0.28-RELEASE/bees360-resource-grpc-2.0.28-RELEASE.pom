<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bees360</groupId>
    <artifactId>bees360-utility-kit-resource</artifactId>
    <version>2.0.28-RELEASE</version>
  </parent>
  <groupId>com.bees360</groupId>
  <artifactId>bees360-resource-grpc</artifactId>
  <version>2.0.28-RELEASE</version>
  <dependencies>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-api</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-grpc-core</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-http-client</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bees360</groupId>
      <artifactId>bees360-resource-grpc-client</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
