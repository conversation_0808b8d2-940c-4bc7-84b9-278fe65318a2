<!-- Copyright (c) Microsoft Corporation. All rights reserved.
     Licensed under the MIT License. -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.azure</groupId>
  <artifactId>azure-client-sdk-parent</artifactId>
  <packaging>pom</packaging>
  <version>1.7.0</version> <!-- {x-version-update;com.azure:azure-client-sdk-parent;current} -->

  <name>Microsoft Azure SDK for Java - Client Libraries</name>
  <description>Parent POM for Microsoft Azure SDK for Java</description>
  <url>https://github.com/Azure/azure-sdk-for-java</url>
  <organization>
    <name>Microsoft Corporation</name>
    <url>http://microsoft.com</url>
  </organization>

  <parent>
    <groupId>com.azure</groupId>
    <artifactId>azure-sdk-parent</artifactId>
    <version>1.6.0</version> <!-- {x-version-update;com.azure:azure-sdk-parent;current} -->
    <relativePath>./parent/pom.xml</relativePath>
  </parent>

  <licenses>
    <license>
      <name>The MIT License (MIT)</name>
      <url>http://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>microsoft</id>
      <name>Microsoft Corporation</name>
    </developer>
  </developers>

  <!-- Repositories definitions -->
  <repositories>
    <repository>
      <id>ossrh</id>
      <name>Sonatype Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
      <layout>default</layout>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>daily</updatePolicy>
      </snapshots>
    </repository>
  </repositories>

  <pluginRepositories>
    <pluginRepository>
      <id>ossrh</id>
      <name>Sonatype Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
      <layout>default</layout>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </pluginRepository>
  </pluginRepositories>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <name>Sonatype Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
      <uniqueVersion>true</uniqueVersion>
      <layout>default</layout>
    </snapshotRepository>
    <site>
      <id>azure-java-build-docs</id>
      <url>${site.url}/site/</url>
    </site>
  </distributionManagement>

  <issueManagement>
    <system>GitHub</system>
    <url>${issues.url}</url>
  </issueManagement>

  <scm>
    <url>https://github.com/Azure/azure-sdk-for-java</url>
    <connection>scm:git:https://github.com/Azure/azure-sdk-for-java.git</connection>
    <developerConnection></developerConnection>
    <tag>HEAD</tag>
  </scm>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <packageOutputDirectory>${project.build.directory}</packageOutputDirectory>
    <legal>
      <![CDATA[[INFO] Any downloads listed may be third party software.  Microsoft grants you no rights for third party software.]]></legal>
    <testMode>playback</testMode>
    <playbackServerPort>11080</playbackServerPort>
    <alternativePlaybackServerPort>11081</alternativePlaybackServerPort>

    <site.url>https://azuresdkartifacts.blob.core.windows.net/azure-sdk-for-java</site.url>
    <issues.url>https://github.com/Azure/azure-sdk-for-java/issues</issues.url>
    <build.context>azure-client-sdk-parent</build.context>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-compiler-plugin;external_dependency} -->
        <configuration>
          <showWarnings>true</showWarnings>
          <failOnWarning>true</failOnWarning>
        </configuration>
      </plugin>

      <!-- This plugin scans checkstyle issues in the code -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.1.0</version> <!-- {x-version-update;org.apache.maven.plugins:maven-checkstyle-plugin;external_dependency} -->
        <dependencies>
          <dependency>
            <groupId>com.azure</groupId>
            <artifactId>sdk-build-tools</artifactId>
            <version>1.0.0</version> <!-- {x-version-update;com.azure:sdk-build-tools;external_dependency} -->
          </dependency>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>8.24</version> <!-- {x-version-update;com.puppycrawl.tools:checkstyle;external_dependency} -->
          </dependency>
        </dependencies>
        <configuration>
          <configLocation>eng/code-quality-reports/src/main/resources/checkstyle/checkstyle.xml</configLocation>
          <suppressionsLocation>eng/code-quality-reports/src/main/resources/checkstyle/checkstyle-suppressions.xml
          </suppressionsLocation>
          <headerLocation>eng/code-quality-reports/src/main/resources/checkstyle/java.header</headerLocation>
          <propertyExpansion>samedir=</propertyExpansion>
          <encoding>UTF-8</encoding>
          <consoleOutput>true</consoleOutput>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
          <linkXRef>true</linkXRef>
          <failsOnError>true</failsOnError>
          <failOnViolation>true</failOnViolation>
        </configuration>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- This plugin scans reports spotbugs in the code -->
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>********</version> <!-- {x-version-update;com.github.spotbugs:spotbugs-maven-plugin;external_dependency} -->
        <dependencies>
          <dependency>
            <groupId>com.azure</groupId>
            <artifactId>sdk-build-tools</artifactId>
            <version>1.0.0</version> <!-- {x-version-update;com.azure:sdk-build-tools;external_dependency} -->
          </dependency>
          <dependency>
            <groupId>com.github.spotbugs</groupId>
            <artifactId>spotbugs</artifactId>
            <version>4.0.0-beta3</version> <!-- {x-version-update;com.github.spotbugs:spotbugs;external_dependency} -->
          </dependency>
        </dependencies>
        <configuration>
          <effort>max</effort>
          <threshold>Low</threshold>
          <xmlOutput>true</xmlOutput>
          <spotbugsXmlOutputDirectory>${project.build.directory}/spotbugs</spotbugsXmlOutputDirectory>
          <excludeFilterFile>spotbugs/spotbugs-exclude.xml</excludeFilterFile>
          <failOnError>true</failOnError>
          <fork>true</fork>
        </configuration>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- This plugin exports spotbugs reports in html form -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>xml-maven-plugin</artifactId>
        <version>1.0</version> <!-- {x-version-update;org.codehaus.mojo:xml-maven-plugin;external_dependency} -->
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>transform</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <transformationSets>
            <transformationSet>
              <dir>${project.build.directory}/spotbugs</dir>
              <includes>
                <include>spotbugsXml.xml</include>
              </includes>
              <outputDir>${project.build.directory}/spotbugs</outputDir>
              <stylesheet>fancy-hist.xsl</stylesheet>
              <fileMappers>
                <fileMapper implementation="org.codehaus.plexus.components.io.filemappers.FileExtensionMapper">
                  <targetExtension>.html</targetExtension>
                </fileMapper>
              </fileMappers>
            </transformationSet>
          </transformationSets>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.github.spotbugs</groupId>
            <artifactId>spotbugs</artifactId>
            <version>4.0.0-beta3</version> <!-- {x-version-update;com.github.spotbugs:spotbugs;external_dependency} -->
          </dependency>
        </dependencies>
      </plugin>

      <!-- Configure the jar plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.1.2</version> <!-- {x-version-update;org.apache.maven.plugins:maven-jar-plugin;external_dependency} -->
        <configuration>
          <outputDirectory>${packageOutputDirectory}</outputDirectory>
        </configuration>
      </plugin>

      <!-- Configure the javadoc plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.1.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-javadoc-plugin;external_dependency} -->
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <jarOutputDirectory>${packageOutputDirectory}</jarOutputDirectory>
              <failOnError>true</failOnError>
              <failOnWarnings>true</failOnWarnings>
              <doclint>all</doclint>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Configure the source plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.0.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-source-plugin;external_dependency} -->
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <outputDirectory>${packageOutputDirectory}</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Copy the pom file to output -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>1.8</version> <!-- {x-version-update;org.apache.maven.plugins:maven-antrun-plugin;external_dependency} -->
        <executions>
          <execution>
            <id>copy</id>
            <phase>package</phase>
            <configuration>
              <target>
                <copy file="${project.pomFile}" tofile="${packageOutputDirectory}/${project.build.finalName}.pom"/>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.4</version> <!-- {x-version-update;org.jacoco:jacoco-maven-plugin;external_dependency} -->
        <executions>
        <execution>
          <id>default-instrument</id>
          <goals>
            <goal>instrument</goal>
          </goals>
        </execution>
        <execution>
          <id>default-restore-instrumented-classes</id>
          <goals>
            <goal>restore-instrumented-classes</goal>
          </goals>
        </execution>
          <execution>
            <!-- This generates the unit test reports for individual modules.
              jacoco-test-coverage generates aggregate reports for all modules -->
            <id>post-unit-test</id>
            <goals>
              <goal>report</goal>
            </goals>
            <configuration>
              <!-- Sets the output directory for the code coverage report. -->
              <outputDirectory>${project.reporting.outputDirectory}/test-coverage</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Allows the sample sources to be built during test-compile phase. -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>3.0.0</version> <!-- {x-version-update;org.codehaus.mojo:build-helper-maven-plugin;external_dependency} -->
      </plugin>
    </plugins>

    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.1.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-javadoc-plugin;external_dependency} -->
          <configuration>
            <source>1.8</source>
            <doctitle>Azure SDK for Java Reference Documentation</doctitle>
            <windowtitle>Azure SDK for Java Reference Documentation</windowtitle>
            <footer>Visit the &lt;a href="https://docs.microsoft.com/java/azure/"&gt;Azure for Java Developers&lt;/a&gt;site
              for more Java documentation, including quick starts, tutorials, and code samples.</footer>
            <linksource>false</linksource>
            <excludePackageNames>
              *.impl*:
              *.implementation*:
              com.azure.tools.checkstyle*
            </excludePackageNames>
            <groups>
              <group>
                <title>Azure Core</title>
                <packages>
                  com.azure.core:com.azure.core.annotation:com.azure.core.credentials:com.azure.core.exception:com.azure.core.http*:com.azure.core.configuration:com.azure.core.util*:com.azure.core.credential:com.azure.core.cryptography
                </packages>
              </group>
              <group>
                <title>Azure Core - AMQP</title>
                <packages>com.azure.core.amqp*</packages>
              </group>
              <group>
                <title>Azure Core - Authentication</title>
                <packages>com.azure.core.auth*</packages>
              </group>
              <group>
                <title>Azure Core - HTTP - Netty</title>
                <packages>com.azure.core.http.netty*</packages>
              </group>
              <group>
                <title>Azure Core - HTTP - OkHttp</title>
                <packages>com.azure.core.http.okhttp*</packages>
              </group>
              <group>
                <title>Azure Core - Management</title>
                <packages>com.azure.core.management*</packages>
              </group>
              <group>
                <title>Azure App Configuration</title>
                <packages>com.azure.data.appconfiguration*</packages>
              </group>
              <group>
                <title>Azure Event Hubs</title>
                <packages>com.azure.messaging.eventhubs*</packages>
              </group>
              <group>
                <title>Azure Identity</title>
                <packages>com.azure.identity*</packages>
              </group>
              <group>
                <title>Azure Key Vault</title>
                <packages>com.azure.security.keyvault*</packages>
              </group>
              <group>
                <title>Azure Storage - Common</title>
                <packages>com.azure.storage.common*</packages>
              </group>
              <group>
                <title>Azure Storage - Blobs</title>
                <packages>com.azure.storage.blob*</packages>
              </group>
              <group>
                <title>Azure Storage Blob - Batch</title>
                <packages>com.azure.storage.blob.batch*</packages>
              </group>
              <group>
                <title>Azure Storage Blobs - Cryptography</title>
                <packages>com.azure.storage.blob.cryptography*</packages>
              </group>
              <group>
                <title>Azure Storage - Files</title>
                <packages>com.azure.storage.file*</packages>
              </group>
              <group>
                <title>Azure Storage Files - Data Lake</title>
                <packages>com.azure.storage.file.datalake*</packages>
              </group>
              <group>
                <title>Azure Storage - Queues</title>
                <packages>com.azure.storage.queue*</packages>
              </group>
              <group>
                <title>Azure Storage Queues - Cryptography</title>
                <packages>com.azure.storage.queue.cryptography*</packages>
              </group>
              <group>
                <title>Azure Telemetry</title>
                <title>Azure Telemetry - OpenCensus</title>
                <packages>com.azure.core.tracing*</packages>
              </group>
            </groups>
            <links>
              <link>https://docs.oracle.com/javase/8/docs/api/</link>
              <link>https://projectreactor.io/docs/core/release/api/</link>
              <link>https://netty.io/4.1/api/</link>
              <link>http://reactivex.io/RxJava/javadoc/</link>
            </links>
            <isOffline>false</isOffline>
            <doclet>org.apidesign.javadoc.codesnippet.Doclet</doclet>
            <docletArtifact>
              <groupId>org.apidesign.javadoc</groupId>
              <artifactId>codesnippet-doclet</artifactId>
              <version>0.32</version> <!-- {x-version-update;org.apidesign.javadoc:codesnippet-doclet;external_dependency} -->
            </docletArtifact>
            <additionalOptions>
              <additionalOption>-maxLineLength 120</additionalOption>
              <additionalOption>-snippetpath ${project.basedir}/src/samples/java</additionalOption>
              <additionalOption>-suppressMissingLinkWarnings</additionalOption>
            </additionalOptions>
            <failOnError>false</failOnError>
            <failOnWarnings>false</failOnWarnings>
            <doclint>all</doclint>
            <sourceFileExcludes>
              <sourceFileExclude>module-info.java</sourceFileExclude>
            </sourceFileExcludes>
          </configuration>
        </plugin>

        <plugin>
          <groupId>com.github.spotbugs</groupId>
          <artifactId>spotbugs-maven-plugin</artifactId>
          <version>********</version> <!-- {x-version-update;com.github.spotbugs:spotbugs-maven-plugin;external_dependency} -->
          <dependencies>
            <dependency>
              <groupId>com.azure</groupId>
              <artifactId>sdk-build-tools</artifactId>
              <version>1.0.0</version> <!-- {x-version-update;com.azure:sdk-build-tools;external_dependency} -->
            </dependency>
            <dependency>
              <groupId>com.github.spotbugs</groupId>
              <artifactId>spotbugs</artifactId>
              <version>4.0.0-beta3</version> <!-- {x-version-update;com.github.spotbugs:spotbugs;external_dependency} -->
            </dependency>
          </dependencies>
          <configuration>
            <effort>max</effort>
            <threshold>Low</threshold>
            <xmlOutput>true</xmlOutput>
            <spotbugsXmlOutputDirectory>${project.build.directory}/spotbugs</spotbugsXmlOutputDirectory>
            <excludeFilterFile>spotbugs/spotbugs-exclude.xml</excludeFilterFile>
            <failOnError>true</failOnError>
            <fork>true</fork>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>3.1.0</version> <!-- {x-version-update;org.apache.maven.plugins:maven-checkstyle-plugin;external_dependency} -->
          <configuration>
            <failsOnError>true</failsOnError>
            <failOnViolation>true</failOnViolation>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.0.0-M3</version> <!-- {x-version-update;org.apache.maven.plugins:maven-surefire-plugin;external_dependency} -->
          <configuration>
            <useSystemClassLoader>false</useSystemClassLoader>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

  </build>

  <dependencies>
    <dependency>
      <!-- must be on the classpath -->
      <groupId>org.jacoco</groupId>
      <artifactId>org.jacoco.agent</artifactId>
      <classifier>runtime</classifier>
      <version>0.8.4</version> <!-- {x-version-update;org.jacoco:org.jacoco.agent;external_dependency} -->
      <scope>test</scope>
    </dependency>
  </dependencies>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.1.0</version> <!-- {x-version-update;org.apache.maven.plugins:maven-checkstyle-plugin;external_dependency} -->
        <configuration>
          <configLocation>eng/code-quality-reports/src/main/resources/checkstyle/checkstyle.xml</configLocation>
          <suppressionsLocation>eng/code-quality-reports/src/main/resources/checkstyle/checkstyle-suppressions.xml
          </suppressionsLocation>
          <headerLocation>eng/code-quality-reports/src/main/resources/checkstyle/java.header</headerLocation>
          <propertyExpansion>samedir=</propertyExpansion>
          <encoding>UTF-8</encoding>
          <consoleOutput>true</consoleOutput>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
          <linkXRef>true</linkXRef>
          <failsOnError>true</failsOnError>
          <failOnViolation>true</failOnViolation>
        </configuration>
        <reportSets>
          <reportSet>
            <id>non-aggregate</id>
            <reports>
              <report>checkstyle</report>
            </reports>
          </reportSet>
          <reportSet>
            <id>aggregate</id>
            <inherited>false</inherited>
            <reports>
              <report>checkstyle-aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>********</version> <!-- {x-version-update;com.github.spotbugs:spotbugs-maven-plugin;external_dependency} -->
        <configuration>
          <effort>max</effort>
          <threshold>Low</threshold>
          <xmlOutput>true</xmlOutput>
          <spotbugsXmlOutputDirectory>${project.build.directory}/spotbugs</spotbugsXmlOutputDirectory>
          <excludeFilterFile>eng/code-quality-reports/src/main/resources/spotbugs/spotbugs-exclude.xml
          </excludeFilterFile>
          <failOnError>true</failOnError>
          <fork>true</fork>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.1.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-javadoc-plugin;external_dependency} -->
        <reportSets>
          <reportSet>
            <id>non-aggregate</id>
            <reports>
              <report>javadoc</report>
            </reports>
          </reportSet>
          <reportSet>
            <id>aggregate</id>
            <inherited>false</inherited>
            <reports>
              <report>aggregate</report>
            </reports>
            <configuration>
              <!-- codesnippets4javadoc does not support scanning sub-directories and doesn't support wildcards for filepaths.
                  So, path for aggregate reports have to be defined relative to parent pom -->
              <additionalOptions>-maxLineLength 120
                -snippetpath ${project.basedir}/sdk/appconfiguration/azure-data-appconfiguration/src/samples/java
                -snippetpath ${project.basedir}/sdk/core/azure-core/src/samples/java
                -snippetpath ${project.basedir}/sdk/core/azure-core-http-netty/src/samples/java
                -snippetpath ${project.basedir}/sdk/core/azure-core-http-okhttp/src/samples/java
                -snippetpath ${project.basedir}/sdk/eventhubs/azure-messaging-eventhubs/src/samples/java
                -snippetpath ${project.basedir}/sdk/keyvault/azure-security-keyvault-certificates/src/samples/java
                -snippetpath ${project.basedir}/sdk/keyvault/azure-security-keyvault-keys/src/samples/java
                -snippetpath ${project.basedir}/sdk/keyvault/azure-security-keyvault-secrets/src/samples/java
                -snippetpath ${project.basedir}/sdk/storage/azure-storage-blob/src/samples/java
                -snippetpath ${project.basedir}/sdk/storage/azure-storage-blob-batch/src/samples/java
                -snippetpath ${project.basedir}/sdk/storage/azure-storage-blob-cryptography/src/samples/java
                -snippetpath ${project.basedir}/sdk/storage/azure-storage-common/src/samples/java
                -snippetpath ${project.basedir}/sdk/storage/azure-storage-file-share/src/samples/java
                -snippetpath ${project.basedir}/sdk/storage/azure-storage-queue/src/samples/java
              </additionalOptions>
              <doclint>all</doclint>
            </configuration>
          </reportSet>
        </reportSets>
        <configuration>
          <!-- Reporting is run for JDK 11 - https://github.com/Azure/azure-sdk-for-java/blob/master/.azure-pipelines/client.yml#L90
               Disabling failOnWarnings for reporting for now due to CodeSnippets4Java issue reported for JDK 9+
               https://github.com/Azure/azure-sdk-for-java/issues/3851 -->
          <failOnWarnings>false</failOnWarnings>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>3.0.0</version> <!-- {x-version-update;org.apache.maven.plugins:maven-project-info-reports-plugin;external_dependency} -->
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>summary</report>
              <report>dependency-info</report>
              <report>dependency-management</report>
              <report>dependency-convergence</report>
              <report>ci-management</report>
              <report>dependencies</report>
              <report>issue-management</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>
  <profiles>

    <!-- Skip module-info.java on Java 8 -->
    <profile>
      <id>java8</id>
      <activation>
        <jdk>[1.8,9)</jdk>
      </activation>
      <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
      </properties>
      <build>
        <resources>
          <resource>
            <directory>src/main/resources</directory>
            <filtering>true</filtering>
          </resource>
        </resources>
        <plugins>
          <!-- Don't compile module-info.java, see java 9+ profile -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.8.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-compiler-plugin;external_dependency} -->
            <configuration>
              <source>1.8</source>
              <target>1.8</target>
              <excludes>
                <exclude>module-info.java</exclude>
              </excludes>
              <compilerArgs>
                <!-- Turn off annotation processing -->
                <arg>-proc:none</arg>

                <!-- https://docs.oracle.com/javase/7/docs/technotes/tools/windows/javac.html#xlintwarnings -->
                <arg>-Xlint:cast</arg>
                <arg>-Xlint:classfile</arg>
                <!-- <arg>-Xlint:deprecation</arg> --> <!-- FIXME: We should enable this ASAP -->
                <arg>-Xlint:dep-ann</arg>
                <arg>-Xlint:divzero</arg>
                <arg>-Xlint:empty</arg>
                <arg>-Xlint:fallthrough</arg>
                <arg>-Xlint:finally</arg>
                <arg>-Xlint:options</arg>
                <arg>-Xlint:overrides</arg>
                <arg>-Xlint:path</arg>
                <!-- <arg>-Xlint:processing</arg> -->
                <arg>-Xlint:rawtypes</arg>
                <!-- <arg>-Xlint:serial</arg> -->
                <arg>-Xlint:static</arg>
                <arg>-Xlint:try</arg>
                <arg>-Xlint:unchecked</arg>
                <arg>-Xlint:varargs</arg>
              </compilerArgs>
            </configuration>
          </plugin>
          <!-- Avoid errors from module-info -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.1.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-javadoc-plugin;external_dependency} -->
            <configuration>
              <sourceFileExcludes>
                <sourceFileExclude>module-info.java</sourceFileExclude>
              </sourceFileExcludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
      <reporting>
        <plugins>
          <!-- Avoid errors from module-info -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.1.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-javadoc-plugin;external_dependency} -->
            <configuration>
              <sourceFileExcludes>
                <sourceFileExclude>module-info.java</sourceFileExclude>
              </sourceFileExcludes>
            </configuration>
          </plugin>
        </plugins>
      </reporting>
    </profile>

    <!-- Setup for Java 11+ -->
    <profile>
      <id>java-lts</id>
      <activation>
        <jdk>[11,)</jdk>
      </activation>
      <build>
        <resources>
          <resource>
            <directory>src/main/resources</directory>
            <filtering>true</filtering>
          </resource>
        </resources>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.8.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-compiler-plugin;external_dependency} -->
            <configuration>
              <testRelease>11</testRelease>
              <compilerArgs>
                <!-- Turn off annotation processing -->
                <arg>-proc:none</arg>

                <!-- https://docs.oracle.com/javase/7/docs/technotes/tools/windows/javac.html#xlintwarnings -->
                <arg>-Xlint:cast</arg>
                <arg>-Xlint:classfile</arg>
                <!-- <arg>-Xlint:deprecation</arg> --> <!-- FIXME: We should enable this ASAP -->
                <arg>-Xlint:dep-ann</arg>
                <arg>-Xlint:divzero</arg>
                <arg>-Xlint:empty</arg>
                <arg>-Xlint:fallthrough</arg>
                <arg>-Xlint:finally</arg>
                <arg>-Xlint:options</arg>
                <arg>-Xlint:overrides</arg>
                <arg>-Xlint:path</arg>
                <!-- <arg>-Xlint:processing</arg> -->
                <arg>-Xlint:rawtypes</arg>
                <!-- <arg>-Xlint:serial</arg> -->
                <arg>-Xlint:static</arg>
                <arg>-Xlint:try</arg>
                <arg>-Xlint:unchecked</arg>
                <arg>-Xlint:varargs</arg>
                <arg>-Xlint:-module</arg> <!-- FIXME: this is required for now as it introduces a build failure -->
                <arg>-Xlint:-requires-transitive-automatic</arg> <!-- FIXME: this is required for now as it introduces a build failure -->
              </compilerArgs>
            </configuration>
            <executions>
              <!-- compile first with module-info for Java 9+ -->
              <execution>
                <id>default-compile</id>
                <configuration>
                  <release>11</release>
                </configuration>
              </execution>
              <!-- then compile without module-info for Java 8 -->
              <execution>
                <id>base-compile</id>
                <goals>
                  <goal>compile</goal>
                </goals>
                <configuration>
                  <release>8</release>
                  <excludes>
                    <exclude>module-info.java</exclude>
                  </excludes>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <!-- Surefire plugin is broken, https://issues.apache.org/jira/browse/SUREFIRE-1501 -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>3.0.0-M3</version> <!-- {x-version-update;org.apache.maven.plugins:maven-surefire-plugin;external_dependency} -->
            <configuration>
              <argLine>
                <!-- KeyVault tests fail without these exports. -->
                --add-exports com.azure.core/com.azure.core.implementation.http=ALL-UNNAMED
                --add-exports com.azure.core/com.azure.core.implementation.serializer=ALL-UNNAMED
                --add-exports com.azure.core/com.azure.core.implementation.serializer.jackson=ALL-UNNAMED
                --add-exports com.azure.core/com.azure.core.implementation.util=ALL-UNNAMED

                <!-- Needed for JUnit offline instrumentation -->
                --add-reads com.azure.core=ALL-UNNAMED
                --add-reads com.azure.core.test=ALL-UNNAMED
                --add-reads com.azure.core.amqp=ALL-UNNAMED

                <!-- JUnit5 and Mockito use reflection to create objects. -->
                --add-opens com.azure.core/com.azure.core=ALL-UNNAMED
                --add-opens com.azure.core/com.azure.core.util.polling=ALL-UNNAMED
                --add-opens com.azure.core/com.azure.core.http.policy=ALL-UNNAMED
                --add-opens com.azure.core/com.azure.core.util=ALL-UNNAMED

                <!-- AMQP tests fail without this. ArgumentCaptor requires reflection. -->
                --add-opens com.azure.core.amqp/com.azure.core.amqp.implementation=ALL-UNNAMED
                --add-opens com.azure.core.amqp/com.azure.core.amqp.implementation.handler=ALL-UNNAMED

                <!-- Checkpoint store tests fail without this -->
                --add-opens com.azure.messaging.eventhubs.checkpointstore.blob/com.azure.messaging.eventhubs.checkpointstore.blob=ALL-UNNAMED
                --add-reads com.azure.messaging.eventhubs=ALL-UNNAMED

                <!-- Azure-core needs to open the test entities to Jackson for unit tests to operate -->
                --add-opens com.azure.core/com.azure.core.implementation.entities=com.fasterxml.jackson.databind
                --add-opens com.azure.core/com.azure.core.implementation.entities=ALL-UNNAMED
              </argLine>
              <useSystemClassLoader>false</useSystemClassLoader>
              <forkCount>1</forkCount>
              <testFailureIgnore>false</testFailureIgnore>
              <systemPropertyVariables>
                <jacoco-agent.destfile>${project.build.directory}/jacoco.exec</jacoco-agent.destfile>
              </systemPropertyVariables>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- Due to the codesnippet4javadoc tool using backward incompatible javadoc classes, build has
      javadoc warnings. Build failures on javadoc warnings are disabled on JDK 9+ for now.
      Here's the github issue - https://github.com/jtulach/codesnippet4javadoc/issues/14 -->
    <profile>
      <id>javadoc-doclet-compatibility</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.1.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-javadoc-plugin;external_dependency} -->
            <configuration>
              <failOnWarnings>false</failOnWarnings>

              <!-- For codesnippet4javadoc tool to work with JDK9+, it is recommended to add this additional option
              https://github.com/jtulach/codesnippet4javadoc#use-with-jdk9 -->
              <additionalJOptions>
                <opt>-J--add-opens=jdk.javadoc/com.sun.tools.javadoc.main=ALL-UNNAMED</opt>
              </additionalJOptions>

            </configuration>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <failOnError>false</failOnError>
                  <failOnWarnings>false</failOnWarnings>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>non-shipping-modules</id>
      <activation>
        <property>
          <name>include-non-shipping-modules</name>
        </property>
      </activation>
      <modules>
        <module>./eng/spotbugs-aggregate-report</module>
        <module>./eng/code-quality-reports</module>
        <module>./eng/jacoco-test-coverage</module>
      </modules>
    </profile>

    <profile>
      <id>template-module</id>
      <activation>
        <property>
          <name>env.ENABLETEMPLATEDOCS</name>
          <value>true</value>
        </property>
      </activation>
      <modules>
        <module>./sdk/template/azure-sdk-template</module>
      </modules>
    </profile>

    <!-- Skip overview generation unless -Dgenerate-overview is passed as an argument.
      This is specifically done to prevent requiring python as a pre-requisite since
      this is only needed by the build system. -->
    <profile>
      <id>generate-overview-from-readme</id>
      <activation>
        <property>
          <name>generate-overview</name>
        </property>
        <file>
          <exists>../../../eng/pipelines/scripts/generate_overview_from_readme.py</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>exec-maven-plugin</artifactId>
              <version>1.2.1</version> <!-- {x-version-update;org.codehaus.mojo:exec-maven-plugin;external_dependency} -->
              <executions>
                  <execution>
                      <id>generate-overview-from-readme</id>
                      <phase>prepare-package</phase>
                      <goals>
                          <goal>exec</goal>
                      </goals>
                  </execution>
              </executions>
              <configuration>
                <executable>python</executable>
                <workingDirectory>${project.basedir}</workingDirectory>
                <arguments>
                      <argument>../../../eng/pipelines/scripts/generate_overview_from_readme.py</argument>
                      <argument>--rf</argument>
                      <argument>${project.basedir}/README.md</argument>
                      <argument>--v</argument>
                      <argument>${project.version}</argument>
                </arguments>
              </configuration>
          </plugin>
          <!-- Add the overview argument to the javadoc args -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.1.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-javadoc-plugin;external_dependency} -->
            <configuration combine.children="append">
              <overview>${project.basedir}/readme_overview.html</overview>
            </configuration>
          </plugin>
         </plugins>
      </build>
    </profile>
  </profiles>

  <modules>
    <module>sdk/appconfiguration/azure-data-appconfiguration</module>
    <module>sdk/core/azure-core</module>
    <module>sdk/core/azure-core-amqp</module>
<!--    <module>sdk/core/azure-core-management</module>--> <!-- Removed for now, as this library is unused -->
    <module>sdk/core/azure-core-http-netty</module>
    <module>sdk/core/azure-core-http-okhttp</module>
    <module>sdk/core/azure-core-test</module>
    <module>sdk/core/azure-core-tracing-opencensus</module>
    <module>sdk/core/azure-core-tracing-opentelemetry</module>
    <module>sdk/eventhubs/azure-messaging-eventhubs</module>
    <module>sdk/eventhubs/azure-messaging-eventhubs-checkpointstore-blob</module>
    <module>sdk/identity/azure-identity</module>
    <module>sdk/keyvault/azure-security-keyvault-certificates</module>
    <module>sdk/keyvault/azure-security-keyvault-keys</module>
    <module>sdk/keyvault/azure-security-keyvault-secrets</module>
    <module>sdk/storage/azure-storage-blob</module>
    <module>sdk/storage/azure-storage-blob-batch</module>
    <module>sdk/storage/azure-storage-blob-cryptography</module>
    <module>sdk/storage/azure-storage-common</module>
    <module>sdk/storage/azure-storage-file-share</module>
    <module>sdk/storage/azure-storage-file-datalake</module>
    <module>sdk/storage/azure-storage-queue</module>
    <module>sdk/storage/azure-storage-queue-cryptography</module>
  </modules>
</project>
