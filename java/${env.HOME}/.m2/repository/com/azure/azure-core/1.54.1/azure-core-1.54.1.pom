<!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.azure</groupId>
    <artifactId>azure-client-sdk-parent</artifactId>
    <version>1.7.0</version> <!-- {x-version-update;com.azure:azure-client-sdk-parent;current} -->
    <relativePath>../../parents/azure-client-sdk-parent</relativePath>
  </parent>

  <groupId>com.azure</groupId>
  <artifactId>azure-core</artifactId>
  <packaging>jar</packaging>
  <version>1.54.1</version> <!-- {x-version-update;com.azure:azure-core;current} -->

  <name>Microsoft Azure Java Core Library</name>
  <description>This package contains core types for Azure Java clients.</description>
  <url>https://github.com/Azure/azure-sdk-for-java</url>

  <licenses>
    <license>
      <name>The MIT License (MIT)</name>
      <url>http://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <distributionManagement>
    <site>
      <id>azure-java-build-docs</id>
      <url>${site.url}/site/${project.artifactId}</url>
    </site>
  </distributionManagement>

  <scm>
    <url>https://github.com/Azure/azure-sdk-for-java</url>
    <connection>scm:git:https://github.com/Azure/azure-sdk-for-java.git</connection>
    <developerConnection>scm:git:https://github.com/Azure/azure-sdk-for-java.git</developerConnection>
  </scm>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <legal><![CDATA[[INFO] Any downloads listed may be third party software.  Microsoft grants you no rights for third party software.]]></legal>
    <jacoco.min.linecoverage>0.60</jacoco.min.linecoverage>
    <jacoco.min.branchcoverage>0.60</jacoco.min.branchcoverage>
    <javaModulesSurefireArgLine>
      --add-exports com.azure.core/com.azure.core.implementation.http=ALL-UNNAMED
      --add-exports com.azure.core/com.azure.core.implementation.http.rest=ALL-UNNAMED
      --add-exports com.azure.core/com.azure.core.implementation.serializer=ALL-UNNAMED
      --add-exports com.azure.core/com.azure.core.implementation.jackson=ALL-UNNAMED

      --add-opens com.azure.core/com.azure.core=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.credential=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.http=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.http.policy=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.http.rest=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.implementation=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.implementation.http=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.implementation.http.rest=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.implementation.models.jsonflatten=com.fasterxml.jackson.databind
      --add-opens com.azure.core/com.azure.core.implementation.models.jsonflatten=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.implementation.serializer=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.implementation.jackson=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.implementation.logging=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.models=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.util=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.util.tracing=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.util.logging=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.util.polling=ALL-UNNAMED
      --add-opens com.azure.core/com.azure.core.util.serializer=ALL-UNNAMED

      --add-reads com.azure.core=ALL-UNNAMED
    </javaModulesSurefireArgLine>

    <!-- If JMH benchmarking was ran Checkstyle may fail on jmh_benchmark generated classes, ignore them. -->
    <checkstyle.excludes>**/generated/**/*.java</checkstyle.excludes>

    <!-- Enables fail on deprecated API usage. -->
    <compiler.failondeprecatedstatus/>
    <javadoc.excludePackageNames>com.azure.json</javadoc.excludePackageNames>

    <checkstyle.suppressionsLocation>checkstyle-suppressions.xml</checkstyle.suppressionsLocation>

    <spotbugs.skip>false</spotbugs.skip>
    <spotbugs.excludeFilterFile>spotbugs-exclude.xml</spotbugs.excludeFilterFile>

    <animal.sniffer.skip>false</animal.sniffer.skip>
    <animal.sniffer.ignores>javax.xml.stream.XMLStreamException,java.lang.invoke.MethodHandle,java.lang.invoke.MethodHandles</animal.sniffer.ignores>
  </properties>

  <developers>
    <developer>
      <id>microsoft</id>
      <name>Microsoft</name>
    </developer>
  </developers>

  <dependencies>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-json</artifactId>
      <version>1.3.0</version> <!-- {x-version-update;com.azure:azure-json;dependency} -->
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-xml</artifactId>
      <version>1.1.0</version> <!-- {x-version-update;com.azure:azure-xml;dependency} -->
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.17.2</version> <!-- {x-version-update;com.fasterxml.jackson.core:jackson-annotations;external_dependency} -->
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.17.2</version> <!-- {x-version-update;com.fasterxml.jackson.core:jackson-core;external_dependency} -->
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.17.2</version> <!-- {x-version-update;com.fasterxml.jackson.core:jackson-databind;external_dependency} -->
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>2.17.2</version> <!-- {x-version-update;com.fasterxml.jackson.datatype:jackson-datatype-jsr310;external_dependency} -->
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.36</version> <!-- {x-version-update;org.slf4j:slf4j-api;external_dependency} -->
    </dependency>

    <!-- Added this dependency to include necessary annotations used by reactor core.
    Without this dependency, javadoc throws a warning as it cannot find enum When.MAYBE
    which is used in @Nullable annotation in reactor core classes -->
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version> <!-- {x-version-update;com.google.code.findbugs:jsr305;external_dependency} -->
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-core</artifactId>
      <version>3.4.41</version> <!-- {x-version-update;io.projectreactor:reactor-core;external_dependency} -->
    </dependency>

    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-json</artifactId>
      <version>1.3.0</version> <!-- {x-version-update;com.azure:azure-json;dependency} -->
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-xml</artifactId>
      <version>2.17.2</version> <!-- {x-version-update;com.fasterxml.jackson.dataformat:jackson-dataformat-xml;external_dependency} -->
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-test</artifactId>
      <version>3.4.41</version> <!-- {x-version-update;io.projectreactor:reactor-test;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.11.2</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-api;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.11.2</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-engine;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.11.2</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-params;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <version>2.2</version> <!-- {x-version-update;org.hamcrest:hamcrest-library;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-core</artifactId>
      <version>1.37</version> <!-- {x-version-update;org.openjdk.jmh:jmh-core;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-generator-annprocess</artifactId>
      <version>1.37</version> <!-- {x-version-update;org.openjdk.jmh:jmh-generator-annprocess;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>javax.json</groupId>
      <artifactId>javax.json-api</artifactId>
      <version>1.1.4</version>  <!-- {x-version-update;javax.json:javax.json-api;external_dependency} -->
      <scope>test</scope>
    </dependency><dependency>
    <groupId>org.eclipse.jetty</groupId>
    <artifactId>jetty-server</artifactId>
    <version>9.4.56.v20240826</version> <!-- {x-version-update;org.eclipse.jetty:jetty-server;external_dependency} -->
    <scope>test</scope>
  </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-servlet</artifactId>
      <version>9.4.56.v20240826</version> <!-- {x-version-update;org.eclipse.jetty:jetty-servlet;external_dependency} -->
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.5.0</version> <!-- {x-version-update;org.apache.maven.plugins:maven-enforcer-plugin;external_dependency} -->
        <configuration>
          <rules>
            <bannedDependencies>
              <includes>
                <include>io.projectreactor:reactor-core:[3.4.41]</include> <!-- {x-include-update;io.projectreactor:reactor-core;external_dependency} -->
                <include>com.fasterxml.jackson.core:jackson-annotations:[2.17.2]</include> <!-- {x-include-update;com.fasterxml.jackson.core:jackson-annotations;external_dependency} -->
                <include>com.fasterxml.jackson.core:jackson-core:[2.17.2]</include> <!-- {x-include-update;com.fasterxml.jackson.core:jackson-core;external_dependency} -->
                <include>com.fasterxml.jackson.core:jackson-databind:[2.17.2]</include> <!-- {x-include-update;com.fasterxml.jackson.core:jackson-databind;external_dependency} -->
                <include>com.fasterxml.jackson.dataformat:jackson-dataformat-xml:[2.17.2]</include> <!-- {x-include-update;com.fasterxml.jackson.dataformat:jackson-dataformat-xml;external_dependency} -->
                <include>com.fasterxml.jackson.datatype:jackson-datatype-jsr310:[2.17.2]</include> <!-- {x-include-update;com.fasterxml.jackson.datatype:jackson-datatype-jsr310;external_dependency} -->
                <include>org.slf4j:slf4j-api:[1.7.36]</include> <!-- {x-include-update;org.slf4j:slf4j-api;external_dependency} -->
              </includes>
            </bannedDependencies>
          </rules>
        </configuration>
      </plugin>

      <!-- This plugin scans reports spotbugs in the code -->
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>4.8.3.1</version> <!-- {x-version-update;com.github.spotbugs:spotbugs-maven-plugin;external_dependency} -->
        <configuration>
          <plugins>
            <plugin>
              <groupId>com.h3xstream.findsecbugs</groupId>
              <artifactId>findsecbugs-plugin</artifactId>
              <version>1.9.0</version> <!-- {x-version-update;com.h3xstream.findsecbugs:findsecbugs-plugin;external_dependency} -->
            </plugin>
          </plugins>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>3.7.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-assembly-plugin;external_dependency} -->
        <executions>
          <execution>
            <id>package-test-jar</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <descriptors>
                <descriptor>src/test/assembly/assembly.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>jmh-benchmark</id>
      <activation>
        <property>
          <name>jmh-benchmark</name>
        </property>
      </activation>

      <properties>
        <!-- JMH needs annotation processing on, so disable proc == none -->
        <compiler.proc></compiler.proc>
      </properties>

      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.13.0</version> <!-- {x-version-update;org.apache.maven.plugins:maven-compiler-plugin;external_dependency} -->
            <configuration>
              <!-- Add the JMH annotation processor to the classpath. -->
              <annotationProcessorPaths>
                <path>
                  <groupId>org.openjdk.jmh</groupId>
                  <artifactId>jmh-generator-annprocess</artifactId>
                  <version>1.37</version> <!-- {x-version-update;org.openjdk.jmh:jmh-generator-annprocess;external_dependency} -->
                </path>
              </annotationProcessorPaths>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
