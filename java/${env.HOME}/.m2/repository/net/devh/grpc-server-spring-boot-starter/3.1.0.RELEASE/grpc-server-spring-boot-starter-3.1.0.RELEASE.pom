<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Grad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>net.devh</groupId>
  <artifactId>grpc-server-spring-boot-starter</artifactId>
  <version>3.1.0.RELEASE</version>
  <name>gRPC Spring Boot Starter</name>
  <description>gRPC Spring Boot Starter</description>
  <url>https://github.com/yidongnan/grpc-spring-boot-starter</url>
  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>https://opensource.org/licenses/Apache-2.0</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>yidongnan</id>
      <name>Michael <PERSON></name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>ST-DDT</id>
      <name>Daniel Theuke</name>
      <email><EMAIL></email>
      <organization>Aequitas Software GmbH &amp; Co. KG</organization>
      <organizationUrl>https://aequitas-software.de/</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/yidongnan/grpc-spring-boot-starter.git</connection>
    <developerConnection>scm:git:<EMAIL>:yidongnan/grpc-spring-boot-starter.git</developerConnection>
    <url>https://github.com/yidongnan/grpc-spring-boot-starter</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>net.devh</groupId>
      <artifactId>grpc-common-spring-boot</artifactId>
      <version>3.1.0.RELEASE</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
      <version>3.2.4</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-inprocess</artifactId>
      <version>1.63.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-netty-shaded</artifactId>
      <version>1.63.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf</artifactId>
      <version>1.63.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-stub</artifactId>
      <version>1.63.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-services</artifactId>
      <version>1.63.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-api</artifactId>
      <version>1.63.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-netty</artifactId>
      <version>1.63.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-observation</artifactId>
      <version>1.12.4</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
      <version>3.2.4</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-core</artifactId>
      <version>6.2.3</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-consul-discovery</artifactId>
      <version>4.1.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
      <version>4.1.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
      <version>4.1.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
      <version>2022.0.0.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>io.zipkin.brave</groupId>
      <artifactId>brave-instrumentation-grpc</artifactId>
      <version>5.16.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.github.vlsi.compactmap</groupId>
          <artifactId>compactmap</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.andrewoma.dexx</groupId>
          <artifactId>dexx-collections</artifactId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-bom</artifactId>
        <version>1.9.23</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>5.10.2</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-bom</artifactId>
        <version>1.63.0</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava-bom</artifactId>
        <version>33.1.0-jre</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-bom</artifactId>
        <version>3.25.3</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-dependencies</artifactId>
        <version>2022.0.0.0</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>2023.0.0</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.4</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
