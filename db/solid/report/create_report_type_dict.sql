-- liquibase formatted sql

-- changeset shoushan:create_report_type_dict logicalFilePath:changelog.sql
CREATE TABLE report_type (
  id            serial NOT NULL PRIMARY KEY,
  key           varchar(16),
  name          varchar(64) NOT NULL,
  is_deleted    bool NOT NULL DEFAULT FALSE,
  created_by    varchar(32) NOT NULL,
  created_at    TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_by    varchar(32),
  updated_at    TIMESTAMP NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX ON report_type (key);
CREATE UNIQUE INDEX ON report_type (name);

INSERT INTO report_type (id, key, name, created_by) VALUES (1, 'DAR', 'Premium Damage Assessment Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (2, 'PMR', 'Premium Measurement Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (4, 'MXR', 'Macro XML Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (5, 'QDAR', 'Preliminary Damage Assessment Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (7, 'QRER', 'Highfly Evaluation Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (8, 'RRIR', 'Real-time Damage Assessment Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (9, 'SXR', 'Symbility XML Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (10, 'WR', 'Weather Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (11, 'NR', 'Estimate Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (12, 'DR', 'Measurement DXF Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (13, 'BR', 'On-Site Bidding Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (14, 'QSR', 'Real-time Quick Square Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (15, 'PIR', 'Property Image Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (16, 'IDAR', 'Infrared Damage Assessment Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (17, 'ROR', 'Roof-only Underwriting Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (18, 'FUR', 'Full-scope Underwriting Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (19, 'ICR', 'Inspection Closeout Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (20, 'CDF', 'Claim Damage Form', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (21, 'CRR', 'Post-Construction Audit Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (22, 'HPR', 'HOVER Pro Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (23, 'HE', 'HOVER', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (24, 'XE', 'XACT', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (25, 'INV', 'Invoice', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (26, 'MP', 'MagicPlan report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (27, 'HIS', 'Homeowner Inspection Survey Results', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (32, 'CHECKLIST', 'Checklist', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (33, 'CLA', 'Claim Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (34, 'GLR', 'General Loss Report', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (37, 'DPS', 'Drone Photo Sheet', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (38, 'MPS', 'Mobile Photo Sheet', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (39, 'PLNR', 'Plnar', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (40, 'CUBI', 'Cubicasa', '10000');
INSERT INTO report_type (id, key, name, created_by) VALUES (41, 'HTLA', 'Hover TLA', '10000');

-- rollback DROP TABLE IF EXISTS report_type;
