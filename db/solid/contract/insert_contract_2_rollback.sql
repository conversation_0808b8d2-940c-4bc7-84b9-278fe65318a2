DO
$$
    DECLARE
        counter RECORD;
    BEGIN
        FOR counter IN select *
                       from customer
                       where name in (
                                      'Olympus Insurance',
                                      'American Modern Insurance'
                           )
            loop
                delete from contract where insured_by = counter.id and processed_by = counter.id;
            END LOOP;
    END;
$$;

GO

DO
$$
    DECLARE
        processedBy bigint;
    BEGIN
        select id into processedBy from customer where name = 'Bees360 Test Carrier';
        delete from contract where insured_by is null and processed_by = processedBy;
    END;
$$;
