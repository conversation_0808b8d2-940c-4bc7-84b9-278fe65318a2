-- liquibase formatted sql
-- changeset xiaojun:add_contract_service_item_express_inspection logicalFilePath:changelog.sql

INSERT INTO public.contract_service(key, display_name, default_price, non_taxable)
VALUES ('Express Inspection Base', 'Express Inspection Base', 400, false)
ON CONFLICT DO NOTHING;

INSERT INTO public.contract_service(key, display_name, default_price, non_taxable)
VALUES ('Express Inspection Base(discount)', 'Express Inspection Base(discount)', 300, false)
ON CONFLICT DO NOTHING;

-- rollback DELETE FROM contract_service WHERE key = 'Express Inspection Base';
-- rollback DELETE FROM contract_service WHERE key = 'Express Inspection Base(discount)';
