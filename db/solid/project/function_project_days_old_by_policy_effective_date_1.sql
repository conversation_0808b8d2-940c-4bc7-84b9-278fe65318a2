CREATE OR REPLACE FUNCTION update_project_days_old_by_policy_effective_date() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    DECLARE
        p_id                    BIGINT;
        sum_end_data            BIGINT;
        sum_open_date           BIGINT;
        sum_days_old            INT;
        start_state             project_state_enum;
        effective_ended_at      TIMESTAMP;
        opened_or_effective_time        TIMESTAMP;
        project_created_at      TIMESTAMP;
        state_open              project_state_enum := ''PROJECT_OPEN'';
        effective_time          TIMESTAMP;
        project_started_at      TIMESTAMP;
        project_updated_at      TIMESTAMP;
    BEGIN
        SELECT id, created_at, NEW.policy_effective_date::TIMESTAMP 
        INTO p_id, project_created_at, effective_time FROM project WHERE policy_id = NEW.id;
        
        IF p_id IS NULL THEN
            RETURN NULL;
        END IF;
        SELECT pdo.opened_or_effective_at INTO opened_or_effective_time FROM project_days_old pdo 
        WHERE project_id = p_id;
        IF effective_time > NOW() THEN 
            IF opened_or_effective_time IS NOT NULL THEN
                opened_or_effective_time = effective_time;
            END IF;
            UPDATE project_days_old SET opened_or_effective_at = opened_or_effective_time, pre_days_old = 0
            WHERE project_id = p_id;
            RETURN NULL;
        END IF;
        
        -- count each days of open period after project stated at
        SELECT GREATEST(project_created_at, NEW.policy_effective_date) INTO project_started_at;
        -- If the relative project already existed, check if the days old should be updated.
        -- recount pre days old by adding each duration of open state
        SELECT COALESCE(SUM(EXTRACT(EPOCH FROM (created_at))), 0) INTO sum_open_date FROM project_state_history
        WHERE project_id = p_id AND project_state = state_open AND created_at > project_started_at;
                    
        SELECT COALESCE(SUM(EXTRACT(EPOCH FROM (created_at))), 0) INTO sum_end_data FROM project_state_history
        WHERE project_id = p_id AND is_changed_from_open = true AND created_at > project_started_at;
                    
        -- If policy effective date was in a project_open state period,
        -- the sum days old should add the duration days from the effective data to the end of this state
        SELECT project_state INTO start_state FROM project_state_history 
        WHERE created_at <= project_started_at and project_id = p_id ORDER BY created_at DESC limit 1;
            
        IF start_state = state_open AND project_started_at < NOW() THEN 
            SELECT EXTRACT(EPOCH FROM (project_started_at)) + sum_open_date
            INTO sum_open_date;
        END IF;
                
        -- IF current state is open, should minus last updated at
        IF opened_or_effective_time IS NOT NULL THEN
        
            SELECT updated_at INTO project_updated_at FROM project_state WHERE project_id = p_id;

            SELECT sum_open_date - EXTRACT(EPOCH FROM (GREATEST(project_updated_at, project_started_at))), 
            GREATEST(project_updated_at, project_started_at)
            INTO sum_open_date, opened_or_effective_time;
            
        END IF;

        SELECT floor((sum_end_data - sum_open_date) / 86400) INTO sum_days_old;

        UPDATE project_days_old SET pre_days_old = sum_days_old, opened_or_effective_at = opened_or_effective_time 
        WHERE project_id = p_id;
        
        RETURN NULL;
   END;
';
