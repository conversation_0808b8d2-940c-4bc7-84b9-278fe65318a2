-- liquibase formatted sql
-- changeset zhifa:add_trigger_to_project_days_old logicalFilePath:changelog.sql

CREATE OR REPLACE FUNCTION notify_project_days_old_changed() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    DECLARE
        row         RECORD;
        event       JSON;
    BEGIN
            SELECT INTO row
                id,
                project_id,
                pre_days_old,
                to_char(opened_or_effective_at at time zone ''UTC'', ''YYYY-MM-DD"T"HH24:MI:SS.US"Z"'') as opened_or_effective_at
            FROM project_days_old
            WHERE id = NEW.id;

            event = json_build_object(
                    ''routing_key'', ''project_days_old_changed'',
                    ''payload'', row_to_json(row)
                );
            PERFORM pg_notify(''publish_event''::text, event::text);
        RETURN NULL;
    END;
';

CREATE TRIGGER trigger_notify_project_days_old_changed
    AFTER INSERT OR UPDATE
    ON project_days_old
    FOR EACH ROW
    EXECUTE PROCEDURE notify_project_days_old_changed();

-- rollback DROP TRIGGER IF EXISTS trigger_notify_project_days_old_changed ON project_days_old;
-- rollback DROP FUNCTION IF EXISTS notify_project_days_old_changed;
