CREATE OR REPLACE FUNCTION update_project_process_status() <PERSON><PERSON><PERSON>NS TRIGGER
    LANGUAGE plpgsql
AS
'
    BEGIN
        INSERT INTO project_process_status (project_id, status, updated_by, created_at, updated_at, comment)
        SELECT NEW.project_id, NEW.status, NEW.updated_by, NEW.updated_at, NEW.updated_at, NEW.comment
        WHERE NOT EXISTS (SELECT project_id
                            FROM project_process_status
                            WHERE project_id = NEW.project_id
                            AND updated_at > NEW.updated_at)
        ON CONFLICT (project_id)
        DO UPDATE
        SET status = NEW.status,
            updated_by = NEW.updated_by,
            updated_at = NEW.updated_at,
            comment = NEW.comment;

        RETURN NULL;
    END;
';
