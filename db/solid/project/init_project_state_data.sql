-- liquibase formatted sql
-- changeset dunhong:init_project_state_data logicalFilePath:changelog.sql

BEGIN;
ALTER TABLE project_state DISABLE TRIGGER set_project_state_update_at;

-- init project state to open
INSERT INTO public.project_state(
    project_id,
    project_state,
    last_state_change_reason_id,
    created_at,
    updated_at,
     updated_by)
SELECT P.id, 'PROJECT_OPEN', 1, P.created_at, P.created_at, ps.updated_by
    FROM project P
    JOIN project_status ps
    ON P.id = ps.project_id
ON conflict(project_id) do nothing;

-- init project state to close
-- 1. fix Test project，days old 大于30的                     [changeReason -> Test/Demo Case] [12]
-- 2. policy # 或者claim # 字符里面含有test的case               [changeReason -> Test/Demo Case] [12]
-- 3. operation tag 是 DENIED等 之一时,                        [changeReason -> Operation Tag related]
-- 4. Project Canceled,                                      [changeReason -> Client Cancelled] [14]
-- 5. Client Received,                                       [changeReason -> Completed] [15]
-- 6. claim 最后更新状态为Return to client且状态变更时间大于5个月,  [changeReason -> Completed] [15]
-- 7. underwriting 项目状态 Return to client,                  [changeReason -> Completed] [15]
-- 8. days old大于365，且没有close的case,                       [changeReason -> Test/Demo Case] [12]

-- 1. fix Test project，days old 大于30的                     [changeReason -> Test/Demo Case] [12]
UPDATE project_state pst
SET project_state = 'PROJECT_CLOSE',
last_state_change_reason_id = 12,
updated_at = ps.updated_at,
updated_by = ps.updated_by
FROM
	project
	P JOIN project_status ps ON P.id = ps.project_id
	JOIN policy ON P.policy_id = policy.id
WHERE
	pst.project_id = P.id
	AND project_state != 'PROJECT_CLOSE'
	AND P.is_test_case = TRUE
	AND GREATEST ( policy.policy_effective_date, P.created_at :: DATE ) < NOW( ) - INTERVAL '30 DAY';

-- 2. policy # 或者claim # 字符里面含有test的case               [changeReason -> Test/Demo Case] [12]
UPDATE project_state pst
SET project_state = 'PROJECT_CLOSE',
last_state_change_reason_id = 12,
updated_at = ps.updated_at,
updated_by = ps.updated_by
FROM
	project
	P JOIN project_status ps ON P.id = ps.project_id
	JOIN policy ON P.policy_id = policy.
	id LEFT JOIN project_claim pc ON pc.project_id = P.id
WHERE
	pst.project_id = P.id
	AND project_state != 'PROJECT_CLOSE'
	AND ( policy.policy_no ~* '.*test.*' OR pc.claim_no ~* '.*test.*');

-- 3. operation tag 是 DENIED等 之一时,                        [changeReason -> Operation Tag related]
create table tmp_operation_tag_to_change_reason (
    operation_tag_id integer UNIQUE,
    change_reason_id integer UNIQUE
);

insert into tmp_operation_tag_to_change_reason VALUES(2, 6);
insert into tmp_operation_tag_to_change_reason VALUES(23, 7);
insert into tmp_operation_tag_to_change_reason VALUES(11, 8);
insert into tmp_operation_tag_to_change_reason VALUES(26, 9);
insert into tmp_operation_tag_to_change_reason VALUES(20, 10);

UPDATE project_state pst
SET project_state = 'PROJECT_CLOSE',
last_state_change_reason_id = (
		select change_reason_id
		from tmp_operation_tag_to_change_reason
		where operation_tag_id = potp.operation_tag_id) ,
updated_at = ps.updated_at,
updated_by = ps.updated_by
FROM
	project
	P JOIN project_status ps ON P.id = ps.project_id
	JOIN project_operation_tag_project potp on potp.project_id = P.id
WHERE
	pst.project_id = P.id
	AND project_state != 'PROJECT_CLOSE'
	AND potp.deleted = ''
	AND potp.operation_tag_id in
	(2,11, 20, 23, 26);

drop table tmp_operation_tag_to_change_reason;


-- 4. Project Canceled,                                      [changeReason -> Client Cancelled] [14]
UPDATE project_state pst
SET project_state = 'PROJECT_CLOSE',
last_state_change_reason_id = 14,
updated_at = ps.updated_at,
updated_by = ps.updated_by
FROM
	project
	P JOIN project_status ps ON P.id = ps.project_id
WHERE
	pst.project_id = P.id
	AND project_state != 'PROJECT_CLOSE'
	AND ps.status = 110;

-- 5. Client Received,                                       [changeReason -> Completed] [15]
UPDATE project_state pst
SET project_state = 'PROJECT_CLOSE',
last_state_change_reason_id = 15,
updated_at = ps.updated_at,
updated_by = ps.updated_by
FROM
	project
	P JOIN project_status ps ON P.id = ps.project_id
WHERE
	pst.project_id = P.id
	AND project_state != 'PROJECT_CLOSE'
	AND ps.status = 100;

-- 6. claim 最后更新状态为Return to client且状态变更时间大于5个月,  [changeReason -> Completed] [15]
UPDATE project_state pst
SET project_state = 'PROJECT_CLOSE',
last_state_change_reason_id = 15,
updated_at = ps.updated_at,
updated_by = ps.updated_by
FROM
	project P
	JOIN project_status ps ON P.id = ps.project_id
	JOIN project_claim pc on P.id  = pc.project_id
WHERE
	pst.project_id = P.id
	AND project_state != 'PROJECT_CLOSE'
	AND ps.status = 100
	AND ps.updated_at < NOW() - INTERVAL '5 MONTH';

-- 7. underwriting 项目状态 Return to client,                  [changeReason -> Completed] [15]
UPDATE project_state pst
SET project_state = 'PROJECT_CLOSE',
last_state_change_reason_id = 15,
updated_at = ps.updated_at,
updated_by = ps.updated_by
FROM
	project P
	JOIN project_status ps ON P.id = ps.project_id
	JOIN project_underwriting pc on P.id  = pc.project_id
WHERE
	pst.project_id = P.id
	AND project_state != 'PROJECT_CLOSE'
	AND ps.status = 90;

-- 8. days old大于365，且没有close的case,                       [changeReason -> Test/Demo Case] [12]
UPDATE project_state pst
SET project_state = 'PROJECT_CLOSE',
last_state_change_reason_id = 12,
updated_at = ps.updated_at,
updated_by = ps.updated_by
FROM
	project
	P JOIN project_status ps ON P.id = ps.project_id
	JOIN policy ON P.policy_id = policy.id
WHERE
	pst.project_id = P.id
	AND project_state != 'PROJECT_CLOSE'
	AND GREATEST ( policy.policy_effective_date, P.created_at :: DATE ) < NOW( ) - INTERVAL '365 DAY';

ALTER TABLE project_state ENABLE TRIGGER set_project_state_update_at;
COMMIT;
-- rollback DELETE FROM project_state;
-- rollback DELETE FROM project_state_history;
-- rollback DELETE FROM project_days_old;
