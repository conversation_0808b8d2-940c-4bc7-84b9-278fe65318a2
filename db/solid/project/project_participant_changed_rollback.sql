ALTER TRIGGER change_participant_on_pipeline_task_owner_changed ON pipeline_task 
RENAME TO tri_add_participant_on_pipeline_task_owner_changed;

DROP TRIGGER IF EXISTS notify_project_participant_changed ON project_participant;
DROP FUNCTION IF EXISTS notify_project_participant_changed();

ALTER FUNCTION change_participant_on_pipeline_task_owner_changed() 
RENAME TO add_participant_on_pipeline_task_owner_changed;