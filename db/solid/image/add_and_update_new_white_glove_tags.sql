-- liquibase formatted sql
-- changeset shoushan:add_and_update_new_white_glove_tags logicalFilePath:changelog.sql
INSERT INTO image_tag (id, title, category, description, seq_no) values (1741, 'Certificate', 'Annotation', 'Component', 1741);
INSERT INTO image_tag (id, title, category, description, seq_no) values (1742, 'Control Panel', 'Annotation', 'Component', 1742);
INSERT INTO image_tag (id, title, category, description, seq_no) values (1743, 'Fire Sprinklers Flow Switch', 'Annotation', 'Component', 1743);
INSERT INTO image_tag (id, title, category, description, seq_no) values (1744, 'Leed Certificate', 'Annotation', 'Component', 1744);
INSERT INTO image_tag (id, title, category, description, seq_no) values (1745, 'Caretaker','Annotation', 'Component', 1745);
INSERT INTO image_tag (id, title, category, description, seq_no) values (1746, 'Impact Rated Glass', 'Annotation', 'Component', 1746);
INSERT INTO image_tag (id, title, category, description, seq_no) values (1747, 'Wind Load Sticker', 'Annotation', 'Component', 1747);
INSERT INTO image_tag (id, title, category, description, seq_no) values (1748, 'Window Protection', 'Annotation', 'Component', 1748);

UPDATE image_tag SET title = 'Fire Sprinklers' WHERE id = 1724;
UPDATE image_tag SET title = 'Fire Alarm' WHERE id = 1721;
INSERT INTO image_tag (id, title, category ,seq_no)
    values (10050, 'Elevator Room', 'Location', 10050)
        ON CONFLICT (id) DO UPDATE SET support_update = false;
INSERT INTO image_tag (id, title, category, description, seq_no)
    values (10082, 'Water Leak Detection', 'Annotation', 'Component', 10082)
        ON CONFLICT (id) DO UPDATE SET support_update = false;
INSERT INTO image_tag (id, title, category, description, seq_no)
    values (10083, 'Gas Leak Detection', 'Annotation', 'Component', 10083)
        ON CONFLICT (id) DO UPDATE SET support_update = false;
INSERT INTO image_tag (id, title, category, description, seq_no)
    values (10084, 'Low-Temp System', 'Annotation', 'Component', 10084)
        ON CONFLICT (id) DO UPDATE SET title = 'Low-Temp System', support_update = false;
INSERT INTO image_tag (id, title, category, description, seq_no)
    values (10086, 'Doorman', 'Annotation', 'Component', 10086)
        ON CONFLICT (id) DO UPDATE SET support_update = false;
INSERT INTO image_tag (id, title, category, description, seq_no)
    values (10089, 'Lobby Cameras', 'Annotation', 'Component', 10089)
        ON CONFLICT (id) DO UPDATE SET support_update = false;
INSERT INTO image_tag (id, title, category, description, seq_no)
    values (10103, 'Burglar Alarm', 'Annotation', 'Component', 10103)
        ON CONFLICT (id) DO UPDATE SET support_update = false;

SELECT setval('"image_tag_id_seq"', 11000);
-- rollback DELETE FROM image_tag where id in (1741, 1742, 1743, 1744, 1745, 1746, 1747, 1748);
-- rollback UPDATE image_tag SET title = 'Home Sprinklers' WHERE id = 1724;
-- rollback UPDATE image_tag SET title = 'Fire Alarm' WHERE id = 1721;
-- rollback UPDATE image_tag SET support_update = true WHERE id = 10050;
-- rollback UPDATE image_tag SET support_update = true WHERE id = 10082;
-- rollback UPDATE image_tag SET support_update = true WHERE id = 10083;
-- rollback UPDATE image_tag SET title = 'Low Temp Monitoring', support_update = true WHERE id = 10084;
-- rollback UPDATE image_tag SET support_update = true WHERE id = 10086;
-- rollback UPDATE image_tag SET support_update = true WHERE id = 10089;
-- rollback UPDATE image_tag SET support_update = true WHERE id = 10103;
