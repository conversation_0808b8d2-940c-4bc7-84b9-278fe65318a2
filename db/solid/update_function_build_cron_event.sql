DROP FUNCTION build_cron_event(VARC<PERSON><PERSON>, VARCHAR, VARCHAR, VARCHAR, VARCHAR);

CREATE OR REPLACE FUNCTION build_cron_event(cron_key VARCHAR, routing_key VARCHAR,
cron_expression VARCHAR, display_name VA<PERSON>HA<PERSON>, created_by VARCHAR)
RETURNS VOID
LANGUAGE plpgsql
AS
'
    DECLARE
payload JSON;
    cron_object JSON;
BEGIN
        payload := json_build_object(
                    ''cron_key'', cron_key,
                    ''cron_expression'', cron_expression,
                    ''display_name'', display_name,
                    ''created_by'', created_by,
                    ''trigger_time'', to_char((current_timestamp at time zone ''UTC''),''YYYY-MM-DD"T"HH24:MI:SS.US"Z"'')
        );
        cron_object := json_build_object(
            ''routing_key'', routing_key,
            ''payload'', payload
        );
        PERFORM pg_notify(''publish_event''::text, cron_object::text);
        RETURN;
END;
';

CREATE OR REPLACE FUNCTION schedule_cron_job()
RETURNS TRIGGER
language plpgsql
AS '
    DECLARE
cron_object JSON;
    payload JSON;
    command VARCHAR;
BEGIN
        command = (select CONCAT(''select build_cron_event('''''', NEW.job_key, '''''','''''',NEW.routing_key, '''''','''''',
         NEW.cron_expression, '''''','''''', NEW.display_name, '''''','''''', NEW.created_by, '''''');''));
        PERFORM cron.schedule(NEW.display_name,
                              NEW.cron_expression,
                              command);
RETURN NULL;
END;
';
