CREATE OR REPLACE FUNCTION add_participant_on_pipeline_task_owner_changed() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
   DECLARE
       P_ID BIGINT;
   BEGIN
      select external_id into P_ID from pipeline where id = NEW.pipeline_id;
      -- 检查NEW.owner_id是否为空
      -- 检查NEW.owner_id和OLD.owner_id是否相同
      -- 检查pipeline_id是否存在project表里
      IF (NEW.owner_id IS NOT NULL and NEW.owner_id != '''') AND
           (OLD.owner_id IS NULL or NEW.owner_id != OLD.owner_id) AND
             (EXISTS(select id from project where id = P_ID)) THEN
                INSERT INTO project_participant(project_id, user_id) VALUES (P_ID, NEW.owner_id)
                ON CONFLICT (project_id, user_id) DO NOTHING;
      END IF;
      RETURN NULL;
   END;
';
