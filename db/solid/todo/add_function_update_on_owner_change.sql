-- liquibase formatted sql
-- changeset weizong:add_function_update_on_owner_change logicalFilePath:changelog.sql

CREATE OR REPLACE FUNCTION update_todo_on_owner_change() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    BEGIN
        if NEW.user_id != OLD.user_id THEN
            UPDATE todo SET created_at = DEFAULT WHERE id = NEW.id;
        END IF;
        RETURN NULL;
    END;
';

CREATE TRIGGER update_todo_on_owner_change
         AFTER UPDATE
            ON todo
       FOR EACH ROW
       EXECUTE PROCEDURE update_todo_on_owner_change();

-- rollback drop trigger update_todo_on_owner_change on todo;
-- rollback drop function update_todo_on_owner_change();
