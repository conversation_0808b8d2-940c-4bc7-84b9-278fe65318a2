-- liquibase formatted sql
-- changeset xiaojun:create_new_todo_table logicalFilePath:changelog.sql

-- create table new_todo
create table new_todo (
	id              bigserial primary key,
	user_id         varchar(32) not null,
	type            smallint not null,
	external_id     bigint not null,
	created_at      timestamp default now(),
	updated_at      timestamp default now(),
	completed       boolean not null default false,
	completed_at    timestamp,
	completed_by    varchar(32),
	is_read         boolean not null default false,
	deleted         boolean not null default false,
	is_pinned       boolean,
	followup_date   timestamp,
	relationship    smallint,
	unique(external_id, type, user_id)
);

create index on new_todo(user_id);

-- create trigger on new_todo
CREATE TRIGGER notify_todo_changed
    AFTER INSERT OR UPDATE ON
    new_todo
    FOR EACH ROW
EXECUTE PROCEDURE notify_todo_changed();

CREATE TRIGGER set_todo_update_at
BEFORE UPDATE OR INSERT ON new_todo
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamp();

-- create sync trigger on table todo
CREATE OR REPLACE FUNCTION sync_todo_to_new_table() RETURNS TRIGGER
	LANGUAGE plpgsql
AS
'
    DECLARE
        row RECORD;
    BEGIN
        SELECT * INTO row FROM todo WHERE id = NEW.id;
        INSERT INTO new_todo (id, user_id, type, external_id, created_at, updated_at, completed, completed_at, completed_by,
                                is_read, deleted, is_pinned, followup_date, relationship)
        VALUES (row.id, row.user_id, row.type, row.external_id, row.created_at, row.updated_at, row.completed, row.completed_at,
                row.completed_by, row.is_read, row.deleted, row.is_pinned, row.followup_date, row.relationship)
        ON CONFLICT (id)
        DO UPDATE SET
        user_id = row.user_id,
        type = row.type,
        external_id = row.external_id,
        created_at = row.created_at,
        updated_at = row.updated_at,
        completed = row.completed,
        completed_at = row.completed_at,
        completed_by = row.completed_by,
        is_read = row.is_read,
        deleted = row.deleted,
        is_pinned = row.is_pinned,
        followup_date = row.followup_date,
        relationship = row.relationship;

        RETURN NULL;
    END;
';

CREATE TRIGGER sync_todo_to_new_table
	AFTER INSERT OR UPDATE ON
	todo
	FOR EACH ROW
EXECUTE PROCEDURE sync_todo_to_new_table();

-- drop old trigger on table todo
DROP TRIGGER notify_todo_changed ON todo;

-- rollback CREATE TRIGGER notify_todo_changed AFTER INSERT OR UPDATE ON todo FOR EACH ROW EXECUTE PROCEDURE notify_todo_changed();
-- rollback DROP TRIGGER sync_todo_to_new_table ON todo;
-- rollback DROP FUNCTION IF EXISTS sync_todo_to_new_table();
-- rollback DROP TABLE IF EXISTS new_todo;
