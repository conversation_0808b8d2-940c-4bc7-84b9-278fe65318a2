CREATE TABLE resource_alias (
  id bigserial primary key,
  alias varchar(512) unique NOT NULL,
  created_at timestamp with time zone default now(),
  uploaded_at timestamp with time zone
);

INSERT INTO resource_alias (alias, created_at, uploaded_at)
SELECT alias, min(created_at), min(updated_at) FILTER ( <PERSON><PERSON><PERSON><PERSON> uploaded IS TRUE )
FROM resource_key_alias GROUP BY alias;

GO

CREATE TABLE resource_key_alias_new (
    id BIGSERIAL PRIMARY KEY,
    key VARCHAR(512) NOT NULL,
    alias_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(key, alias_id)
);

INSERT INTO resource_key_alias_new (key, alias_id, created_at)
SELECT p.key, q.id, p.created_at  FROM resource_key_alias as p inner join resource_alias as q on p.alias = q.alias;

GO

DROP TRIGGER notify_resource_key_alias_bind ON resource_key_alias;
DROP TRIGGER notify_resource_key_alias_unbind ON resource_key_alias;
DROP TRIGGER set_timestamp ON resource_key_alias;
DROP FUNCTION notify_resource_key_alias_bind_changed;

ALTER TABLE resource_key_alias RENAME TO resource_key_alias_old;
ALTER TABLE resource_key_alias_new RENAME TO resource_key_alias;

CREATE INDEX ON resource_alias (id, uploaded_at);
CREATE INDEX ON resource_key_alias (key, alias_id, created_at);

GO

CREATE FUNCTION notify_resource_key_alias_bind()
    RETURNS TRIGGER
    LANGUAGE plpgsql
AS $$
declare
    row record;
BEGIN
    FOR row IN (SELECT key, NEW.alias,
                to_char(NEW.uploaded_at at time zone 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"') AS updated_at
                FROM resource_key_alias
                WHERE alias_id = NEW.id AND NEW.uploaded_at IS NOT NULL)
    LOOP
        PERFORM pg_notify('resource_key_changed.resource_key_alias_bind'::text, row_to_json(row)::text);
    END LOOP;
    RETURN NULL;
END;
$$;

GO

CREATE FUNCTION notify_resource_key_alias_bind_from_resource_key_alias()
    RETURNS TRIGGER
    LANGUAGE plpgsql
AS $$
declare
    row record;
BEGIN
    FOR row IN (SELECT NEW.key, alias,
                to_char(NEW.created_at at time zone 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"') AS updated_at
                FROM resource_alias
                WHERE id = NEW.alias_id AND uploaded_at IS NOT NULL)
    LOOP
        PERFORM pg_notify('resource_key_changed.resource_key_alias_bind'::text, row_to_json(row)::text);
    END LOOP;
    RETURN NULL;
END;
$$;

GO

CREATE FUNCTION notify_resource_key_alias_unbind()
    RETURNS TRIGGER
    LANGUAGE plpgsql
AS $$
declare
    row record;
BEGIN
    FOR row IN (SELECT OLD.key, alias,
                to_char(now() at time zone 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"') as updated_at
                FROM resource_alias
                WHERE OLD.alias_id = resource_alias.id AND resource_alias.uploaded_at IS NOT NULL)
    LOOP
        PERFORM pg_notify('resource_key_changed.resource_key_alias_unbind'::text, row_to_json(row)::text);
    END LOOP;
    RETURN NULL;
END;
$$;

GO

CREATE TRIGGER trigger_resource_key_alias_bind
    AFTER UPDATE OR INSERT
    ON resource_alias
    FOR EACH ROW
    WHEN (NEW.uploaded_at IS NOT NULL)
EXECUTE PROCEDURE notify_resource_key_alias_bind();

CREATE TRIGGER trigger_resource_key_alias_bind_from_resource_key_alias
    AFTER INSERT
    ON resource_key_alias
    FOR EACH ROW
EXECUTE PROCEDURE notify_resource_key_alias_bind_from_resource_key_alias();

CREATE TRIGGER trigger_resource_key_alias_unbind
    AFTER DELETE
    ON resource_key_alias
    FOR EACH ROW
EXECUTE PROCEDURE notify_resource_key_alias_unbind();

GO

DO $$
    DECLARE max_created_at timestamptz;
    BEGIN
        SELECT max(created_at) INTO max_created_at FROM resource_key_alias;

        INSERT INTO resource_alias (alias, created_at, uploaded_at)
        SELECT alias, min(created_at), min(updated_at) FILTER ( WHERE uploaded IS TRUE )
        FROM resource_key_alias_old WHERE created_at > max_created_at GROUP BY alias
        ON CONFLICT (alias) DO NOTHING;

        INSERT INTO resource_key_alias (key, created_at, alias_id)
        SELECT p.key, p.created_at, q.id
        FROM resource_key_alias_old as p inner join resource_alias AS q ON p.alias = q.alias
        WHERE p.created_at > max_created_at
        ON CONFLICT (key, alias_id) DO UPDATE SET created_at = excluded.created_at;
    END;
$$;

GO

DROP TABLE resource_key_alias_old;
