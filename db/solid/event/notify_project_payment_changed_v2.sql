CREATE OR REPLACE FUNCTION notify_project_payment_changed() RETURNS TRIGGER
    LANGUAGE plpgsql
AS
'
    DECLARE
        payment JSON;
        event   JSON;
        payload JSON;
    BEGIN
        if OLD.is_paid != NEW.is_paid THEN
            payment = json_build_object(''project_id'', NEW.project_id,
                                        ''is_paid'', NEW.is_paid,
                                        ''updated_at'',to_char(NEW.updated_at at time zone ''UTC'',
                                                ''YYYY-MM-DD"T"HH24:MI:SS.US"Z"''));
            payload = json_build_object(''payment'', payment);
            event = json_build_object(''routing_key'', ''project_payment_changed'', ''payload'', payload);
            PERFORM event_notify(''publish_event''::text, event::text);
        END IF;
        RETURN NULL;
    END;
';
